version: '3.8'

services:
  mysql:
    image: mysql:5.7.42
    container_name: ${PROJECT_NAME}_mysql
    command: --default-authentication-plugin=mysql_native_password --max_allowed_packet=1G --innodb_buffer_pool_size=1G --innodb_log_file_size=256M --innodb_flush_log_at_trx_commit=2 --sync_binlog=0
    platform: linux/amd64
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    ports:
      - "${MYSQL_EXTERNAL_PORT}:3306"
    volumes:
      - ./mysql-data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
      - ./mysql-conf:/etc/mysql/conf.d
    networks:
      - drupalnet
    restart: always
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
  adminer:
    image: adminer
    container_name: ${PROJECT_NAME}_adminer
    ports:
      - "${ADMINER_EXTERNAL_PORT}:8080"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - drupalnet
    restart: always
  drupal:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ${PROJECT_NAME}_drupal
    environment:
      AH_SITE_ENVIRONMENT: ${AH_SITE_ENVIRONMENT}
      MYSQL_HOST: ${MYSQL_HOST}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      DRUPAL_HASH_SALT: ${DRUPAL_HASH_SALT}
      DRUPAL_BASE_URL: ${DRUPAL_BASE_URL}
    ports:
      - "${DRUPAL_EXTERNAL_PORT}:80"
    depends_on:
      mysql:
        condition: service_healthy
    volumes:
      - ./docroot/sites/default/files:/var/www/html/sites/default/files
    networks:
      - drupalnet
    restart: always

  memcache:
    container_name: ${PROJECT_NAME}_memcache
    image: wodby/memcached:1.4-2.0.0
    platform: linux/amd64
    ports:
      - "${MEMCACHE_EXTERNAL_PORT}:11211"
    networks:
      - drupalnet
    restart: always

networks:
  drupalnet:
