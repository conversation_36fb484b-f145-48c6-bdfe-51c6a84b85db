[mysqld]
# Configurazioni per import di grandi database
max_allowed_packet = 1G
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0
innodb_doublewrite = 0
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1

# Timeout settings
wait_timeout = 28800
interactive_timeout = 28800
net_read_timeout = 600
net_write_timeout = 600

# Memory settings
key_buffer_size = 256M
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
myisam_sort_buffer_size = 64M
thread_cache_size = 8
query_cache_size = 32M
tmp_table_size = 64M
max_heap_table_size = 64M

# Connection settings
max_connections = 100
max_connect_errors = 10000

[mysql]
max_allowed_packet = 1G

[mysqldump]
max_allowed_packet = 1G
