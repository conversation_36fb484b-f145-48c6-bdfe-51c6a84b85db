# Usa un'immagine Drupal pre-costruita
FROM drupal:7.98-apache

# Configurazione PHP per Drupal
RUN echo "memory_limit=1G" > /usr/local/etc/php/conf.d/memory-limit.ini \
  && echo "upload_max_filesize=64M" > /usr/local/etc/php/conf.d/upload.ini \
  && echo "post_max_size=64M" >> /usr/local/etc/php/conf.d/upload.ini \
  && echo "max_execution_time=300" > /usr/local/etc/php/conf.d/execution.ini

# Abilita i moduli Apache necessari
RUN a2enmod rewrite

# Rimuovi il codice Drupal di default e copia il nostro
RUN rm -rf /var/www/html/*

# Copia il codice Drupal
COPY docroot /var/www/html

# Crea le directory necessarie
RUN mkdir -p /var/www/html/sites/default/files \
  && mkdir -p /home/<USER>/settings

# Copia il file di configurazione
COPY settings.test.php /home/<USER>/settings/

# Crea uno script di configurazione dinamica
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Imposta i permessi corretti
RUN chown -R www-data:www-data /var/www/html \
  && chmod -R 755 /var/www/html \
  && chmod -R 777 /var/www/html/sites/default/files

# Espone la porta su cui Apache ascolta
EXPOSE 80

# Usa lo script di entrypoint personalizzato
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["apache2-foreground"]