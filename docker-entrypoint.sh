#!/bin/bash
set -e

# Funzione per attendere che MySQL sia pronto
wait_for_mysql() {
    echo "Aspettando che MySQL sia pronto..."
    while ! php -r "
        try {
            \$pdo = new PDO('mysql:host=${MYSQL_HOST};port=3306', '${MYSQL_USER}', '${MYSQL_PASSWORD}');
            echo 'MySQL è pronto!';
            exit(0);
        } catch (Exception \$e) {
            exit(1);
        }
    "; do
        echo "MySQL non è ancora pronto - aspetto..."
        sleep 2
    done
    echo "MySQL è pronto!"
}

# Funzione per aggiornare la configurazione del database nel settings.php esistente
update_database_config() {
    echo "Aggiornamento configurazione database nel settings.php esistente..."

    # Backup del file originale
    cp /var/www/html/sites/default/settings.php /var/www/html/sites/default/settings.php.backup

    # Aggiorna la configurazione del database usando sed
    sed -i "s/'database' => '[^']*'/'database' => '${MYSQL_DATABASE}'/g" /var/www/html/sites/default/settings.php
    sed -i "s/'username' => '[^']*'/'username' => '${MYSQL_USER}'/g" /var/www/html/sites/default/settings.php
    sed -i "s/'password' => '[^']*'/'password' => '${MYSQL_PASSWORD}'/g" /var/www/html/sites/default/settings.php
    sed -i "s/'host' => '[^']*'/'host' => '${MYSQL_HOST}'/g" /var/www/html/sites/default/settings.php

    # Aggiorna il drupal_hash_salt se è vuoto
    if grep -q "\$drupal_hash_salt = '';" /var/www/html/sites/default/settings.php; then
        sed -i "s/\$drupal_hash_salt = '';/\$drupal_hash_salt = '${DRUPAL_HASH_SALT}';/g" /var/www/html/sites/default/settings.php
    fi

    # Aggiungi configurazioni specifiche alla fine del file se non esistono già
    if ! grep -q "DOCKER ENVIRONMENT CONFIGURATIONS" /var/www/html/sites/default/settings.php; then
        cat >> /var/www/html/sites/default/settings.php << 'EOF'

/**
 * DOCKER ENVIRONMENT CONFIGURATIONS
 * Configurazioni specifiche per l'ambiente Docker
 */

// Configurazioni per l'ambiente di sviluppo
$conf['theme_debug'] = TRUE;
$conf['preprocess_css'] = FALSE;
$conf['preprocess_js'] = FALSE;
$conf['cache'] = FALSE;
$conf['block_cache'] = FALSE;
$conf['page_cache_maximum_age'] = 0;

// Base URL per Docker
if (getenv('DRUPAL_BASE_URL')) {
  $base_url = getenv('DRUPAL_BASE_URL');
}

// Configurazioni specifiche Sardegna Turismo
if (getenv('STRUTTURE_RICETTIVE_URL')) {
  $conf['strutture_ricettive_url'] = getenv('STRUTTURE_RICETTIVE_URL');
  $conf['strutture_ricettive_client_key'] = getenv('STRUTTURE_RICETTIVE_CLIENT_KEY');
  $conf['strutture_ricettive_client_secret'] = getenv('STRUTTURE_RICETTIVE_CLIENT_SECRET');
}

// Configurazioni MailUp
if (getenv('MAILUP_ENABLE_SYNC')) {
  $conf['mailup_enable_sync'] = (getenv('MAILUP_ENABLE_SYNC') === 'TRUE');
  $conf['mailup_client_id'] = getenv('MAILUP_CLIENT_ID');
  $conf['mailup_client_secret'] = getenv('MAILUP_CLIENT_SECRET');
  $conf['mailup_callback_uri'] = getenv('MAILUP_CALLBACK_URI');
  $conf['mailup_username'] = getenv('MAILUP_USERNAME');
  $conf['mailup_password'] = getenv('MAILUP_PASSWORD');
}

// API Security
if (getenv('API_SECRET_KEY')) {
  $conf['api'] = array(
    'allowed_ips' => array(
      '************', // IP RAS
      '*************', // IP Net7
      '*************', // App Sardinia PROD
      '*************', // App Sardinia PROD
      '*************', // App Sardinia STAGE
      '*************', // Abbinsula
      '*************', // Abbinsula
      '127.0.0.1', // Localhost per sviluppo
      '*********/8', // Docker networks
    ),
    'excluded_ips' => array(),
    'secret_key' => getenv('API_SECRET_KEY'),
  );
}

// Configurazione Memcache (se disponibile)
if (getenv('AH_SITE_ENVIRONMENT') === 'local') {
  $conf['memcache_servers'] = array(
    'memcache:11211' => 'default',
  );
  $conf['cache_backends'][] = 'sites/all/modules/contrib/memcache/memcache.inc';
  $conf['cache_default_class'] = 'MemCacheDrupal';
  $conf['cache_class_cache_form'] = 'DrupalDatabaseCache';
}
EOF
    fi

    echo "Configurazione database aggiornata con successo!"
}

# Aspetta che MySQL sia pronto
wait_for_mysql

# Aggiorna la configurazione del database
update_database_config

# Imposta i permessi corretti
chown www-data:www-data /var/www/html/sites/default/settings.php
chmod 644 /var/www/html/sites/default/settings.php

echo "Inizializzazione completata. Avvio di Apache..."

# Esegui il comando originale
exec "$@"
