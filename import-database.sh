#!/bin/bash

# Script per importare il database Sardegna Turismo
# Gestisce file di grandi dimensioni con timeout appropriati

set -e

# Configurazioni
DB_CONTAINER="sardegnaturismosite_mysql"
DB_NAME="sardegnaturismosite"
DB_USER="user"
DB_PASSWORD="password"
DUMP_FILE="$1"

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funzione per stampare messaggi colorati
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verifica che il file dump sia specificato
if [ -z "$DUMP_FILE" ]; then
    print_error "Uso: $0 <path-to-dump-file>"
    print_error "Esempio: $0 ~/Developer/RAS/dump/stAcquia_StagingD7/test-sardegnaturismosite-sardegnaturismositefgwa2iqnwj-2025-03-11.sql"
    exit 1
fi

# Verifica che il file dump esista
if [ ! -f "$DUMP_FILE" ]; then
    print_error "File dump non trovato: $DUMP_FILE"
    exit 1
fi

# Mostra informazioni sul file
DUMP_SIZE=$(ls -lh "$DUMP_FILE" | awk '{print $5}')
print_message "File dump: $DUMP_FILE"
print_message "Dimensione: $DUMP_SIZE"

# Verifica che il container MySQL sia in esecuzione
if ! docker ps | grep -q "$DB_CONTAINER"; then
    print_error "Container MySQL non in esecuzione: $DB_CONTAINER"
    print_message "Avvia i container con: docker-compose up -d"
    exit 1
fi

# Verifica connessione al database
print_message "Verifica connessione al database..."
if ! docker exec "$DB_CONTAINER" mysqladmin ping -h localhost -u "$DB_USER" -p"$DB_PASSWORD" --silent; then
    print_error "Impossibile connettersi al database"
    exit 1
fi

print_message "Connessione al database OK"

# Ricrea il database
print_message "Ricreazione del database $DB_NAME..."
docker exec "$DB_CONTAINER" mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "DROP DATABASE IF EXISTS $DB_NAME; CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

print_message "Database ricreato con successo"

# Import del database
print_message "Avvio import del database..."
print_warning "Questo processo può richiedere diversi minuti per file di grandi dimensioni..."

# Usa pv se disponibile per mostrare il progresso
if command -v pv &> /dev/null; then
    print_message "Importazione con indicatore di progresso..."
    pv "$DUMP_FILE" | docker exec -i "$DB_CONTAINER" mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"
else
    print_message "Importazione senza indicatore di progresso..."
    docker exec -i "$DB_CONTAINER" mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$DUMP_FILE"
fi

if [ $? -eq 0 ]; then
    print_message "Import completato con successo!"
    
    # Verifica alcune tabelle importate
    print_message "Verifica tabelle importate..."
    TABLE_COUNT=$(docker exec "$DB_CONTAINER" mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SHOW TABLES;" | wc -l)
    print_message "Numero di tabelle importate: $((TABLE_COUNT - 1))"
    
    # Mostra alcune tabelle principali di Drupal
    print_message "Verifica tabelle principali di Drupal..."
    docker exec "$DB_CONTAINER" mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SHOW TABLES LIKE 'users'; SHOW TABLES LIKE 'node'; SHOW TABLES LIKE 'system';"
    
else
    print_error "Errore durante l'import del database"
    exit 1
fi

print_message "Processo completato!"
print_message "Ora puoi accedere al sito su: http://localhost"
print_message "Adminer disponibile su: http://localhost:8080"
