{"name": "brumann/polyfill-unserialize", "description": "Backports unserialize options introduced in PHP 7.0 to older PHP versions.", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Brumann\\Polyfill\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\Brumann\\Polyfill\\": "tests/"}}, "minimum-stability": "stable", "require": {"php": "^5.3|^7.0"}}