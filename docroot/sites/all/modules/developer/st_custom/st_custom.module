<?php

module_load_include('inc', 'st_custom', 'st_custom.page');

function st_custom_language_switch_links_alter(array &$links, $type, $path)
{
  global $language;
}

/**
 * Implements hook_form_BASE_FORM_ID_alter().
 */
function st_custom_form_user_profile_form_alter(&$form, &$form_state, $form_id)
{
  if (empty($form['nome']['it'][0]['value']['#default_value'])) {
    $form['nome']['it'][0]['value']['#default_value'] = (isset($form['#user']->nome['und'][0]['value']) && !empty($form['#user']->nome['und'][0]['value'])) ? $form['#user']->nome['und'][0]['value'] : '';
  }
}

/**
 * Hook form_alter().
 *
 * @param type $form
 * @param type $form_state
 * @param type $form_id
 */
function st_custom_form_alter(&$form, &$form_state, $form_id)
{

  // Questo codice. Serve per modificare gli exposed filters
  // rispecchiando gli ispiratori al secondo livello della tassonomia.
  if ($form_id == 'views_exposed_form') {
    $view = $form_state['view'];
    if ($view->name == 'ispiratori_primo_livello' && $view->current_display == 'block_cosa_fare_contestuale') {
      ///-{ Modifica dei blocchi contestuali  alter your exposed form here
      //-{ Questo scorrerebbe le options.
      // foreach ($form['field_cosa_fare_tid']['#options'] as $key => $value) {
      //    $placeholder_codice = 1;
      // }

      // Le options dovrebbero essere un array associativo
      // --> $options = array('All' => '- Any -');
      $term_id = arg(2);
      $term_children = taxonomy_get_children($term_id);
      $t_options = array();
      global $language;
      $langcode = $language->language;
      // $t_options2 = array();
      foreach ($term_children as $key => $value) {
        $t_key = $key;
        // $t_value = $value->name;
        $t_term = taxonomy_term_load($t_key);
        // $trans_term = i18n_taxonomy_term_get_translation($t_term, $langcode);
        $trans_term = i18n_taxonomy_localize_terms($t_term);
        $t_options[$t_key] = $trans_term->name;
        // $t_options2[] = array($t_key => $t_value);
      }
      $form['field_cosa_fare_tid']['#options'] = $t_options;
      // $placeholder_codice = 2;
    }
  }

  if ($form_id == 'attrattore_node_form') {
    if (
      !isset($form['nid']['#value']) ||
      !isset($form['#node']->field_importanza_attrattore[LANGUAGE_NONE][0]['value'])
    ) {
      return;
    }

    $nid = $form['nid']['#value'];
    $node_p = node_load($nid);
    if (!isset($node_p)) {
      return;
    }
    $value_strange = $form['#node']->field_importanza_attrattore[LANGUAGE_NONE][0]['value'];
    $value_correct = $node_p->field_importanza_attrattore[LANGUAGE_NONE][0]['value'];
    if ($value_strange !== $value_correct) {
      // Inutile fare il check ma potrebbe servire per analisi.
      $form['#node']->field_importanza_attrattore[LANGUAGE_NONE][0]['value'] = $value_correct;
      $form['field_importanza_attrattore'][LANGUAGE_NONE]['#default_value'] = $value_correct;
      // Un Alias di #node -> $form['#entity']->field_importanza_attrattore['und'][0]['value'] = $value_correct;
    }
  }
}

/**
 * Implements hook_views_pre_view().
 */
function st_custom_views_pre_view(&$view, &$display_id, &$args)
{

  if ((($view->name == 'strutture_ricettive') && ($view->current_display == 'page_strutture_ricettive'))
    || $view->name == 'mappe_ricerca'
  ) {
    // Alcune località non vengono rilevate fa google se non si aggiunge "sardegna"
    $filter_input = $view->get_exposed_input();
    // Nota: non lo eseguo al form alter, perché questo modificherebbe anche la visualizzazione del filtro
    // if (isset($form_state['input']['node_field_georef_latlon']) && !empty($form_state['input']['node_field_georef_latlon'])) {
    // $form_state['input']['node_field_georef_latlon'] .= ', sardegna';
    // }
    if (!empty($filter_input['node_field_georef_latlon']) && strpos($filter_input['node_field_georef_latlon'], 'ardegna') === false) {
      $view->exposed_input['node_field_georef_latlon'] .= ', sardegna';
    }
  }

  // Fix per: https://wellnet.atlassian.net/browse/FS-604
  if ($view->name == 'mappe_ricerca' && $display_id == 'page_esplora') {
    $filter_input = $view->get_exposed_input();
    if (isset($filter_input['node_field_georef_latlon']) && empty($filter_input['node_field_georef_latlon'])) {
      unset($view->exposed_input['node_field_georef_latlon']);
    }
  }
}

/**
 * Implements hook_views_post_execute().
 */
function st_custom_views_post_execute(&$view)
{
  if (($view->name == 'vista_di_ricerca') && ($view->current_display == 'page_ricerca')) {
    foreach ($view->result as $resultRow) {
      if (isset($resultRow->_entity_properties['search_api_aggregation_2']) && is_array($resultRow->_entity_properties['search_api_aggregation_2'])) {
        $scalar_row = array_shift($resultRow->_entity_properties['search_api_aggregation_2']);
        foreach ($resultRow->_entity_properties['search_api_aggregation_2'] as $chunk) {
          $first_char = $chunk[0];
          if ($first_char != ',' && $first_char != '.' && $first_char != ';' && $first_char != ':') {
            $scalar_row .= ' ';
          }
          $scalar_row .= ltrim($chunk);
        }
        $resultRow->_entity_properties['search_api_aggregation_2'] = array(
          0 => $scalar_row,
        );
      } elseif (
        isset($resultRow->_entity_properties['search_api_aggregation_2']) &&
        is_string($resultRow->_entity_properties['search_api_aggregation_2'])
      ) {
        $resultRow->_entity_properties['search_api_aggregation_2'] = preg_replace(array("/(\s*,+)+/", "/(\s*\.+)+/", "/(\s*\:+)+/", "/(\s*\;+)+/"), array(",", ".", ":", ";"), $resultRow->_entity_properties['search_api_aggregation_2']);
      }
    }
  }
}

/**
 * Implements hook_search_api_solr_query_alter
 */
function st_custom_search_api_solr_query_alter(array &$call_args, SearchApiQueryInterface $query)
{

  // TASK: MAN-72
  $path = current_path();
  if (in_array($path, ['organizza/dormire'])) {
    $q_string = drupal_get_query_parameters();
    $node_field_georef_latlon = filter_xss($q_string['node_field_georef_latlon']);

    // Devi rimuovere questo parametro dalla query
    if (!empty($node_field_georef_latlon)) {

      // query sulla località
      $geofilter = array_pop($call_args['params']['fq']);
      $geofilter = _get_geofilt_query_parameters($geofilter);
      foreach ($geofilter as $p => $v) {
        $call_args['params'][$p] = $v;
      }

      $fields = [
        'tm_node$field_indirizzo',
        'tm_node$field_comune$name',
        'tm_node$field_comune$name',
      ];
      foreach ($fields as $key => $field) {
        $fields[$key] = $field . ':' . $node_field_georef_latlon;
      }
      $fq = implode(" OR ", $fields) . ' OR _query_:"{!geofilt}"';
      array_push($call_args['params']['fq'], $fq);
    }

    watchdog("esplora_solr_query", "@msg", ['@msg' => print_r($call_args, TRUE)], WATCHDOG_INFO);
  }
}

/**
 * A partire dalla stringa con cui views genera il filtro geografico per solr
 * genera un array con i parametri del filtro, da passare come parametri
 * della query.
 *
 * @param $geofilter string
 *
 * @return array
 */
function _get_geofilt_query_parameters($geofilter)
{
  // format of $geofilter string.
  // {!geofilt pt=39.3149285,8.5355884 sfield=locs_node$field_georef$latlon d=15}
  $geofilter = trim($geofilter, "{}!");
  $fragments = explode(" ", $geofilter);

  $q = [];
  foreach ($fragments as $fragment) {
    if ($fragment == 'geofilt') {
      continue;
    }

    $items = explode("=", $fragment);
    $q[$items[0]] = $items[1];
  }

  return $q;
}

/**
 * Implements hook_field_attach_submit().
 *
 * Per l'evento, il campo geofield "field_riassunto_tappe_georef" genera un errore
 * nell'anteprima. Questo codice risolve con una sola asunzione hardcoded,
 * i campi di tipo geofield non sono tradotti, quindi l'id della lingua è sempre 'und'
 */
function st_custom_field_attach_submit($entity_type, $entity, $form, &$form_state)
{

  list($entity_id,, $bundle_name) = entity_extract_ids($entity_type, $entity);
  $instances = field_info_instances($entity_type, $bundle_name);
  foreach ($instances as $instance) {
    $field = field_info_field($instance['field_name']);
    if ($field['type'] === 'geofield') {
      ctools_include('plugins');
      $backend = ctools_get_plugins('geofield', 'geofield_backend', $field['settings']['backend']);
      $save_callback = $backend['save'];
      $field_name = $instance['field_name'];
      // assume that all geoField are not translated
      $langcode = 'und';
      if (empty($entity->{$field_name}) || empty($entity->{$field_name}[$langcode])) {
        // The entity object may not include this field even though it is
        // defined in the bundle instances.
        continue;
      }
      $items = &$entity->{$field_name}[$langcode];
      foreach ($items as $delta => $item) {
        $items[$delta] = geofield_compute_values($item);
        if (!empty($items[$delta]['geom'])) {
          $items[$delta]['geom'] = $save_callback($items[$delta]['geom']);
        }
      }
    }
  }
}
/**
 * Implements hook_services_authentication_info_alter().
 *
 * Nell'interrogazione dei servizi:
 *  - rest/eventilocali.json (sturismo_service)
 *  - rest/attrattori.json (sturismo_service)
 *  - rest/taxonomy_term.json (sturismo_tassonomie)
 *  - rest/tassonomie.json (sturismo_tassonomie)
 *
 * utilizziamo l'utente ras-admin per processare le richieste.
 * Questo perché senza autenticazione vengono restituiti risultati non
 * corretti.
 */
function st_custom_services_authentication_info_alter(&$info, $module)
{
  global $user;

  $endpoint_name = services_get_server_info('endpoint');
  if (in_array($endpoint_name, array('sturismo_service', 'sturismo_tassonomie')) && $user->uid == 0) {
    $user = user_load(1);
  }
}

/**
 * Implements hook_page_delivery_callback_alter().
 */
function st_custom_page_delivery_callback_alter(&$callback)
{
  if (arg(0) == 'node' && user_is_anonymous() && $callback !== 'ajax_deliver') {
    $callback = 'st_custom_deliver_html_page';
  }
}

/**
 * Implements hook_file_url_alter().
 *
 * Reindirizza gli URL dei file mancanti in locale verso produzione.
 * Questo è utile quando si lavora con un dump del database di produzione
 * ma senza i file fisici.
 */
function st_custom_file_url_alter(&$uri) {
  // Solo per file pubblici (public://)
  $scheme = file_uri_scheme($uri);
  if ($scheme == 'public') {
    $wrapper = file_stream_wrapper_get_instance_by_scheme($scheme);
    $local_path = $wrapper->getDirectoryPath() . '/' . file_uri_target($uri);

    // Se il file non esiste localmente, reindirizza verso produzione
    if (!file_exists($local_path)) {
      $production_base = 'https://sardegnaturismo.it';
      $file_path = file_uri_target($uri);
      $uri = $production_base . '/sites/default/files/' . $file_path;
    }
  }
}
