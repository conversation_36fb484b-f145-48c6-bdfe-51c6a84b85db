<?php

/**
 * @file
 * Tests for book.module.
 */

/**
 * Tests the functionality of the Book module.
 */
class BookTestCase extends DrupalWebTestCase {

  /**
   * A book node.
   *
   * @var object
   */
  protected $book;

  /**
   * A user with permission to create and edit books.
   *
   * @var object
   */
  protected $book_author;

  /**
   * A user with permission to view a book and access printer-friendly version.
   *
   * @var object
   */
  protected $web_user;

  /**
   * A user with permission to create and edit books and to administer blocks.
   *
   * @var object
   */
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Book functionality',
      'description' => 'Create a book, add pages, and test book interface.',
      'group' => 'Book',
    );
  }

  function setUp() {
    parent::setUp(array('book', 'node_access_test'));

    // node_access_test requires a node_access_rebuild().
    node_access_rebuild();

    // Create users.
    $this->book_author = $this->drupalCreateUser(array('create new books', 'create book content', 'edit own book content', 'add content to books'));
    $this->web_user = $this->drupalCreateUser(array('access printer-friendly version', 'node test view'));
    $this->admin_user = $this->drupalCreateUser(array('create new books', 'create book content', 'edit own book content', 'add content to books', 'administer blocks', 'administer permissions', 'administer book outlines', 'node test view'));
  }

  /**
   * Creates a new book with a page hierarchy.
   */
  function createBook() {
    // Create new book.
    $this->drupalLogin($this->book_author);

    $this->book = $this->createBookNode('new');
    $book = $this->book;

    /*
     * Add page hierarchy to book.
     * Book
     *  |- Node 0
     *   |- Node 1
     *   |- Node 2
     *  |- Node 3
     *  |- Node 4
     */
    $nodes = array();
    $nodes[] = $this->createBookNode($book->nid); // Node 0.
    $nodes[] = $this->createBookNode($book->nid, $nodes[0]->book['mlid']); // Node 1.
    $nodes[] = $this->createBookNode($book->nid, $nodes[0]->book['mlid']); // Node 2.
    $nodes[] = $this->createBookNode($book->nid); // Node 3.
    $nodes[] = $this->createBookNode($book->nid); // Node 4.

    $this->drupalLogout();

    return $nodes;
  }

  /**
   * Tests book functionality through node interfaces.
   */
  function testBook() {
    // Create new book.
    $nodes = $this->createBook();
    $book = $this->book;

    $this->drupalLogin($this->web_user);

    // Check that book pages display along with the correct outlines and
    // previous/next links.
    $this->checkBookNode($book, array($nodes[0], $nodes[3], $nodes[4]), FALSE, FALSE, $nodes[0]);
    $this->checkBookNode($nodes[0], array($nodes[1], $nodes[2]), $book, $book, $nodes[1], array($book));
    $this->checkBookNode($nodes[1], NULL, $nodes[0], $nodes[0], $nodes[2], array($book, $nodes[0]));
    $this->checkBookNode($nodes[2], NULL, $nodes[1], $nodes[0], $nodes[3], array($book, $nodes[0]));
    $this->checkBookNode($nodes[3], NULL, $nodes[2], $book, $nodes[4], array($book));
    $this->checkBookNode($nodes[4], NULL, $nodes[3], $book, FALSE, array($book));

    $this->drupalLogout();

    // Create a second book, and move an existing book page into it.
    $this->drupalLogin($this->book_author);
    $other_book = $this->createBookNode('new');
    $node = $this->createBookNode($book->nid);
    $edit = array('book[bid]' => $other_book->nid);
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));

    $this->drupalLogout();
    $this->drupalLogin($this->web_user);

    // Check that the nodes in the second book are displayed correctly.
    // First we must set $this->book to the second book, so that the
    // correct regex will be generated for testing the outline.
    $this->book = $other_book;
    $this->checkBookNode($other_book, array($node), FALSE, FALSE, $node);
    $this->checkBookNode($node, NULL, $other_book, $other_book, FALSE, array($other_book));
  }

  /**
   * Checks the outline of sub-pages; previous, up, and next.
   *
   * Also checks the printer friendly version of the outline.
   *
   * @param $node
   *   Node to check.
   * @param $nodes
   *   Nodes that should be in outline.
   * @param $previous
   *   (optional) Previous link node. Defaults to FALSE.
   * @param $up
   *   (optional) Up link node. Defaults to FALSE.
   * @param $next
   *   (optional) Next link node. Defaults to FALSE.
   * @param $breadcrumb
   *   (optional) The nodes that should be displayed in the breadcrumb.
   */
  function checkBookNode($node, $nodes = NULL, $previous = FALSE, $up = FALSE, $next = FALSE, array $breadcrumb = array()) {
    // $number does not use drupal_static as it should not be reset
    // since it uniquely identifies each call to checkBookNode().
    static $number = 0;
    $this->drupalGet('node/' . $node->nid);

    // Check outline structure.
    if ($nodes !== NULL) {
      $this->assertPattern($this->generateOutlinePattern($nodes), format_string('Node %number outline confirmed.', array('%number' => $number)));
    }
    else {
      $this->pass(format_string('Node %number does not have outline.', array('%number' => $number)));
    }

    // Check previous, up, and next links.
    if ($previous) {
      $this->assertRaw(l('‹ ' . $previous->title, 'node/' . $previous->nid, array('attributes' => array('class' => array('page-previous'), 'title' => t('Go to previous page')))), 'Previous page link found.');
    }

    if ($up) {
      $this->assertRaw(l('up', 'node/' . $up->nid, array('attributes' => array('class' => array('page-up'), 'title' => t('Go to parent page')))), 'Up page link found.');
    }

    if ($next) {
      $this->assertRaw(l($next->title . ' ›', 'node/' . $next->nid, array('attributes' => array('class' => array('page-next'), 'title' => t('Go to next page')))), 'Next page link found.');
    }

    // Compute the expected breadcrumb.
    $expected_breadcrumb = array();
    $expected_breadcrumb[] = url('');
    foreach ($breadcrumb as $a_node) {
      $expected_breadcrumb[] = url('node/' . $a_node->nid);
    }

    // Fetch links in the current breadcrumb.
    $links = $this->xpath('//div[@class="breadcrumb"]/a');
    $got_breadcrumb = array();
    foreach ($links as $link) {
      $got_breadcrumb[] = (string) $link['href'];
    }

    // Compare expected and got breadcrumbs.
    $this->assertIdentical($expected_breadcrumb, $got_breadcrumb, 'The breadcrumb is correctly displayed on the page.');

    // Check printer friendly version.
    $this->drupalGet('book/export/html/' . $node->nid);
    $this->assertText($node->title, 'Printer friendly title found.');
    $this->assertRaw(check_markup($node->body[LANGUAGE_NONE][0]['value'], $node->body[LANGUAGE_NONE][0]['format']), 'Printer friendly body found.');

    $number++;
  }

  /**
   * Creates a regular expression to check for the sub-nodes in the outline.
   *
   * @param array $nodes
   *   An array of nodes to check in outline.
   *
   * @return
   *   A regular expression that locates sub-nodes of the outline.
   */
  function generateOutlinePattern($nodes) {
    $outline = '';
    foreach ($nodes as $node) {
      $outline .= '(node\/' . $node->nid . ')(.*?)(' . $node->title . ')(.*?)';
    }

    return '/<div id="book-navigation-' . $this->book->nid . '"(.*?)<ul(.*?)' . $outline . '<\/ul>/s';
  }

  /**
   * Creates a book node.
   *
   * @param $book_nid
   *   A book node ID or set to 'new' to create a new book.
   * @param $parent
   *   (optional) Parent book reference ID. Defaults to NULL.
   */
  function createBookNode($book_nid, $parent = NULL) {
    // $number does not use drupal_static as it should not be reset
    // since it uniquely identifies each call to createBookNode().
    static $number = 0; // Used to ensure that when sorted nodes stay in same order.

    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $number . ' - SimpleTest test node ' . $this->randomName(10);
    $edit["body[$langcode][0][value]"] = 'SimpleTest test body ' . $this->randomName(32) . ' ' . $this->randomName(32);
    $edit['book[bid]'] = $book_nid;

    if ($parent !== NULL) {
      $this->drupalPost('node/add/book', $edit, t('Change book (update list of parents)'));

      $edit['book[plid]'] = $parent;
      $this->drupalPost(NULL, $edit, t('Save'));
    }
    else {
      $this->drupalPost('node/add/book', $edit, t('Save'));
    }

    // Check to make sure the book node was created.
    $node = $this->drupalGetNodeByTitle($edit['title']);
    $this->assertNotNull(($node === FALSE ? NULL : $node), 'Book node found in database.');
    $number++;

    return $node;
  }

  /**
   * Tests book export ("printer-friendly version") functionality.
   */
  function testBookExport() {
    // Create a book.
    $nodes = $this->createBook();

    // Login as web user and view printer-friendly version.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node/' . $this->book->nid);
    $this->clickLink(t('Printer-friendly version'));

    // Make sure each part of the book is there.
    foreach ($nodes as $node) {
      $this->assertText($node->title, 'Node title found in printer friendly version.');
      $this->assertRaw(check_markup($node->body[LANGUAGE_NONE][0]['value'], $node->body[LANGUAGE_NONE][0]['format']), 'Node body found in printer friendly version.');
    }

    // Make sure we can't export an unsupported format.
    $this->drupalGet('book/export/foobar/' . $this->book->nid);
    $this->assertResponse('404', 'Unsupported export format returned "not found".');

    // Make sure we get a 404 on a not existing book node.
    $this->drupalGet('book/export/html/123');
    $this->assertResponse('404', 'Not existing book node returned "not found".');

    // Make sure an anonymous user cannot view printer-friendly version.
    $this->drupalLogout();

    // Load the book and verify there is no printer-friendly version link.
    $this->drupalGet('node/' . $this->book->nid);
    $this->assertNoLink(t('Printer-friendly version'), 'Anonymous user is not shown link to printer-friendly version.');

    // Try getting the URL directly, and verify it fails.
    $this->drupalGet('book/export/html/' . $this->book->nid);
    $this->assertResponse('403', 'Anonymous user properly forbidden.');

    // Now grant anonymous users permission to view the printer-friendly
    // version and verify that node access restrictions still prevent them from
    // seeing it.
    user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array('access printer-friendly version'));
    $this->drupalGet('book/export/html/' . $this->book->nid);
    $this->assertResponse('403', 'Anonymous user properly forbidden from seeing the printer-friendly version when denied by node access.');
  }

  /**
   * Tests the functionality of the book navigation block.
   */
  function testBookNavigationBlock() {
    $this->drupalLogin($this->admin_user);

    // Set block title to confirm that the interface is available.
    $block_title = $this->randomName(16);
    $this->drupalPost('admin/structure/block/manage/book/navigation/configure', array('title' => $block_title), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

    // Set the block to a region to confirm block is available.
    $edit = array();
    $edit['blocks[book_navigation][region]'] = 'footer';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertText(t('The block settings have been updated.'), 'Block successfully move to footer region.');

     // Give anonymous users the permission 'node test view'.
     $edit = array();
     $edit[DRUPAL_ANONYMOUS_RID . '[node test view]'] = TRUE;
     $this->drupalPost('admin/people/permissions/' . DRUPAL_ANONYMOUS_RID, $edit, t('Save permissions'));
     $this->assertText(t('The changes have been saved.'), "Permission 'node test view' successfully assigned to anonymous users.");

    // Test correct display of the block.
    $nodes = $this->createBook();
    $this->drupalGet('<front>');
    $this->assertText($block_title, 'Book navigation block is displayed.');
    $this->assertText($this->book->title, format_string('Link to book root (@title) is displayed.', array('@title' => $nodes[0]->title)));
    $this->assertNoText($nodes[0]->title, 'No links to individual book pages are displayed.');
  }

  /**
   * Tests the book navigation block when an access module is enabled.
   */
   function testNavigationBlockOnAccessModuleEnabled() {
     $this->drupalLogin($this->admin_user);
     $edit = array();

     // Set the block title.
     $block_title = $this->randomName(16);
     $edit['title'] = $block_title;

     // Set block display to 'Show block only on book pages'.
     $edit['book_block_mode'] = 'book pages';
     $this->drupalPost('admin/structure/block/manage/book/navigation/configure', $edit, t('Save block'));
     $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

     // Set the block to a region to confirm block is available.
     $edit = array();
     $edit['blocks[book_navigation][region]'] = 'footer';
     $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
     $this->assertText(t('The block settings have been updated.'), 'Block successfully move to footer region.');

     // Give anonymous users the permission 'node test view'.
     $edit = array();
     $edit[DRUPAL_ANONYMOUS_RID . '[node test view]'] = TRUE;
     $this->drupalPost('admin/people/permissions/' . DRUPAL_ANONYMOUS_RID, $edit, t('Save permissions'));
     $this->assertText(t('The changes have been saved.'), "Permission 'node test view' successfully assigned to anonymous users.");

     // Create a book.
     $this->createBook();

     // Test correct display of the block to registered users.
     $this->drupalLogin($this->web_user);
     $this->drupalGet('node/' . $this->book->nid);
     $this->assertText($block_title, 'Book navigation block is displayed to registered users.');
     $this->drupalLogout();

     // Test correct display of the block to anonymous users.
     $this->drupalGet('node/' . $this->book->nid);
     $this->assertText($block_title, 'Book navigation block is displayed to anonymous users.');
   }

  /**
   * Tests the access for deleting top-level book nodes.
   */
   function testBookDelete() {
     $nodes = $this->createBook();
     $this->drupalLogin($this->admin_user);
     $edit = array();

     // Test access to delete top-level and child book nodes.
     $this->drupalGet('node/' . $this->book->nid . '/outline/remove');
     $this->assertResponse('403', 'Deleting top-level book node properly forbidden.');
     $this->drupalPost('node/' . $nodes[4]->nid . '/outline/remove', $edit, t('Remove'));
     $node4 = node_load($nodes[4]->nid, NULL, TRUE);
     $this->assertTrue(empty($node4->book), 'Deleting child book node properly allowed.');

     // Delete all child book nodes and retest top-level node deletion.
     foreach ($nodes as $node) {
       $nids[] = $node->nid;
     }
     node_delete_multiple($nids);
     $this->drupalPost('node/' . $this->book->nid . '/outline/remove', $edit, t('Remove'));
     $node = node_load($this->book->nid, NULL, TRUE);
     $this->assertTrue(empty($node->book), 'Deleting childless top-level book node properly allowed.');
   }
}
