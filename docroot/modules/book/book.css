 /**
  * @file
  * Styling for the Book module.
  */

.book-navigation .menu {
  border-top: 1px solid #888;
  padding: 1em 0 0 3em; /* LTR */
}
.book-navigation .page-links {
  border-top: 1px solid #888;
  border-bottom: 1px solid #888;
  text-align: center;
  padding: 0.5em;
}
.book-navigation .page-previous {
  text-align: left;
  width: 42%;
  display: block;
  float: left; /* LTR */
}
.book-navigation .page-up {
  margin: 0 5%;
  width: 4%;
  display: block;
  float: left; /* LTR */
}
.book-navigation .page-next {
  text-align: right;
  width: 42%;
  display: block;
  float: right;
}
#book-outline {
  min-width: 56em;
}
.book-outline-form .form-item {
  margin-top: 0;
  margin-bottom: 0;
}
html.js #edit-book-pick-book {
  display: none;
}
.form-item-book-bid .description {
  clear: both;
}
#book-admin-edit select {
  margin-right: 24px;
}
#book-admin-edit select.progress-disabled {
  margin-right: 0;
}
#book-admin-edit tr.ajax-new-content {
  background-color: #ffd;
}
#book-admin-edit .form-item {
  float: left;
}
