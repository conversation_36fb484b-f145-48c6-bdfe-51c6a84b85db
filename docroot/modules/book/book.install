<?php

/**
 * @file
 * Install, update and uninstall functions for the book module.
 */

/**
 * Implements hook_install().
 */
function book_install() {
  // Add the node type.
  _book_install_type_create();
}

/**
 * Implements hook_uninstall().
 */
function book_uninstall() {
  variable_del('book_allowed_types');
  variable_del('book_child_type');
  variable_del('book_block_mode');

  // Delete menu links.
  db_delete('menu_links')
    ->condition('module', 'book')
    ->execute();
  menu_cache_clear_all();
}

/**
 * Creates the book content type.
 */
function _book_install_type_create() {
  // Create an additional node type.
  $book_node_type = array(
    'type' => 'book',
    'name' => t('Book page'),
    'base' => 'node_content',
    'description' => t('<em>Books</em> have a built-in hierarchical navigation. Use for handbooks or tutorials.'),
    'custom' => 1,
    'modified' => 1,
    'locked' => 0,
  );

  $book_node_type = node_type_set_defaults($book_node_type);
  node_type_save($book_node_type);
  node_add_body_field($book_node_type);
  // Default to not promoted.
  variable_set('node_options_book', array('status'));
  // Use this default type for adding content to books.
  variable_set('book_allowed_types', array('book'));
  variable_set('book_child_type', 'book');
}

/**
 * Implements hook_schema().
 */
function book_schema() {
  $schema['book'] = array(
  'description' => 'Stores book outline information. Uniquely connects each node in the outline to a link in {menu_links}',
    'fields' => array(
      'mlid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => "The book page's {menu_links}.mlid.",
      ),
      'nid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => "The book page's {node}.nid.",
      ),
      'bid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => "The book ID is the {book}.nid of the top-level page.",
      ),
    ),
    'primary key' => array('mlid'),
    'unique keys' => array(
      'nid' => array('nid'),
    ),
    'indexes' => array(
      'bid' => array('bid'),
    ),
  );

  return $schema;
}
