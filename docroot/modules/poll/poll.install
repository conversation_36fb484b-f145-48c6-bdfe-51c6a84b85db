<?php

/**
 * @file
 * Install, update and uninstall functions for the poll module.
 */

/**
 * Implements hook_schema().
 */
function poll_schema() {
  $schema['poll'] = array(
    'description' => 'Stores poll-specific information for poll nodes.',
    'fields' => array(
      'nid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => "The poll's {node}.nid.",
      ),
      'runtime' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The number of seconds past {node}.created during which the poll is open.',
      ),
      'active' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Boolean indicating whether or not the poll is open.',
      ),
    ),
    'primary key' => array('nid'),
    'foreign keys' => array(
      'poll_node' => array(
        'table' => 'node',
        'columns' => array('nid' => 'nid'),
      ),
    ),
  );

  $schema['poll_choice'] = array(
    'description' => 'Stores information about all choices for all {poll}s.',
    'fields' => array(
      'chid' => array(
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'Unique identifier for a poll choice.',
      ),
      'nid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {node}.nid this choice belongs to.',
      ),
      'chtext' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The text for this choice.',
        'translatable' => TRUE,
      ),
      'chvotes' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The total number of votes this choice has received by all users.',
      ),
      'weight' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The sort order of this choice among all choices for the same node.',
      ),
    ),
    'indexes' => array(
      'nid' => array('nid'),
    ),
    'primary key' => array('chid'),
    'foreign keys' => array(
      'choice_node' => array(
        'table' => 'node',
        'columns' => array('nid' => 'nid'),
      ),
    ),
  );

  $schema['poll_vote'] = array(
    'description' => 'Stores per-{users} votes for each {poll}.',
    'fields' => array(
      'chid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => "The {users}'s vote for this poll.",
      ),
      'nid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'The {poll} node this vote is for.',
      ),
      'uid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {users}.uid this vote is from unless the voter was anonymous.',
      ),
      'hostname' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The IP address this vote is from unless the voter was logged in.',
      ),
      'timestamp' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The timestamp of the vote creation.',
      ),
    ),
    'primary key' => array('nid', 'uid', 'hostname'),
    'foreign keys' => array(
      'poll_node' => array(
        'table' => 'node',
        'columns' => array('nid' => 'nid'),
      ),
      'voter' => array(
        'table' => 'users',
        'columns' => array('uid' => 'uid'),
      ),
    ),
    'indexes' => array(
      'chid'     => array('chid'),
      'hostname' => array('hostname'),
      'uid' => array('uid'),
    ),
  );

  return $schema;
}

/**
 * Use the poll_choice primary key to record votes in poll_votes rather than
 * the choice order. Rename chorder to weight.
 *
 * Rename {poll_choices} table to {poll_choice} and {poll_votes} to {poll_vote}.
 */
function poll_update_7001() {
  // Add chid column and convert existing votes.
  db_add_field('poll_votes', 'chid', array('type' => 'int', 'unsigned' => TRUE, 'not null' => TRUE, 'default' => 0));
  db_add_index('poll_votes', 'chid', array('chid'));
  db_update('poll_votes')
    ->expression('chid', Database::getConnection()->prefixTables('COALESCE((SELECT chid FROM {poll_choices} c WHERE {poll_votes}.chorder = c.chorder AND {poll_votes}.nid = c.nid), 0)'))
    ->execute();
  // Delete invalid votes.
  db_delete('poll_votes')->condition('chid', 0)->execute();
  // Remove old chorder column.
  db_drop_field('poll_votes', 'chorder');

  // Change the chorder column to weight in poll_choices.
  db_change_field('poll_choices', 'chorder', 'weight', array('type' => 'int', 'not null' => TRUE, 'default' => 0, 'size' => 'tiny'));

  db_rename_table('poll_votes', 'poll_vote');
  db_rename_table('poll_choices', 'poll_choice');
}

/**
 * Add timestamp field to {poll_vote}.
 */
function poll_update_7002() {
  $field = array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
  );
  db_add_field('poll_vote', 'timestamp', $field);
}

/**
 * Change the weight column to normal int.
 */
function poll_update_7003() {
  db_change_field('poll_choice', 'weight', 'weight', array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
    'description' => 'The sort order of this choice among all choices for the same node.',
  ));
}

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Update the database to match the schema.
 */
function poll_update_7004() {
  // Remove field default.
  db_change_field('poll_vote', 'chid', 'chid', array('type' => 'int', 'unsigned' => TRUE, 'not null' => TRUE));
}

/**
 * @} End of "addtogroup updates-7.x-extra".
 */
