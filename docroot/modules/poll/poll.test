<?php

/**
 * @file
 * Tests for poll.module.
 */

class PollTestCase extends DrupalWebTestCase {

  /**
   * Creates a poll.
   *
   * @param string $title
   *   The title of the poll.
   * @param array $choices
   *   A list of choice labels.
   * @param boolean $preview
   *   (optional) Whether to test if the preview is working or not. Defaults to
   *   TRUE.
   *
   * @return
   *   The node id of the created poll, or FALSE on error.
   */
  function pollCreate($title, $choices, $preview = TRUE) {
    $this->assertTrue(TRUE, 'Create a poll');

    $admin_user = $this->drupalCreateUser(array('create poll content', 'administer nodes'));
    $web_user = $this->drupalCreateUser(array('create poll content', 'access content', 'edit own poll content'));
    $this->drupalLogin($admin_user);

    // Get the form first to initialize the state of the internal browser.
    $this->drupalGet('node/add/poll');

    // Prepare a form with two choices.
    list($edit, $index) = $this->_pollGenerateEdit($title, $choices);

    // Verify that the vote count element only allows non-negative integers.
    $edit['choice[new:1][chvotes]'] = -1;
    $edit['choice[new:0][chvotes]'] = $this->randomString(7);
    $this->drupalPost(NULL, $edit, t('Save'));
    $this->assertText(t('Negative values are not allowed.'));
    $this->assertText(t('Vote count for new choice must be an integer.'));

    // Repeat steps for initializing the state of the internal browser.
    $this->drupalLogin($web_user);
    $this->drupalGet('node/add/poll');
    list($edit, $index) = $this->_pollGenerateEdit($title, $choices);

    // Re-submit the form until all choices are filled in.
    if (count($choices) > 2) {
      while ($index < count($choices)) {
        $this->drupalPost(NULL, $edit, t('More choices'));
        $this->assertPollChoiceOrder($choices, $index);
        list($edit, $index) = $this->_pollGenerateEdit($title, $choices, $index);
      }
    }

    if ($preview) {
      $this->drupalPost(NULL, $edit, t('Preview'));
      $this->assertPollChoiceOrder($choices, $index, TRUE);
      list($edit, $index) = $this->_pollGenerateEdit($title, $choices, $index);
    }

    $this->drupalPost(NULL, $edit, t('Save'));
    $node = $this->drupalGetNodeByTitle($title);
    $this->assertText(t('@type @title has been created.', array('@type' => node_type_get_name('poll'), '@title' => $title)), 'Poll has been created.');
    $this->assertTrue($node->nid, 'Poll has been found in the database.');

    return isset($node->nid) ? $node->nid : FALSE;
  }

  /**
   * Generates POST values for the poll node form, specifically poll choices.
   *
   * @param $title
   *   The title for the poll node.
   * @param $choices
   *   An array containing poll choices, as generated by
   *   PollTestCase::_generateChoices().
   * @param $index
   *   (optional) The amount/number of already submitted poll choices. Defaults
   *   to 0.
   *
   * @return
   *   An indexed array containing:
   *   - The generated POST values, suitable for
   *     DrupalWebTestCase::drupalPost().
   *   - The number of poll choices contained in 'edit', for potential re-usage
   *     in subsequent invocations of this function.
   */
  function _pollGenerateEdit($title, array $choices, $index = 0) {
    $max_new_choices = ($index == 0 ? 2 : 5);
    $already_submitted_choices = array_slice($choices, 0, $index);
    $new_choices = array_values(array_slice($choices, $index, $max_new_choices));

    $edit = array(
      'title' => $title,
    );
    foreach ($already_submitted_choices as $k => $text) {
      $edit['choice[chid:' . $k . '][chtext]'] = $text;
    }
    foreach ($new_choices as $k => $text) {
      $edit['choice[new:' . $k . '][chtext]'] = $text;
    }
    return array($edit, count($already_submitted_choices) + count($new_choices));
  }

  function _generateChoices($count = 7) {
    $choices = array();
    for ($i = 1; $i <= $count; $i++) {
      $choices[] = $this->randomName();
    }
    return $choices;
  }

  /**
   * Assert correct poll choice order in the node form after submission.
   *
   * Verifies both the order in the DOM and in the 'weight' form elements.
   *
   * @param $choices
   *   An array containing poll choices, as generated by
   *   PollTestCase::_generateChoices().
   * @param $index
   *   (optional) The amount/number of already submitted poll choices. Defaults
   *   to 0.
   * @param $preview
   *   (optional) Whether to also check the poll preview.
   *
   * @see PollTestCase::_pollGenerateEdit()
   */
  function assertPollChoiceOrder(array $choices, $index = 0, $preview = FALSE) {
    $expected = array();
    $weight = 0;
    foreach ($choices as $id => $label) {
      if ($id < $index) {
        // The expected weight of each choice is higher than the previous one.
        $weight++;
        // Directly assert the weight form element value for this choice.
        $this->assertFieldByName('choice[chid:' . $id . '][weight]', $weight, format_string('Found choice @id with weight @weight.', array(
          '@id' => $id,
          '@weight' => $weight,
        )));
        // Append to our (to be reversed) stack of labels.
        $expected[$weight] = $label;
      }
    }
    ksort($expected);

    // Verify DOM order of poll choices (i.e., #weight of form elements).
    $elements = $this->xpath('//input[starts-with(@name, :prefix) and contains(@name, :suffix)]', array(
      ':prefix' => 'choice[chid:',
      ':suffix' => '][chtext]',
    ));
    $expected_order = $expected;
    foreach ($elements as $element) {
      $next_label = array_shift($expected_order);
      $this->assertEqual((string) $element['value'], $next_label);
    }

    // If requested, also verify DOM order in preview.
    if ($preview) {
      $elements = $this->xpath('//div[contains(@class, :teaser)]/descendant::div[@class=:text]', array(
        ':teaser' => 'node-teaser',
        ':text' => 'text',
      ));
      $expected_order = $expected;
      foreach ($elements as $element) {
        $next_label = array_shift($expected_order);
        $this->assertEqual((string) $element, $next_label, format_string('Found choice @label in preview.', array(
          '@label' => $next_label,
        )));
      }
    }
  }

  function pollUpdate($nid, $title, $edit) {
    // Edit the poll node.
    $this->drupalPost('node/' . $nid . '/edit', $edit, t('Save'));
    $this->assertText(t('@type @title has been updated.', array('@type' => node_type_get_name('poll'), '@title' => $title)), 'Poll has been updated.');
  }
}

class PollCreateTestCase extends PollTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Poll create',
      'description' => 'Adds "more choices", previews and creates a poll.',
      'group' => 'Poll'
    );
  }

  function setUp() {
    parent::setUp('poll');
  }

  function testPollCreate() {
    $title = $this->randomName();
    $choices = $this->_generateChoices(7);
    $poll_nid = $this->pollCreate($title, $choices, TRUE);

    // Verify poll appears on 'poll' page.
    $this->drupalGet('poll');
    $this->assertText($title, 'Poll appears in poll list.');
    $this->assertText('open', 'Poll is active.');

    // Click on the poll title to go to node page.
    $this->clickLink($title);
    $this->assertText('Total votes: 0', 'Link to poll correct.');

    // Now add a new option to make sure that when we update the node the
    // option is displayed.
    $node = node_load($poll_nid);

    $new_option = $this->randomName();

    $vote_count = '2000';
    $node->choice[] = array(
      'chid' => NULL,
      'chtext' => $new_option,
      'chvotes' => (int) $vote_count,
      'weight' => 1000,
    );

    node_save($node);

    $this->drupalGet('poll');
    $this->clickLink($title);
    $this->assertText($new_option, 'New option found.');

    $option = $this->xpath('//div[@id="node-1"]//div[@class="poll"]//div[@class="text"]');
    $this->assertEqual(end($option), $new_option, 'Last item is equal to new option.');

    $votes = $this->xpath('//div[@id="node-1"]//div[@class="poll"]//div[@class="percent"]');
    $this->assertTrue(strpos(end($votes), $vote_count) > 0, "Votes saved.");
  }

  function testPollClose() {
    $content_user = $this->drupalCreateUser(array('create poll content', 'edit any poll content', 'access content'));
    $vote_user = $this->drupalCreateUser(array('cancel own vote', 'inspect all votes', 'vote on polls', 'access content'));

    // Create poll.
    $title = $this->randomName();
    $choices = $this->_generateChoices(7);
    $poll_nid = $this->pollCreate($title, $choices, FALSE);

    $this->drupalLogout();
    $this->drupalLogin($content_user);

    // Edit the poll node and close the poll.
    $close_edit = array('active' => 0);
    $this->pollUpdate($poll_nid, $title, $close_edit);

    // Verify 'Vote' button no longer appears.
    $this->drupalGet('node/' . $poll_nid);
    $elements = $this->xpath('//input[@id="edit-vote"]');
    $this->assertTrue(empty($elements), "Vote button doesn't appear.");

    // Verify status on 'poll' page is 'closed'.
    $this->drupalGet('poll');
    $this->assertText($title, 'Poll appears in poll list.');
    $this->assertText('closed', 'Poll is closed.');

    // Edit the poll node and re-activate.
    $open_edit = array('active' => 1);
    $this->pollUpdate($poll_nid, $title, $open_edit);

    // Vote on the poll.
    $this->drupalLogout();
    $this->drupalLogin($vote_user);
    $vote_edit = array('choice' => '1');
    $this->drupalPost('node/' . $poll_nid, $vote_edit, t('Vote'));
    $this->assertText('Your vote was recorded.', 'Your vote was recorded.');
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(isset($elements[0]), "'Cancel your vote' button appears.");

    // Edit the poll node and close the poll.
    $this->drupalLogout();
    $this->drupalLogin($content_user);
    $close_edit = array('active' => 0);
    $this->pollUpdate($poll_nid, $title, $close_edit);

    // Verify 'Cancel your vote' button no longer appears.
    $this->drupalGet('node/' . $poll_nid);
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(empty($elements), "'Cancel your vote' button no longer appears.");
  }
}

class PollVoteTestCase extends PollTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Poll vote',
      'description' => 'Vote on a poll',
      'group' => 'Poll'
    );
  }

  function setUp() {
    parent::setUp('poll');
  }

  function tearDown() {
    parent::tearDown();
  }

  function testPollVote() {
    $title = $this->randomName();
    $choices = $this->_generateChoices(7);
    $poll_nid = $this->pollCreate($title, $choices, FALSE);
    $this->drupalLogout();

    $vote_user = $this->drupalCreateUser(array('cancel own vote', 'inspect all votes', 'vote on polls', 'access content'));
    $restricted_vote_user = $this->drupalCreateUser(array('vote on polls', 'access content'));

    $this->drupalLogin($vote_user);

    // Record a vote without selecting any choice.
    $edit = array();
    $this->drupalPost('node/' . $poll_nid, $edit, t('Vote'));
    $this->assertText(t('Your vote could not be recorded because you did not select any of the choices.'), 'Found the empty poll submission error message.');

    // Record a vote for the first choice.
    $edit = array(
      'choice' => '1',
    );
    $this->drupalPost('node/' . $poll_nid, $edit, t('Vote'));
    $this->assertText('Your vote was recorded.', 'Your vote was recorded.');
    $this->assertText('Total votes: 1', 'Vote count updated correctly.');
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(isset($elements[0]), "'Cancel your vote' button appears.");

    $this->drupalGet("node/$poll_nid/votes");
    $this->assertText(t('This table lists all the recorded votes for this poll. If anonymous users are allowed to vote, they will be identified by the IP address of the computer they used when they voted.'), 'Vote table text.');
    $this->assertText($choices[0], 'Vote recorded');

    // Ensure poll listing page has correct number of votes.
    $this->drupalGet('poll');
    $this->assertText($title, 'Poll appears in poll list.');
    $this->assertText('1 vote', 'Poll has 1 vote.');

    // Cancel a vote.
    $this->drupalPost('node/' . $poll_nid, array(), t('Cancel your vote'));
    $this->assertText('Your vote was cancelled.', 'Your vote was cancelled.');
    $this->assertNoText('Cancel your vote', "Cancel vote button doesn't appear.");

    $this->drupalGet("node/$poll_nid/votes");
    $this->assertNoText($choices[0], 'Vote cancelled');

    // Ensure poll listing page has correct number of votes.
    $this->drupalGet('poll');
    $this->assertText($title, 'Poll appears in poll list.');
    $this->assertText('0 votes', 'Poll has 0 votes.');

    // Log in as a user who can only vote on polls.
    $this->drupalLogout();
    $this->drupalLogin($restricted_vote_user);

    // Vote on a poll.
    $edit = array(
      'choice' => '1',
    );
    $this->drupalPost('node/' . $poll_nid, $edit, t('Vote'));
    $this->assertText('Your vote was recorded.', 'Your vote was recorded.');
    $this->assertText('Total votes: 1', 'Vote count updated correctly.');
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(empty($elements), "'Cancel your vote' button does not appear.");
  }
}

class PollBlockTestCase extends PollTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Block availability',
      'description' => 'Check if the most recent poll block is available.',
      'group' => 'Poll',
    );
  }

  function setUp() {
    parent::setUp('poll');

    // Create and login user
    $admin_user = $this->drupalCreateUser(array('administer blocks'));
    $this->drupalLogin($admin_user);
  }

  function testRecentBlock() {
    // Set block title to confirm that the interface is available.
    $this->drupalPost('admin/structure/block/manage/poll/recent/configure', array('title' => $this->randomName(8)), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

    // Set the block to a region to confirm block is available.
    $edit = array();
    $edit['blocks[poll_recent][region]'] = 'footer';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertText(t('The block settings have been updated.'), 'Block successfully move to footer region.');

    // Create a poll which should appear in recent polls block.
    $title = $this->randomName();
    $choices = $this->_generateChoices(7);
    $poll_nid = $this->pollCreate($title, $choices, TRUE);

    // Verify poll appears in a block.
    // View user page so we're not matching the poll node on front page.
    $this->drupalGet('user');
    // If a 'block' view not generated, this title would not appear even though
    // the choices might.
    $this->assertText($title, 'Poll appears in block.');

    // Logout and login back in as a user who can vote.
    $this->drupalLogout();
    $vote_user = $this->drupalCreateUser(array('cancel own vote', 'inspect all votes', 'vote on polls', 'access content'));
    $this->drupalLogin($vote_user);

    // Verify we can vote via the block.
    $edit = array(
      'choice' => '1',
    );
    $this->drupalPost('user/' . $vote_user->uid, $edit, t('Vote'));
    $this->assertText('Your vote was recorded.', 'Your vote was recorded.');
    $this->assertText('Total votes: 1', 'Vote count updated correctly.');
    $this->assertText('Older polls', 'Link to older polls appears.');
    $this->clickLink('Older polls');
    $this->assertText('1 vote - open', 'Link to poll listing correct.');

    // Close the poll and verify block doesn't appear.
    $content_user = $this->drupalCreateUser(array('create poll content', 'edit any poll content', 'access content'));
    $this->drupalLogout();
    $this->drupalLogin($content_user);
    $close_edit = array('active' => 0);
    $this->pollUpdate($poll_nid, $title, $close_edit);
    $this->drupalGet('user/' . $content_user->uid);
    $this->assertNoText($title, 'Poll no longer appears in block.');
  }
}

/**
 * Test adding new choices.
 */
class PollJSAddChoice extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Poll add choice',
      'description' => 'Submits a POST request for an additional poll choice.',
      'group' => 'Poll'
    );
  }

  function setUp() {
    parent::setUp('poll');
  }

  /**
   * Test adding a new choice.
   */
  function testAddChoice() {
    $web_user = $this->drupalCreateUser(array('create poll content', 'access content'));
    $this->drupalLogin($web_user);
    $this->drupalGet('node/add/poll');
    $edit = array(
      "title" => $this->randomName(),
      'choice[new:0][chtext]' => $this->randomName(),
      'choice[new:1][chtext]' => $this->randomName(),
    );

    // Press 'add choice' button through Ajax, and place the expected HTML result
    // as the tested content.
    $commands = $this->drupalPostAJAX(NULL, $edit, array('op' => t('More choices')));
    $this->content = $commands[1]['data'];

    $this->assertFieldByName('choice[chid:0][chtext]', $edit['choice[new:0][chtext]'], format_string('Field !i found', array('!i' => 0)));
    $this->assertFieldByName('choice[chid:1][chtext]', $edit['choice[new:1][chtext]'], format_string('Field !i found', array('!i' => 1)));
    $this->assertFieldByName('choice[new:0][chtext]', '', format_string('Field !i found', array('!i' => 2)));
  }
}

class PollVoteCheckHostname extends PollTestCase {
  protected $admin_user;
  protected $poll_nid;
  protected $web_user1;
  protected $web_user2;

  public static function getInfo() {
    return array(
      'name' => 'User poll vote capability.',
      'description' => 'Check that users and anonymous users from specified ip-address can only vote once.',
      'group' => 'Poll'
    );
  }

  function setUp() {
    parent::setUp('poll');

    // Create and login user.
    $this->admin_user = $this->drupalCreateUser(array('administer permissions', 'create poll content'));
    $this->drupalLogin($this->admin_user);

    // Allow anonymous users to vote on polls.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access content' => TRUE,
      'vote on polls' => TRUE,
      'cancel own vote' => TRUE,
    ));

    // Enable page cache to verify that the result page is not saved in the
    // cache when anonymous voting is allowed.
    variable_set('cache', 1);

    // Create poll.
    $title = $this->randomName();
    $choices = $this->_generateChoices(3);
    $this->poll_nid = $this->pollCreate($title, $choices, FALSE);

    $this->drupalLogout();

    // Create web users.
    $this->web_user1 = $this->drupalCreateUser(array('access content', 'vote on polls', 'cancel own vote'));
    $this->web_user2 = $this->drupalCreateUser(array('access content', 'vote on polls'));
  }

  /**
   * Check that anonymous users with same ip cannot vote on poll more than once
   * unless user is logged in.
   */
  function testHostnamePollVote() {
    // Login User1.
    $this->drupalLogin($this->web_user1);

    $edit = array(
      'choice' => '1',
    );

    // User1 vote on Poll.
    $this->drupalPost('node/' . $this->poll_nid, $edit, t('Vote'));
    $this->assertText(t('Your vote was recorded.'), format_string('%user vote was recorded.', array('%user' => $this->web_user1->name)));
    $this->assertText(t('Total votes: @votes', array('@votes' => 1)), 'Vote count updated correctly.');

    // Check to make sure User1 cannot vote again.
    $this->drupalGet('node/' . $this->poll_nid);
    $elements = $this->xpath('//input[@value="Vote"]');
    $this->assertTrue(empty($elements), format_string("%user is not able to vote again.", array('%user' => $this->web_user1->name)));
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(!empty($elements), "'Cancel your vote' button appears.");

    // Logout User1.
    $this->drupalLogout();

    // Fill the page cache by requesting the poll.
    $this->drupalGet('node/' . $this->poll_nid);
    $this->assertEqual($this->drupalGetHeader('x-drupal-cache'), 'MISS', 'Page was cacheable but was not in the cache.');
    $this->drupalGet('node/' . $this->poll_nid);
    $this->assertEqual($this->drupalGetHeader('x-drupal-cache'), 'HIT', 'Page was cached.');

    // Anonymous user vote on Poll.
    $this->drupalPost(NULL, $edit, t('Vote'));
    $this->assertText(t('Your vote was recorded.'), 'Anonymous vote was recorded.');
    $this->assertText(t('Total votes: @votes', array('@votes' => 2)), 'Vote count updated correctly.');
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(!empty($elements), "'Cancel your vote' button appears.");

    // Check to make sure Anonymous user cannot vote again.
    $this->drupalGet('node/' . $this->poll_nid);
    $this->assertFalse($this->drupalGetHeader('x-drupal-cache'), 'Page was not cacheable.');
    $elements = $this->xpath('//input[@value="Vote"]');
    $this->assertTrue(empty($elements), "Anonymous is not able to vote again.");
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(!empty($elements), "'Cancel your vote' button appears.");

    // Login User2.
    $this->drupalLogin($this->web_user2);

    // User2 vote on poll.
    $this->drupalPost('node/' . $this->poll_nid, $edit, t('Vote'));
    $this->assertText(t('Your vote was recorded.'), format_string('%user vote was recorded.', array('%user' => $this->web_user2->name)));
    $this->assertText(t('Total votes: @votes', array('@votes' => 3)), 'Vote count updated correctly.');
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(empty($elements), "'Cancel your vote' button does not appear.");

    // Logout User2.
    $this->drupalLogout();

    // Change host name for anonymous users.
    db_update('poll_vote')
      ->fields(array(
        'hostname' => '123.456.789.1',
      ))
      ->condition('hostname', '', '<>')
      ->execute();

    // Check to make sure Anonymous user can vote again with a new session after
    // a hostname change.
    $this->drupalGet('node/' . $this->poll_nid);
    $this->assertEqual($this->drupalGetHeader('x-drupal-cache'), 'MISS', 'Page was cacheable but was not in the cache.');
    $this->drupalPost(NULL, $edit, t('Vote'));
    $this->assertText(t('Your vote was recorded.'), format_string('%user vote was recorded.', array('%user' => $this->web_user2->name)));
    $this->assertText(t('Total votes: @votes', array('@votes' => 4)), 'Vote count updated correctly.');
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(!empty($elements), "'Cancel your vote' button appears.");

    // Check to make sure Anonymous user cannot vote again with a new session,
    // and that the vote from the previous session cannot be cancelledd.
    $this->curlClose();
    $this->drupalGet('node/' . $this->poll_nid);
    $this->assertEqual($this->drupalGetHeader('x-drupal-cache'), 'MISS', 'Page was cacheable but was not in the cache.');
    $elements = $this->xpath('//input[@value="Vote"]');
    $this->assertTrue(empty($elements), 'Anonymous is not able to vote again.');
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(empty($elements), "'Cancel your vote' button does not appear.");

    // Login User1.
    $this->drupalLogin($this->web_user1);

    // Check to make sure User1 still cannot vote even after hostname changed.
    $this->drupalGet('node/' . $this->poll_nid);
    $elements = $this->xpath('//input[@value="Vote"]');
    $this->assertTrue(empty($elements), format_string("%user is not able to vote again.", array('%user' => $this->web_user1->name)));
    $elements = $this->xpath('//input[@value="Cancel your vote"]');
    $this->assertTrue(!empty($elements), "'Cancel your vote' button appears.");
  }
}

/**
 * Test poll token replacement in strings.
 */
class PollTokenReplaceTestCase extends PollTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Poll token replacement',
      'description' => 'Generates text using placeholders for dummy content to check poll token replacement.',
      'group' => 'Poll',
    );
  }

  function setUp() {
    parent::setUp('poll');
  }

  /**
   * Creates a poll, then tests the tokens generated from it.
   */
  function testPollTokenReplacement() {
    global $language;

    // Craete a poll with three choices.
    $title = $this->randomName();
    $choices = $this->_generateChoices(3);
    $poll_nid = $this->pollCreate($title, $choices, FALSE);
    $this->drupalLogout();

    // Create four users and have each of them vote.
    $vote_user1 = $this->drupalCreateUser(array('vote on polls', 'access content'));
    $this->drupalLogin($vote_user1);
    $edit = array(
      'choice' => '1',
    );
    $this->drupalPost('node/' . $poll_nid, $edit, t('Vote'));
    $this->drupalLogout();

    $vote_user2 = $this->drupalCreateUser(array('vote on polls', 'access content'));
    $this->drupalLogin($vote_user2);
    $edit = array(
      'choice' => '1',
    );
    $this->drupalPost('node/' . $poll_nid, $edit, t('Vote'));
    $this->drupalLogout();

    $vote_user3 = $this->drupalCreateUser(array('vote on polls', 'access content'));
    $this->drupalLogin($vote_user3);
    $edit = array(
      'choice' => '2',
    );
    $this->drupalPost('node/' . $poll_nid, $edit, t('Vote'));
    $this->drupalLogout();

    $vote_user4 = $this->drupalCreateUser(array('vote on polls', 'access content'));
    $this->drupalLogin($vote_user4);
    $edit = array(
      'choice' => '3',
    );
    $this->drupalPost('node/' . $poll_nid, $edit, t('Vote'));
    $this->drupalLogout();

    $poll = node_load($poll_nid, NULL, TRUE);

    // Generate and test sanitized tokens.
    $tests = array();
    $tests['[node:poll-votes]'] = 4;
    $tests['[node:poll-winner]'] = filter_xss($poll->choice[1]['chtext']);
    $tests['[node:poll-winner-votes]'] = 2;
    $tests['[node:poll-winner-percent]'] = 50;
    $tests['[node:poll-duration]'] = format_interval($poll->runtime, 1, $language->language);

    // Test to make sure that we generated something for each token.
    $this->assertFalse(in_array(0, array_map('strlen', $tests)), 'No empty tokens generated.');

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $poll), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Sanitized poll token %token replaced.', array('%token' => $input)));
    }

    // Generate and test unsanitized tokens.
    $tests['[node:poll-winner]'] = $poll->choice[1]['chtext'];

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $poll), array('language' => $language, 'sanitize' => FALSE));
      $this->assertEqual($output, $expected, format_string('Unsanitized poll token %token replaced.', array('%token' => $input)));
    }
  }
}

class PollExpirationTestCase extends PollTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Poll expiration',
      'description' => 'Test the poll auto-expiration logic.',
      'group' => 'Poll',
    );
  }

  function setUp() {
    parent::setUp('poll');
  }

  function testAutoExpire() {
    // Set up a poll.
    $title = $this->randomName();
    $choices = $this->_generateChoices(2);
    $poll_nid = $this->pollCreate($title, $choices, FALSE);
    $this->assertTrue($poll_nid, 'Poll for auto-expire test created.');

    // Visit the poll edit page and verify that by default, expiration
    // is set to unlimited.
    $this->drupalGet("node/$poll_nid/edit");
    $this->assertField('runtime', 'Poll expiration setting found.');
    $elements = $this->xpath('//select[@id="edit-runtime"]/option[@selected="selected"]');
    $this->assertTrue(isset($elements[0]['value']) && $elements[0]['value'] == 0, 'Poll expiration set to unlimited.');

    // Set the expiration to one week.
    $edit = array();
    $poll_expiration = 604800; // One week.
    $edit['runtime'] = $poll_expiration;
    $this->drupalPost(NULL, $edit, t('Save'));
    $this->assertRaw(t('Poll %title has been updated.', array('%title' => $title)), 'Poll expiration settings saved.');

    // Make sure that the changed expiration settings is kept.
    $this->drupalGet("node/$poll_nid/edit");
    $elements = $this->xpath('//select[@id="edit-runtime"]/option[@selected="selected"]');
    $this->assertTrue(isset($elements[0]['value']) && $elements[0]['value'] == $poll_expiration, 'Poll expiration set to unlimited.');

    // Force a cron run. Since the expiration date has not yet been reached,
    // the poll should remain active.
    drupal_cron_run();
    $this->drupalGet("node/$poll_nid/edit");
    $elements = $this->xpath('//input[@id="edit-active-1"]');
    $this->assertTrue(isset($elements[0]) && !empty($elements[0]['checked']), 'Poll is still active.');

    // Test expiration. Since REQUEST_TIME is a constant and we don't
    // want to keep SimpleTest waiting until the moment of expiration arrives,
    // we forcibly change the expiration date in the database.
    $created = db_query('SELECT created FROM {node} WHERE nid = :nid', array(':nid' => $poll_nid))->fetchField();
    db_update('node')
      ->fields(array('created' => $created - ($poll_expiration * 1.01)))
      ->condition('nid', $poll_nid)
      ->execute();

    // Run cron and verify that the poll is now marked as "closed".
    drupal_cron_run();
    $this->drupalGet("node/$poll_nid/edit");
    $elements = $this->xpath('//input[@id="edit-active-0"]');
    $this->assertTrue(isset($elements[0]) && !empty($elements[0]['checked']), 'Poll has expired.');
  }
}

class PollDeleteChoiceTestCase extends PollTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Poll choice deletion',
      'description' => 'Test the poll choice deletion logic.',
      'group' => 'Poll',
    );
  }

  function setUp() {
    parent::setUp('poll');
  }

  function testChoiceRemoval() {
    // Set up a poll with three choices.
    $title = $this->randomName();
    $choices = array('First choice', 'Second choice', 'Third choice');
    $poll_nid = $this->pollCreate($title, $choices, FALSE);
    $this->assertTrue($poll_nid, 'Poll for choice deletion logic test created.');

    // Edit the poll, and try to delete first poll choice.
    $this->drupalGet("node/$poll_nid/edit");
    $edit['choice[chid:1][chtext]'] = '';
    $this->drupalPost(NULL, $edit, t('Save'));

    // Click on the poll title to go to node page.
    $this->drupalGet('poll');
    $this->clickLink($title);

    // Check the first poll choice is deleted, while the others remain.
    $this->assertNoText('First choice', 'First choice removed.');
    $this->assertText('Second choice', 'Second choice remains.');
    $this->assertText('Third choice', 'Third choice remains.');
  }
}

/**
 * Tests poll translation logic.
 */
class PollTranslateTestCase extends PollTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Poll translation',
      'description' => 'Test the poll translation logic.',
      'group' => 'Poll',
    );
  }

  function setUp() {
    parent::setUp('poll', 'translation');
  }

  /**
   * Tests poll creation and translation.
   *
   * Checks that the choice names get copied from the original poll and that
   * the vote count values are set to 0.
   */
  function testPollTranslate() {
    $admin_user = $this->drupalCreateUser(array('administer content types', 'administer languages', 'edit any poll content', 'create poll content', 'administer nodes', 'translate content'));

    // Set up a poll with two choices.
    $title = $this->randomName();
    $choices = array($this->randomName(), $this->randomName());
    $poll_nid = $this->pollCreate($title, $choices, FALSE);
    $this->assertTrue($poll_nid, 'Poll for translation logic test created.');

    $this->drupalLogout();
    $this->drupalLogin($admin_user);

    // Enable a second language.
    $this->drupalGet('admin/config/regional/language');
    $edit = array();
    $edit['langcode'] = 'nl';
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));
    $this->assertRaw(t('The language %language has been created and can now be used.', array('%language' => 'Dutch')), 'Language Dutch has been created.');

    // Set "Poll" content type to use multilingual support with translation.
    $this->drupalGet('admin/structure/types/manage/poll');
    $edit = array();
    $edit['language_content_type'] = 2;
    $this->drupalPost('admin/structure/types/manage/poll', $edit, t('Save content type'));
    $this->assertRaw(t('The content type %type has been updated.', array('%type' => 'Poll')), 'Poll content type has been updated.');

    // Edit poll.
    $this->drupalGet("node/$poll_nid/edit");
    $edit = array();
    // Set the poll's first choice count to 200.
    $edit['choice[chid:1][chvotes]'] = 200;
    // Set the language to Dutch.
    $edit['language'] = 'nl';
    $this->drupalPost(NULL, $edit, t('Save'));

    // Translate the Dutch poll.
    $this->drupalGet('node/add/poll', array('query' => array('translation' => $poll_nid, 'target' => 'en')));

    $dutch_poll = node_load($poll_nid);

    // Check that the vote count values didn't get copied from the Dutch poll
    // and are set to 0.
    $this->assertFieldByName('choice[chid:1][chvotes]', '0', ('Found choice with vote count 0'));
    $this->assertFieldByName('choice[chid:2][chvotes]', '0', ('Found choice with vote count 0'));
    // Check that the choice names got copied from the Dutch poll.
    $this->assertFieldByName('choice[chid:1][chtext]', $dutch_poll->choice[1]['chtext'], format_string('Found choice with text @text', array('@text' => $dutch_poll->choice[1]['chtext'])));
    $this->assertFieldByName('choice[chid:2][chtext]', $dutch_poll->choice[2]['chtext'], format_string('Found choice with text @text', array('@text' => $dutch_poll->choice[2]['chtext'])));
  }
}
