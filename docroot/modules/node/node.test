<?php

/**
 * @file
 * Tests for node.module.
 */

/**
 * Defines a base class for testing the Node module.
 */
class NodeWebTestCase extends DrupalWebTestCase {
  function setUp() {
    $modules = func_get_args();
    if (isset($modules[0]) && is_array($modules[0])) {
      $modules = $modules[0];
    }
    $modules[] = 'node';
    parent::setUp($modules);

    // Create Basic page and Article node types.
    if ($this->profile != 'standard') {
      $this->drupalCreateContentType(array('type' => 'page', 'name' => 'Basic page'));
      $this->drupalCreateContentType(array('type' => 'article', 'name' => 'Article'));
    }
  }
}

/**
 * Test the node_load_multiple() function.
 */
class NodeLoadMultipleTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Load multiple nodes',
      'description' => 'Test the loading of multiple nodes.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();
    $web_user = $this->drupalCreateUser(array('create article content', 'create page content'));
    $this->drupalLogin($web_user);
  }

  /**
   * Create four nodes and ensure they're loaded correctly.
   */
  function testNodeMultipleLoad() {
    $node1 = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $node2 = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $node3 = $this->drupalCreateNode(array('type' => 'article', 'promote' => 0));
    $node4 = $this->drupalCreateNode(array('type' => 'page', 'promote' => 0));

    // Confirm that promoted nodes appear in the default node listing.
    $this->drupalGet('node');
    $this->assertText($node1->title, 'Node title appears on the default listing.');
    $this->assertText($node2->title, 'Node title appears on the default listing.');
    $this->assertNoText($node3->title, 'Node title does not appear in the default listing.');
    $this->assertNoText($node4->title, 'Node title does not appear in the default listing.');

    // Load nodes with only a condition. Nodes 3 and 4 will be loaded.
    $nodes = node_load_multiple(NULL, array('promote' => 0));
    $this->assertEqual($node3->title, $nodes[$node3->nid]->title, 'Node was loaded.');
    $this->assertEqual($node4->title, $nodes[$node4->nid]->title, 'Node was loaded.');
    $count = count($nodes);
    $this->assertTrue($count == 2, format_string('@count nodes loaded.', array('@count' => $count)));

    // Load nodes by nid. Nodes 1, 2 and 4 will be loaded.
    $nodes = node_load_multiple(array(1, 2, 4));
    $count = count($nodes);
    $this->assertTrue(count($nodes) == 3, format_string('@count nodes loaded', array('@count' => $count)));
    $this->assertTrue(isset($nodes[$node1->nid]), 'Node is correctly keyed in the array');
    $this->assertTrue(isset($nodes[$node2->nid]), 'Node is correctly keyed in the array');
    $this->assertTrue(isset($nodes[$node4->nid]), 'Node is correctly keyed in the array');
    foreach ($nodes as $node) {
      $this->assertTrue(is_object($node), 'Node is an object');
    }

    // Load nodes by nid, where type = article. Nodes 1, 2 and 3 will be loaded.
    $nodes = node_load_multiple(array(1, 2, 3, 4), array('type' => 'article'));
    $count = count($nodes);
    $this->assertTrue($count == 3, format_string('@count nodes loaded', array('@count' => $count)));
    $this->assertEqual($nodes[$node1->nid]->title, $node1->title, 'Node successfully loaded.');
    $this->assertEqual($nodes[$node2->nid]->title, $node2->title, 'Node successfully loaded.');
    $this->assertEqual($nodes[$node3->nid]->title, $node3->title, 'Node successfully loaded.');
    $this->assertFalse(isset($nodes[$node4->nid]));

    // Now that all nodes have been loaded into the static cache, ensure that
    // they are loaded correctly again when a condition is passed.
    $nodes = node_load_multiple(array(1, 2, 3, 4), array('type' => 'article'));
    $count = count($nodes);
    $this->assertTrue($count == 3, format_string('@count nodes loaded.', array('@count' => $count)));
    $this->assertEqual($nodes[$node1->nid]->title, $node1->title, 'Node successfully loaded');
    $this->assertEqual($nodes[$node2->nid]->title, $node2->title, 'Node successfully loaded');
    $this->assertEqual($nodes[$node3->nid]->title, $node3->title, 'Node successfully loaded');
    $this->assertFalse(isset($nodes[$node4->nid]), 'Node was not loaded');

    // Load nodes by nid, where type = article and promote = 0.
    $nodes = node_load_multiple(array(1, 2, 3, 4), array('type' => 'article', 'promote' => 0));
    $count = count($nodes);
    $this->assertTrue($count == 1, format_string('@count node loaded', array('@count' => $count)));
    $this->assertEqual($nodes[$node3->nid]->title, $node3->title, 'Node successfully loaded.');
  }
}

/**
 * Tests for the hooks invoked during node_load().
 */
class NodeLoadHooksTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node load hooks',
      'description' => 'Test the hooks invoked when a node is being loaded.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp('node_test');
  }

  /**
   * Test that hook_node_load() is invoked correctly.
   */
  function testHookNodeLoad() {
    // Create some sample articles and pages.
    $node1 = $this->drupalCreateNode(array('type' => 'article', 'status' => NODE_PUBLISHED));
    $node2 = $this->drupalCreateNode(array('type' => 'article', 'status' => NODE_PUBLISHED));
    $node3 = $this->drupalCreateNode(array('type' => 'article', 'status' => NODE_NOT_PUBLISHED));
    $node4 = $this->drupalCreateNode(array('type' => 'page', 'status' => NODE_NOT_PUBLISHED));

    // Check that when a set of nodes that only contains articles is loaded,
    // the properties added to the node by node_test_load_node() correctly
    // reflect the expected values.
    $nodes = node_load_multiple(array(), array('status' => NODE_PUBLISHED));
    $loaded_node = end($nodes);
    $this->assertEqual($loaded_node->node_test_loaded_nids, array($node1->nid, $node2->nid), 'hook_node_load() received the correct list of node IDs the first time it was called.');
    $this->assertEqual($loaded_node->node_test_loaded_types, array('article'), 'hook_node_load() received the correct list of node types the first time it was called.');

    // Now, as part of the same page request, load a set of nodes that contain
    // both articles and pages, and make sure the parameters passed to
    // node_test_node_load() are correctly updated.
    $nodes = node_load_multiple(array(), array('status' => NODE_NOT_PUBLISHED));
    $loaded_node = end($nodes);
    $this->assertEqual($loaded_node->node_test_loaded_nids, array($node3->nid, $node4->nid), 'hook_node_load() received the correct list of node IDs the second time it was called.');
    $this->assertEqual($loaded_node->node_test_loaded_types, array('article', 'page'), 'hook_node_load() received the correct list of node types the second time it was called.');
  }
}

/**
 * Tests the node revision functionality.
 */
class NodeRevisionsTestCase extends DrupalWebTestCase {

  /**
   * Nodes used by the test.
   *
   * @var array
   */
  protected $nodes;

  /**
   * The revision messages for node revisions created in the test.
   *
   * @var array
   */
  protected $logs;

  public static function getInfo() {
    return array(
      'name' => 'Node revisions',
      'description' => 'Create a node with revisions and test viewing, saving, reverting, and deleting revisions.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    // Create and login user.
    $web_user = $this->drupalCreateUser(array('view revisions', 'revert revisions', 'edit any page content',
                                               'delete revisions', 'delete any page content'));
    $this->drupalLogin($web_user);

    // Create initial node.
    $node = $this->drupalCreateNode();
    $settings = get_object_vars($node);
    $settings['revision'] = 1;

    $nodes = array();
    $logs = array();

    // Get original node.
    $nodes[] = $node;

    // Create three revisions.
    $revision_count = 3;
    for ($i = 0; $i < $revision_count; $i++) {
      $logs[] = $settings['log'] = $this->randomName(32);

      // Create revision with random title and body and update variables.
      $this->drupalCreateNode($settings);
      $node = node_load($node->nid); // Make sure we get revision information.
      $settings = get_object_vars($node);

      $nodes[] = $node;
    }

    $this->nodes = $nodes;
    $this->logs = $logs;
  }

  /**
   * Checks node revision related operations.
   */
  function testRevisions() {
    $nodes = $this->nodes;
    $logs = $this->logs;

    // Get last node for simple checks.
    $node = $nodes[3];

    // Confirm the correct revision text appears on "view revisions" page.
    $this->drupalGet("node/$node->nid/revisions/$node->vid/view");
    $this->assertText($node->body[LANGUAGE_NONE][0]['value'], 'Correct text displays for version.');

    // Confirm the correct log message appears on "revisions overview" page.
    $this->drupalGet("node/$node->nid/revisions");
    foreach ($logs as $log) {
      $this->assertText($log, 'Log message found.');
    }

    // Confirm that revisions revert properly.
    $this->drupalPost("node/$node->nid/revisions/{$nodes[1]->vid}/revert", array(), t('Revert'));
    $this->assertRaw(t('@type %title has been reverted back to the revision from %revision-date.',
                        array('@type' => 'Basic page', '%title' => $nodes[1]->title,
                              '%revision-date' => format_date($nodes[1]->revision_timestamp))), 'Revision reverted.');
    $reverted_node = node_load($node->nid);
    $this->assertTrue(($nodes[1]->body[LANGUAGE_NONE][0]['value'] == $reverted_node->body[LANGUAGE_NONE][0]['value']), 'Node reverted correctly.');

    // Confirm revisions delete properly.
    $this->drupalPost("node/$node->nid/revisions/{$nodes[1]->vid}/delete", array(), t('Delete'));
    $this->assertRaw(t('Revision from %revision-date of @type %title has been deleted.',
                        array('%revision-date' => format_date($nodes[1]->revision_timestamp),
                              '@type' => 'Basic page', '%title' => $nodes[1]->title)), 'Revision deleted.');
    $this->assertTrue(db_query('SELECT COUNT(vid) FROM {node_revision} WHERE nid = :nid and vid = :vid', array(':nid' => $node->nid, ':vid' => $nodes[1]->vid))->fetchField() == 0, 'Revision not found.');
  }

  /**
   * Checks that revisions are correctly saved without log messages.
   */
  function testNodeRevisionWithoutLogMessage() {
    // Create a node with an initial log message.
    $log = $this->randomName(10);
    $node = $this->drupalCreateNode(array('log' => $log));

    // Save over the same revision and explicitly provide an empty log message
    // (for example, to mimic the case of a node form submitted with no text in
    // the "log message" field), and check that the original log message is
    // preserved.
    $new_title = $this->randomName(10) . 'testNodeRevisionWithoutLogMessage1';
    $updated_node = (object) array(
      'nid' => $node->nid,
      'vid' => $node->vid,
      'uid' => $node->uid,
      'type' => $node->type,
      'title' => $new_title,
      'log' => '',
    );
    node_save($updated_node);
    $this->drupalGet('node/' . $node->nid);
    $this->assertText($new_title, 'New node title appears on the page.');
    $node_revision = node_load($node->nid, NULL, TRUE);
    $this->assertEqual($node_revision->log, $log, 'After an existing node revision is re-saved without a log message, the original log message is preserved.');

    // Create another node with an initial log message.
    $node = $this->drupalCreateNode(array('log' => $log));

    // Save a new node revision without providing a log message, and check that
    // this revision has an empty log message.
    $new_title = $this->randomName(10) . 'testNodeRevisionWithoutLogMessage2';
    $updated_node = (object) array(
      'nid' => $node->nid,
      'vid' => $node->vid,
      'uid' => $node->uid,
      'type' => $node->type,
      'title' => $new_title,
      'revision' => 1,
    );
    node_save($updated_node);
    $this->drupalGet('node/' . $node->nid);
    $this->assertText($new_title, 'New node title appears on the page.');
    $node_revision = node_load($node->nid, NULL, TRUE);
    $this->assertTrue(empty($node_revision->log), 'After a new node revision is saved with an empty log message, the log message for the node is empty.');
  }
}

/**
 * Tests the node edit functionality.
 */
class PageEditTestCase extends DrupalWebTestCase {

  /**
   * A user with permission to create and edit own page content.
   *
   * @var object
   */
  protected $web_user;

  /**
   * A user with permission to bypass node access and administer nodes.
   *
   * @var object
   */
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Node edit',
      'description' => 'Create a node and test node edit functionality.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    $this->web_user = $this->drupalCreateUser(array('edit own page content', 'create page content'));
    $this->admin_user = $this->drupalCreateUser(array('bypass node access', 'administer nodes'));
  }

  /**
   * Checks node edit functionality.
   */
  function testPageEdit() {
    $this->drupalLogin($this->web_user);

    $langcode = LANGUAGE_NONE;
    $title_key = "title";
    $body_key = "body[$langcode][0][value]";
    // Create node to edit.
    $edit = array();
    $edit[$title_key] = $this->randomName(8);
    $edit[$body_key] = $this->randomName(16);
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the node exists in the database.
    $node = $this->drupalGetNodeByTitle($edit[$title_key]);
    $this->assertTrue($node, 'Node found in database.');

    // Check that "edit" link points to correct page.
    $this->clickLink(t('Edit'));
    $edit_url = url("node/$node->nid/edit", array('absolute' => TRUE));
    $actual_url = $this->getURL();
    $this->assertEqual($edit_url, $actual_url, 'On edit page.');

    // Check that the title and body fields are displayed with the correct values.
    $active = '<span class="element-invisible">' . t('(active tab)') . '</span>';
    $link_text = t('!local-task-title!active', array('!local-task-title' => t('Edit'), '!active' => $active));
    $this->assertText(strip_tags($link_text), 0, 'Edit tab found and marked active.');
    $this->assertFieldByName($title_key, $edit[$title_key], 'Title field displayed.');
    $this->assertFieldByName($body_key, $edit[$body_key], 'Body field displayed.');

    // Edit the content of the node.
    $edit = array();
    $edit[$title_key] = $this->randomName(8);
    $edit[$body_key] = $this->randomName(16);
    // Stay on the current page, without reloading.
    $this->drupalPost(NULL, $edit, t('Save'));

    // Check that the title and body fields are displayed with the updated values.
    $this->assertText($edit[$title_key], 'Title displayed.');
    $this->assertText($edit[$body_key], 'Body displayed.');

    // Login as a second administrator user.
    $second_web_user = $this->drupalCreateUser(array('administer nodes', 'edit any page content'));
    $this->drupalLogin($second_web_user);
    // Edit the same node, creating a new revision.
    $this->drupalGet("node/$node->nid/edit");
    $edit = array();
    $edit['title'] = $this->randomName(8);
    $edit[$body_key] = $this->randomName(16);
    $edit['revision'] = TRUE;
    $this->drupalPost(NULL, $edit, t('Save'));

    // Ensure that the node revision has been created.
    $revised_node = $this->drupalGetNodeByTitle($edit['title']);
    $this->assertNotIdentical($node->vid, $revised_node->vid, 'A new revision has been created.');
    // Ensure that the node author is preserved when it was not changed in the
    // edit form.
    $this->assertIdentical($node->uid, $revised_node->uid, 'The node author has been preserved.');
    // Ensure that the revision authors are different since the revisions were
    // made by different users.
    $first_node_version = node_load($node->nid, $node->vid);
    $second_node_version = node_load($node->nid, $revised_node->vid);
    $this->assertNotIdentical($first_node_version->revision_uid, $second_node_version->revision_uid, 'Each revision has a distinct user.');
  }

  /**
   * Tests changing a node's "authored by" field.
   */
  function testPageAuthoredBy() {
    $this->drupalLogin($this->admin_user);

    // Create node to edit.
    $langcode = LANGUAGE_NONE;
    $body_key = "body[$langcode][0][value]";
    $edit = array();
    $edit['title'] = $this->randomName(8);
    $edit[$body_key] = $this->randomName(16);
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the node was authored by the currently logged in user.
    $node = $this->drupalGetNodeByTitle($edit['title']);
    $this->assertIdentical($node->uid, $this->admin_user->uid, 'Node authored by admin user.');

    // Try to change the 'authored by' field to an invalid user name.
    $edit = array(
      'name' => 'invalid-name',
    );
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->assertText('The username invalid-name does not exist.');

    // Change the authored by field to an empty string, which should assign
    // authorship to the anonymous user (uid 0).
    $edit['name'] = '';
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $node = node_load($node->nid, NULL, TRUE);
    $this->assertIdentical($node->uid, '0', 'Node authored by anonymous user.');

    // Change the authored by field to another user's name (that is not
    // logged in).
    $edit['name'] = $this->web_user->name;
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $node = node_load($node->nid, NULL, TRUE);
    $this->assertIdentical($node->uid, $this->web_user->uid, 'Node authored by normal user.');

    // Check that normal users cannot change the authored by information.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node/' . $node->nid . '/edit');
    $this->assertNoFieldByName('name');
  }
}

/**
 * Tests the node entity preview functionality.
 */
class PagePreviewTestCase extends DrupalWebTestCase {
  protected $field;
  protected $field_name;
  protected $instance;
  protected $vocabulary;
  protected $term;

  public static function getInfo() {
    return array(
      'name' => 'Node preview',
      'description' => 'Test node preview functionality.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp(array('taxonomy', 'node'));

    $web_user = $this->drupalCreateUser(array('edit own page content', 'create page content'));
    $this->drupalLogin($web_user);

    // Add a vocabulary so we can test different view modes.
    $vocabulary = (object) array(
      'name' => $this->randomName(),
      'description' => $this->randomName(),
      'machine_name' => drupal_strtolower($this->randomName()),
      'help' => '',
      'nodes' => array('page' => 'page'),
    );
    taxonomy_vocabulary_save($vocabulary);

    $this->vocabulary = $vocabulary;

    // Add a term to the vocabulary.
    $term = (object) array(
      'name' => $this->randomName(),
      'description' => $this->randomName(),
      // Use the first available text format.
      'format' => db_query_range('SELECT format FROM {filter_format}', 0, 1)->fetchField(),
      'vid' => $this->vocabulary->vid,
      'vocabulary_machine_name' => $vocabulary->machine_name,
    );
    taxonomy_term_save($term);

    $this->term = $term;

    // Set up a field and instance.
    $this->field_name = drupal_strtolower($this->randomName());
    $this->field = array(
      'field_name' => $this->field_name,
      'type' => 'taxonomy_term_reference',
      'settings' => array(
        'allowed_values' => array(
          array(
            'vocabulary' => $this->vocabulary->machine_name,
            'parent' => '0',
          ),
        ),
      )
    );

    field_create_field($this->field);
    $this->instance = array(
      'field_name' => $this->field_name,
      'entity_type' => 'node',
      'bundle' => 'page',
      'widget' => array(
        'type' => 'options_select',
      ),
      // Hide on full display but render on teaser.
      'display' => array(
        'default' => array(
          'type' => 'hidden',
        ),
        'teaser' => array(
          'type' => 'taxonomy_term_reference_link',
        ),
      ),
    );
    field_create_instance($this->instance);
  }

  /**
   * Checks the node preview functionality.
   */
  function testPagePreview() {
    $langcode = LANGUAGE_NONE;
    $title_key = "title";
    $body_key = "body[$langcode][0][value]";
    $term_key = "{$this->field_name}[$langcode]";

    // Fill in node creation form and preview node.
    $edit = array();
    $edit[$title_key] = $this->randomName(8);
    $edit[$body_key] = $this->randomName(16);
    $edit[$term_key] = $this->term->tid;
    $this->drupalPost('node/add/page', $edit, t('Preview'));

    // Check that the preview is displaying the title, body, and term.
    $this->assertTitle(t('Preview | Drupal'), 'Basic page title is preview.');
    $this->assertText($edit[$title_key], 'Title displayed.');
    $this->assertText($edit[$body_key], 'Body displayed.');
    $this->assertText($this->term->name, 'Term displayed.');

    // Check that the title, body, and term fields are displayed with the
    // correct values.
    $this->assertFieldByName($title_key, $edit[$title_key], 'Title field displayed.');
    $this->assertFieldByName($body_key, $edit[$body_key], 'Body field displayed.');
    $this->assertFieldByName($term_key, $edit[$term_key], 'Term field displayed.');
  }

  /**
   * Checks the node preview functionality, when using revisions.
   */
  function testPagePreviewWithRevisions() {
    $langcode = LANGUAGE_NONE;
    $title_key = "title";
    $body_key = "body[$langcode][0][value]";
    $term_key = "{$this->field_name}[$langcode]";
    // Force revision on "Basic page" content.
    variable_set('node_options_page', array('status', 'revision'));

    // Fill in node creation form and preview node.
    $edit = array();
    $edit[$title_key] = $this->randomName(8);
    $edit[$body_key] = $this->randomName(16);
    $edit[$term_key] = $this->term->tid;
    $edit['log'] = $this->randomName(32);
    $this->drupalPost('node/add/page', $edit, t('Preview'));

    // Check that the preview is displaying the title, body, and term.
    $this->assertTitle(t('Preview | Drupal'), 'Basic page title is preview.');
    $this->assertText($edit[$title_key], 'Title displayed.');
    $this->assertText($edit[$body_key], 'Body displayed.');
    $this->assertText($this->term->name, 'Term displayed.');

    // Check that the title, body, and term fields are displayed with the
    // correct values.
    $this->assertFieldByName($title_key, $edit[$title_key], 'Title field displayed.');
    $this->assertFieldByName($body_key, $edit[$body_key], 'Body field displayed.');
    $this->assertFieldByName($term_key, $edit[$term_key], 'Term field displayed.');

    // Check that the log field has the correct value.
    $this->assertFieldByName('log', $edit['log'], 'Log field displayed.');
  }
}

/**
 * Tests creating and saving a node.
 */
class NodeCreationTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node creation',
      'description' => 'Create a node and test saving it.',
      'group' => 'Node',
    );
  }

  function setUp() {
    // Enable dummy module that implements hook_node_insert for exceptions.
    parent::setUp('node_test_exception');

    $web_user = $this->drupalCreateUser(array('create page content', 'edit own page content'));
    $this->drupalLogin($web_user);
  }

  /**
   * Creates a "Basic page" node and verifies its consistency in the database.
   */
  function testNodeCreation() {
    // Create a node.
    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $this->randomName(8);
    $edit["body[$langcode][0][value]"] = $this->randomName(16);
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the Basic page has been created.
    $this->assertRaw(t('!post %title has been created.', array('!post' => 'Basic page', '%title' => $edit["title"])), 'Basic page created.');

    // Check that the node exists in the database.
    $node = $this->drupalGetNodeByTitle($edit["title"]);
    $this->assertTrue($node, 'Node found in database.');
  }

  /**
   * Verifies that a transaction rolls back the failed creation.
   */
  function testFailedPageCreation() {
    // Create a node.
    $edit = array(
      'uid'      => $this->loggedInUser->uid,
      'name'     => $this->loggedInUser->name,
      'type'     => 'page',
      'language' => LANGUAGE_NONE,
      'title'    => 'testing_transaction_exception',
    );

    try {
      // An exception is generated by node_test_exception_node_insert() if the
      // title is 'testing_transaction_exception'.
      node_save((object) $edit);
      $this->fail(t('Expected exception has not been thrown.'));
    }
    catch (Exception $e) {
      $this->pass(t('Expected exception has been thrown.'));
    }

    if (Database::getConnection()->supportsTransactions()) {
      // Check that the node does not exist in the database.
      $node = $this->drupalGetNodeByTitle($edit['title']);
      $this->assertFalse($node, 'Transactions supported, and node not found in database.');
    }
    else {
      // Check that the node exists in the database.
      $node = $this->drupalGetNodeByTitle($edit['title']);
      $this->assertTrue($node, 'Transactions not supported, and node found in database.');

      // Check that the failed rollback was logged.
      $records = db_query("SELECT wid FROM {watchdog} WHERE message LIKE 'Explicit rollback failed%'")->fetchAll();
      $this->assertTrue(count($records) > 0, 'Transactions not supported, and rollback error logged to watchdog.');
    }

    // Check that the rollback error was logged.
    // PostgreSQL doesn't support bytea LIKE queries, so we need to unserialize
    // first to check for the rollback exception message.
    $matches = array();
    $records = db_query("SELECT wid, variables FROM {watchdog}")->fetchAll();
    foreach ($records as $record) {
      $variables = (array) unserialize($record->variables);
      if (isset($variables['!message']) && $variables['!message'] === 'Test exception for rollback.') {
        $matches[] = $record->wid;
      }
    }
    $this->assertTrue(count($matches) > 0, 'Rollback explanatory error logged to watchdog.');
  }

  /**
   * Create an unpublished node and confirm correct redirect behavior.
   */
  function testUnpublishedNodeCreation() {
    // Set "Basic page" content type to be unpublished by default.
    variable_set('node_options_page', array());
    // Set the front page to the default "node" page.
    variable_set('site_frontpage', 'node');

    // Create a node.
    $edit = array();
    $edit["title"] = $this->randomName(8);
    $edit["body[" . LANGUAGE_NONE . "][0][value]"] = $this->randomName(16);
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the user was redirected to the home page.
    $this->assertText(t('Welcome to Drupal'), t('The user is redirected to the home page.'));
  }
}

/**
 * Tests the functionality of node entity edit permissions.
 */
class PageViewTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node edit permissions',
      'description' => 'Create a node and test edit permissions.',
      'group' => 'Node',
    );
  }

  /**
   * Tests an anonymous and unpermissioned user attempting to edit the node.
   */
  function testPageView() {
    // Create a node to view.
    $node = $this->drupalCreateNode();
    $this->assertTrue(node_load($node->nid), 'Node created.');

    // Try to edit with anonymous user.
    $html = $this->drupalGet("node/$node->nid/edit");
    $this->assertResponse(403);

    // Create a user without permission to edit node.
    $web_user = $this->drupalCreateUser(array('access content'));
    $this->drupalLogin($web_user);

    // Attempt to access edit page.
    $this->drupalGet("node/$node->nid/edit");
    $this->assertResponse(403);

    // Create user with permission to edit node.
    $web_user = $this->drupalCreateUser(array('bypass node access'));
    $this->drupalLogin($web_user);

    // Attempt to access edit page.
    $this->drupalGet("node/$node->nid/edit");
    $this->assertResponse(200);
  }
}

/**
 * Tests the summary length functionality.
 */
class SummaryLengthTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Summary length',
      'description' => 'Test summary length.',
      'group' => 'Node',
    );
  }

  /**
   * Tests the node summary length functionality.
   */
  function testSummaryLength() {
    // Create a node to view.
    $settings = array(
      'body' => array(LANGUAGE_NONE => array(array('value' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vitae arcu at leo cursus laoreet. Curabitur dui tortor, adipiscing malesuada tempor in, bibendum ac diam. Cras non tellus a libero pellentesque condimentum. What is a Drupalism? Suspendisse ac lacus libero. Ut non est vel nisl faucibus interdum nec sed leo. Pellentesque sem risus, vulputate eu semper eget, auctor in libero. Ut fermentum est vitae metus convallis scelerisque. Phasellus pellentesque rhoncus tellus, eu dignissim purus posuere id. Quisque eu fringilla ligula. Morbi ullamcorper, lorem et mattis egestas, tortor neque pretium velit, eget eleifend odio turpis eu purus. Donec vitae metus quis leo pretium tincidunt a pulvinar sem. Morbi adipiscing laoreet mauris vel placerat. Nullam elementum, nisl sit amet scelerisque malesuada, dolor nunc hendrerit quam, eu ultrices erat est in orci. Curabitur feugiat egestas nisl sed accumsan.'))),
      'promote' => 1,
    );
    $node = $this->drupalCreateNode($settings);
    $this->assertTrue(node_load($node->nid), 'Node created.');

    // Create user with permission to view the node.
    $web_user = $this->drupalCreateUser(array('access content', 'administer content types'));
    $this->drupalLogin($web_user);

    // Attempt to access the front page.
    $this->drupalGet("node");
    // The node teaser when it has 600 characters in length
    $expected = 'What is a Drupalism?';
    $this->assertRaw($expected, 'Check that the summary is 600 characters in length', 'Node');

    // Change the teaser length for "Basic page" content type.
    $instance = field_info_instance('node', 'body', $node->type);
    $instance['display']['teaser']['settings']['trim_length'] = 200;
    field_update_instance($instance);

    // Attempt to access the front page again and check if the summary is now only 200 characters in length.
    $this->drupalGet("node");
    $this->assertNoRaw($expected, 'Check that the summary is not longer than 200 characters', 'Node');
  }
}

/**
 * Tests XSS functionality with a node entity.
 */
class NodeTitleXSSTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node title XSS filtering',
      'description' => 'Create a node with dangerous tags in its title and test that they are escaped.',
      'group' => 'Node',
    );
  }

  /**
   * Tests XSS functionality with a node entity.
   */
  function testNodeTitleXSS() {
    // Prepare a user to do the stuff.
    $web_user = $this->drupalCreateUser(array('create page content', 'edit any page content'));
    $this->drupalLogin($web_user);

    $xss = '<script>alert("xss")</script>';
    $title = $xss . $this->randomName();
    $edit = array("title" => $title);

    $this->drupalPost('node/add/page', $edit, t('Preview'));
    $this->assertNoRaw($xss, 'Harmful tags are escaped when previewing a node.');

    $settings = array('title' => $title);
    $node = $this->drupalCreateNode($settings);

    $this->drupalGet('node/' . $node->nid);
    // assertTitle() decodes HTML-entities inside the <title> element.
    $this->assertTitle($edit["title"] . ' | Drupal', 'Title is diplayed when viewing a node.');
    $this->assertNoRaw($xss, 'Harmful tags are escaped when viewing a node.');

    $this->drupalGet('node/' . $node->nid . '/edit');
    $this->assertNoRaw($xss, 'Harmful tags are escaped when editing a node.');
  }
}

/**
 * Tests the availability of the syndicate block.
 */
class NodeBlockTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Block availability',
      'description' => 'Check if the syndicate block is available.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    // Create and login user
    $admin_user = $this->drupalCreateUser(array('administer blocks'));
    $this->drupalLogin($admin_user);
  }

  /**
   * Tests that the "Syndicate" block is shown when enabled.
   */
  function testSyndicateBlock() {
    // Set block title to confirm that the interface is available.
    $this->drupalPost('admin/structure/block/manage/node/syndicate/configure', array('title' => $this->randomName(8)), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

    // Set the block to a region to confirm block is available.
    $edit = array();
    $edit['blocks[node_syndicate][region]'] = 'footer';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertText(t('The block settings have been updated.'), 'Block successfully move to footer region.');
  }
}

/**
 * Checks that the post information displays when enabled for a content type.
 */
class NodePostSettingsTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node post information display',
      'description' => 'Check that the post information (submitted by Username on date) text displays appropriately.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    $web_user = $this->drupalCreateUser(array('create page content', 'administer content types', 'access user profiles'));
    $this->drupalLogin($web_user);
  }

  /**
   * Confirms "Basic page" content type and post information is on a new node.
   */
  function testPagePostInfo() {

    // Set "Basic page" content type to display post information.
    $edit = array();
    $edit['node_submitted'] = TRUE;
    $this->drupalPost('admin/structure/types/manage/page', $edit, t('Save content type'));

    // Create a node.
    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $this->randomName(8);
    $edit["body[$langcode][0][value]"] = $this->randomName(16);
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the post information is displayed.
    $node = $this->drupalGetNodeByTitle($edit["title"]);
    $elements = $this->xpath('//div[contains(@class,:class)]', array(':class' => 'submitted'));
    $this->assertEqual(count($elements), 1, 'Post information is displayed.');
  }

  /**
   * Confirms absence of post information on a new node.
   */
  function testPageNotPostInfo() {

    // Set "Basic page" content type to display post information.
    $edit = array();
    $edit['node_submitted'] = FALSE;
    $this->drupalPost('admin/structure/types/manage/page', $edit, t('Save content type'));

    // Create a node.
    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $this->randomName(8);
    $edit["body[$langcode][0][value]"] = $this->randomName(16);
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the post information is displayed.
    $node = $this->drupalGetNodeByTitle($edit["title"]);
    $this->assertNoRaw('<span class="submitted">', 'Post information is not displayed.');
  }
}

/**
 * Ensures that data added to nodes by other modules appears in RSS feeds.
 *
 * Create a node, enable the node_test module to ensure that extra data is
 * added to the node->content array, then verify that the data appears on the
 * sitewide RSS feed at rss.xml.
 */
class NodeRSSContentTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node RSS Content',
      'description' => 'Ensure that data added to nodes by other modules appears in RSS feeds.',
      'group' => 'Node',
    );
  }

  function setUp() {
    // Enable dummy module that implements hook_node_view.
    parent::setUp('node_test');

    // Use bypass node access permission here, because the test class uses
    // hook_grants_alter() to deny access to everyone on node_access
    // queries.
    $user = $this->drupalCreateUser(array('bypass node access', 'access content', 'create article content'));
    $this->drupalLogin($user);
  }

  /**
   * Ensures that a new node includes the custom data when added to an RSS feed.
   */
  function testNodeRSSContent() {
    // Create a node.
    $node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));

    $this->drupalGet('rss.xml');

    // Check that content added in 'rss' view mode appear in RSS feed.
    $rss_only_content = t('Extra data that should appear only in the RSS feed for node !nid.', array('!nid' => $node->nid));
    $this->assertText($rss_only_content, 'Node content designated for RSS appear in RSS feed.');

    // Check that content added in view modes other than 'rss' doesn't
    // appear in RSS feed.
    $non_rss_content = t('Extra data that should appear everywhere except the RSS feed for node !nid.', array('!nid' => $node->nid));
    $this->assertNoText($non_rss_content, 'Node content not designed for RSS doesn\'t appear in RSS feed.');

    // Check that extra RSS elements and namespaces are added to RSS feed.
    $test_element = array(
      'key' => 'testElement',
      'value' => t('Value of testElement RSS element for node !nid.', array('!nid' => $node->nid)),
    );
    $test_ns = 'xmlns:drupaltest="http://example.com/test-namespace"';
    $this->assertRaw(format_xml_elements(array($test_element)), 'Extra RSS elements appear in RSS feed.');
    $this->assertRaw($test_ns, 'Extra namespaces appear in RSS feed.');

    // Check that content added in 'rss' view mode doesn't appear when
    // viewing node.
    $this->drupalGet("node/$node->nid");
    $this->assertNoText($rss_only_content, 'Node content designed for RSS doesn\'t appear when viewing node.');

    // Check that the node feed page does not try to interpret additional path
    // components as arguments for node_feed() and returns default content.
    $this->drupalGet('rss.xml/' . $this->randomName() . '/' . $this->randomName());
    $this->assertText($rss_only_content, 'Ignore page arguments when delivering rss.xml.');
  }
}

/**
 * Tests basic node_access functionality.
 *
 * Note that hook_node_access_records() is covered in another test class.
 *
 * @todo Cover hook_node_access in a separate test class.
 */
class NodeAccessTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node access',
      'description' => 'Test node_access function',
      'group' => 'Node',
    );
  }

  /**
   * Asserts node_access() correctly grants or denies access.
   */
  function assertNodeAccess($ops, $node, $account) {
    foreach ($ops as $op => $result) {
      $msg = format_string("node_access returns @result with operation '@op'.", array('@result' => $result ? 'true' : 'false', '@op' => $op));
      $this->assertEqual($result, node_access($op, $node, $account), $msg);
    }
  }

  function setUp() {
    parent::setUp();
    // Clear permissions for authenticated users.
    db_delete('role_permission')
      ->condition('rid', DRUPAL_AUTHENTICATED_RID)
      ->execute();
  }

  /**
   * Runs basic tests for node_access function.
   */
  function testNodeAccess() {
    // Ensures user without 'access content' permission can do nothing.
    $web_user1 = $this->drupalCreateUser(array('create page content', 'edit any page content', 'delete any page content'));
    $node1 = $this->drupalCreateNode(array('type' => 'page'));
    $this->assertNodeAccess(array('create' => FALSE), 'page', $web_user1);
    $this->assertNodeAccess(array('view' => FALSE, 'update' => FALSE, 'delete' => FALSE), $node1, $web_user1);

    // Ensures user with 'bypass node access' permission can do everything.
    $web_user2 = $this->drupalCreateUser(array('bypass node access'));
    $node2 = $this->drupalCreateNode(array('type' => 'page'));
    $this->assertNodeAccess(array('create' => TRUE), 'page', $web_user2);
    $this->assertNodeAccess(array('view' => TRUE, 'update' => TRUE, 'delete' => TRUE), $node2, $web_user2);

    // User cannot 'view own unpublished content'.
    $web_user3 = $this->drupalCreateUser(array('access content'));
    $node3 = $this->drupalCreateNode(array('status' => 0, 'uid' => $web_user3->uid));
    $this->assertNodeAccess(array('view' => FALSE), $node3, $web_user3);

    // User cannot create content without permission.
    $this->assertNodeAccess(array('create' => FALSE), 'page', $web_user3);

    // User can 'view own unpublished content', but another user cannot.
    $web_user4 = $this->drupalCreateUser(array('access content', 'view own unpublished content'));
    $web_user5 = $this->drupalCreateUser(array('access content', 'view own unpublished content'));
    $node4 = $this->drupalCreateNode(array('status' => 0, 'uid' => $web_user4->uid));
    $this->assertNodeAccess(array('view' => TRUE, 'update' => FALSE), $node4, $web_user4);
    $this->assertNodeAccess(array('view' => FALSE), $node4, $web_user5);

    // Tests the default access provided for a published node.
    $node5 = $this->drupalCreateNode();
    $this->assertNodeAccess(array('view' => TRUE, 'update' => FALSE, 'delete' => FALSE), $node5, $web_user3);
  }
}

/**
 * Tests hook_node_access_records() functionality.
 */
class NodeAccessRecordsTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node access records',
      'description' => 'Test hook_node_access_records when acquiring grants.',
      'group' => 'Node',
    );
  }

  function setUp() {
    // Enable dummy module that implements hook_node_grants(),
    // hook_node_access_records(), hook_node_grants_alter() and
    // hook_node_access_records_alter().
    parent::setUp('node_test');
  }

  /**
   * Creates a node and tests the creation of node access rules.
   */
  function testNodeAccessRecords() {
    // Create an article node.
    $node1 = $this->drupalCreateNode(array('type' => 'article'));
    $this->assertTrue(node_load($node1->nid), 'Article node created.');

    // Check to see if grants added by node_test_node_access_records made it in.
    $records = db_query('SELECT realm, gid FROM {node_access} WHERE nid = :nid', array(':nid' => $node1->nid))->fetchAll();
    $this->assertEqual(count($records), 1, 'Returned the correct number of rows.');
    $this->assertEqual($records[0]->realm, 'test_article_realm', 'Grant with article_realm acquired for node without alteration.');
    $this->assertEqual($records[0]->gid, 1, 'Grant with gid = 1 acquired for node without alteration.');

    // Create an unpromoted "Basic page" node.
    $node2 = $this->drupalCreateNode(array('type' => 'page', 'promote' => 0));
    $this->assertTrue(node_load($node2->nid), 'Unpromoted basic page node created.');

    // Check to see if grants added by node_test_node_access_records made it in.
    $records = db_query('SELECT realm, gid FROM {node_access} WHERE nid = :nid', array(':nid' => $node2->nid))->fetchAll();
    $this->assertEqual(count($records), 1, 'Returned the correct number of rows.');
    $this->assertEqual($records[0]->realm, 'test_page_realm', 'Grant with page_realm acquired for node without alteration.');
    $this->assertEqual($records[0]->gid, 1, 'Grant with gid = 1 acquired for node without alteration.');

    // Create an unpromoted, unpublished "Basic page" node.
    $node3 = $this->drupalCreateNode(array('type' => 'page', 'promote' => 0, 'status' => 0));
    $this->assertTrue(node_load($node3->nid), 'Unpromoted, unpublished basic page node created.');

    // Check to see if grants added by node_test_node_access_records made it in.
    $records = db_query('SELECT realm, gid FROM {node_access} WHERE nid = :nid', array(':nid' => $node3->nid))->fetchAll();
    $this->assertEqual(count($records), 1, 'Returned the correct number of rows.');
    $this->assertEqual($records[0]->realm, 'test_page_realm', 'Grant with page_realm acquired for node without alteration.');
    $this->assertEqual($records[0]->gid, 1, 'Grant with gid = 1 acquired for node without alteration.');

    // Create a promoted "Basic page" node.
    $node4 = $this->drupalCreateNode(array('type' => 'page', 'promote' => 1));
    $this->assertTrue(node_load($node4->nid), 'Promoted basic page node created.');

    // Check to see if grant added by node_test_node_access_records was altered
    // by node_test_node_access_records_alter.
    $records = db_query('SELECT realm, gid FROM {node_access} WHERE nid = :nid', array(':nid' => $node4->nid))->fetchAll();
    $this->assertEqual(count($records), 1, 'Returned the correct number of rows.');
    $this->assertEqual($records[0]->realm, 'test_alter_realm', 'Altered grant with alter_realm acquired for node.');
    $this->assertEqual($records[0]->gid, 2, 'Altered grant with gid = 2 acquired for node.');

    // Check to see if we can alter grants with hook_node_grants_alter().
    $operations = array('view', 'update', 'delete');
    // Create a user that is allowed to access content.
    $web_user = $this->drupalCreateUser(array('access content'));
    foreach ($operations as $op) {
      $grants = node_test_node_grants($op, $web_user);
      $altered_grants = $grants;
      drupal_alter('node_grants', $altered_grants, $web_user, $op);
      $this->assertNotEqual($grants, $altered_grants, format_string('Altered the %op grant for a user.', array('%op' => $op)));
    }

    // Check that core does not grant access to an unpublished node when an
    // empty $grants array is returned.
    $node6 = $this->drupalCreateNode(array('status' => 0, 'disable_node_access' => TRUE));
    $records = db_query('SELECT realm, gid FROM {node_access} WHERE nid = :nid', array(':nid' => $node6->nid))->fetchAll();
    $this->assertEqual(count($records), 0, 'Returned no records for unpublished node.');
  }
}

/**
 * Tests for Node Access with a non-node base table.
 */
class NodeAccessBaseTableTestCase extends DrupalWebTestCase {
  protected $nodesByUser;
  protected $webUser;
  protected $publicTid;
  protected $privateTid;
  protected $nids_visible;

  public static function getInfo() {
    return array(
      'name' => 'Node Access on any table',
      'description' => 'Checks behavior of the node access subsystem if the base table is not node.',
      'group' => 'Node',
    );
  }

  public function setUp() {
    parent::setUp('node_access_test');
    node_access_rebuild();
    variable_set('node_access_test_private', TRUE);
  }

  /**
   * Tests the "private" node access functionality.
   *
   * - Create 2 users with "access content" and "create article" permissions.
   * - Each user creates one private and one not private article.

   * - Test that each user can view the other user's non-private article.
   * - Test that each user cannot view the other user's private article.
   * - Test that each user finds only appropriate (non-private + own private)
   *   in taxonomy listing.
   * - Create another user with 'view any private content'.
   * - Test that user 4 can view all content created above.
   * - Test that user 4 can view all content on taxonomy listing.
   */
  function testNodeAccessBasic() {
    $num_simple_users = 2;
    $simple_users = array();

    // nodes keyed by uid and nid: $nodes[$uid][$nid] = $is_private;
    $this->nodesByUser = array();
    $titles = array(); // Titles keyed by nid
    $private_nodes = array(); // Array of nids marked private.
    for ($i = 0; $i < $num_simple_users; $i++) {
      $simple_users[$i] = $this->drupalCreateUser(array('access content', 'create article content'));
    }
    foreach ($simple_users as $this->webUser) {
      $this->drupalLogin($this->webUser);
      foreach (array(0 => 'Public', 1 => 'Private') as $is_private => $type) {
        $edit = array(
          'title' => t('@private_public Article created by @user', array('@private_public' => $type, '@user' => $this->webUser->name)),
        );
        if ($is_private) {
          $edit['private'] = TRUE;
          $edit['body[und][0][value]'] = 'private node';
          $edit['field_tags[und]'] = 'private';
        }
        else {
          $edit['body[und][0][value]'] = 'public node';
          $edit['field_tags[und]'] = 'public';
        }

        $this->drupalPost('node/add/article', $edit, t('Save'));
        $nid = db_query('SELECT nid FROM {node} WHERE title = :title', array(':title' => $edit['title']))->fetchField();
        $private_status = db_query('SELECT private FROM {node_access_test} where nid = :nid', array(':nid' => $nid))->fetchField();
        $this->assertTrue($is_private == $private_status, 'The private status of the node was properly set in the node_access_test table.');
        if ($is_private) {
          $private_nodes[] = $nid;
        }
        $titles[$nid] = $edit['title'];
        $this->nodesByUser[$this->webUser->uid][$nid] = $is_private;
      }
    }
    $this->publicTid = db_query('SELECT tid FROM {taxonomy_term_data} WHERE name = :name', array(':name' => 'public'))->fetchField();
    $this->privateTid = db_query('SELECT tid FROM {taxonomy_term_data} WHERE name = :name', array(':name' => 'private'))->fetchField();
    $this->assertTrue($this->publicTid, 'Public tid was found');
    $this->assertTrue($this->privateTid, 'Private tid was found');
    foreach ($simple_users as $this->webUser) {
      $this->drupalLogin($this->webUser);
      // Check own nodes to see that all are readable.
      foreach ($this->nodesByUser as $uid => $data) {
        foreach ($data as $nid => $is_private) {
          $this->drupalGet('node/' . $nid);
          if ($is_private) {
            $should_be_visible = $uid == $this->webUser->uid;
          }
          else {
            $should_be_visible = TRUE;
          }
          $this->assertResponse($should_be_visible ? 200 : 403, strtr('A %private node by user %uid is %visible for user %current_uid.', array(
            '%private' => $is_private ? 'private' : 'public',
            '%uid' => $uid,
            '%visible' => $should_be_visible ? 'visible' : 'not visible',
            '%current_uid' => $this->webUser->uid,
          )));
        }
      }

      // Check to see that the correct nodes are shown on taxonomy/private
      // and taxonomy/public.
      $this->assertTaxonomyPage(FALSE);
    }

    // Now test that a user with 'access any private content' can view content.
    $access_user = $this->drupalCreateUser(array('access content', 'create article content', 'node test view', 'search content'));
    $this->drupalLogin($access_user);

    foreach ($this->nodesByUser as $uid => $private_status) {
      foreach ($private_status as $nid => $is_private) {
        $this->drupalGet('node/' . $nid);
        $this->assertResponse(200);
      }
    }

    // This user should be able to see all of the nodes on the relevant
    // taxonomy pages.
    $this->assertTaxonomyPage(TRUE);
  }

  /**
   * Checks taxonomy/term listings to ensure only accessible nodes are listed.
   *
   * @param $is_admin
   *   A boolean indicating whether the current user is an administrator. If
   *   TRUE, all nodes should be listed. If FALSE, only public nodes and the
   *   user's own private nodes should be listed.
   */
  protected function assertTaxonomyPage($is_admin) {
    foreach (array($this->publicTid, $this->privateTid) as $tid_is_private => $tid) {
      $this->drupalGet("taxonomy/term/$tid");
      $this->nids_visible = array();
      foreach ($this->xpath("//a[text()='Read more']") as $link) {
        $this->assertTrue(preg_match('|node/(\d+)$|', (string) $link['href'], $matches), 'Read more points to a node');
        $this->nids_visible[$matches[1]] = TRUE;
      }
      foreach ($this->nodesByUser as $uid => $data) {
        foreach ($data as $nid => $is_private) {
          // Private nodes should be visible on the private term page,
          // public nodes should be visible on the public term page.
          $should_be_visible = $tid_is_private == $is_private;
          // Non-administrators can only see their own nodes on the private
          // term page.
          if (!$is_admin && $tid_is_private) {
            $should_be_visible = $should_be_visible && $uid == $this->webUser->uid;
          }
          $this->assertIdentical(isset($this->nids_visible[$nid]), $should_be_visible, strtr('A %private node by user %uid is %visible for user %current_uid on the %tid_is_private page.', array(
            '%private' => $is_private ? 'private' : 'public',
            '%uid' => $uid,
            '%visible' => isset($this->nids_visible[$nid]) ? 'visible' : 'not visible',
            '%current_uid' => $this->webUser->uid,
            '%tid_is_private' => $tid_is_private ? 'private' : 'public',
          )));
        }
      }
    }
  }
}

/**
 * Tests node save related functionality, including import-save.
 */
class NodeSaveTestCase extends DrupalWebTestCase {
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Node save',
      'description' => 'Test node_save() for saving content.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp('node_test');
    // Create a user that is allowed to post; we'll use this to test the submission.
    $web_user = $this->drupalCreateUser(array('create article content'));
    $this->drupalLogin($web_user);
    $this->web_user = $web_user;
  }

  /**
   * Checks whether custom node IDs are saved properly during an import operation.
   *
   * Workflow:
   *  - first create a piece of content
   *  - save the content
   *  - check if node exists
   */
  function testImport() {
    // Node ID must be a number that is not in the database.
    $max_nid = db_query('SELECT MAX(nid) FROM {node}')->fetchField();
    $test_nid = $max_nid + mt_rand(1000, 1000000);
    $title = $this->randomName(8);
    $node = array(
      'title' => $title,
      'body' => array(LANGUAGE_NONE => array(array('value' => $this->randomName(32)))),
      'uid' => $this->web_user->uid,
      'type' => 'article',
      'nid' => $test_nid,
      'is_new' => TRUE,
    );
    $node = node_submit((object) $node);

    // Verify that node_submit did not overwrite the user ID.
    $this->assertEqual($node->uid, $this->web_user->uid, 'Function node_submit() preserves user ID');

    node_save($node);
    // Test the import.
    $node_by_nid = node_load($test_nid);
    $this->assertTrue($node_by_nid, 'Node load by node ID.');

    $node_by_title = $this->drupalGetNodeByTitle($title);
    $this->assertTrue($node_by_title, 'Node load by node title.');
  }

  /**
   * Verifies accuracy of the "created" and "changed" timestamp functionality.
   */
  function testTimestamps() {
    // Use the default timestamps.
    $edit = array(
      'uid' => $this->web_user->uid,
      'type' => 'article',
      'title' => $this->randomName(8),
    );

    node_save((object) $edit);
    $node = $this->drupalGetNodeByTitle($edit['title']);
    $this->assertEqual($node->created, REQUEST_TIME, 'Creating a node sets default "created" timestamp.');
    $this->assertEqual($node->changed, REQUEST_TIME, 'Creating a node sets default "changed" timestamp.');

    // Store the timestamps.
    $created = $node->created;
    $changed = $node->changed;

    node_save($node);
    $node = $this->drupalGetNodeByTitle($edit['title'], TRUE);
    $this->assertEqual($node->created, $created, 'Updating a node preserves "created" timestamp.');

    // Programmatically set the timestamps using hook_node_presave.
    $node->title = 'testing_node_presave';

    node_save($node);
    $node = $this->drupalGetNodeByTitle('testing_node_presave', TRUE);
    $this->assertEqual($node->created, 280299600, 'Saving a node uses "created" timestamp set in presave hook.');
    $this->assertEqual($node->changed, 979534800, 'Saving a node uses "changed" timestamp set in presave hook.');

    // Programmatically set the timestamps on the node.
    $edit = array(
      'uid' => $this->web_user->uid,
      'type' => 'article',
      'title' => $this->randomName(8),
      'created' => 280299600, // Sun, 19 Nov 1978 05:00:00 GMT
      'changed' => 979534800, // Drupal 1.0 release.
    );

    node_save((object) $edit);
    $node = $this->drupalGetNodeByTitle($edit['title']);
    $this->assertEqual($node->created, 280299600, 'Creating a node uses user-set "created" timestamp.');
    $this->assertNotEqual($node->changed, 979534800, 'Creating a node doesn\'t use user-set "changed" timestamp.');

    // Update the timestamps.
    $node->created = 979534800;
    $node->changed = 280299600;

    node_save($node);
    $node = $this->drupalGetNodeByTitle($edit['title'], TRUE);
    $this->assertEqual($node->created, 979534800, 'Updating a node uses user-set "created" timestamp.');
    $this->assertNotEqual($node->changed, 280299600, 'Updating a node doesn\'t use user-set "changed" timestamp.');
  }

  /**
   * Tests determing changes in hook_node_presave() and verifies the static node
   * load cache is cleared upon save.
   */
  function testDeterminingChanges() {
    // Initial creation.
    $node = (object) array(
      'uid' => $this->web_user->uid,
      'type' => 'article',
      'title' => 'test_changes',
    );
    node_save($node);

    // Update the node without applying changes.
    node_save($node);
    $this->assertEqual($node->title, 'test_changes', 'No changes have been determined.');

    // Apply changes.
    $node->title = 'updated';
    node_save($node);

    // The hook implementations node_test_node_presave() and
    // node_test_node_update() determine changes and change the title.
    $this->assertEqual($node->title, 'updated_presave_update', 'Changes have been determined.');

    // Test the static node load cache to be cleared.
    $node = node_load($node->nid);
    $this->assertEqual($node->title, 'updated_presave', 'Static cache has been cleared.');
  }

  /**
   * Tests saving a node on node insert.
   *
   * This test ensures that a node has been fully saved when hook_node_insert()
   * is invoked, so that the node can be saved again in a hook implementation
   * without errors.
   *
   * @see node_test_node_insert()
   */
  function testNodeSaveOnInsert() {
    // node_test_node_insert() triggers a save on insert if the title equals
    // 'new'.
    $node = $this->drupalCreateNode(array('title' => 'new'));
    $this->assertEqual($node->title, 'Node ' . $node->nid, 'Node saved on node insert.');
  }
}

/**
 * Tests related to node types.
 */
class NodeTypeTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node types',
      'description' => 'Ensures that node type functions work correctly.',
      'group' => 'Node',
    );
  }

  /**
   * Ensures that node type functions (node_type_get_*) work correctly.
   *
   * Load available node types and validate the returned data.
   */
  function testNodeTypeGetFunctions() {
    $node_types = node_type_get_types();
    $node_names = node_type_get_names();

    $this->assertTrue(isset($node_types['article']), 'Node type article is available.');
    $this->assertTrue(isset($node_types['page']), 'Node type basic page is available.');

    $this->assertEqual($node_types['article']->name, $node_names['article'], 'Correct node type base has been returned.');

    $this->assertEqual($node_types['article'], node_type_get_type('article'), 'Correct node type has been returned.');
    $this->assertEqual($node_types['article']->name, node_type_get_name('article'), 'Correct node type name has been returned.');
    $this->assertEqual($node_types['page']->base, node_type_get_base('page'), 'Correct node type base has been returned.');
  }

  /**
   * Tests creating a content type programmatically and via a form.
   */
  function testNodeTypeCreation() {
    // Create a content type programmaticaly.
    $type = $this->drupalCreateContentType();

    $type_exists = db_query('SELECT 1 FROM {node_type} WHERE type = :type', array(':type' => $type->type))->fetchField();
    $this->assertTrue($type_exists, 'The new content type has been created in the database.');

    // Login a test user.
    $web_user = $this->drupalCreateUser(array('create ' . $type->name . ' content'));
    $this->drupalLogin($web_user);

    $this->drupalGet('node/add/' . str_replace('_', '-', $type->name));
    $this->assertResponse(200, 'The new content type can be accessed at node/add.');

    // Create a content type via the user interface.
    $web_user = $this->drupalCreateUser(array('bypass node access', 'administer content types'));
    $this->drupalLogin($web_user);
    $edit = array(
      'name' => 'foo',
      'title_label' => 'title for foo',
      'type' => 'foo',
    );
    $this->drupalPost('admin/structure/types/add', $edit, t('Save content type'));
    $type_exists = db_query('SELECT 1 FROM {node_type} WHERE type = :type', array(':type' => 'foo'))->fetchField();
    $this->assertTrue($type_exists, 'The new content type has been created in the database.');
  }

  /**
   * Tests editing a node type using the UI.
   */
  function testNodeTypeEditing() {
    $web_user = $this->drupalCreateUser(array('bypass node access', 'administer content types', 'administer fields'));
    $this->drupalLogin($web_user);

    $instance = field_info_instance('node', 'body', 'page');
    $this->assertEqual($instance['label'], 'Body', 'Body field was found.');

    // Verify that title and body fields are displayed.
    $this->drupalGet('node/add/page');
    $this->assertRaw('Title', 'Title field was found.');
    $this->assertRaw('Body', 'Body field was found.');

    // Rename the title field.
    $edit = array(
      'title_label' => 'Foo',
    );
    $this->drupalPost('admin/structure/types/manage/page', $edit, t('Save content type'));
    // Refresh the field information for the rest of the test.
    field_info_cache_clear();

    $this->drupalGet('node/add/page');
    $this->assertRaw('Foo', 'New title label was displayed.');
    $this->assertNoRaw('Title', 'Old title label was not displayed.');

    // Change the name, machine name and description.
    $edit = array(
      'name' => 'Bar',
      'type' => 'bar',
      'description' => 'Lorem ipsum.',
    );
    $this->drupalPost('admin/structure/types/manage/page', $edit, t('Save content type'));
    field_info_cache_clear();

    $this->drupalGet('node/add');
    $this->assertRaw('Bar', 'New name was displayed.');
    $this->assertRaw('Lorem ipsum', 'New description was displayed.');
    $this->clickLink('Bar');
    $this->assertEqual(url('node/add/bar', array('absolute' => TRUE)), $this->getUrl(), 'New machine name was used in URL.');
    $this->assertRaw('Foo', 'Title field was found.');
    $this->assertRaw('Body', 'Body field was found.');

    // Remove the body field.
    $this->drupalPost('admin/structure/types/manage/bar/fields/body/delete', NULL, t('Delete'));
    // Resave the settings for this type.
    $this->drupalPost('admin/structure/types/manage/bar', array(), t('Save content type'));
    // Check that the body field doesn't exist.
    $this->drupalGet('node/add/bar');
    $this->assertNoRaw('Body', 'Body field was not found.');
  }

  /**
   * Tests that node_types_rebuild() correctly handles the 'disabled' flag.
   */
  function testNodeTypeStatus() {
    // Enable all core node modules, and all types should be active.
    module_enable(array('blog', 'book', 'poll'), FALSE);
    node_types_rebuild();
    $types = node_type_get_types();
    foreach (array('blog', 'book', 'poll', 'article', 'page') as $type) {
      $this->assertTrue(isset($types[$type]), format_string('%type is found in node types.', array('%type' => $type)));
      $this->assertTrue(isset($types[$type]->disabled) && empty($types[$type]->disabled), format_string('%type type is enabled.', array('%type' => $type)));
    }

    // Disable poll module and the respective type should be marked as disabled.
    module_disable(array('poll'), FALSE);
    node_types_rebuild();
    $types = node_type_get_types();
    $this->assertTrue(!empty($types['poll']->disabled), "Poll module's node type disabled.");
    $this->assertTrue(isset($types['blog']) && empty($types['blog']->disabled), "Blog module's node type still active.");

    // Disable blog module and the respective type should be marked as disabled.
    module_disable(array('blog'), FALSE);
    node_types_rebuild();
    $types = node_type_get_types();
    $this->assertTrue(!empty($types['blog']->disabled), "Blog module's node type disabled.");
    $this->assertTrue(!empty($types['poll']->disabled), "Poll module's node type still disabled.");

    // Disable book module and the respective type should still be active, since
    // it is not provided by hook_node_info().
    module_disable(array('book'), FALSE);
    node_types_rebuild();
    $types = node_type_get_types();
    $this->assertTrue(isset($types['book']) && empty($types['book']->disabled), "Book module's node type still active.");
    $this->assertTrue(!empty($types['blog']->disabled), "Blog module's node type still disabled.");
    $this->assertTrue(!empty($types['poll']->disabled), "Poll module's node type still disabled.");
    $this->assertTrue(isset($types['article']) && empty($types['article']->disabled), "Article node type still active.");
    $this->assertTrue(isset($types['page']) && empty($types['page']->disabled), "Basic page node type still active.");

    // Re-enable the modules and verify that the types are active again.
    module_enable(array('blog', 'book', 'poll'), FALSE);
    node_types_rebuild();
    $types = node_type_get_types();
    foreach (array('blog', 'book', 'poll', 'article', 'page') as $type) {
      $this->assertTrue(isset($types[$type]), format_string('%type is found in node types.', array('%type' => $type)));
      $this->assertTrue(isset($types[$type]->disabled) && empty($types[$type]->disabled), format_string('%type type is enabled.', array('%type' => $type)));
    }
  }

  /**
   * Tests deleting a content type that still has content.
   */
  function testNodeTypeDeletion() {
    // Create a content type programmatically.
    $type = $this->drupalCreateContentType();

    // Log in a test user.
    $web_user = $this->drupalCreateUser(array(
      'bypass node access',
      'administer content types',
    ));
    $this->drupalLogin($web_user);

    // Add a new node of this type.
    $node = $this->drupalCreateNode(array('type' => $type->type));
    // Attempt to delete the content type, which should not be allowed.
    $this->drupalGet('admin/structure/types/manage/' . $type->name . '/delete');
    $this->assertRaw(
      t(
        '%type is used by 1 piece of content on your site. You cannot remove this content type until you have removed all of the %type content.',
        array('%type' => $type->name)
      ),
      'The content type will not be deleted until all nodes of that type are removed.'
    );
    $this->assertNoText(t('This action cannot be undone.'), 'The node type deletion confirmation form is not available.');

    // Delete the node.
    node_delete($node->nid);
    // Attempt to delete the content type, which should now be allowed.
    $this->drupalGet('admin/structure/types/manage/' . $type->name . '/delete');
    $this->assertRaw(
      t(
        'Are you sure you want to delete the content type %type?',
        array('%type' => $type->name)
      ),
      'The content type is available for deletion.'
    );
    $this->assertText(t('This action cannot be undone.'), 'The node type deletion confirmation form is available.');
  }

}

/**
 * Test node type customizations persistence.
 */
class NodeTypePersistenceTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node type persist',
      'description' => 'Ensures that node type customization survives module enabling and disabling.',
      'group' => 'Node',
    );
  }

  /**
   * Tests that node type customizations persist through disable and uninstall.
   */
  function testNodeTypeCustomizationPersistence() {
    $web_user = $this->drupalCreateUser(array('bypass node access', 'administer content types', 'administer modules'));
    $this->drupalLogin($web_user);
    $poll_key = 'modules[Core][poll][enable]';
    $poll_enable = array($poll_key => "1");
    $poll_disable = array($poll_key => FALSE);

    // Enable poll and verify that the node type is in the DB and is not
    // disabled.
    $this->drupalPost('admin/modules', $poll_enable, t('Save configuration'));
    $disabled = db_query('SELECT disabled FROM {node_type} WHERE type = :type', array(':type' => 'poll'))->fetchField();
    $this->assertNotIdentical($disabled, FALSE, 'Poll node type found in the database');
    $this->assertEqual($disabled, 0, 'Poll node type is not disabled');

    // Check that poll node type (uncustomized) shows up.
    $this->drupalGet('node/add');
    $this->assertText('poll', 'poll type is found on node/add');

    // Customize poll description.
    $description = $this->randomName();
    $edit = array('description' => $description);
    $this->drupalPost('admin/structure/types/manage/poll', $edit, t('Save content type'));

    // Check that poll node type customization shows up.
    $this->drupalGet('node/add');
    $this->assertText($description, 'Customized description found');

    // Disable poll and check that the node type gets disabled.
    $this->drupalPost('admin/modules', $poll_disable, t('Save configuration'));
    $disabled = db_query('SELECT disabled FROM {node_type} WHERE type = :type', array(':type' => 'poll'))->fetchField();
    $this->assertEqual($disabled, 1, 'Poll node type is disabled');
    $this->drupalGet('node/add');
    $this->assertNoText('poll', 'poll type is not found on node/add');

    // Reenable poll and check that the customization survived the module
    // disable.
    $this->drupalPost('admin/modules', $poll_enable, t('Save configuration'));
    $disabled = db_query('SELECT disabled FROM {node_type} WHERE type = :type', array(':type' => 'poll'))->fetchField();
    $this->assertNotIdentical($disabled, FALSE, 'Poll node type found in the database');
    $this->assertEqual($disabled, 0, 'Poll node type is not disabled');
    $this->drupalGet('node/add');
    $this->assertText($description, 'Customized description found');

    // Disable and uninstall poll.
    $this->drupalPost('admin/modules', $poll_disable, t('Save configuration'));
    $edit = array('uninstall[poll]' => 'poll');
    $this->drupalPost('admin/modules/uninstall', $edit, t('Uninstall'));
    $this->drupalPost(NULL, array(), t('Uninstall'));
    $disabled = db_query('SELECT disabled FROM {node_type} WHERE type = :type', array(':type' => 'poll'))->fetchField();
    $this->assertTrue($disabled, 'Poll node type is in the database and is disabled');
    $this->drupalGet('node/add');
    $this->assertNoText('poll', 'poll type is no longer found on node/add');

    // Reenable poll and check that the customization survived the module
    // uninstall.
    $this->drupalPost('admin/modules', $poll_enable, t('Save configuration'));
    $this->drupalGet('node/add');
    $this->assertText($description, 'Customized description is found even after uninstall and reenable.');
  }
}

/**
 * Verifies the rebuild functionality for the node_access table.
 */
class NodeAccessRebuildTestCase extends DrupalWebTestCase {
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Node access rebuild',
      'description' => 'Ensures that node access rebuild functions work correctly.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    $web_user = $this->drupalCreateUser(array('administer site configuration', 'access administration pages', 'access site reports'));
    $this->drupalLogin($web_user);
    $this->web_user = $web_user;
  }

  /**
   * Tests rebuilding the node access permissions table.
   */
  function testNodeAccessRebuild() {
    $this->drupalGet('admin/reports/status');
    $this->clickLink(t('Rebuild permissions'));
    $this->drupalPost(NULL, array(), t('Rebuild permissions'));
    $this->assertText(t('Content permissions have been rebuilt.'));
  }
}

/**
 * Tests node administration page functionality.
 */
class NodeAdminTestCase extends DrupalWebTestCase {
  protected $admin_user;
  protected $base_user_1;
  protected $base_user_2;
  protected $base_user_3;

  public static function getInfo() {
    return array(
      'name' => 'Node administration',
      'description' => 'Test node administration page functionality.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    // Remove the "view own unpublished content" permission which is set
    // by default for authenticated users so we can test this permission
    // correctly.
    user_role_revoke_permissions(DRUPAL_AUTHENTICATED_RID, array('view own unpublished content'));

    $this->admin_user = $this->drupalCreateUser(array('access administration pages', 'access content overview', 'administer nodes', 'bypass node access'));
    $this->base_user_1 = $this->drupalCreateUser(array('access content overview'));
    $this->base_user_2 = $this->drupalCreateUser(array('access content overview', 'view own unpublished content'));
    $this->base_user_3 = $this->drupalCreateUser(array('access content overview', 'bypass node access'));
  }

  /**
   * Tests that the table sorting works on the content admin pages.
   */
  function testContentAdminSort() {
    $this->drupalLogin($this->admin_user);
    foreach (array('dd', 'aa', 'DD', 'bb', 'cc', 'CC', 'AA', 'BB') as $prefix) {
      $this->drupalCreateNode(array('title' => $prefix . $this->randomName(6)));
    }

    // Test that the default sort by node.changed DESC actually fires properly.
    $nodes_query = db_select('node', 'n')
      ->fields('n', array('nid'))
      ->orderBy('changed', 'DESC')
      ->execute()
      ->fetchCol();

    $nodes_form = array();
    $this->drupalGet('admin/content');
    foreach ($this->xpath('//table/tbody/tr/td/div/input/@value') as $input) {
      $nodes_form[] = $input;
    }
    $this->assertEqual($nodes_query, $nodes_form, 'Nodes are sorted in the form according to the default query.');

    // Compare the rendered HTML node list to a query for the nodes ordered by
    // title to account for possible database-dependent sort order.
    $nodes_query = db_select('node', 'n')
      ->fields('n', array('nid'))
      ->orderBy('title')
      ->execute()
      ->fetchCol();

    $nodes_form = array();
    $this->drupalGet('admin/content', array('query' => array('sort' => 'asc', 'order' => 'Title')));
    foreach ($this->xpath('//table/tbody/tr/td/div/input/@value') as $input) {
      $nodes_form[] = $input;
    }
    $this->assertEqual($nodes_query, $nodes_form, 'Nodes are sorted in the form the same as they are in the query.');
  }

  /**
   * Tests content overview with different user permissions.
   *
   * Taxonomy filters are tested separately.
   *
   * @see TaxonomyNodeFilterTestCase
   */
  function testContentAdminPages() {
    $this->drupalLogin($this->admin_user);

    $nodes['published_page'] = $this->drupalCreateNode(array('type' => 'page'));
    $nodes['published_article'] = $this->drupalCreateNode(array('type' => 'article'));
    $nodes['unpublished_page_1'] = $this->drupalCreateNode(array('type' => 'page', 'uid' => $this->base_user_1->uid, 'status' => 0));
    $nodes['unpublished_page_2'] = $this->drupalCreateNode(array('type' => 'page', 'uid' => $this->base_user_2->uid, 'status' => 0));

    // Verify view, edit, and delete links for any content.
    $this->drupalGet('admin/content');
    $this->assertResponse(200);
    foreach ($nodes as $node) {
      $this->assertLinkByHref('node/' . $node->nid);
      $this->assertLinkByHref('node/' . $node->nid . '/edit');
      $this->assertLinkByHref('node/' . $node->nid . '/delete');
      // Verify tableselect.
      $this->assertFieldByName('nodes[' . $node->nid . ']', '', 'Tableselect found.');
    }

    // Verify filtering by publishing status.
    $edit = array(
      'status' => 'status-1',
    );
    $this->drupalPost(NULL, $edit, t('Filter'));

    $this->assertRaw(t('where %property is %value', array('%property' => t('status'), '%value' => 'published')), 'Content list is filtered by status.');

    $this->assertLinkByHref('node/' . $nodes['published_page']->nid . '/edit');
    $this->assertLinkByHref('node/' . $nodes['published_article']->nid . '/edit');
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_1']->nid . '/edit');

    // Verify filtering by status and content type.
    $edit = array(
      'type' => 'page',
    );
    $this->drupalPost(NULL, $edit, t('Refine'));

    $this->assertRaw(t('where %property is %value', array('%property' => t('status'), '%value' => 'published')), 'Content list is filtered by status.');
    $this->assertRaw(t('and where %property is %value', array('%property' => t('type'), '%value' => 'Basic page')), 'Content list is filtered by content type.');

    $this->assertLinkByHref('node/' . $nodes['published_page']->nid . '/edit');
    $this->assertNoLinkByHref('node/' . $nodes['published_article']->nid . '/edit');

    // Verify no operation links are displayed for regular users.
    $this->drupalLogout();
    $this->drupalLogin($this->base_user_1);
    $this->drupalGet('admin/content');
    $this->assertResponse(200);
    $this->assertLinkByHref('node/' . $nodes['published_page']->nid);
    $this->assertLinkByHref('node/' . $nodes['published_article']->nid);
    $this->assertNoLinkByHref('node/' . $nodes['published_page']->nid . '/edit');
    $this->assertNoLinkByHref('node/' . $nodes['published_page']->nid . '/delete');
    $this->assertNoLinkByHref('node/' . $nodes['published_article']->nid . '/edit');
    $this->assertNoLinkByHref('node/' . $nodes['published_article']->nid . '/delete');

    // Verify no unpublished content is displayed without permission.
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_1']->nid);
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_1']->nid . '/edit');
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_1']->nid . '/delete');

    // Verify no tableselect.
    $this->assertNoFieldByName('nodes[' . $nodes['published_page']->nid . ']', '', 'No tableselect found.');

    // Verify unpublished content is displayed with permission.
    $this->drupalLogout();
    $this->drupalLogin($this->base_user_2);
    $this->drupalGet('admin/content');
    $this->assertResponse(200);
    $this->assertLinkByHref('node/' . $nodes['unpublished_page_2']->nid);
    // Verify no operation links are displayed.
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_2']->nid . '/edit');
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_2']->nid . '/delete');

    // Verify user cannot see unpublished content of other users.
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_1']->nid);
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_1']->nid . '/edit');
    $this->assertNoLinkByHref('node/' . $nodes['unpublished_page_1']->nid . '/delete');

    // Verify no tableselect.
    $this->assertNoFieldByName('nodes[' . $nodes['unpublished_page_2']->nid . ']', '', 'No tableselect found.');

    // Verify node access can be bypassed.
    $this->drupalLogout();
    $this->drupalLogin($this->base_user_3);
    $this->drupalGet('admin/content');
    $this->assertResponse(200);
    foreach ($nodes as $node) {
      $this->assertLinkByHref('node/' . $node->nid);
      $this->assertLinkByHref('node/' . $node->nid . '/edit');
      $this->assertLinkByHref('node/' . $node->nid . '/delete');
    }
  }
}

/**
 * Tests node title functionality.
 */
class NodeTitleTestCase extends DrupalWebTestCase {

  /**
   * A user with permission to create and edit content and to administer nodes.
   *
   * @var object
   */
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Node title',
      'description' => 'Test node title.',
      'group' => 'Node'
    );
  }

  function setUp() {
    parent::setUp();
    $this->admin_user = $this->drupalCreateUser(array('administer nodes', 'create article content', 'create page content'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   *  Creates one node and tests if the node title has the correct value.
   */
  function testNodeTitle() {
    // Create "Basic page" content with title.
    // Add the node to the frontpage so we can test if teaser links are clickable.
    $settings = array(
      'title' => $this->randomName(8),
      'promote' => 1,
    );
    $node = $this->drupalCreateNode($settings);

    // Test <title> tag.
    $this->drupalGet("node/$node->nid");
    $xpath = '//title';
    $this->assertEqual(current($this->xpath($xpath)), $node->title .' | Drupal', 'Page title is equal to node title.', 'Node');

    // Test breadcrumb in comment preview.
    $this->drupalGet("comment/reply/$node->nid");
    $xpath = '//div[@class="breadcrumb"]/a[last()]';
    $this->assertEqual(current($this->xpath($xpath)), $node->title, 'Node breadcrumb is equal to node title.', 'Node');

    // Test node title in comment preview.
    $this->assertEqual(current($this->xpath('//div[@id=:id]/h2/a', array(':id' => 'node-' . $node->nid))), $node->title, 'Node preview title is equal to node title.', 'Node');

    // Test node title is clickable on teaser list (/node).
    $this->drupalGet('node');
    $this->clickLink($node->title);
  }
}

/**
 * Test the node_feed() functionality.
 */
class NodeFeedTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node feed',
      'description' => 'Ensures that node_feed() functions correctly.',
      'group' => 'Node',
   );
  }

  /**
   * Ensures that node_feed() accepts and prints extra channel elements.
   */
  function testNodeFeedExtraChannelElements() {
    ob_start();
    node_feed(array(), array('copyright' => 'Drupal is a registered trademark of Dries Buytaert.'));
    $output = ob_get_clean();

    $this->assertTrue(strpos($output, '<copyright>Drupal is a registered trademark of Dries Buytaert.</copyright>') !== FALSE);
  }
}

/**
 * Functional tests for the node module blocks.
 */
class NodeBlockFunctionalTest extends DrupalWebTestCase {
  protected $admin_user;
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Node blocks',
      'description' => 'Test node block functionality.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp('node', 'block');

    // Create users and test node.
    $this->admin_user = $this->drupalCreateUser(array('administer content types', 'administer nodes', 'administer blocks'));
    $this->web_user = $this->drupalCreateUser(array('access content', 'create article content'));
  }

  /**
   * Tests the recent comments block.
   */
  function testRecentNodeBlock() {
    $this->drupalLogin($this->admin_user);

    // Disallow anonymous users to view content.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access content' => FALSE,
    ));

    // Set the block to a region to confirm block is available.
    $edit = array(
      'blocks[node_recent][region]' => 'sidebar_first',
    );
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertText(t('The block settings have been updated.'), 'Block saved to first sidebar region.');

    // Set block title and variables.
    $block = array(
      'title' => $this->randomName(),
      'node_recent_block_count' => 2,
    );
    $this->drupalPost('admin/structure/block/manage/node/recent/configure', $block, t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block saved.');

    // Test that block is not visible without nodes
    $this->drupalGet('');
    $this->assertText(t('No content available.'), 'Block with "No content available." found.');

    // Add some test nodes.
    $default_settings = array('uid' => $this->web_user->uid, 'type' => 'article');
    $node1 = $this->drupalCreateNode($default_settings);
    $node2 = $this->drupalCreateNode($default_settings);
    $node3 = $this->drupalCreateNode($default_settings);

    // Change the changed time for node so that we can test ordering.
    db_update('node')
      ->fields(array(
        'changed' => $node1->changed + 100,
      ))
      ->condition('nid', $node2->nid)
      ->execute();
    db_update('node')
      ->fields(array(
        'changed' => $node1->changed + 200,
      ))
      ->condition('nid', $node3->nid)
      ->execute();

    // Test that a user without the 'access content' permission cannot
    // see the block.
    $this->drupalLogout();
    $this->drupalGet('');
    $this->assertNoText($block['title'], 'Block was not found.');

    // Test that only the 2 latest nodes are shown.
    $this->drupalLogin($this->web_user);
    $this->assertNoText($node1->title, 'Node not found in block.');
    $this->assertText($node2->title, 'Node found in block.');
    $this->assertText($node3->title, 'Node found in block.');

    // Check to make sure nodes are in the right order.
    $this->assertTrue($this->xpath('//div[@id="block-node-recent"]/div/table/tbody/tr[position() = 1]/td/div/a[text() = "' . $node3->title . '"]'), 'Nodes were ordered correctly in block.');

    // Set the number of recent nodes to show to 10.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);
    $block = array(
      'node_recent_block_count' => 10,
    );
    $this->drupalPost('admin/structure/block/manage/node/recent/configure', $block, t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block saved.');

    // Post an additional node.
    $node4 = $this->drupalCreateNode($default_settings);

    // Test that all four nodes are shown.
    $this->drupalGet('');
    $this->assertText($node1->title, 'Node found in block.');
    $this->assertText($node2->title, 'Node found in block.');
    $this->assertText($node3->title, 'Node found in block.');
    $this->assertText($node4->title, 'Node found in block.');

    // Create the custom block.
    $custom_block = array();
    $custom_block['info'] = $this->randomName();
    $custom_block['title'] = $this->randomName();
    $custom_block['types[article]'] = TRUE;
    $custom_block['body[value]'] = $this->randomName(32);
    $custom_block['regions[' . variable_get('theme_default', 'bartik') . ']'] = 'content';
    if ($admin_theme = variable_get('admin_theme')) {
      $custom_block['regions[' . $admin_theme . ']'] = 'content';
    }
    $this->drupalPost('admin/structure/block/add', $custom_block, t('Save block'));

    $bid = db_query("SELECT bid FROM {block_custom} WHERE info = :info", array(':info' => $custom_block['info']))->fetchField();
    $this->assertTrue($bid, 'Custom block with visibility rule was created.');

    // Verify visibility rules.
    $this->drupalGet('');
    $this->assertNoText($custom_block['title'], 'Block was displayed on the front page.');
    $this->drupalGet('node/add/article');
    $this->assertText($custom_block['title'], 'Block was displayed on the node/add/article page.');
    $this->drupalGet('node/' . $node1->nid);
    $this->assertText($custom_block['title'], 'Block was displayed on the node/N.');

    // Delete the created custom block & verify that it's been deleted.
    $this->drupalPost('admin/structure/block/manage/block/' . $bid . '/delete', array(), t('Delete'));
    $bid = db_query("SELECT 1 FROM {block_node_type} WHERE module = 'block' AND delta = :delta", array(':delta' => $bid))->fetchField();
    $this->assertFalse($bid, 'Custom block was deleted.');
  }
}
/**
 * Tests basic options of multi-step node forms.
 */
class MultiStepNodeFormBasicOptionsTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Multistep node form basic options',
      'description' => 'Test the persistence of basic options through multiple steps.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp('poll');
    $web_user = $this->drupalCreateUser(array('administer nodes', 'create poll content'));
    $this->drupalLogin($web_user);
  }

  /**
   * Tests changing the default values of basic options to ensure they persist.
   */
  function testMultiStepNodeFormBasicOptions() {
    $edit = array(
      'title' => 'a',
      'status' => FALSE,
      'promote' => FALSE,
      'sticky' => 1,
      'choice[new:0][chtext]' => 'a',
      'choice[new:1][chtext]' => 'a',
    );
    $this->drupalPost('node/add/poll', $edit, t('More choices'));
    $this->assertNoFieldChecked('edit-status', 'status stayed unchecked');
    $this->assertNoFieldChecked('edit-promote', 'promote stayed unchecked');
    $this->assertFieldChecked('edit-sticky', 'sticky stayed checked');
  }
}

/**
 * Test to ensure that a node's content is always rebuilt.
 */
class NodeBuildContent extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Rebuild content',
      'description' => 'Test the rebuilding of content for different build modes.',
      'group' => 'Node',
    );
  }

 /**
  * Ensures that content array is rebuilt on every call to node_build_content().
  */
  function testNodeRebuildContent() {
    $node = $this->drupalCreateNode();

    // Set a property in the content array so we can test for its existence later on.
    $node->content['test_content_property'] = array('#value' => $this->randomString());
    $content = node_build_content($node);

    // If the property doesn't exist it means the node->content was rebuilt.
    $this->assertFalse(isset($content['test_content_property']), 'Node content was emptied prior to being built.');
  }
}

/**
 * Tests node_query_node_access_alter().
 */
class NodeQueryAlter extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Node query alter',
      'description' => 'Test that node access queries are properly altered by the node module.',
      'group' => 'Node',
    );
  }

  /**
   * User with permission to view content.
   *
   * @var object
   */
  protected $accessUser;

  /**
   * User without permission to view content.
   *
   * @var object
   */
  protected $noAccessUser;

  /**
   * User without permission to view content.
   *
   * @var object
   */
  protected $noAccessUser2;

  function setUp() {
    parent::setUp('node_access_test');
    node_access_rebuild();

    // Create some content.
    $this->drupalCreateNode();
    $this->drupalCreateNode();
    $this->drupalCreateNode();
    $this->drupalCreateNode();

    // Create user with simple node access permission. The 'node test view'
    // permission is implemented and granted by the node_access_test module.
    $this->accessUser = $this->drupalCreateUser(array('access content overview', 'access content', 'node test view'));
    $this->noAccessUser = $this->drupalCreateUser(array('access content overview', 'access content'));
    $this->noAccessUser2 = $this->drupalCreateUser(array('access content overview', 'access content'));
  }

  /**
   * Tests that node access permissions are followed.
   */
  function testNodeQueryAlterWithUI() {
    // Verify that a user with access permission can see at least one node.
    $this->drupalLogin($this->accessUser);
    $this->drupalGet('node_access_test_page');
    $this->assertText('Yes, 4 nodes', "4 nodes were found for access user");
    $this->assertNoText('Exception', "No database exception");

    // Test the content overview page.
    $this->drupalGet('admin/content');
    $table_rows = $this->xpath('//tbody/tr');
    $this->assertEqual(4, count($table_rows), "4 nodes were found for access user");

    // Verify that a user with no access permission cannot see nodes.
    $this->drupalLogin($this->noAccessUser);
    $this->drupalGet('node_access_test_page');
    $this->assertText('No nodes', "No nodes were found for no access user");
    $this->assertNoText('Exception', "No database exception");

    $this->drupalGet('admin/content');
    $this->assertText(t('No content available.'));
  }

  /**
   * Tests 'node_access' query alter, for user with access.
   *
   * Verifies that a non-standard table alias can be used, and that a user with
   * node access can view the nodes.
   */
  function testNodeQueryAlterLowLevelWithAccess() {
    // User with access should be able to view 4 nodes.
    try {
      $query = db_select('node', 'mytab')
        ->fields('mytab');
      $query->addTag('node_access');
      $query->addMetaData('op', 'view');
      $query->addMetaData('account', $this->accessUser);

      $result = $query->execute()->fetchAll();
      $this->assertEqual(count($result), 4, 'User with access can see correct nodes');
    }
    catch (Exception $e) {
      $this->fail(t('Altered query is malformed'));
    }
  }

  /**
   * Tests 'node_access' query alter, for user without access.
   *
   * Verifies that a non-standard table alias can be used, and that a user
   * without node access cannot view the nodes.
   */
  function testNodeQueryAlterLowLevelNoAccess() {
    // User without access should be able to view 0 nodes.
    try {
      $query = db_select('node', 'mytab')
        ->fields('mytab');
      $query->addTag('node_access');
      $query->addMetaData('op', 'view');
      $query->addMetaData('account', $this->noAccessUser);

      $result = $query->execute()->fetchAll();
      $this->assertEqual(count($result), 0, 'User with no access cannot see nodes');
    }
    catch (Exception $e) {
      $this->fail(t('Altered query is malformed'));
    }
  }

  /**
   * Tests 'node_access' query alter, for edit access.
   *
   * Verifies that a non-standard table alias can be used, and that a user with
   * view-only node access cannot edit the nodes.
   */
  function testNodeQueryAlterLowLevelEditAccess() {
    // User with view-only access should not be able to edit nodes.
    try {
      $query = db_select('node', 'mytab')
        ->fields('mytab');
      $query->addTag('node_access');
      $query->addMetaData('op', 'update');
      $query->addMetaData('account', $this->accessUser);

      $result = $query->execute()->fetchAll();
      $this->assertEqual(count($result), 0, 'User with view-only access cannot edit nodes');
    }
    catch (Exception $e) {
      $this->fail($e->getMessage());
      $this->fail((string) $query);
      $this->fail(t('Altered query is malformed'));
    }
  }

  /**
   * Tests 'node_access' query alter override.
   *
   * Verifies that node_access_view_all_nodes() is called from
   * node_query_node_access_alter(). We do this by checking that a user who
   * normally would not have view privileges is able to view the nodes when we
   * add a record to {node_access} paired with a corresponding privilege in
   * hook_node_grants().
   */
  function testNodeQueryAlterOverride() {
    $record = array(
      'nid' => 0,
      'gid' => 0,
      'realm' => 'node_access_all',
      'grant_view' => 1,
      'grant_update' => 0,
      'grant_delete' => 0,
    );
    drupal_write_record('node_access', $record);

    // Test that the noAccessUser still doesn't have the 'view'
    // privilege after adding the node_access record.
    drupal_static_reset('node_access_view_all_nodes');
    try {
      $query = db_select('node', 'mytab')
        ->fields('mytab');
      $query->addTag('node_access');
      $query->addMetaData('op', 'view');
      $query->addMetaData('account', $this->noAccessUser);

      $result = $query->execute()->fetchAll();
      $this->assertEqual(count($result), 0, 'User view privileges are not overridden');
    }
    catch (Exception $e) {
      $this->fail(t('Altered query is malformed'));
    }

    // Have node_test_node_grants return a node_access_all privilege,
    // to grant the noAccessUser 'view' access.  To verify that
    // node_access_view_all_nodes is properly checking the specified
    // $account instead of the global $user, we will log in as
    // noAccessUser2.
    $this->drupalLogin($this->noAccessUser2);
    variable_set('node_test_node_access_all_uid', $this->noAccessUser->uid);
    drupal_static_reset('node_access_view_all_nodes');
    try {
      $query = db_select('node', 'mytab')
        ->fields('mytab');
      $query->addTag('node_access');
      $query->addMetaData('op', 'view');
      $query->addMetaData('account', $this->noAccessUser);

      $result = $query->execute()->fetchAll();
      $this->assertEqual(count($result), 4, 'User view privileges are overridden');
    }
    catch (Exception $e) {
      $this->fail(t('Altered query is malformed'));
    }
    variable_del('node_test_node_access_all_uid');
  }
}


/**
 * Tests node_query_entity_field_access_alter().
 */
class NodeEntityFieldQueryAlter extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Node entity query alter',
      'description' => 'Test that node access entity queries are properly altered by the node module.',
      'group' => 'Node',
    );
  }

  /**
   * User with permission to view content.
   *
   * @var object
   */
  protected $accessUser;

  /**
   * User without permission to view content.
   *
   * @var object
   */
  protected $noAccessUser;

  function setUp() {
    parent::setUp('node_access_test');
    node_access_rebuild();

    // Creating 4 nodes with an entity field so we can test that sort of query
    // alter. All field values starts with 'A' so we can identify and fetch them
    // in the node_access_test module.
    $settings = array('language' => LANGUAGE_NONE);
    for ($i = 0; $i < 4; $i++) {
      $body = array(
        'value' => 'A' . $this->randomName(32),
        'format' => filter_default_format(),
      );
      $settings['body'][LANGUAGE_NONE][0] = $body;
      $this->drupalCreateNode($settings);
    }

    // Create user with simple node access permission. The 'node test view'
    // permission is implemented and granted by the node_access_test module.
    $this->accessUser = $this->drupalCreateUser(array('access content', 'node test view'));
    $this->noAccessUser = $this->drupalCreateUser(array('access content'));
  }

  /**
   * Tests that node access permissions are followed.
   */
  function testNodeQueryAlterWithUI() {
    // Verify that a user with access permission can see at least one node.
    $this->drupalLogin($this->accessUser);
    $this->drupalGet('node_access_entity_test_page');
    $this->assertText('Yes, 4 nodes', "4 nodes were found for access user");
    $this->assertNoText('Exception', "No database exception");

    // Verify that a user with no access permission cannot see nodes.
    $this->drupalLogin($this->noAccessUser);
    $this->drupalGet('node_access_entity_test_page');
    $this->assertText('No nodes', "No nodes were found for no access user");
    $this->assertNoText('Exception', "No database exception");
  }
}

/**
 * Test node token replacement in strings.
 */
class NodeTokenReplaceTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Node token replacement',
      'description' => 'Generates text using placeholders for dummy content to check node token replacement.',
      'group' => 'Node',
    );
  }

  /**
   * Creates a node, then tests the tokens generated from it.
   */
  function testNodeTokenReplacement() {
    global $language;
    $url_options = array(
      'absolute' => TRUE,
      'language' => $language,
    );

    // Create a user and a node.
    $account = $this->drupalCreateUser();
    $settings = array(
      'type' => 'article',
      'uid' => $account->uid,
      'title' => '<blink>Blinking Text</blink>',
      'body' => array(LANGUAGE_NONE => array(array('value' => $this->randomName(32), 'summary' => $this->randomName(16)))),
    );
    $node = $this->drupalCreateNode($settings);

    // Load node so that the body and summary fields are structured properly.
    $node = node_load($node->nid);
    $instance = field_info_instance('node', 'body', $node->type);

    // Generate and test sanitized tokens.
    $tests = array();
    $langcode = entity_language('node', $node);
    $tests['[node:nid]'] = $node->nid;
    $tests['[node:vid]'] = $node->vid;
    $tests['[node:tnid]'] = $node->tnid;
    $tests['[node:type]'] = 'article';
    $tests['[node:type-name]'] = 'Article';
    $tests['[node:title]'] = check_plain($node->title);
    $tests['[node:body]'] = _text_sanitize($instance, $langcode, $node->body[$langcode][0], 'value');
    $tests['[node:summary]'] = _text_sanitize($instance, $langcode, $node->body[$langcode][0], 'summary');
    $tests['[node:language]'] = check_plain($langcode);
    $tests['[node:url]'] = url('node/' . $node->nid, $url_options);
    $tests['[node:edit-url]'] = url('node/' . $node->nid . '/edit', $url_options);
    $tests['[node:author]'] = check_plain(format_username($account));
    $tests['[node:author:uid]'] = $node->uid;
    $tests['[node:author:name]'] = check_plain(format_username($account));
    $tests['[node:created:since]'] = format_interval(REQUEST_TIME - $node->created, 2, $language->language);
    $tests['[node:changed:since]'] = format_interval(REQUEST_TIME - $node->changed, 2, $language->language);

    // Test to make sure that we generated something for each token.
    $this->assertFalse(in_array(0, array_map('strlen', $tests)), 'No empty tokens generated.');

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $node), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Sanitized node token %token replaced.', array('%token' => $input)));
    }

    // Test if the node without nid gets correct tokens (e.g. unsaved node).
    $new_node_without_nid = clone $node;
    unset($new_node_without_nid->nid);

    // Update tokens values which should be empty
    $tests['[node:nid]'] = '';
    $tests['[node:url]'] = '';
    $tests['[node:edit-url]'] = '';

    // Generate and test sanitized tokens.
    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $new_node_without_nid), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Sanitized node token %token replaced.', array('%token' => $input)));
    }

    // Revert tokens values
    $tests['[node:nid]'] = $node->nid;
    $tests['[node:url]'] = url('node/' . $node->nid, $url_options);
    $tests['[node:edit-url]'] = url('node/' . $node->nid . '/edit', $url_options);

    // Generate and test unsanitized tokens.
    $tests['[node:title]'] = $node->title;
    $tests['[node:body]'] = $node->body[$langcode][0]['value'];
    $tests['[node:summary]'] = $node->body[$langcode][0]['summary'];
    $tests['[node:language]'] = $langcode;
    $tests['[node:author:name]'] = format_username($account);

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $node), array('language' => $language, 'sanitize' => FALSE));
      $this->assertEqual($output, $expected, format_string('Unsanitized node token %token replaced.', array('%token' => $input)));
    }

    // Repeat for a node without a summary.
    $settings['body'] = array(LANGUAGE_NONE => array(array('value' => $this->randomName(32), 'summary' => '')));
    $node = $this->drupalCreateNode($settings);

    // Load node (without summary) so that the body and summary fields are
    // structured properly.
    $node = node_load($node->nid);
    $instance = field_info_instance('node', 'body', $node->type);

    // Generate and test sanitized token - use full body as expected value.
    $tests = array();
    $tests['[node:summary]'] = _text_sanitize($instance, $langcode, $node->body[$langcode][0], 'value');

    // Test to make sure that we generated something for each token.
    $this->assertFalse(in_array(0, array_map('strlen', $tests)), 'No empty tokens generated for node without a summary.');

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $node), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Sanitized node token %token replaced for node without a summary.', array('%token' => $input)));
    }

    // Generate and test unsanitized tokens.
    $tests['[node:summary]'] = $node->body[$langcode][0]['value'];

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $node), array('language' => $language, 'sanitize' => FALSE));
      $this->assertEqual($output, $expected, format_string('Unsanitized node token %token replaced for node without a summary.', array('%token' => $input)));
    }
  }
}

/**
 * Tests user permissions for node revisions.
 */
class NodeRevisionPermissionsTestCase extends DrupalWebTestCase {

  /**
   * Nodes used by the test.
   *
   * @var array
   */
  protected $node_revisions = array();

  /**
   * Users with different revision permission used by the test.
   *
   * @var array
   */
  protected $accounts = array();

  /**
   * Map revision permission names to node revision access ops.
   *
   * @var array
   */
  protected $map = array(
    'view' => 'view revisions',
    'update' => 'revert revisions',
    'delete' => 'delete revisions',
  );

  public static function getInfo() {
    return array(
      'name' => 'Node revision permissions',
      'description' => 'Tests user permissions for node revision operations.',
      'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    // Create a node with several revisions.
    $node = $this->drupalCreateNode();
    $this->node_revisions[] = $node;

    for ($i = 0; $i < 3; $i++) {
      // Create a revision for the same nid and settings with a random log.
      $revision = clone $node;
      $revision->revision = 1;
      $revision->log = $this->randomName(32);
      node_save($revision);
      $this->node_revisions[] = $revision;
    }

    // Create three users, one with each revision permission.
    foreach ($this->map as $op => $permission) {
      // Create the user.
      $account = $this->drupalCreateUser(
        array(
          'access content',
          'edit any page content',
          'delete any page content',
          $permission,
        )
      );
      $account->op = $op;
      $this->accounts[] = $account;
    }

    // Create an admin account (returns TRUE for all revision permissions).
    $admin_account = $this->drupalCreateUser(array('access content', 'administer nodes'));
    $admin_account->is_admin = TRUE;
    $this->accounts['admin'] = $admin_account;

    // Create a normal account (returns FALSE for all revision permissions).
    $normal_account = $this->drupalCreateUser();
    $normal_account->op = FALSE;
    $this->accounts[] = $normal_account;
  }

  /**
   * Tests the _node_revision_access() function.
   */
  function testNodeRevisionAccess() {
    $revision = $this->node_revisions[1];

    $parameters = array(
      'op' => array_keys($this->map),
      'account' => $this->accounts,
    );

    $permutations = $this->generatePermutations($parameters);
    foreach ($permutations as $case) {
      if (!empty($case['account']->is_admin) || $case['op'] == $case['account']->op) {
        $this->assertTrue(_node_revision_access($revision, $case['op'], $case['account']), "{$this->map[$case['op']]} granted.");
      }
      else {
        $this->assertFalse(_node_revision_access($revision, $case['op'], $case['account']), "{$this->map[$case['op']]} not granted.");
      }
    }

    // Test that access is FALSE for a node administrator with an invalid $node
    // or $op parameters.
    $admin_account = $this->accounts['admin'];
    $this->assertFalse(_node_revision_access(FALSE, 'view', $admin_account), '_node_revision_access() returns FALSE with an invalid node.');
    $this->assertFalse(_node_revision_access($revision, 'invalid-op', $admin_account), '_node_revision_access() returns FALSE with an invalid op.');

    // Test that the $account parameter defaults to the "logged in" user.
    $original_user = $GLOBALS['user'];
    $GLOBALS['user'] = $admin_account;
    $this->assertTrue(_node_revision_access($revision, 'view'), '_node_revision_access() returns TRUE when used with global user.');
    $GLOBALS['user'] = $original_user;
  }
}

/**
 * Tests pagination with a node access module enabled.
 */
class NodeAccessPagerTestCase extends DrupalWebTestCase {
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Node access pagination',
      'description' => 'Test access controlled node views have the right amount of comment pages.',
      'group' => 'Node',
    );
  }

  public function setUp() {
    parent::setUp('node_access_test', 'comment', 'forum');
    node_access_rebuild();
    $this->web_user = $this->drupalCreateUser(array('access content', 'access comments', 'node test view'));
  }

  /**
   * Tests the comment pager for nodes with multiple grants per realm.
   */
  public function testCommentPager() {
    // Create a node.
    $node = $this->drupalCreateNode();

    // Create 60 comments.
    for ($i = 0; $i < 60; $i++) {
      $comment = new stdClass();
      $comment->cid = 0;
      $comment->pid = 0;
      $comment->uid = $this->web_user->uid;
      $comment->nid = $node->nid;
      $comment->subject = $this->randomName();
      $comment->comment_body = array(
        LANGUAGE_NONE => array(
          array('value' => $this->randomName()),
        ),
      );
      comment_save($comment);
    }

    $this->drupalLogin($this->web_user);

    // View the node page. With the default 50 comments per page there should
    // be two pages (0, 1) but no third (2) page.
    $this->drupalGet('node/' . $node->nid);
    $this->assertText($node->title);
    $this->assertText(t('Comments'));
    $this->assertRaw('page=1');
    $this->assertNoRaw('page=2');
  }

  /**
   * Tests the forum node pager for nodes with multiple grants per realm.
   */
  public function testForumPager() {
    // Look up the forums vocabulary ID.
    $vid = variable_get('forum_nav_vocabulary', 0);
    $this->assertTrue($vid, 'Forum navigation vocabulary ID is set.');

    // Look up the general discussion term.
    $tree = taxonomy_get_tree($vid, 0, 1);
    $tid = reset($tree)->tid;
    $this->assertTrue($tid, 'General discussion term is found in the forum vocabulary.');

    // Create 30 nodes.
    for ($i = 0; $i < 30; $i++) {
      $this->drupalCreateNode(array(
        'nid' => NULL,
        'type' => 'forum',
        'taxonomy_forums' => array(
          LANGUAGE_NONE => array(
            array('tid' => $tid, 'vid' => $vid, 'vocabulary_machine_name' => 'forums'),
          ),
        ),
      ));
    }

    // View the general discussion forum page. With the default 25 nodes per
    // page there should be two pages for 30 nodes, no more.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('forum/' . $tid);
    $this->assertRaw('page=1');
    $this->assertNoRaw('page=2');
  }
}


/**
 * Tests the interaction of the node access system with fields.
 */
class NodeAccessFieldTestCase extends NodeWebTestCase {
  protected $field;
  protected $field_name;
  protected $instance;
  protected $admin_user;
  protected $content_admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Node access and fields',
      'description' => 'Tests the interaction of the node access system with fields.',
      'group' => 'Node',
    );
  }

  public function setUp() {
    parent::setUp('node_access_test', 'field_ui');
    node_access_rebuild();

    // Create some users.
    $this->admin_user = $this->drupalCreateUser(array('access content', 'bypass node access', 'administer fields'));
    $this->content_admin_user = $this->drupalCreateUser(array('access content', 'administer content types', 'administer fields'));

    // Add a custom field to the page content type.
    $this->field_name = drupal_strtolower($this->randomName() . '_field_name');
    $this->field = field_create_field(array('field_name' => $this->field_name, 'type' => 'text'));
    $this->instance = field_create_instance(array(
      'field_name' => $this->field_name,
      'entity_type' => 'node',
      'bundle' => 'page',
    ));
  }

  /**
   * Tests administering fields when node access is restricted.
   */
  function testNodeAccessAdministerField() {
    // Create a page node.
    $langcode = LANGUAGE_NONE;
    $field_data = array();
    $value = $field_data[$langcode][0]['value'] = $this->randomName();
    $node = $this->drupalCreateNode(array($this->field_name => $field_data));

    // Log in as the administrator and confirm that the field value is present.
    $this->drupalLogin($this->admin_user);
    $this->drupalGet("node/{$node->nid}");
    $this->assertText($value, 'The saved field value is visible to an administrator.');

    // Log in as the content admin and try to view the node.
    $this->drupalLogin($this->content_admin_user);
    $this->drupalGet("node/{$node->nid}");
    $this->assertText('Access denied', 'Access is denied for the content admin.');

    // Modify the field default as the content admin.
    $edit = array();
    $default = 'Sometimes words have two meanings';
    $edit["{$this->field_name}[$langcode][0][value]"] = $default;
    $this->drupalPost(
      "admin/structure/types/manage/page/fields/{$this->field_name}",
      $edit,
      t('Save settings')
    );

    // Log in as the administrator.
    $this->drupalLogin($this->admin_user);

    // Confirm that the existing node still has the correct field value.
    $this->drupalGet("node/{$node->nid}");
    $this->assertText($value, 'The original field value is visible to an administrator.');

    // Confirm that the new default value appears when creating a new node.
    $this->drupalGet('node/add/page');
    $this->assertRaw($default, 'The updated default value is displayed when creating a new node.');
  }
}

/**
 * Tests changing view modes for nodes.
 */
class NodeEntityViewModeAlterTest extends NodeWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Node entity view mode',
      'description' => 'Test changing view mode.',
      'group' => 'Node'
    );
  }

  function setUp() {
    parent::setUp(array('node_test'));
  }

  /**
   * Create a "Basic page" node and verify its consistency in the database.
   */
  function testNodeViewModeChange() {
    $web_user = $this->drupalCreateUser(array('create page content', 'edit own page content'));
    $this->drupalLogin($web_user);

    // Create a node.
    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $this->randomName(8);
    $edit["body[$langcode][0][value]"] = 'Data that should appear only in the body for the node.';
    $edit["body[$langcode][0][summary]"] = 'Extra data that should appear only in the teaser for the node.';
    $this->drupalPost('node/add/page', $edit, t('Save'));

    $node = $this->drupalGetNodeByTitle($edit["title"]);

    // Set the flag to alter the view mode and view the node.
    variable_set('node_test_change_view_mode', 'teaser');
    $this->drupalGet('node/' . $node->nid);

    // Check that teaser mode is viewed.
    $this->assertText('Extra data that should appear only in the teaser for the node.', 'Teaser text present');
    // Make sure body text is not present.
    $this->assertNoText('Data that should appear only in the body for the node.', 'Body text not present');

    // Test that the correct build mode has been set.
    $build = node_view($node);
    $this->assertEqual($build['#view_mode'], 'teaser', 'The view mode has correctly been set to teaser.');
  }

  /**
   * Tests fields that were previously hidden when the view mode is changed.
   */
  function testNodeViewModeChangeHiddenField() {
    // Hide the tags field on the default display
    $instance = field_info_instance('node', 'field_tags', 'article');
    $instance['display']['default']['type'] = 'hidden';
    field_update_instance($instance);

    $web_user = $this->drupalCreateUser(array('create article content', 'edit own article content'));
    $this->drupalLogin($web_user);

    // Create a node.
    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $this->randomName(8);
    $edit["body[$langcode][0][value]"] = 'Data that should appear only in the body for the node.';
    $edit["body[$langcode][0][summary]"] = 'Extra data that should appear only in the teaser for the node.';
    $edit["field_tags[$langcode]"] = 'Extra tag';
    $this->drupalPost('node/add/article', $edit, t('Save'));

    $node = $this->drupalGetNodeByTitle($edit["title"]);

    // Set the flag to alter the view mode and view the node.
    variable_set('node_test_change_view_mode', 'teaser');
    $this->drupalGet('node/' . $node->nid);

    // Check that teaser mode is viewed.
    $this->assertText('Extra data that should appear only in the teaser for the node.', 'Teaser text present');
    // Make sure body text is not present.
    $this->assertNoText('Data that should appear only in the body for the node.', 'Body text not present');
    // Make sure tags are present.
    $this->assertText('Extra tag', 'Taxonomy term present');

    // Test that the correct build mode has been set.
    $build = node_view($node);
    $this->assertEqual($build['#view_mode'], 'teaser', 'The view mode has correctly been set to teaser.');
  }
}

/**
 * Tests the cache invalidation of node operations.
 */
class NodePageCacheTest extends NodeWebTestCase {

  /**
   * An admin user with administrative permissions for nodes.
   */
  protected $admin_user;

  public static function getInfo() {
    return array(
        'name' => 'Node page cache test',
        'description' => 'Test cache invalidation of node operations.',
        'group' => 'Node',
    );
  }

  function setUp() {
    parent::setUp();

    variable_set('cache', 1);
    variable_set('page_cache_maximum_age', 300);

    $this->admin_user = $this->drupalCreateUser(array(
        'bypass node access',
        'access content overview',
        'administer nodes',
    ));
  }

  /**
   * Tests deleting nodes clears page cache.
   */
  public function testNodeDelete() {
    $node_path = 'node/' . $this->drupalCreateNode()->nid;

    // Populate page cache.
    $this->drupalGet($node_path);

    // Login and delete the node.
    $this->drupalLogin($this->admin_user);
    $this->drupalPost($node_path . '/delete', array(), t('Delete'));

    // Logout and check the node is not available.
    $this->drupalLogout();
    $this->drupalGet($node_path);
    $this->assertResponse(404);

    // Create two new nodes.
    $nodes[0] = $this->drupalCreateNode();
    $nodes[1] = $this->drupalCreateNode();
    $node_path = 'node/' . $nodes[0]->nid;

    // Populate page cache.
    $this->drupalGet($node_path);

    // Login and delete the nodes.
    $this->drupalLogin($this->admin_user);
    $this->drupalGet('admin/content');
    $edit = array(
        'operation' => 'delete',
        'nodes[' . $nodes[0]->nid . ']' => TRUE,
        'nodes[' . $nodes[1]->nid . ']' => TRUE,
    );
    $this->drupalPost(NULL, $edit, t('Update'));
    $this->drupalPost(NULL, array(), t('Delete'));

    // Logout and check the node is not available.
    $this->drupalLogout();
    $this->drupalGet($node_path);
    $this->assertResponse(404);
  }
}

/**
 * Tests that multi-byte UTF-8 characters are stored and retrieved correctly.
 */
class NodeMultiByteUtf8Test extends NodeWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Multi-byte UTF-8',
      'description' => 'Test that multi-byte UTF-8 characters are stored and retrieved correctly.',
      'group' => 'Node',
    );
  }

  /**
   * Tests that multi-byte UTF-8 characters are stored and retrieved correctly.
   */
  public function testMultiByteUtf8() {
    $connection = Database::getConnection();
    // On MySQL, this test will only run if 'charset' is set to 'utf8mb4' in
    // settings.php.
    if (!($connection->utf8mb4IsSupported() && $connection->utf8mb4IsActive())) {
      return;
    }
    $title = '🐙';
    $this->assertTrue(drupal_strlen($title, 'utf-8') < strlen($title), 'Title has multi-byte characters.');
    $node = $this->drupalCreateNode(array('title' => $title));
    $this->drupalGet('node/' . $node->nid);
    $result = $this->xpath('//h1[@id="page-title"]');
    $this->assertEqual(trim((string) $result[0]), $title, 'The passed title was returned.');
  }

}
