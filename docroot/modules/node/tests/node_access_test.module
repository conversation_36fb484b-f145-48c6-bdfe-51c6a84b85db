<?php

/**
 * @file
 * A dummy module implementing node access related hooks for testing purposes.
 *
 * A dummy module implementing node access related hooks to test API interaction
 * with the Node module. This module restricts view permission to those with
 * a special 'node test view' permission.
 */

/**
 * Implements hook_node_grants().
 */
function node_access_test_node_grants($account, $op) {
  $grants = array();
  // First grant a grant to the author for own content.
  $grants['node_access_test_author'] = array($account->uid);
  if ($op == 'view' && user_access('node test view', $account)) {
    $grants['node_access_test'] = array(8888, 8889);
  }
  if ($op == 'view' && $account->uid == variable_get('node_test_node_access_all_uid', 0)) {
    $grants['node_access_all'] = array(0);
  }
  return $grants;
}

/**
 * Implements hook_node_access_records().
 */
function node_access_test_node_access_records($node) {
  $grants = array();
  // For NodeAccessBaseTableTestCase, only set records for private nodes.
  if (!variable_get('node_access_test_private') || $node->private) {
    $grants[] = array(
      'realm' => 'node_access_test',
      'gid' => 8888,
      'grant_view' => 1,
      'grant_update' => 0,
      'grant_delete' => 0,
      'priority' => 0,
    );
    $grants[] = array(
      'realm' => 'node_access_test',
      'gid' => 8889,
      'grant_view' => 1,
      'grant_update' => 0,
      'grant_delete' => 0,
      'priority' => 0,
    );
    // For the author realm, the GID is equivalent to a UID, which
    // means there are many many groups of just 1 user.
    $grants[] = array(
      'realm' => 'node_access_test_author',
      'gid' => $node->uid,
      'grant_view' => 1,
      'grant_update' => 1,
      'grant_delete' => 1,
      'priority' => 0,
    );
  }

  return $grants;
}

/**
 * Implements hook_permission().
 *
 * Sets up permissions for this module.
 */
function node_access_test_permission() {
  return array('node test view' => array('title' => 'View content'));
}

/**
 * Implements hook_menu().
 *
 * Sets up a page that lists nodes.
 */
function node_access_test_menu() {
  $items = array();
  $items['node_access_test_page'] = array(
    'title' => 'Node access test',
    'page callback' => 'node_access_test_page',
    'access arguments' => array('access content'),
    'type' => MENU_SUGGESTED_ITEM,
  );
  $items['node_access_entity_test_page'] = array(
    'title' => 'Node access test',
    'page callback' => 'node_access_entity_test_page',
    'access arguments' => array('access content'),
    'type' => MENU_SUGGESTED_ITEM,
  );
  return $items;
}

/**
 * Page callback for node access test page.
 *
 * Page should say "No nodes" if there are no nodes, and "Yes, # nodes" (with
 * the number filled in) if there were nodes the user could access. Also, the
 * database query is shown, and a list of the node IDs, for debugging purposes.
 * And if there is a query exception, the page says "Exception" and gives the
 * error.
 */
function node_access_test_page() {
  $output = '';

  try {
    $query = db_select('node', 'mytab')
      ->fields('mytab');
    $query->addTag('node_access');
    $result = $query->execute()->fetchAll();

    if (count($result)) {
      $output .= '<p>Yes, ' . count($result) . ' nodes</p>';
      $output .= '<ul>';
      foreach ($result as $item) {
        $output .= '<li>' . $item->nid . '</li>';
      }
      $output .= '</ul>';
    }
    else {
      $output .= '<p>No nodes</p>';
    }

    $output .= '<p>' . ((string) $query ) . '</p>';
  }
  catch (Exception $e) {
    $output = '<p>Exception</p>';
    $output .= '<p>' . $e->getMessage() . '</p>';
  }

  return $output;
}

/**
 * Page callback for node access entity test page.
 *
 * Page should say "No nodes" if there are no nodes, and "Yes, # nodes" (with
 * the number filled in) if there were nodes the user could access. Also, the
 * database query is shown, and a list of the node IDs, for debugging purposes.
 * And if there is a query exception, the page says "Exception" and gives the
 * error.
 *
 * @see node_access_test_menu()
 */
function node_access_entity_test_page() {
  $output = '';
  try {
    $query = new EntityFieldQuery;
    $result = $query->fieldCondition('body', 'value', 'A', 'STARTS_WITH')->execute();
    if (!empty($result['node'])) {
      $output .= '<p>Yes, ' . count($result['node']) . ' nodes</p>';
      $output .= '<ul>';
      foreach ($result['node'] as $nid => $v) {
        $output .= '<li>' . $nid . '</li>';
      }
      $output .= '</ul>';
    }
    else {
      $output .= '<p>No nodes</p>';
    }
  }
  catch (Exception $e) {
    $output = '<p>Exception</p>';
    $output .= '<p>' . $e->getMessage() . '</p>';
  }

  return $output;
}

/**
 * Implements hook_form_BASE_FORM_ID_alter().
 */
function node_access_test_form_node_form_alter(&$form, $form_state) {
  // Only show this checkbox for NodeAccessBaseTableTestCase.
  if (variable_get('node_access_test_private')) {
    $form['private'] = array(
      '#type' => 'checkbox',
      '#title' => t('Private'),
      '#description' => t('Check here if this content should be set private and only shown to privileged users.'),
      '#default_value' => isset($form['#node']->private) ? $form['#node']->private : FALSE,
    );
  }
}

/**
 * Implements hook_node_load().
 */
function node_access_test_node_load($nodes, $types) {
  $result = db_query('SELECT nid, private FROM {node_access_test} WHERE nid IN(:nids)', array(':nids' => array_keys($nodes)));
  foreach ($result as $record) {
    $nodes[$record->nid]->private = $record->private;
  }
}

/**
 * Implements hook_node_delete().
 */

function node_access_test_node_delete($node) {
  db_delete('node_access_test')->condition('nid', $node->nid)->execute();
}

/**
 * Implements hook_node_insert().
 */
function node_access_test_node_insert($node) {
  _node_access_test_node_write($node);
}

/**
 * Implements hook_node_update().
 */
function node_access_test_node_update($node) {
  _node_access_test_node_write($node);
}

/**
 * Helper for node insert/update.
 */
function _node_access_test_node_write($node) {
  if (isset($node->private)) {
    db_merge('node_access_test')
      ->key(array('nid' => $node->nid))
      ->fields(array('private' => (int) $node->private))
      ->execute();
  }
}
