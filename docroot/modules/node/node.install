<?php

/**
 * @file
 * Install, update and uninstall functions for the node module.
 */

/**
 * Implements hook_schema().
 */
function node_schema() {
  $schema['node'] = array(
    'description' => 'The base table for nodes.',
    'fields' => array(
      'nid' => array(
        'description' => 'The primary identifier for a node.',
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
      ),
      // Defaults to NULL in order to avoid a brief period of potential
      // deadlocks on the index.
      'vid' => array(
        'description' => 'The current {node_revision}.vid version identifier.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => FALSE,
        'default' => NULL,
      ),
      'type' => array(
        'description' => 'The {node_type}.type of this node.',
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'default' => '',
      ),
      'language' => array(
        'description' => 'The {languages}.language of this node.',
        'type' => 'varchar',
        'length' => 12,
        'not null' => TRUE,
        'default' => '',
      ),
      'title' => array(
        'description' => 'The title of this node, always treated as non-markup plain text.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
      ),
      'uid' => array(
        'description' => 'The {users}.uid that owns this node; initially, this is the user that created it.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'status' => array(
        'description' => 'Boolean indicating whether the node is published (visible to non-administrators).',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 1,
      ),
      'created' => array(
        'description' => 'The Unix timestamp when the node was created.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'changed' => array(
        'description' => 'The Unix timestamp when the node was most recently saved.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'comment' => array(
        'description' => 'Whether comments are allowed on this node: 0 = no, 1 = closed (read only), 2 = open (read/write).',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'promote' => array(
        'description' => 'Boolean indicating whether the node should be displayed on the front page.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'sticky' => array(
        'description' => 'Boolean indicating whether the node should be displayed at the top of lists in which it appears.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'tnid' => array(
        'description' => 'The translation set id for this node, which equals the node id of the source post in each set.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'translate' => array(
        'description' => 'A boolean indicating whether this translation page needs to be updated.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
    ),
    'indexes' => array(
      'node_changed'        => array('changed'),
      'node_created'        => array('created'),
      'node_frontpage'      => array('promote', 'status', 'sticky', 'created'),
      'node_status_type'    => array('status', 'type', 'nid'),
      'node_title_type'     => array('title', array('type', 4)),
      'node_type'           => array(array('type', 4)),
      'uid'                 => array('uid'),
      'tnid'                => array('tnid'),
      'translate'           => array('translate'),
      'language'            => array('language'),
    ),
    'unique keys' => array(
      'vid' => array('vid'),
    ),
    'foreign keys' => array(
      'node_revision' => array(
        'table' => 'node_revision',
        'columns' => array('vid' => 'vid'),
      ),
      'node_author' => array(
        'table' => 'users',
        'columns' => array('uid' => 'uid'),
      ),
    ),
    'primary key' => array('nid'),
  );

  $schema['node_access'] = array(
    'description' => 'Identifies which realm/grant pairs a user must possess in order to view, update, or delete specific nodes.',
    'fields' => array(
      'nid' => array(
        'description' => 'The {node}.nid this record affects.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'gid' => array(
        'description' => "The grant ID a user must possess in the specified realm to gain this row's privileges on the node.",
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'realm' => array(
        'description' => 'The realm in which the user must possess the grant ID. Each node access node can define one or more realms.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
      ),
      'grant_view' => array(
        'description' => 'Boolean indicating whether a user with the realm/grant pair can view this node.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
      ),
      'grant_update' => array(
        'description' => 'Boolean indicating whether a user with the realm/grant pair can edit this node.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
      ),
      'grant_delete' => array(
        'description' => 'Boolean indicating whether a user with the realm/grant pair can delete this node.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
      ),
    ),
    'primary key' => array('nid', 'gid', 'realm'),
    'foreign keys' => array(
      'affected_node' => array(
        'table' => 'node',
        'columns' => array('nid' => 'nid'),
      ),
     ),
  );

  $schema['node_revision'] = array(
    'description' => 'Stores information about each saved version of a {node}.',
    'fields' => array(
      'nid' => array(
        'description' => 'The {node} this version belongs to.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'vid' => array(
        'description' => 'The primary identifier for this version.',
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
      ),
      'uid' => array(
        'description' => 'The {users}.uid that created this version.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'title' => array(
        'description' => 'The title of this version.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
      ),
      'log' => array(
        'description' => 'The log entry explaining the changes in this version.',
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
      ),
      'timestamp' => array(
        'description' => 'A Unix timestamp indicating when this version was created.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'status' => array(
        'description' => 'Boolean indicating whether the node (at the time of this revision) is published (visible to non-administrators).',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 1,
      ),
      'comment' => array(
        'description' => 'Whether comments are allowed on this node (at the time of this revision): 0 = no, 1 = closed (read only), 2 = open (read/write).',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'promote' => array(
        'description' => 'Boolean indicating whether the node (at the time of this revision) should be displayed on the front page.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'sticky' => array(
        'description' => 'Boolean indicating whether the node (at the time of this revision) should be displayed at the top of lists in which it appears.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
    ),
    'indexes' => array(
      'nid' => array('nid'),
      'uid' => array('uid'),
    ),
    'primary key' => array('vid'),
    'foreign keys' => array(
      'versioned_node' => array(
        'table' => 'node',
        'columns' => array('nid' => 'nid'),
      ),
      'version_author' => array(
        'table' => 'users',
        'columns' => array('uid' => 'uid'),
      ),
    ),
  );

  $schema['node_type'] = array(
    'description' => 'Stores information about all defined {node} types.',
    'fields' => array(
      'type' => array(
        'description' => 'The machine-readable name of this type.',
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
      ),
      'name' => array(
        'description' => 'The human-readable name of this type.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'translatable' => TRUE,
      ),
      'base' => array(
        'description' => 'The base string used to construct callbacks corresponding to this node type.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
      ),
      'module' => array(
        'description' => 'The module defining this node type.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
      ),
      'description' => array(
        'description' => 'A brief description of this type.',
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'medium',
        'translatable' => TRUE,
      ),
      'help' => array(
        'description' => 'Help information shown to the user when creating a {node} of this type.',
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'medium',
        'translatable' => TRUE,
      ),
      'has_title' => array(
        'description' => 'Boolean indicating whether this type uses the {node}.title field.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'size' => 'tiny',
      ),
      'title_label' => array(
        'description' => 'The label displayed for the title field on the edit form.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'translatable' => TRUE,
      ),
      'custom' => array(
        'description' => 'A boolean indicating whether this type is defined by a module (FALSE) or by a user via Add content type (TRUE).',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
      ),
      'modified' => array(
        'description' => 'A boolean indicating whether this type has been modified by an administrator; currently not used in any way.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
      ),
      'locked' => array(
        'description' => 'A boolean indicating whether the administrator can change the machine name of this type.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
      ),
      'disabled' => array(
        'description' => 'A boolean indicating whether the node type is disabled.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny'
      ),
      'orig_type' => array(
        'description' => 'The original machine-readable name of this node type. This may be different from the current type name if the locked field is 0.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
      ),
    ),
    'primary key' => array('type'),
  );

  $schema['block_node_type'] = array(
    'description' => 'Sets up display criteria for blocks based on content types',
    'fields' => array(
      'module' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'description' => "The block's origin module, from {block}.module.",
      ),
      'delta' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'description' => "The block's unique delta within module, from {block}.delta.",
      ),
      'type' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'description' => "The machine-readable name of this type from {node_type}.type.",
      ),
    ),
    'primary key' => array('module', 'delta', 'type'),
    'indexes' => array(
      'type' => array('type'),
    ),
  );

  $schema['history'] = array(
    'description' => 'A record of which {users} have read which {node}s.',
    'fields' => array(
      'uid' => array(
        'description' => 'The {users}.uid that read the {node} nid.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
      'nid' => array(
        'description' => 'The {node}.nid that was read.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'timestamp' => array(
        'description' => 'The Unix timestamp at which the read occurred.',
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ),
    ),
    'primary key' => array('uid', 'nid'),
    'indexes' => array(
      'nid' => array('nid'),
    ),
  );

  return $schema;
}

/**
 * Implements hook_install().
 */
function node_install() {
  // Populate the node access table.
  db_insert('node_access')
    ->fields(array(
      'nid' => 0,
      'gid' => 0,
      'realm' => 'all',
      'grant_view' => 1,
      'grant_update' => 0,
      'grant_delete' => 0,
    ))
    ->execute();
}

/**
 * Implements hook_update_dependencies().
 */
function node_update_dependencies() {
  // node_update_7006() migrates node data to fields and therefore must run
  // after all Field modules have been enabled, which happens in
  // system_update_7027(). It also needs to query the {filter_format} table to
  // get a list of existing text formats, so it must run after
  // filter_update_7000(), which creates that table.
  $dependencies['node'][7006] = array(
    'system' => 7027,
    'filter' => 7000,
  );

  // node_update_7008() migrates role permissions and therefore must run after
  // the {role} and {role_permission} tables are properly set up, which happens
  // in user_update_7007().
  $dependencies['node'][7008] = array(
    'user' => 7007,
  );

  return $dependencies;
}

/**
 * Utility function: fetch the node types directly from the database.
 *
 * This function is valid for a database schema version 7000.
 *
 * @ingroup update_api
 */
function _update_7000_node_get_types() {
  $node_types = db_query('SELECT * FROM {node_type}')->fetchAllAssoc('type', PDO::FETCH_OBJ);

  // Create default settings for orphan nodes.
  $all_types = db_query('SELECT DISTINCT type FROM {node}')->fetchCol();
  $extra_types = array_diff($all_types, array_keys($node_types));

  foreach ($extra_types as $type) {
    $type_object = new stdClass();
    $type_object->type = $type;

    // In Drupal 6, whether you have a body field or not is a flag in the node
    // type table. If it's enabled, nodes may or may not have an empty string
    // for the bodies. As we can't detect what this setting should be in
    // Drupal 7 without access to the Drupal 6 node type settings, we assume
    // the default, which is to enable the body field.
    $type_object->has_body = 1;
    $type_object->body_label = 'Body';
    $node_types[$type_object->type] = $type_object;
  }
  return $node_types;
}

/**
 * @addtogroup updates-6.x-to-7.x
 * @{
 */

/**
 * Upgrade the node type table and fix node type 'module' attribute to avoid name-space conflicts.
 */
function node_update_7000() {
  // Rename the module column to base.
  db_change_field('node_type', 'module', 'base', array('type' => 'varchar', 'length' => 255, 'not null' => TRUE));

  db_add_field('node_type', 'module', array(
    'description' => 'The module defining this node type.',
    'type' => 'varchar',
    'default' => '',
    'length' => 255,
    'not null' => TRUE,
  ));

  db_add_field('node_type', 'disabled', array(
    'description' => 'A boolean indicating whether the node type is disabled.',
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
    'size' => 'tiny'
  ));

  $modules = db_select('system', 's')
    ->fields('s', array('name'))
    ->condition('type', 'module');
  db_update('node_type')
    ->expression('module', 'base')
    ->condition('base', $modules, 'IN')
    ->execute();

  db_update('node_type')
    ->fields(array('base' => 'node_content'))
    ->condition('base', 'node')
    ->execute();
}

/**
 * Rename {node_revisions} table to {node_revision}.
 */
function node_update_7001() {
  db_rename_table('node_revisions', 'node_revision');
}

/**
 * Extend the node_promote_status index to include all fields required for the node page query.
 */
function node_update_7002() {
  db_drop_index('node', 'node_promote_status');
  db_add_index('node', 'node_frontpage', array('promote', 'status', 'sticky', 'created'));
}

/**
 * Remove the node_counter if the statistics module is uninstalled.
 */
function node_update_7003() {
  if (drupal_get_installed_schema_version('statistics') == SCHEMA_UNINSTALLED) {
    db_drop_table('node_counter');
  }
}

/**
 * Extend the existing default preview and teaser settings to all node types.
 */
function node_update_7004() {
  // Get original settings and all types.
  $original_length = variable_get('teaser_length', 600);
  $original_preview = variable_get('node_preview', 0);

  // Map old preview setting to new values order.
  $original_preview ? $original_preview = 2 : $original_preview = 1;
  node_type_cache_reset();

  // Apply original settings to all types.
  foreach (_update_7000_node_get_types() as $type => $type_object) {
    variable_set('teaser_length_' . $type, $original_length);
    variable_set('node_preview_' . $type, $original_preview);
  }
  // Delete old variable but leave 'teaser_length' for aggregator module upgrade.
  variable_del('node_preview');
}

/**
 * Add status/comment/promote and sticky columns to the {node_revision} table.
 */
function node_update_7005() {
  foreach (array('status', 'comment', 'promote', 'sticky') as $column) {
    db_add_field('node_revision', $column, array(
      'type' => 'int',
      'not null' => TRUE,
      'default' => 0,
    ));
  }
}

/**
 * Convert body and teaser from node properties to fields, and migrate status/comment/promote and sticky columns to the {node_revision} table.
 */
function node_update_7006(&$sandbox) {
  $sandbox['#finished'] = 0;

  // Get node type info for every invocation.
  node_type_cache_reset();

  if (!isset($sandbox['total'])) {
    // Initial invocation.

    // First, create the body field.
    $body_field = array(
      'field_name' => 'body',
      'type' => 'text_with_summary',
      'module' => 'text',
      'cardinality' => 1,
      'entity_types' => array('node'),
      'translatable' => TRUE,
    );
    _update_7000_field_create_field($body_field);

    $default_trim_length = variable_get('teaser_length', 600);

    // Get node type info, specifically the body field settings.
    $node_types = _update_7000_node_get_types();

    // Add body field instances for existing node types.
    foreach ($node_types as $node_type) {
      if ($node_type->has_body) {
        $trim_length = variable_get('teaser_length_' . $node_type->type, $default_trim_length);

        $instance = array(
          'entity_type' => 'node',
          'bundle' => $node_type->type,
          'label' => $node_type->body_label,
          'description' => isset($node_type->description) ? $node_type->description : '',
          'required' => (isset($node_type->min_word_count) && $node_type->min_word_count > 0) ? 1 : 0,
          'widget' => array(
            'type' => 'text_textarea_with_summary',
            'settings' => array(
              'rows' => 20,
              'summary_rows' => 5,
            ),
            'weight' => -4,
            'module' => 'text',
          ),
          'settings' => array('display_summary' => TRUE),
          'display' => array(
            'default' => array(
              'label' => 'hidden',
              'type' => 'text_default',
            ),
            'teaser' => array(
              'label' => 'hidden',
              'type' => 'text_summary_or_trimmed',
              'trim_length' => $trim_length,
            ),
          ),
        );
        _update_7000_field_create_instance($body_field, $instance);
        variable_del('teaser_length_' . $node_type->type);
      }
      // Leave 'teaser_length' variable for aggregator module upgrade.

      $sandbox['node_types_info'][$node_type->type] = array(
        'has_body' => $node_type->has_body,
      );
    }

    // Used below when updating the stored text format of each node body.
    $sandbox['existing_text_formats'] = db_query("SELECT format FROM {filter_format}")->fetchCol();

    // Initialize state for future calls.
    $sandbox['last'] = 0;
    $sandbox['count'] = 0;

    $query = db_select('node', 'n');
    $query->join('node_revision', 'nr', 'n.nid = nr.nid');
    $sandbox['total'] = $query->countQuery()->execute()->fetchField();

    $sandbox['body_field_id'] = $body_field['id'];
  }
  else {
    // Subsequent invocations.

    $found = FALSE;
    if ($sandbox['total']) {
      // Operate on every revision of every node (whee!), in batches.
      $batch_size = 200;
      $query = db_select('node_revision', 'nr');
      $query->innerJoin('node', 'n', 'n.nid = nr.nid');
      $query
        ->fields('nr', array('nid', 'vid', 'body', 'teaser', 'format'))
        ->fields('n', array('type', 'status', 'comment', 'promote', 'sticky', 'language'))
        ->condition('nr.vid', $sandbox['last'], '>')
        ->orderBy('nr.vid', 'ASC')
        ->range(0, $batch_size);
      $revisions = $query->execute();

      // Load each revision of each node, set up 'body'
      // appropriately, and save the node's field data.  Note that
      // node_load() will not return the body or teaser values from
      // {node_revision} because those columns have been removed from the
      // schema structure in memory (but not yet from the database),
      // so we get the values from the explicit query of the table
      // instead.
      foreach ($revisions as $revision) {
        $found = TRUE;

        if ($sandbox['node_types_info'][$revision->type]['has_body']) {
          $node = (object) array(
            'nid' => $revision->nid,
            'vid' => $revision->vid,
            'type' => $revision->type,
          );
          // After node_update_7009() we will always have LANGUAGE_NONE as
          // language neutral language code, but here we still have empty
          // strings.
          $langcode = empty($revision->language) ? LANGUAGE_NONE : $revision->language;
          if (!empty($revision->teaser) && $revision->teaser != text_summary($revision->body)) {
            $node->body[$langcode][0]['summary'] = $revision->teaser;
          }
          // Do this after text_summary() above.
          $break = '<!--break-->';
          if (substr($revision->body, 0, strlen($break)) == $break) {
            $revision->body = substr($revision->body, strlen($break));
          }
          $node->body[$langcode][0]['value'] = $revision->body;
          // Update the revision's text format for the changes to the Drupal 7
          // filter system. This uses the same kind of logic that occurs, for
          // example, in user_update_7010(), but we do this here rather than
          // via a separate set of database queries, since we are already
          // migrating the data.
          if (empty($revision->body) && empty($revision->format)) {
            $node->body[$langcode][0]['format'] = NULL;
          }
          elseif (!in_array($revision->format, $sandbox['existing_text_formats'])) {
            $node->body[$langcode][0]['format'] = variable_get('filter_default_format', 1);
          }
          else {
            $node->body[$langcode][0]['format'] = $revision->format;
          }
          // This is a core update and no contrib modules are enabled yet, so
          // we can assume default field storage for a faster update.
          _update_7000_field_sql_storage_write('node', $node->type, $node->nid, $node->vid, 'body', $node->body);
        }

        // Migrate the status columns to the {node_revision} table.
        db_update('node_revision')
          ->fields(array(
            'status' => $revision->status,
            'comment' => $revision->comment,
            'promote' => $revision->promote,
            'sticky' => $revision->sticky,
          ))
          ->condition('vid', $revision->vid)
          ->execute();

        $sandbox['last'] = $revision->vid;
        $sandbox['count'] += 1;
      }

      $sandbox['#finished'] = min(0.99, $sandbox['count'] / $sandbox['total']);
    }

    if (!$found) {
      // All nodes are processed.

      // Remove the now-obsolete body info from node_revision.
      db_drop_field('node_revision', 'body');
      db_drop_field('node_revision', 'teaser');
      db_drop_field('node_revision', 'format');

      // Remove node_type properties related to the former 'body'.
      db_drop_field('node_type', 'has_body');
      db_drop_field('node_type', 'body_label');

      // We're done.
      $sandbox['#finished'] = 1;
    }
  }
}

/**
 * Remove column min_word_count.
 */
function node_update_7007() {
  db_drop_field('node_type', 'min_word_count');
}

/**
 * Split the 'administer nodes' permission from 'access content overview'.
 */
function node_update_7008() {
  $roles = user_roles(FALSE, 'administer nodes');
  foreach ($roles as $rid => $role) {
    _update_7000_user_role_grant_permissions($rid, array('access content overview'), 'node');
  }
}

/**
 * Convert node languages from the empty string to LANGUAGE_NONE.
 */
function node_update_7009() {
  db_update('node')
    ->fields(array('language' => LANGUAGE_NONE))
    ->condition('language', '')
    ->execute();
}

/**
 * Add the {block_node_type} table.
 */
function node_update_7010() {
  $schema['block_node_type'] = array(
    'description' => 'Sets up display criteria for blocks based on content types',
    'fields' => array(
      'module' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'description' => "The block's origin module, from {block}.module.",
      ),
      'delta' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'description' => "The block's unique delta within module, from {block}.delta.",
      ),
      'type' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'description' => "The machine-readable name of this type from {node_type}.type.",
      ),
    ),
    'primary key' => array('module', 'delta', 'type'),
    'indexes' => array(
      'type' => array('type'),
    ),
  );

  db_create_table('block_node_type', $schema['block_node_type']);
}

/**
 * @} End of "addtogroup updates-6.x-to-7.x".
 */

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Update the database from Drupal 6 to match the schema.
 */
function node_update_7011() {
  // Drop node moderation field.
  db_drop_field('node', 'moderate');
  db_drop_index('node', 'node_moderate');

  // Change {node_revision}.status field to default to 1.
  db_change_field('node_revision', 'status', 'status', array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 1,
  ));

  // Change {node_type}.module field default.
  db_change_field('node_type', 'module', 'module', array(
    'type' => 'varchar',
    'length' => 255,
    'not null' => TRUE,
  ));
}

/**
 * Switches body fields to untranslatable while upgrading from D6 and makes them language neutral.
 */
function node_update_7012() {
  // If we are upgrading from D6, then body fields should be set back to
  // untranslatable, as D6 did not know about the idea of translating fields,
  // but only nodes. If a D7 > D7 update is running we need to skip this update,
  // as it is a valid use case to have translatable body fields in this context.
  if (variable_get('update_d6', FALSE)) {
    // Make node bodies untranslatable: field_update_field() cannot be used
    // throughout the upgrade process and we do not have an update counterpart
    // for _update_7000_field_create_field(). Hence we are forced to update the
    // 'field_config' table directly. This is a safe operation since it is
    // being performed while upgrading from D6. Perfoming the same operation
    // during a D7 update is highly discouraged.
    db_update('field_config')
      ->fields(array('translatable' => 0))
      ->condition('field_name', 'body')
      ->execute();

    // Switch field languages to LANGUAGE_NONE, since initially they were
    // assigned the node language.
    foreach (array('field_data_body', 'field_revision_body') as $table) {
      db_update($table)
        ->fields(array('language' => LANGUAGE_NONE))
        ->execute();
    }

    node_type_cache_reset();
  }
}

/**
 * Change {node}.vid default value from 0 to NULL to avoid deadlock issues on MySQL.
 */
function node_update_7013() {
  db_drop_unique_key('node', 'vid');
  db_change_field('node', 'vid', 'vid', array(
    'description' => 'The current {node_revision}.vid version identifier.',
    'type' => 'int',
    'unsigned' => TRUE,
    'not null' => FALSE,
    'default' => NULL,
  ));
  db_add_unique_key('node', 'vid', array('vid'));
}

/**
 * Add an index on {node}.language.
 */
function node_update_7014() {
  db_add_index('node', 'language', array('language'));
}

/**
 * Enable node types that may have been erroneously disabled in Drupal 7.36.
 */
function node_update_7015() {
  db_update('node_type')
    ->fields(array('disabled' => 0))
    ->condition('base', 'node_content')
    ->execute();
}

/**
 * Change {history}.nid to an unsigned int in order to match {node}.nid.
 */
function node_update_7016() {
  db_drop_primary_key('history');
  db_drop_index('history', 'nid');
  db_change_field('history', 'nid', 'nid', array(
    'description' => 'The {node}.nid that was read.',
    'type' => 'int',
    'unsigned' => TRUE,
    'not null' => TRUE,
    'default' => 0,
  ));
  db_add_primary_key('history', array('uid', 'nid'));
  db_add_index('history', 'nid', array('nid'));
}

/**
 * @} End of "addtogroup updates-7.x-extra".
 */
