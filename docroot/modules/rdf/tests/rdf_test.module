<?php

/**
 * @file
 * Test API interaction with the RDF module.
 */

/**
 * Implements hook_rdf_mapping().
 */
function rdf_test_rdf_mapping() {
  return array(
    array(
      'type' => 'test_entity',
      'bundle' => 'test_bundle',
      'mapping' => array(
        'rdftype' => array('sioc:Post'),
        'title' => array(
          'predicates' => array('dc:title'),
        ),
        'created' => array(
          'predicates' => array('dc:created'),
          'datatype' => 'xsd:dateTime',
          'callback' => 'date_iso8601',
        ),
        'uid' => array(
          'predicates' => array('sioc:has_creator', 'dc:creator'),
          'type' => 'rel',
        ),
        'foobar' => array(
          'predicates' => array('foo:bar'),
        ),
        'foobar1' => array(
          'datatype' => 'foo:bar1type',
          'predicates' => array('foo:bar1'),
        ),
        'foobar_objproperty1' => array(
          'predicates' => array('sioc:has_creator', 'dc:creator'),
          'type' => 'rel',
        ),
        'foobar_objproperty2' => array(
          'predicates' => array('sioc:reply_of'),
          'type' => 'rev',
        ),
      ),
    ),
    array(
      'type' => 'node',
      'bundle' => 'blog',
      'mapping' => array(
        'rdftype' => array('sioct:Weblog'),
      ),
    ),
  );
}

/**
 * Implements hook_rdf_namespaces().
 */
function rdf_test_rdf_namespaces() {
  return array(
    'dc'       => 'http://purl.org/conflicting/namespace',
    'foaf'     => 'http://xmlns.com/foaf/0.1/',
    'foaf1'    => 'http://xmlns.com/foaf/0.1/',
  );
}
