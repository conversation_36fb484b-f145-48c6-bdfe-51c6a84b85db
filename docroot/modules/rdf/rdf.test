<?php

/**
 * @file
 * Tests for rdf.module.
 */

class RdfMappingHookTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'RDF mapping hook',
      'description' => 'Test hook_rdf_mapping().',
      'group' => 'RDF',
    );
  }

  function setUp() {
    parent::setUp('rdf', 'rdf_test', 'field_test');
  }

  /**
   * Test that hook_rdf_mapping() correctly returns and processes mapping.
   */
  function testMapping() {
    // Test that the mapping is returned correctly by the hook.
    $mapping = rdf_mapping_load('test_entity', 'test_bundle');
    $this->assertIdentical($mapping['rdftype'], array('sioc:Post'), 'Mapping for rdftype is sioc:Post.');
    $this->assertIdentical($mapping['title'], array('predicates' => array('dc:title')), 'Mapping for title is dc:title.');
    $this->assertIdentical($mapping['created'], array(
      'predicates' => array('dc:created'),
      'datatype' => 'xsd:dateTime',
      'callback' => 'date_iso8601',
    ), t('Mapping for created is dc:created with datatype xsd:dateTime and callback date_iso8601.'));
    $this->assertIdentical($mapping['uid'], array('predicates' => array('sioc:has_creator', 'dc:creator'), 'type' => 'rel'), 'Mapping for uid is sioc:has_creator and dc:creator, and type is rel.');

    $mapping = rdf_mapping_load('test_entity', 'test_bundle_no_mapping');
    $this->assertEqual($mapping, array(), 'Empty array returned when an entity type, bundle pair has no mapping.');
  }
}

/**
 * Test RDFa markup generation.
 */
class RdfRdfaMarkupTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'RDFa markup',
      'description' => 'Test RDFa markup generation.',
      'group' => 'RDF',
    );
  }

  function setUp() {
    parent::setUp('rdf', 'field_test', 'rdf_test');
  }

  /**
   * Test rdf_rdfa_attributes().
   */
  function testDrupalRdfaAttributes() {
    // Same value as the one in the HTML tag (no callback function).
    $expected_attributes = array(
      'property' => array('dc:title'),
    );
    $mapping = rdf_mapping_load('test_entity', 'test_bundle');
    $attributes = rdf_rdfa_attributes($mapping['title']);
    ksort($expected_attributes);
    ksort($attributes);
    $this->assertEqual($expected_attributes, $attributes);

    // Value different from the one in the HTML tag (callback function).
    $date = 1252750327;
    $isoDate = date('c', $date);
    $expected_attributes = array(
      'datatype' => 'xsd:dateTime',
      'property' => array('dc:created'),
      'content' => $isoDate,
    );
    $mapping = rdf_mapping_load('test_entity', 'test_bundle');
    $attributes = rdf_rdfa_attributes($mapping['created'], $date);
    ksort($expected_attributes);
    ksort($attributes);
    $this->assertEqual($expected_attributes, $attributes);

    // Same value as the one in the HTML tag with datatype.
    $expected_attributes = array(
      'datatype' => 'foo:bar1type',
      'property' => array('foo:bar1'),
    );
    $mapping = rdf_mapping_load('test_entity', 'test_bundle');
    $attributes = rdf_rdfa_attributes($mapping['foobar1']);
    ksort($expected_attributes);
    ksort($attributes);
    $this->assertEqual($expected_attributes, $attributes);

    // ObjectProperty mapping (rel).
    $expected_attributes = array(
      'rel' => array('sioc:has_creator', 'dc:creator'),
    );
    $mapping = rdf_mapping_load('test_entity', 'test_bundle');
    $attributes = rdf_rdfa_attributes($mapping['foobar_objproperty1']);
    ksort($expected_attributes);
    ksort($attributes);
    $this->assertEqual($expected_attributes, $attributes);

    // Inverse ObjectProperty mapping (rev).
    $expected_attributes = array(
      'rev' => array('sioc:reply_of'),
    );
    $mapping = rdf_mapping_load('test_entity', 'test_bundle');
    $attributes = rdf_rdfa_attributes($mapping['foobar_objproperty2']);
    ksort($expected_attributes);
    ksort($attributes);
    $this->assertEqual($expected_attributes, $attributes);
  }

  /**
   * Ensure that file fields have the correct resource as the object in RDFa
   * when displayed as a teaser.
   */
  function testAttributesInMarkupFile() {
    // Create a user to post the image.
    $admin_user = $this->drupalCreateUser(array('edit own article content', 'revert revisions', 'administer content types'));
    $this->drupalLogin($admin_user);

    $langcode = LANGUAGE_NONE;
    $bundle_name = "article";

    $field_name = 'file_test';
    $field = array(
      'field_name' => $field_name,
      'type' => 'file',
    );
    field_create_field($field);
    $instance = array(
      'field_name' => $field_name,
      'entity_type' => 'node',
      'bundle' => $bundle_name,
      'display' => array(
        'teaser' => array(
          'type' => 'file_default',
        ),
      ),
    );
    field_create_instance($instance);

    // Set the RDF mapping for the new field.
    $rdf_mapping = rdf_mapping_load('node', $bundle_name);
    $rdf_mapping += array($field_name => array('predicates' => array('rdfs:seeAlso'), 'type' => 'rel'));
    $rdf_mapping_save = array('mapping' => $rdf_mapping, 'type' => 'node', 'bundle' => $bundle_name);
    rdf_mapping_save($rdf_mapping_save);

    // Get the test file that simpletest provides.
    $file = current($this->drupalGetTestFiles('text'));

    // Prepare image variables.
    $image_field = "field_image";
    // Get the test image that simpletest provides.
    $image = current($this->drupalGetTestFiles('image'));

    // Create an array for drupalPost with the field names as the keys and
    // the URIs for the test files as the values.
    $edit = array("files[" . $field_name . "_" . $langcode . "_0]" => drupal_realpath($file->uri),
                  "files[" . $image_field . "_" . $langcode . "_0]" => drupal_realpath($image->uri));

    // Create node and save, then edit node to upload files.
    $node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));

    // Get filenames and nid for comparison with HTML output.
    $file_filename = $file->filename;
    $image_filename = $image->filename;
    $nid = $node->nid;
    // Navigate to front page, where node is displayed in teaser form.
    $this->drupalGet('node');

    // We only check to make sure that the resource attribute contains '.txt'
    // instead of the full file name because the filename is altered on upload.
    $file_rel = $this->xpath('//div[contains(@about, :node-uri)]//div[contains(@rel, "rdfs:seeAlso") and contains(@resource, ".txt")]', array(
      ':node-uri' => 'node/' . $nid,
    ));
    $this->assertTrue(!empty($file_rel), "Attribute 'rel' set on file field. Attribute 'resource' is also set.");
    $image_rel = $this->xpath('//div[contains(@about, :node-uri)]//div[contains(@rel, "rdfs:seeAlso") and contains(@resource, :image)]//img[contains(@typeof, "foaf:Image")]', array(
      ':node-uri' => 'node/' . $nid,
      ':image' => $image_filename,
    ));

    $this->assertTrue(!empty($image_rel), "Attribute 'rel' set on image field. Attribute 'resource' is also set.");

    // Edits the node to add tags.
    $tag1 = $this->randomName(8);
    $tag2 = $this->randomName(8);
    $edit = array();
    $edit['field_tags[' . LANGUAGE_NONE . ']'] = "$tag1, $tag2";
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    // Ensures the RDFa markup for the relationship between the node and its
    // tags is correct.
    $term_rdfa_meta = $this->xpath('//div[@about=:node-url and contains(@typeof, "sioc:Item") and contains(@typeof, "foaf:Document")]//ul[@class="links"]/li[@rel="dc:subject"]/a[@typeof="skos:Concept" and @datatype="" and text()=:term-name]', array(
      ':node-url' => url('node/' . $node->nid),
      ':term-name' => $tag1,
    ));
    $this->assertTrue(!empty($term_rdfa_meta), 'Property dc:subject is present for the tag1 field item.');
    $term_rdfa_meta = $this->xpath('//div[@about=:node-url and contains(@typeof, "sioc:Item") and contains(@typeof, "foaf:Document")]//ul[@class="links"]/li[@rel="dc:subject"]/a[@typeof="skos:Concept" and @datatype="" and text()=:term-name]', array(
      ':node-url' => url('node/' . $node->nid),
      ':term-name' => $tag2,
    ));
    $this->assertTrue(!empty($term_rdfa_meta), 'Property dc:subject is present for the tag2 field item.');
  }
}

class RdfCrudTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'RDF mapping CRUD functions',
      'description' => 'Test the RDF mapping CRUD functions.',
      'group' => 'RDF',
    );
  }

  function setUp() {
    parent::setUp('rdf', 'rdf_test');
  }

  /**
   * Test inserting, loading, updating, and deleting RDF mappings.
   */
  function testCRUD() {
    // Verify loading of a default mapping.
    $mapping = _rdf_mapping_load('test_entity', 'test_bundle');
    $this->assertTrue(count($mapping), 'Default mapping was found.');

    // Verify saving a mapping.
    $mapping = array(
      'type' => 'crud_test_entity',
      'bundle' => 'crud_test_bundle',
      'mapping' => array(
        'rdftype' => array('sioc:Post'),
        'title' => array(
          'predicates' => array('dc:title'),
        ),
        'uid' => array(
          'predicates' => array('sioc:has_creator', 'dc:creator'),
          'type' => 'rel',
        ),
      ),
    );
    $this->assertTrue(rdf_mapping_save($mapping) === SAVED_NEW, 'Mapping was saved.');

    // Read the raw record from the {rdf_mapping} table.
    $result = db_query('SELECT * FROM {rdf_mapping} WHERE type = :type AND bundle = :bundle', array(':type' => $mapping['type'], ':bundle' => $mapping['bundle']));
    $stored_mapping = $result->fetchAssoc();
    $stored_mapping['mapping'] = unserialize($stored_mapping['mapping']);
    $this->assertEqual($mapping, $stored_mapping, 'Mapping was stored properly in the {rdf_mapping} table.');

    // Verify loading of saved mapping.
    $this->assertEqual($mapping['mapping'], _rdf_mapping_load($mapping['type'], $mapping['bundle']), 'Saved mapping loaded successfully.');

    // Verify updating of mapping.
    $mapping['mapping']['title'] = array(
      'predicates' => array('dc2:bar2'),
    );
    $this->assertTrue(rdf_mapping_save($mapping) === SAVED_UPDATED, 'Mapping was updated.');

    // Read the raw record from the {rdf_mapping} table.
    $result = db_query('SELECT * FROM {rdf_mapping} WHERE type = :type AND bundle = :bundle', array(':type' => $mapping['type'], ':bundle' => $mapping['bundle']));
    $stored_mapping = $result->fetchAssoc();
    $stored_mapping['mapping'] = unserialize($stored_mapping['mapping']);
    $this->assertEqual($mapping, $stored_mapping, 'Updated mapping was stored properly in the {rdf_mapping} table.');

    // Verify loading of saved mapping.
    $this->assertEqual($mapping['mapping'], _rdf_mapping_load($mapping['type'], $mapping['bundle']), 'Saved mapping loaded successfully.');

    // Verify deleting of mapping.
    $this->assertTrue(rdf_mapping_delete($mapping['type'], $mapping['bundle']), 'Mapping was deleted.');
    $this->assertFalse(_rdf_mapping_load($mapping['type'], $mapping['bundle']), 'Deleted mapping is no longer found in the database.');
  }
}

class RdfMappingDefinitionTestCase extends TaxonomyWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'RDF mapping definition functionality',
      'description' => 'Test the different types of RDF mappings and ensure the proper RDFa markup in included in nodes and user profile pages.',
      'group' => 'RDF',
    );
  }

  function setUp() {
    parent::setUp('rdf', 'rdf_test', 'blog');
  }

  /**
   * Create a node of type blog and test whether the RDF mapping defined for
   * this node type in rdf_test.module is used in the node page.
   */
  function testAttributesInMarkup1() {
    $node = $this->drupalCreateNode(array('type' => 'blog'));
    $isoDate = date('c', $node->changed);
    $url = url('node/' . $node->nid);
    $this->drupalGet('node/' . $node->nid);

    // Ensure the default bundle mapping for node is used. These attributes come
    // from the node default bundle definition.
    $blog_title = $this->xpath("//div[@about='$url']/span[@property='dc:title' and @content='$node->title']");
    $blog_meta = $this->xpath("//div[(@about='$url') and (@typeof='sioct:Weblog')]//span[contains(@property, 'dc:date') and contains(@property, 'dc:created') and @datatype='xsd:dateTime' and @content='$isoDate']");
    $this->assertTrue(!empty($blog_title), 'Property dc:title is present in meta tag.');
    $this->assertTrue(!empty($blog_meta), 'RDF type is present on post. Properties dc:date and dc:created are present on post date.');
  }

  /**
   * Create a content type and a node of type test_bundle_hook_install and test
   * whether the RDF mapping defined in rdf_test.install is used.
   */
  function testAttributesInMarkup2() {
    $type = $this->drupalCreateContentType(array('type' => 'test_bundle_hook_install'));
    // Create node with single quotation mark title to ensure it does not get
    // escaped more than once.
    $node = $this->drupalCreateNode(array(
      'type' => 'test_bundle_hook_install',
      'title' => $this->randomName(8) . "'",
    ));
    $isoDate = date('c', $node->changed);
    $url = url('node/' . $node->nid);
    $this->drupalGet('node/' . $node->nid);

    // Ensure the mapping defined in rdf_module.test is used.
    $test_bundle_title = $this->xpath("//div[@about='$url']/span[@property='dc:title' and @content=\"$node->title\"]");
    $test_bundle_meta = $this->xpath("//div[(@about='$url') and contains(@typeof, 'foo:mapping_install1') and contains(@typeof, 'bar:mapping_install2')]//span[contains(@property, 'dc:date') and contains(@property, 'dc:created') and @datatype='xsd:dateTime' and @content='$isoDate']");
    $this->assertTrue(!empty($test_bundle_title), 'Property dc:title is present in meta tag.');
    $this->assertTrue(!empty($test_bundle_meta), 'RDF type is present on post. Properties dc:date and dc:created are present on post date.');
  }

  /**
   * Create a random content type and node and ensure the default mapping for
   * node is used.
   */
  function testAttributesInMarkup3() {
    $type = $this->drupalCreateContentType();
    $node = $this->drupalCreateNode(array('type' => $type->type));
    $isoDate = date('c', $node->changed);
    $url = url('node/' . $node->nid);
    $this->drupalGet('node/' . $node->nid);

    // Ensure the default bundle mapping for node is used. These attributes come
    // from the node default bundle definition.
    $random_bundle_title = $this->xpath("//div[@about='$url']/span[@property='dc:title' and @content='$node->title']");
    $random_bundle_meta = $this->xpath("//div[(@about='$url') and contains(@typeof, 'sioc:Item') and contains(@typeof, 'foaf:Document')]//span[contains(@property, 'dc:date') and contains(@property, 'dc:created') and @datatype='xsd:dateTime' and @content='$isoDate']");
    $this->assertTrue(!empty($random_bundle_title), 'Property dc:title is present in meta tag.');
    $this->assertTrue(!empty($random_bundle_meta), 'RDF type is present on post. Properties dc:date and dc:created are present on post date.');
  }

  /**
   * Create a random user and ensure the default mapping for user is used.
   */
  function testUserAttributesInMarkup() {
    // Create two users, one with access to user profiles.
    $user1 = $this->drupalCreateUser(array('access user profiles'));
    $user2 = $this->drupalCreateUser();
    $username = $user2->name;
    $this->drupalLogin($user1);
    // Browse to the user profile page.
    $this->drupalGet('user/' . $user2->uid);
    // Ensure the default bundle mapping for user is used on the user profile
    // page. These attributes come from the user default bundle definition.
    $account_uri = url('user/' . $user2->uid);
    $person_uri = url('user/' . $user2->uid, array('fragment' => 'me'));

    $user2_profile_about = $this->xpath('//div[@class="profile" and @typeof="sioc:UserAccount" and @about=:account-uri]', array(
      ':account-uri' => $account_uri,
    ));
    $this->assertTrue(!empty($user2_profile_about), 'RDFa markup found on user profile page');

    $user_account_holder = $this->xpath('//meta[contains(@typeof, "foaf:Person") and @about=:person-uri and @resource=:account-uri and contains(@rel, "foaf:account")]', array(
      ':person-uri' => $person_uri,
      ':account-uri' => $account_uri,
    ));
    $this->assertTrue(!empty($user_account_holder), 'URI created for account holder and username set on sioc:UserAccount.');

    $user_username = $this->xpath('//meta[@about=:account-uri and contains(@property, "foaf:name") and @content=:username]', array(
      ':account-uri' => $account_uri,
      ':username' => $username,
    ));
    $this->assertTrue(!empty($user_username), 'foaf:name set on username.');

    // User 2 creates node.
    $this->drupalLogin($user2);
    $node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $this->drupalLogin($user1);
    $this->drupalGet('node/' . $node->nid);
    // Ensures the default bundle mapping for user is used on the Authored By
    // information on the node.
    $author_about = $this->xpath('//a[@typeof="sioc:UserAccount" and @about=:account-uri and @property="foaf:name" and @datatype="" and contains(@xml:lang, "")]', array(
      ':account-uri' => $account_uri,
    ));
    $this->assertTrue(!empty($author_about), 'RDFa markup found on author information on post. xml:lang on username is set to empty string.');
  }

  /**
   * Creates a random term and ensures the right RDFa markup is used.
   */
  function testTaxonomyTermRdfaAttributes() {
    $vocabulary = $this->createVocabulary();
    $term = $this->createTerm($vocabulary);

    // Views the term and checks that the RDFa markup is correct.
    $this->drupalGet('taxonomy/term/' . $term->tid);
    $term_url = url('taxonomy/term/' . $term->tid);
    $term_name = $term->name;
    $term_rdfa_meta = $this->xpath('//meta[@typeof="skos:Concept" and @about=:term-url and contains(@property, "rdfs:label") and contains(@property, "skos:prefLabel") and @content=:term-name]', array(
      ':term-url' => $term_url,
      ':term-name' => $term_name,
    ));
    $this->assertTrue(!empty($term_rdfa_meta), 'RDFa markup found on term page.');
  }
}

class RdfCommentAttributesTestCase extends CommentHelperCase {
  protected $node1;
  protected $node2;

  public static function getInfo() {
    return array(
      'name' => 'RDF comment mapping',
      'description' => 'Tests the RDFa markup of comments.',
      'group' => 'RDF',
    );
  }

  public function setUp() {
    parent::setUp('comment', 'rdf', 'rdf_test');

    $this->admin_user = $this->drupalCreateUser(array('administer content types', 'administer comments', 'administer permissions', 'administer blocks'));
    $this->web_user = $this->drupalCreateUser(array('access comments', 'post comments', 'create article content', 'access user profiles'));

    // Enables anonymous user comments.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => TRUE,
      'post comments' => TRUE,
      'skip comment approval' => TRUE,
    ));
    // Allows anonymous to leave their contact information.
    $this->setCommentAnonymous(COMMENT_ANONYMOUS_MAY_CONTACT);
    $this->setCommentPreview(DRUPAL_OPTIONAL);
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Comment paging changed.');

    // Creates the nodes on which the test comments will be posted.
    $this->drupalLogin($this->web_user);
    $this->node1 = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $this->node2 = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $this->drupalLogout();
  }

  /**
   * Tests the presence of the RDFa markup for the number of comments.
   */
  public function testNumberOfCommentsRdfaMarkup() {
    // Posts 2 comments as a registered user.
    $this->drupalLogin($this->web_user);
    $this->postComment($this->node1, $this->randomName(), $this->randomName());
    $this->postComment($this->node1, $this->randomName(), $this->randomName());

    // Tests number of comments in teaser view.
    $this->drupalGet('node');
    $node_url = url('node/' . $this->node1->nid);
    $comment_count_teaser = $this->xpath('//div[@about=:node-url]/span[@property="sioc:num_replies" and @content="2" and @datatype="xsd:integer"]', array(':node-url' => $node_url));
    $this->assertTrue(!empty($comment_count_teaser), 'RDFa markup for the number of comments found on teaser view.');

    // Tests number of comments in full node view.
    $this->drupalGet('node/' . $this->node1->nid);
    $comment_count_teaser = $this->xpath('//div[@about=:node-url]/span[@property="sioc:num_replies" and @content="2" and @datatype="xsd:integer"]', array(':node-url' => $node_url));
    $this->assertTrue(!empty($comment_count_teaser), 'RDFa markup for the number of comments found on full node view.');
  }

  /**
   * Tests the presence of the RDFa markup for the title, date and author and
   * homepage on registered users and anonymous comments.
   */
  public function testCommentRdfaMarkup() {

    // Posts comment #1 as a registered user.
    $this->drupalLogin($this->web_user);
    $comment1_subject = $this->randomName();
    $comment1_body = $this->randomName();
    $comment1 = $this->postComment($this->node1, $comment1_body, $comment1_subject);

    // Tests comment #1 with access to the user profile.
    $this->drupalGet('node/' . $this->node1->nid);
    $this->_testBasicCommentRdfaMarkup($comment1);

    // Tests comment #1 with no access to the user profile (as anonymous user).
    $this->drupalLogout();
    $this->drupalGet('node/' . $this->node1->nid);
    $this->_testBasicCommentRdfaMarkup($comment1);

    // Posts comment #2 as anonymous user.
    $comment2_subject = $this->randomName();
    $comment2_body = $this->randomName();
    $anonymous_user = array();
    $anonymous_user['name'] = $this->randomName();
    $anonymous_user['mail'] = '<EMAIL>';
    $anonymous_user['homepage'] = 'http://example.org/';
    $comment2 = $this->postComment($this->node2, $comment2_body, $comment2_subject, $anonymous_user);
    $this->drupalGet('node/' . $this->node2->nid);

    // Tests comment #2 as anonymous user.
    $this->_testBasicCommentRdfaMarkup($comment2, $anonymous_user);
    // Tests the RDFa markup for the homepage (specific to anonymous comments).
    $comment_homepage = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//span[@rel="sioc:has_creator"]/a[contains(@class, "username") and @typeof="sioc:UserAccount" and @property="foaf:name" and @datatype="" and @href="http://example.org/" and contains(@rel, "foaf:page")]');
    $this->assertTrue(!empty($comment_homepage), 'RDFa markup for the homepage of anonymous user found.');
    // There should be no about attribute on anonymous comments.
    $comment_homepage = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//span[@rel="sioc:has_creator"]/a[@about]');
    $this->assertTrue(empty($comment_homepage), 'No about attribute is present on anonymous user comment.');

    // Tests comment #2 as logged in user.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node/' . $this->node2->nid);
    $this->_testBasicCommentRdfaMarkup($comment2, $anonymous_user);
    // Tests the RDFa markup for the homepage (specific to anonymous comments).
    $comment_homepage = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//span[@rel="sioc:has_creator"]/a[contains(@class, "username") and @typeof="sioc:UserAccount" and @property="foaf:name" and @datatype="" and @href="http://example.org/" and contains(@rel, "foaf:page")]');
    $this->assertTrue(!empty($comment_homepage), "RDFa markup for the homepage of anonymous user found.");
    // There should be no about attribute on anonymous comments.
    $comment_homepage = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//span[@rel="sioc:has_creator"]/a[@about]');
    $this->assertTrue(empty($comment_homepage), "No about attribute is present on anonymous user comment.");
  }

  /**
   * Test RDF comment replies.
   */
  public function testCommentReplyOfRdfaMarkup() {
    // Posts comment #1 as a registered user.
    $this->drupalLogin($this->web_user);
    $comments[] = $this->postComment($this->node1, $this->randomName(), $this->randomName());

    // Tests the reply_of relationship of a first level comment.
    $result = $this->xpath("(id('comments')//div[contains(@class,'comment ')])[position()=1]//span[@rel='sioc:reply_of' and @resource=:node]", array(':node' => url("node/{$this->node1->nid}")));
    $this->assertEqual(1, count($result), 'RDFa markup referring to the node is present.');
    $result = $this->xpath("(id('comments')//div[contains(@class,'comment ')])[position()=1]//span[@rel='sioc:reply_of' and @resource=:comment]", array(':comment' => url('comment/1#comment-1')));
    $this->assertFalse($result, 'No RDFa markup referring to the comment itself is present.');

    // Posts a reply to the first comment.
    $this->drupalGet('comment/reply/' . $this->node1->nid . '/' . $comments[0]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // Tests the reply_of relationship of a second level comment.
    $result = $this->xpath("(id('comments')//div[contains(@class,'comment ')])[position()=2]//span[@rel='sioc:reply_of' and @resource=:node]", array(':node' => url("node/{$this->node1->nid}")));
    $this->assertEqual(1, count($result), 'RDFa markup referring to the node is present.');
    $result = $this->xpath("(id('comments')//div[contains(@class,'comment ')])[position()=2]//span[@rel='sioc:reply_of' and @resource=:comment]", array(':comment' => url('comment/1', array('fragment' => 'comment-1'))));
    $this->assertEqual(1, count($result), 'RDFa markup referring to the parent comment is present.');
    $comments = $this->xpath("(id('comments')//div[contains(@class,'comment ')])[position()=2]");
  }

  /**
   * Helper function for testCommentRdfaMarkup().
   *
   * Tests the current page for basic comment RDFa markup.
   *
   * @param $comment
   *   Comment object.
   * @param $account
   *   An array containing information about an anonymous user.
   */
  function _testBasicCommentRdfaMarkup($comment, $account = array()) {
    $comment_container = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]');
    $this->assertTrue(!empty($comment_container), "Comment RDF type for comment found.");
    $comment_title = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//h3[@property="dc:title"]');
    $this->assertEqual((string)$comment_title[0]->a, $comment->subject, "RDFa markup for the comment title found.");
    $comment_date = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//*[contains(@property, "dc:date") and contains(@property, "dc:created")]');
    $this->assertTrue(!empty($comment_date), "RDFa markup for the date of the comment found.");
    // The author tag can be either a or span
    $comment_author = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//span[@rel="sioc:has_creator"]/*[contains(@class, "username") and @typeof="sioc:UserAccount" and @property="foaf:name" and @datatype=""]');
    $name = empty($account["name"]) ? $this->web_user->name : $account["name"] . " (not verified)";
    $this->assertEqual((string)$comment_author[0], $name, "RDFa markup for the comment author found.");
    $comment_body = $this->xpath('//div[contains(@class, "comment") and contains(@typeof, "sioct:Comment")]//div[@class="content"]//div[contains(@class, "comment-body")]//div[@property="content:encoded"]');
    $this->assertEqual((string)$comment_body[0]->p, $comment->comment, "RDFa markup for the comment body found.");
  }
}

class RdfTrackerAttributesTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'RDF tracker page mapping',
      'description' => 'Test the mapping for the tracker page and ensure the proper RDFa markup in included.',
      'group' => 'RDF',
    );
  }

  function setUp() {
    parent::setUp('rdf', 'rdf_test', 'tracker');
    // Enable anonymous posting of content.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'create article content' => TRUE,
      'access comments' => TRUE,
      'post comments' => TRUE,
      'skip comment approval' => TRUE,
    ));
  }

  /**
   * Create nodes as both admin and anonymous user and test for correct RDFa
   * markup on the tracker page for those nodes and their comments.
   */
  function testAttributesInTracker() {
    // Create node as anonymous user.
    $node_anon = $this->drupalCreateNode(array('type' => 'article', 'uid' => 0));
    // Create node as admin user.
    $node_admin = $this->drupalCreateNode(array('type' => 'article', 'uid' => 1));

    // Pass both the anonymously posted node and the administrator posted node
    // through to test for the RDF attributes.
    $this->_testBasicTrackerRdfaMarkup($node_anon);
    $this->_testBasicTrackerRdfaMarkup($node_admin);

  }

  /**
   * Helper function for testAttributesInTracker().
   *
   * Tests the tracker page for RDFa markup.
   *
   * @param $node
   * The node just created.
   */
  function _testBasicTrackerRdfaMarkup($node) {
    $url = url('node/' . $node->nid);

    $user = ($node->uid == 0) ? 'Anonymous user' : 'Registered user';

    // Navigate to tracker page.
    $this->drupalGet('tracker');

    // Tests whether the about property is applied. This is implicit in the
    // success of the following tests, but making it explicit will make
    // debugging easier in case of failure.
    $tracker_about = $this->xpath('//tr[@about=:url]', array(':url' => $url));
    $this->assertTrue(!empty($tracker_about), format_string('About attribute found on table row for @user content.', array('@user'=> $user)));

    // Tests whether the title has the correct property attribute.
    $tracker_title = $this->xpath('//tr[@about=:url]/td[@property="dc:title" and @datatype=""]', array(':url' => $url));
    $this->assertTrue(!empty($tracker_title), format_string('Title property attribute found on @user content.', array('@user'=> $user)));

    // Tests whether the relationship between the content and user has been set.
    $tracker_user = $this->xpath('//tr[@about=:url]//td[contains(@rel, "sioc:has_creator")]//*[contains(@typeof, "sioc:UserAccount") and contains(@property, "foaf:name")]', array(':url' => $url));
    $this->assertTrue(!empty($tracker_user), format_string('Typeof and name property attributes found on @user.', array('@user'=> $user)));
    // There should be an about attribute on logged in users and no about
    // attribute for anonymous users.
    $tracker_user = $this->xpath('//tr[@about=:url]//td[@rel="sioc:has_creator"]/*[@about]', array(':url' => $url));
    if ($node->uid == 0) {
      $this->assertTrue(empty($tracker_user), format_string('No about attribute is present on @user.', array('@user'=> $user)));
    }
    elseif ($node->uid > 0) {
      $this->assertTrue(!empty($tracker_user), format_string('About attribute is present on @user.', array('@user'=> $user)));
    }

    // Tests whether the property has been set for number of comments.
    $tracker_replies = $this->xpath('//tr[@about=:url]//td[contains(@property, "sioc:num_replies") and contains(@content, "0") and @datatype="xsd:integer"]', array(':url' => $url));
    $this->assertTrue($tracker_replies, format_string('Num replies property and content attributes found on @user content.', array('@user'=> $user)));

    // Tests that the appropriate RDFa markup to annotate the latest activity
    // date has been added to the tracker output before comments have been
    // posted, meaning the latest activity reflects changes to the node itself.
    $isoDate = date('c', $node->changed);
    $tracker_activity = $this->xpath('//tr[@about=:url]//td[contains(@property, "dc:modified") and contains(@property, "sioc:last_activity_date") and contains(@datatype, "xsd:dateTime") and @content=:date]', array(':url' => $url, ':date' => $isoDate));
    $this->assertTrue(!empty($tracker_activity), format_string('Latest activity date and changed properties found when there are no comments on @user content. Latest activity date content is correct.', array('@user'=> $user)));

    // Tests that the appropriate RDFa markup to annotate the latest activity
    // date has been added to the tracker output after a comment is posted.
    $comment = array(
      'subject' => $this->randomName(),
      'comment_body[' . LANGUAGE_NONE . '][0][value]' => $this->randomName(),
    );
    $this->drupalPost('comment/reply/' . $node->nid, $comment, t('Save'));
    $this->drupalGet('tracker');

    // Tests whether the property has been set for number of comments.
    $tracker_replies = $this->xpath('//tr[@about=:url]//td[contains(@property, "sioc:num_replies") and contains(@content, "1") and @datatype="xsd:integer"]', array(':url' => $url));
    $this->assertTrue($tracker_replies, format_string('Num replies property and content attributes found on @user content.', array('@user'=> $user)));

    // Need to query database directly to obtain last_activity_date because
    // it cannot be accessed via node_load().
    $result = db_query('SELECT t.changed FROM {tracker_node} t WHERE t.nid = (:nid)', array(':nid' => $node->nid));
    foreach ($result as $node) {
      $expected_last_activity_date = $node->changed;
    }
    $isoDate = date('c', $expected_last_activity_date);
    $tracker_activity = $this->xpath('//tr[@about=:url]//td[@property="sioc:last_activity_date" and @datatype="xsd:dateTime" and @content=:date]', array(':url' => $url, ':date' => $isoDate));
    $this->assertTrue(!empty($tracker_activity), format_string('Latest activity date found when there are comments on @user content. Latest activity date content is correct.', array('@user'=> $user)));
  }
}

/**
 * Tests for RDF namespaces declaration with hook_rdf_namespaces().
 */
class RdfGetRdfNamespacesTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'RDF namespaces',
      'description' => 'Test hook_rdf_namespaces() and ensure only "safe" namespaces are returned.',
      'group' => 'RDF',
    );
  }

  function setUp() {
    parent::setUp('rdf', 'rdf_test');
  }

  /**
   * Test getting RDF namesapces.
   */
  function testGetRdfNamespaces() {
    // Get all RDF namespaces.
    $ns = rdf_get_namespaces();

    $this->assertEqual($ns['rdfs'], 'http://www.w3.org/2000/01/rdf-schema#', 'A prefix declared once is included.');
    $this->assertEqual($ns['foaf'], 'http://xmlns.com/foaf/0.1/', 'The same prefix declared in several implementations of hook_rdf_namespaces() is valid as long as all the namespaces are the same.');
    $this->assertEqual($ns['foaf1'], 'http://xmlns.com/foaf/0.1/', 'Two prefixes can be assigned the same namespace.');
    $this->assertTrue(!isset($ns['dc']), 'A prefix with conflicting namespaces is discarded.');
  }
}
