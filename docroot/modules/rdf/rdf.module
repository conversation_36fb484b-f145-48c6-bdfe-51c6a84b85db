<?php

/**
 * @file
 * Enables semantically enriched output for Drupal sites in the form of RDFa.
 */

/**
 * Implements hook_help().
 */
function rdf_help($path, $arg) {
  switch ($path) {
    case 'admin/help#rdf':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The RDF module enriches your content with metadata to let other applications (e.g., search engines, aggregators, and so on) better understand its relationships and attributes. This semantically enriched, machine-readable output for Drupal sites uses the <a href="@rdfa">RDFa specification</a> which allows RDF data to be embedded in HTML markup. Other modules can define mappings of their data to RDF terms, and the RDF module makes this RDF data available to the theme. The core Drupal modules define RDF mappings for their data model, and the core Drupal themes output this RDF metadata information along with the human-readable visual information. For more information, see the online handbook entry for <a href="@rdf">RDF module</a>.', array('@rdfa' => 'http://www.w3.org/TR/xhtml-rdfa-primer/', '@rdf' => 'http://drupal.org/documentation/modules/rdf')) . '</p>';
      return $output;
  }
}

/**
 * @defgroup rdf RDF Mapping API
 * @{
 * Functions to describe entities and bundles in RDF.
 *
 * The RDF module introduces RDF and RDFa to Drupal. RDF is a W3C standard to
 * describe structured data. RDF can be serialized as RDFa in XHTML attributes
 * to augment visual data with machine-readable hints.
 * @see http://www.w3.org/RDF/
 * @see http://www.w3.org/TR/xhtml-rdfa-primer/
 *
 * Modules can provide mappings of their bundles' data and metadata to RDF
 * classes and properties. This module takes care of injecting these mappings
 * into variables available to theme functions and templates. All Drupal core
 * themes are coded to be RDFa compatible.
 *
 * Example mapping from node.module:
 * @code
 *   array(
 *     'type' => 'node',
 *     'bundle' => RDF_DEFAULT_BUNDLE,
 *     'mapping' => array(
 *       'rdftype' => array('sioc:Item', 'foaf:Document'),
 *       'title' => array(
 *         'predicates' => array('dc:title'),
 *       ),
 *       'created' => array(
 *         'predicates' => array('dc:date', 'dc:created'),
 *         'datatype' => 'xsd:dateTime',
 *         'callback' => 'date_iso8601',
 *       ),
 *      'body' => array(
 *         'predicates' => array('content:encoded'),
 *       ),
 *       'uid' => array(
 *         'predicates' => array('sioc:has_creator'),
 *       ),
 *       'name' => array(
 *         'predicates' => array('foaf:name'),
 *       ),
 *     ),
 *   );
 * @endcode
 */

/**
 * RDF bundle flag: Default bundle.
 *
 * Implementations of hook_rdf_mapping() should use this constant for the
 * 'bundle' key when defining a default set of RDF mappings for an entity type.
 * Each bundle will inherit the default mappings defined for the entity type
 * unless the bundle defines its own specific mappings.
 */
define('RDF_DEFAULT_BUNDLE', '');

/**
 * Implements hook_rdf_namespaces().
 */
function rdf_rdf_namespaces() {
  return array(
    'content'  => 'http://purl.org/rss/1.0/modules/content/',
    'dc'       => 'http://purl.org/dc/terms/',
    'foaf'     => 'http://xmlns.com/foaf/0.1/',
    'og'       => 'http://ogp.me/ns#',
    'rdfs'     => 'http://www.w3.org/2000/01/rdf-schema#',
    'sioc'     => 'http://rdfs.org/sioc/ns#',
    'sioct'    => 'http://rdfs.org/sioc/types#',
    'skos'     => 'http://www.w3.org/2004/02/skos/core#',
    'xsd'      => 'http://www.w3.org/2001/XMLSchema#',
  );
}

/**
 * Returns an array of RDF namespaces defined in modules that implement
 * hook_rdf_namespaces().
 */
function rdf_get_namespaces() {
  $rdf_namespaces = module_invoke_all('rdf_namespaces');
  // module_invoke_all() uses array_merge_recursive() which might return nested
  // arrays if several modules redefine the same prefix multiple times. We need
  // to ensure the array of namespaces is flat and only contains strings as
  // URIs.
  foreach ($rdf_namespaces as $prefix => $uri) {
    if (is_array($uri)) {
      if (count(array_unique($uri)) == 1) {
        // All namespaces declared for this prefix are the same, merge them all
        // into a single namespace.
        $rdf_namespaces[$prefix] = $uri[0];
      }
      else {
        // There are conflicting namespaces for this prefix, do not include
        // duplicates in order to avoid asserting any inaccurate RDF
        // statements.
        unset($rdf_namespaces[$prefix]);
      }
    }
  }
  return $rdf_namespaces;
}

/**
 * Returns the mapping for attributes of a given entity type/bundle pair.
 *
 * @param $type
 *   An entity type.
 * @param $bundle
 *   (optional) A bundle name.
 *
 * @return
 *   The mapping corresponding to the requested entity type/bundle pair or an
 *   empty array.
 */
function rdf_mapping_load($type, $bundle = RDF_DEFAULT_BUNDLE) {
  // Retrieves the bundle-specific mapping from the entity info.
  $entity_info = entity_get_info($type);
  if (!empty($entity_info['bundles'][$bundle]['rdf_mapping'])) {
    return $entity_info['bundles'][$bundle]['rdf_mapping'];
  }
  // If there is no mapping defined for this bundle, we return the default
  // mapping that is defined for this entity type.
  else {
    return _rdf_get_default_mapping($type);
  }
}

/**
 * @} End of "defgroup rdf".
 */

/**
 * Helper function to get the default RDF mapping for a given entity type.
 *
 * @param $type
 *   An entity type, e.g. 'node' or 'comment'.
 *
 * @return
 *   The RDF mapping or an empty array if no mapping is defined for this entity
 *   type.
 */
function _rdf_get_default_mapping($type) {
  $default_mappings = &drupal_static(__FUNCTION__);

  if (!isset($default_mappings)) {
    // Get all of the modules that implement hook_rdf_mapping().
    $modules = module_implements('rdf_mapping');

    // Only consider the default entity mapping definitions.
    foreach ($modules as $module) {
      $mappings = module_invoke($module, 'rdf_mapping');
      foreach ($mappings as $mapping) {
        if ($mapping['bundle'] === RDF_DEFAULT_BUNDLE) {
          $default_mappings[$mapping['type']] = $mapping['mapping'];
        }
      }
    }
  }

  return isset($default_mappings[$type]) ? $default_mappings[$type] : array();
}

/**
 * Helper function to retrieve an RDF mapping from the database.
 *
 * @param $type
 *   The entity type the mapping refers to.
 * @param $bundle
 *   The bundle the mapping refers to.
 *
 * @return
 *   An RDF mapping structure or an empty array if no record was found.
 */
function _rdf_mapping_load($type, $bundle) {
  $mappings = _rdf_mapping_load_multiple($type, array($bundle));
  return $mappings ? reset($mappings) : array();
}

/**
 * Helper function to retrieve a set of RDF mappings from the database.
 *
 * @param $type
 *   The entity type of the mappings.
 * @param $bundles
 *   The bundles the mappings refer to.
 *
 * @return
 *   An array of RDF mapping structures, or an empty array.
 */
function _rdf_mapping_load_multiple($type, array $bundles) {
  $mappings = db_select('rdf_mapping')
    ->fields(NULL, array('bundle', 'mapping'))
    ->condition('type', $type)
    ->condition('bundle', $bundles)
    ->execute()
    ->fetchAllKeyed();

  foreach ($mappings as $bundle => $mapping) {
    $mappings[$bundle] = unserialize($mapping);
  }
  return $mappings;
}

/**
 * @addtogroup rdf
 * @{
 */

/**
 * Saves an RDF mapping to the database.
 *
 * Takes a mapping structure returned by hook_rdf_mapping() implementations
 * and creates or updates a record mapping for each encountered entity
 * type/bundle pair. If available, adds default values for non-existent mapping
 * keys.
 *
 * @param $mapping
 *   The RDF mapping to save, as an array.
 *
 * @return
 *   Status flag indicating the outcome of the operation.
 */
function rdf_mapping_save($mapping) {
  // In the case where a field has a mapping defined in the default entity
  // mapping, but a mapping is not specified in the bundle-specific mapping,
  // then use the default mapping for that field.
  $mapping['mapping'] += _rdf_get_default_mapping($mapping['type']);

  $status = db_merge('rdf_mapping')
    ->key(array(
      'type' => $mapping['type'],
      'bundle' => $mapping['bundle'],
    ))
    ->fields(array(
      'mapping' => serialize($mapping['mapping']),
    ))
    ->execute();

  entity_info_cache_clear();

  return $status;
}

/**
 * Deletes the mapping for the given bundle from the database.
 *
 * @param $type
 *   The entity type the mapping refers to.
 * @param $bundle
 *   The bundle the mapping refers to.
 *
 * @return
 *   Return boolean TRUE if mapping deleted, FALSE if not.
 */
function rdf_mapping_delete($type, $bundle) {
  $num_rows = db_delete('rdf_mapping')
    ->condition('type', $type)
    ->condition('bundle', $bundle)
    ->execute();

  return (bool) ($num_rows > 0);
}

/**
 * Builds an array of RDFa attributes for a given mapping. This array will
 * typically be passed through drupal_attributes() to create the attributes
 * variables that are available to template files. These include $attributes,
 * $title_attributes, $content_attributes and the field-specific
 * $item_attributes variables. For more information, see
 * theme_rdf_template_variable_wrapper().
 *
 * @param $mapping
 *   An array containing a mandatory 'predicates' key and optional 'datatype',
 *   'callback' and 'type' keys. For example:
 *   @code
 *     array(
 *       'predicates' => array('dc:created'),
 *       'datatype' => 'xsd:dateTime',
 *       'callback' => 'date_iso8601',
 *       ),
 *     );
 *   @endcode
 * @param $data
 *   A value that needs to be converted by the provided callback function.
 *
 * @return
 *   An array containing RDFa attributes suitable for drupal_attributes().
 */
function rdf_rdfa_attributes($mapping, $data = NULL) {
  // The type of mapping defaults to 'property'.
  $type = isset($mapping['type']) ? $mapping['type'] : 'property';

  switch ($type) {
    // The mapping expresses the relationship between two resources.
    case 'rel':
    case 'rev':
      $attributes[$type] = $mapping['predicates'];
      break;

    // The mapping expresses the relationship between a resource and some
    // literal text.
    case 'property':
      $attributes['property'] = $mapping['predicates'];
      // Convert $data to a specific format as per the callback function.
      if (isset($data) && isset($mapping['callback']) && function_exists($mapping['callback'])) {
        $callback = $mapping['callback'];
        $attributes['content'] = $callback($data);
      }
      if (isset($mapping['datatype'])) {
        $attributes['datatype'] = $mapping['datatype'];
      }
      break;
  }

  return $attributes;
}

/**
 * @} End of "addtogroup rdf".
 */

/**
 * Implements hook_modules_installed().
 *
 * Checks if the installed modules have any RDF mapping definitions to declare
 * and stores them in the rdf_mapping table.
 *
 * While both default entity mappings and specific bundle mappings can be
 * defined in hook_rdf_mapping(), default entity mappings are not stored in the
 * database. Only overridden mappings are stored in the database. The default
 * entity mappings can be overriden by specific bundle mappings which are
 * stored in the database and can be altered via the RDF CRUD mapping API.
 */
function rdf_modules_installed($modules) {
  foreach ($modules as $module) {
    $function = $module . '_rdf_mapping';
    if (function_exists($function)) {
      foreach ($function() as $mapping) {
        // Only the bundle mappings are saved in the database.
        if ($mapping['bundle'] !== RDF_DEFAULT_BUNDLE) {
          rdf_mapping_save($mapping);
        }
      }
    }
  }
}

/**
 * Implements hook_modules_uninstalled().
 */
function rdf_modules_uninstalled($modules) {
  // @todo Remove RDF mappings of uninstalled modules.
}

/**
 * Implements hook_entity_info_alter().
 *
 * Adds the proper RDF mapping to each entity type/bundle pair.
 *
 * @todo May need to move the comment below to another place.
 * This hook should not be used by modules to alter the bundle mappings.
 * The UI should always be authoritative. UI mappings are stored in the
 * database and if hook_entity_info_alter was used to override module defined
 * mappings, it would override the user defined mapping as well.
 */
function rdf_entity_info_alter(&$entity_info) {
  // Loop through each entity type and its bundles.
  foreach ($entity_info as $entity_type => $entity_type_info) {
    if (!empty($entity_type_info['bundles'])) {
      $bundles = array_keys($entity_type_info['bundles']);
      $mappings = _rdf_mapping_load_multiple($entity_type, $bundles);

      foreach ($bundles as $bundle) {
        if (isset($mappings[$bundle])) {
          $entity_info[$entity_type]['bundles'][$bundle]['rdf_mapping'] = $mappings[$bundle];
        }
        else {
          // If no mapping was found in the database, assign the default RDF
          // mapping for this entity type.
          $entity_info[$entity_type]['bundles'][$bundle]['rdf_mapping'] = _rdf_get_default_mapping($entity_type);
        }
      }
    }
  }
}

/**
 * Implements hook_entity_load().
 */
function rdf_entity_load($entities, $type) {
  foreach ($entities as $entity) {
    // Extracts the bundle of the entity being loaded.
    list($id, $vid, $bundle) = entity_extract_ids($type, $entity);
    $entity->rdf_mapping = rdf_mapping_load($type, $bundle);
  }
}

/**
 * Implements hook_comment_load().
 */
function rdf_comment_load($comments) {
  foreach ($comments as $comment) {
    // Pages with many comments can show poor performance. This information
    // isn't needed until rdf_preprocess_comment() is called, but set it here
    // to optimize performance for websites that implement an entity cache.
    $comment->rdf_data['date'] = rdf_rdfa_attributes($comment->rdf_mapping['created'], $comment->created);
    $comment->rdf_data['nid_uri'] = url('node/' . $comment->nid);
    if ($comment->pid) {
      $comment->rdf_data['pid_uri'] = url('comment/' . $comment->pid, array('fragment' => 'comment-' . $comment->pid));
    }
  }
}

/**
 * Implements hook_theme().
 */
function rdf_theme() {
  return array(
    'rdf_template_variable_wrapper' => array(
      'variables' => array('content' => NULL, 'attributes' => array(), 'context' => array(), 'inline' => TRUE),
    ),
    'rdf_metadata' => array(
      'variables' => array('metadata' => array()),
    ),
  );
}

/**
 * Template process function for adding extra tags to hold RDFa attributes.
 *
 * Since template files already have built-in support for $attributes,
 * $title_attributes, and $content_attributes, and field templates have support
 * for $item_attributes, we try to leverage those as much as possible. However,
 * in some cases additional attributes are needed not covered by these. We deal
 * with those here.
 */
function rdf_process(&$variables, $hook) {
  // Handles attributes needed for content not covered by title, content,
  // and field items. It does this by adjusting the variable sent to the
  // template so that the template doesn't have to worry about it. See
  // theme_rdf_template_variable_wrapper().
  if (!empty($variables['rdf_template_variable_attributes_array'])) {
    foreach ($variables['rdf_template_variable_attributes_array'] as $variable_name => $attributes) {
      $context = array(
        'hook' => $hook,
        'variable_name' => $variable_name,
        'variables' => $variables,
      );
      $variables[$variable_name] = theme('rdf_template_variable_wrapper', array('content' => $variables[$variable_name], 'attributes' => $attributes, 'context' => $context));
    }
  }
  // Handles additional attributes about a template entity that for RDF parsing
  // reasons, can't be placed into that template's $attributes variable. This
  // is "meta" information that is related to particular content, so render it
  // close to that content.
  if (!empty($variables['rdf_metadata_attributes_array'])) {
    if (!isset($variables['content']['#prefix'])) {
      $variables['content']['#prefix'] = '';
    }
    $variables['content']['#prefix'] = theme('rdf_metadata', array('metadata' => $variables['rdf_metadata_attributes_array'])) . $variables['content']['#prefix'];
  }
}

/**
 * Implements MODULE_preprocess_HOOK().
 */
function rdf_preprocess_node(&$variables) {
  // Adds RDFa markup to the node container. The about attribute specifies the
  // URI of the resource described within the HTML element, while the @typeof
  // attribute indicates its RDF type (e.g., foaf:Document, sioc:Person, and so
  // on.)
  $variables['attributes_array']['about'] = empty($variables['node_url']) ? NULL: $variables['node_url'];
  $variables['attributes_array']['typeof'] = empty($variables['node']->rdf_mapping['rdftype']) ? NULL : $variables['node']->rdf_mapping['rdftype'];

  // Adds RDFa markup about the title of the node to the title_suffix.
  if (!empty($variables['node']->rdf_mapping['title']['predicates'])) {
    $variables['title_suffix']['rdf_meta_title'] = array(
      '#theme' => 'rdf_metadata',
      '#metadata' => array(
        array(
          'property' => $variables['node']->rdf_mapping['title']['predicates'],
          'content' => $variables['node']->title,
        ),
      ),
    );
  }

  // Adds RDFa markup for the date.
  if (!empty($variables['rdf_mapping']['created'])) {
    $date_attributes_array = rdf_rdfa_attributes($variables['rdf_mapping']['created'], $variables['created']);
    $variables['rdf_template_variable_attributes_array']['date'] = $date_attributes_array;
    if ($variables['submitted']) {
      $variables['rdf_template_variable_attributes_array']['submitted'] = $date_attributes_array;
    }
  }
  // Adds RDFa markup for the relation between the node and its author.
  if (!empty($variables['rdf_mapping']['uid'])) {
    $variables['rdf_template_variable_attributes_array']['name']['rel'] = $variables['rdf_mapping']['uid']['predicates'];
    if ($variables['submitted']) {
      $variables['rdf_template_variable_attributes_array']['submitted']['rel'] = $variables['rdf_mapping']['uid']['predicates'];
    }
  }

  // Adds RDFa markup annotating the number of comments a node has.
  if (isset($variables['node']->comment_count) &&
      !empty($variables['node']->rdf_mapping['comment_count']['predicates']) &&
      user_access('access comments')) {
    // Adds RDFa markup for the comment count near the node title as metadata.
    $variables['title_suffix']['rdf_meta_comment_count'] = array(
      '#theme' => 'rdf_metadata',
      '#metadata' => array(
        array(
          'property' => $variables['node']->rdf_mapping['comment_count']['predicates'],
          'content' => $variables['node']->comment_count,
          'datatype' => $variables['node']->rdf_mapping['comment_count']['datatype'],
        ),
      ),
    );
  }
}

/**
 * Implements MODULE_preprocess_HOOK().
 */
function rdf_preprocess_field(&$variables) {
  $element = $variables['element'];
  $mapping = rdf_mapping_load($element['#entity_type'], $element['#bundle']);
  $field_name = $element['#field_name'];

  if (!empty($mapping) && !empty($mapping[$field_name])) {
    foreach ($element['#items'] as $delta => $item) {
      $variables['item_attributes_array'][$delta] = rdf_rdfa_attributes($mapping[$field_name], $item);
      // If this field is an image, RDFa will not output correctly when the
      // image is in a containing <a> tag. If the field is a file, RDFa will
      // not output correctly if the filetype icon comes before the link to the
      // file. We correct this by adding a resource attribute to the div if
      // this field has a URI.
      if (isset($item['uri'])) {
        if (!empty($element[$delta]['#image_style'])) {
          $variables['item_attributes_array'][$delta]['resource'] = image_style_url($element[$delta]['#image_style'], $item['uri']);
        }
        else {
          $variables['item_attributes_array'][$delta]['resource'] = file_create_url($item['uri']);
        }
      }
    }
  }
}

/**
 * Implements MODULE_preprocess_HOOK().
 */
function rdf_preprocess_user_profile(&$variables) {
  $account = $variables['elements']['#account'];
  $uri = entity_uri('user', $account);

  // Adds RDFa markup to the user profile page. Fields displayed in this page
  // will automatically describe the user.
  if (!empty($account->rdf_mapping['rdftype'])) {
    $variables['attributes_array']['typeof'] = $account->rdf_mapping['rdftype'];
    $variables['attributes_array']['about'] = url($uri['path'], $uri['options']);
  }
  // Adds the relationship between the sioc:UserAccount and the foaf:Person who
  // holds the account.
  $account_holder_meta = array(
    '#tag' => 'meta',
    '#attributes' => array(
      'about' => url($uri['path'], array_merge($uri['options'], array('fragment' => 'me'))),
      'typeof' => array('foaf:Person'),
      'rel' => array('foaf:account'),
      'resource' => url($uri['path'], $uri['options']),
    ),
  );
  // Adds the markup for username.
  $username_meta = array(
    '#tag' => 'meta',
    '#attributes' => array(
      'about' => url($uri['path'], $uri['options']),
      'property' => $account->rdf_mapping['name']['predicates'],
      'content' => $account->name,
    )
  );
  drupal_add_html_head($account_holder_meta, 'rdf_user_account_holder');
  drupal_add_html_head($username_meta, 'rdf_user_username');
}

/**
 * Implements MODULE_preprocess_HOOK().
 */
function rdf_preprocess_username(&$variables) {
  // Because xml:lang is set on the HTML element that wraps the page, the
  // username inherits this language attribute. However, since the username
  // might not be transliterated to the same language that the content is in,
  // we do not want it to inherit the language attribute, so we set the
  // attribute to an empty string.
  if (empty($variables['attributes_array']['xml:lang'])) {
    $variables['attributes_array']['xml:lang'] = '';
  }

  // $variables['account'] is a pseudo account object, and as such, does not
  // contain the RDF mappings for the user. In the case of nodes and comments,
  // it contains the mappings for the node or comment object instead. However,
  // while the RDF mappings are available from a full user_load(), this should
  // be avoided for performance reasons. Since the type and bundle for users is
  // already known, call rdf_mapping_load() directly.
  $rdf_mapping = rdf_mapping_load('user', 'user');

  // The profile URI is used to identify the user account. The about attribute
  // is used to set the URI as the default subject of the predicates embedded
  // as RDFa in the child elements. Even if the user profile is not accessible
  // to the current user, we use its URI in order to identify the user in RDF.
  // We do not use this attribute for the anonymous user because we do not have
  // a user profile URI for it (only a homepage which cannot be used as user
  // profile in RDF.)
  if ($variables['uid'] > 0) {
    $variables['attributes_array']['about'] = url('user/' . $variables['uid']);
  }

  $attributes = array();
  // The typeof attribute specifies the RDF type(s) of this resource. They
  // are defined in the 'rdftype' property of the user RDF mapping.
  if (!empty($rdf_mapping['rdftype'])) {
    $attributes['typeof'] = $rdf_mapping['rdftype'];
  }
  // Annotate the username in RDFa. A property attribute is used with an empty
  // datatype attribute to ensure the username is parsed as a plain literal
  // in RDFa 1.0 and 1.1.
  if (!empty($rdf_mapping['name'])) {
    $attributes['property'] = $rdf_mapping['name']['predicates'];
    $attributes['datatype'] = '';
  }
  // Add the homepage RDFa markup if present.
  if (!empty($variables['homepage']) && !empty($rdf_mapping['homepage'])) {
    $attributes['rel'] = $rdf_mapping['homepage']['predicates'];
  }
  // The remaining attributes can have multiple values listed, with whitespace
  // separating the values in the RDFa attributes
  // (see http://www.w3.org/TR/rdfa-syntax/#rdfa-attributes).
  // Therefore, merge rather than override so as not to clobber values set by
  // earlier preprocess functions.
  $variables['attributes_array'] = array_merge_recursive($variables['attributes_array'], $attributes);
}

/**
 * Implements MODULE_preprocess_HOOK().
 */
function rdf_preprocess_comment(&$variables) {
  $comment = $variables['comment'];
  if (!empty($comment->rdf_mapping['rdftype'])) {
    // Adds RDFa markup to the comment container. The about attribute specifies
    // the URI of the resource described within the HTML element, while the
    // typeof attribute indicates its RDF type (e.g., sioc:Post, foaf:Document,
    // and so on.)
    $uri = entity_uri('comment', $comment);
    $variables['attributes_array']['about'] = url($uri['path'], $uri['options']);
    $variables['attributes_array']['typeof'] = $comment->rdf_mapping['rdftype'];
  }

  // Adds RDFa markup for the date of the comment.
  if (!empty($comment->rdf_mapping['created'])) {
    // The comment date is precomputed as part of the rdf_data so that it can be
    // cached as part of the entity.
    $date_attributes_array = $comment->rdf_data['date'];
    $variables['rdf_template_variable_attributes_array']['created'] = $date_attributes_array;
    $variables['rdf_template_variable_attributes_array']['submitted'] = $date_attributes_array;
  }
  // Adds RDFa markup for the relation between the comment and its author.
  if (!empty($comment->rdf_mapping['uid'])) {
    $variables['rdf_template_variable_attributes_array']['author']['rel'] = $comment->rdf_mapping['uid']['predicates'];
    $variables['rdf_template_variable_attributes_array']['submitted']['rel'] = $comment->rdf_mapping['uid']['predicates'];
  }
  if (!empty($comment->rdf_mapping['title'])) {
    // Adds RDFa markup to the subject of the comment. Because the RDFa markup
    // is added to an <h3> tag which might contain HTML code, we specify an
    // empty datatype to ensure the value of the title read by the RDFa parsers
    // is a literal.
    $variables['title_attributes_array']['property'] = $comment->rdf_mapping['title']['predicates'];
    $variables['title_attributes_array']['datatype'] = '';
  }

  // Annotates the parent relationship between the current comment and the node
  // it belongs to. If available, the parent comment is also annotated.
  if (!empty($comment->rdf_mapping['pid'])) {
    // Adds the relation to the parent node.
    $parent_node_attributes['rel'] = $comment->rdf_mapping['pid']['predicates'];
    // The parent node URI is precomputed as part of the rdf_data so that it can
    // be cached as part of the entity.
    $parent_node_attributes['resource'] = $comment->rdf_data['nid_uri'];
    $variables['rdf_metadata_attributes_array'][] = $parent_node_attributes;

    // Adds the relation to parent comment, if it exists.
    if ($comment->pid != 0) {
      $parent_comment_attributes['rel'] = $comment->rdf_mapping['pid']['predicates'];
      // The parent comment URI is precomputed as part of the rdf_data so that
      // it can be cached as part of the entity.
      $parent_comment_attributes['resource'] = $comment->rdf_data['pid_uri'];
      $variables['rdf_metadata_attributes_array'][] = $parent_comment_attributes;
    }
  }
}

/**
 * Implements MODULE_preprocess_HOOK().
 */
function rdf_preprocess_taxonomy_term(&$variables) {
  // Adds the RDF type of the term and the term name in a <meta> tag.
  $term = $variables['term'];
  $term_label_meta = array(
      '#tag' => 'meta',
      '#attributes' => array(
        'about' => url('taxonomy/term/' . $term->tid),
        'typeof' => $term->rdf_mapping['rdftype'],
        'property' => $term->rdf_mapping['name']['predicates'],
        'content' => $term->name,
      ),
    );
  drupal_add_html_head($term_label_meta, 'rdf_term_label');
}

/**
 * Implements hook_field_attach_view_alter().
 */
function rdf_field_attach_view_alter(&$output, $context) {
  // Append term mappings on displayed taxonomy links.
  foreach (element_children($output) as $field_name) {
    $element = &$output[$field_name];
    if ($element['#field_type'] == 'taxonomy_term_reference' && $element['#formatter'] == 'taxonomy_term_reference_link') {
      foreach ($element['#items'] as $delta => $item) {
        // This function is invoked during entity preview when taxonomy term
        // reference items might contain free-tagging terms that do not exist
        // yet and thus have no $item['taxonomy_term'].
        if (isset($item['taxonomy_term'])) {
          $term = $item['taxonomy_term'];
          if (!empty($term->rdf_mapping['rdftype'])) {
            $element[$delta]['#options']['attributes']['typeof'] = $term->rdf_mapping['rdftype'];
          }
          if (!empty($term->rdf_mapping['name']['predicates'])) {
            // A property attribute is used with an empty datatype attribute so
            // the term name is parsed as a plain literal in RDFa 1.0 and 1.1.
            $element[$delta]['#options']['attributes']['property'] = $term->rdf_mapping['name']['predicates'];
            $element[$delta]['#options']['attributes']['datatype'] = '';
          }
        }
      }
    }
  }
}

/**
 * Implements MODULE_preprocess_HOOK().
 */
function rdf_preprocess_image(&$variables) {
  // Adds the RDF type for image. We cannot use the usual entity-based mapping
  // to get 'foaf:Image' because image does not have its own entity type or
  // bundle.
  $variables['attributes']['typeof'] = array('foaf:Image');
}

/**
 * Returns HTML for a template variable wrapped in an HTML element with the
 * RDF attributes.
 *
 * This is called by rdf_process() shortly before the theme system renders
 * a template file. It is called once for each template variable for which
 * additional attributes are needed. While template files are responsible for
 * rendering the attributes for the template's primary object (via the
 * $attributes variable), title (via the $title_attributes variable), and
 * content (via the $content_attributes variable), additional template
 * variables that need containing attributes are routed through this function,
 * allowing the template file to receive properly wrapped variables.
 *
 * Tip for themers: if you're already outputting a wrapper element around a
 * particular template variable in your template file, and if you don't want
 * an extra wrapper element, you can override this function to not wrap that
 * variable and instead print the following inside your template file:
 * @code
 *   drupal_attributes($rdf_template_variable_attributes_array[$variable_name])
 * @endcode
 *
 * @param $variables
 *   An associative array containing:
 *   - content: A string of content to be wrapped with attributes.
 *   - attributes: An array of attributes to be placed on the wrapping element.
 *   - context: An array of context information about the content to be wrapped:
 *     - hook: The theme hook that will use the wrapped content. This
 *       corresponds to the key within the theme registry for this template.
 *       For example, if this content is about to be used in node.tpl.php or
 *       node-[type].tpl.php, then the 'hook' is 'node'.
 *     - variable_name: The name of the variable by which the template will
 *       refer to this content. Each template file has documentation about
 *       the variables it uses. For example, if this function is called in
 *       preparing the $author variable for comment.tpl.php, then the
 *       'variable_name' is 'author'.
 *     - variables: The full array of variables about to be passed to the
 *       template.
 *   - inline: TRUE if the content contains only inline HTML elements and
 *     therefore can be validly wrapped by a <span> tag. FALSE if the content
 *     might contain block level HTML elements and therefore cannot be validly
 *     wrapped by a <span> tag. Modules implementing preprocess functions that
 *     set 'rdf_template_variable_attributes_array' for a particular template
 *     variable that might contain block level HTML must also implement
 *     hook_preprocess_rdf_template_variable_wrapper() and set 'inline' to FALSE
 *     for that context. Themes that render normally inline content with block
 *     level HTML must similarly implement
 *     hook_preprocess_rdf_template_variable_wrapper() and set 'inline'
 *     accordingly.
 *
 * @see rdf_process()
 * @ingroup themeable
 * @ingroup rdf
 */
function theme_rdf_template_variable_wrapper($variables) {
  $output = $variables['content'];
  if (!empty($output) && !empty($variables['attributes'])) {
    $attributes = drupal_attributes($variables['attributes']);
    $output = $variables['inline'] ? "<span$attributes>$output</span>" : "<div$attributes>$output</div>";
  }
  return $output;
}

/**
 * Returns HTML for a series of empty spans for exporting RDF metadata in RDFa.
 *
 * Sometimes it is useful to export data which is not semantically present in
 * the HTML output. For example, a hierarchy of comments is visible for a human
 * but not for machines because this hiearchy is not present in the DOM tree.
 * We can express it in RDFa via empty <span> tags. These aren't visible and
 * give machines extra information about the content and its structure.
 *
 * @param $variables
 *   An associative array containing:
 *   - metadata: An array of attribute arrays. Each item in the array
 *     corresponds to its own set of attributes, and therefore, needs its own
 *     element.
 *
 * @see rdf_process()
 * @ingroup themeable
 * @ingroup rdf
 */
function theme_rdf_metadata($variables) {
  $output = '';
  foreach ($variables['metadata'] as $attributes) {
    // Add a class so that developers viewing the HTML source can see why there
    // are empty <span> tags in the document.
    $attributes['class'][] = 'rdf-meta';
    $attributes['class'][] = 'element-hidden';
    // The XHTML+RDFa doctype allows either <span></span> or <span /> syntax to
    // be used, but for maximum browser compatibility, W3C recommends the
    // former when serving pages using the text/html media type, see
    // http://www.w3.org/TR/xhtml1/#C_3.
    $output .= '<span' . drupal_attributes($attributes) . '></span>';
  }
  return $output;
}
