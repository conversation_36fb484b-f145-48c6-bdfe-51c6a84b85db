<?php

/**
 * @file
 * Install, update and uninstall functions for the rdf module.
 */

/**
 * Implements hook_schema().
 */
function rdf_schema() {
  $schema['rdf_mapping'] = array(
    'description' => 'Stores custom RDF mappings for user defined content types or overriden module-defined mappings',
    'fields' => array(
      'type' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'description' => 'The name of the entity type a mapping applies to (node, user, comment, etc.).',
      ),
      'bundle' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'description' => 'The name of the bundle a mapping applies to.',
      ),
      'mapping' => array(
        'description' => 'The serialized mapping of the bundle type and fields to RDF terms.',
        'type' => 'blob',
        'not null' => FALSE,
        'size' => 'big',
        'serialize' => TRUE,
      ),
    ),
    'primary key' => array('type', 'bundle'),
  );

  return $schema;
}

/**
 * Implements hook_install().
 */
function rdf_install() {
  // Collect any RDF mappings that were declared by modules installed before
  // this one.
  $modules = module_implements('rdf_mapping');
  rdf_modules_installed($modules);
}
