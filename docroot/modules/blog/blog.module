<?php

/**
 * @file
 * Enables multi-user blogs.
 */

/**
 * Implements hook_node_info().
 */
function blog_node_info() {
  return array(
    'blog' => array(
      'name' => t('Blog entry'),
      'base' => 'blog',
      'description' => t('Use for multi-user blogs. Every user gets a personal blog.'),
    )
  );
}

/**
 * Implements hook_user_view().
 */
function blog_user_view($account) {
  if (user_access('create blog content', $account)) {
    $account->content['summary']['blog'] =  array(
      '#type' => 'user_profile_item',
      '#title' => t('Blog'),
      // l() escapes the attributes, so we should not escape !username here.
      '#markup' => l(t('View recent blog entries'), "blog/$account->uid", array('attributes' => array('title' => t("Read !username's latest blog entries.", array('!username' => format_username($account)))))),
      '#attributes' => array('class' => array('blog')),
    );
  }
}

/**
 * Implements hook_help().
 */
function blog_help($path, $arg) {
  switch ($path) {
    case 'admin/help#blog':
      $output = '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t("The Blog module allows registered users to maintain an online journal, or <em>blog</em>. Blogs are made up of individual <em>blog entries</em>. By default, the blog entries are displayed by creation time in descending order, with comments enabled, and are promoted to the site's front page. For more information, see the online handbook entry for <a href='@blog'>Blog module</a>.", array('@blog' => 'http://drupal.org/documentation/modules/blog/')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Single-user blogs') . '</dt>';
      $output .= '<dd>' . t("Each user's blog entries are automatically displayed with a link to the user's main blog page. You can create as many single-user blogs as you have site users with permission to create blog content.") . '</dd>';
      $output .= '<dt>' . t('Multi-user blogs') . '</dt>';
      $output .= '<dd>' . t("Blog entries from each single-user blog are also aggregated into one central multi-user blog, which displays the blog content of all users in a single listing.") . '</dd>';
      $output .= '<dt>' . t('Navigation') . '</dt>';
      $output .= '<dd>' . t("There is an optional <em>Blogs</em> menu item added to the Navigation menu, which displays all blogs available on your site, and a <em>My blog</em> item displaying the current user's blog entries.") . '</dd>';
      $output .= '<dt>' . t('Blocks') . '</dt>';
      $output .= '<dd>' . t('The Blog module also creates a default <em>Recent blog posts</em> block that may be enabled at the <a href="@blocks">blocks administration page</a>.', array('@blocks' => url('admin/structure/block'))) . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_form().
 */
function blog_form($node, $form_state) {
  return node_content_form($node, $form_state);
}

/**
 * Implements hook_view().
 */
function blog_view($node, $view_mode) {
  if ($view_mode == 'full' && node_is_page($node)) {
    // Breadcrumb navigation.  l() escapes title, so we should not escape !name.
    drupal_set_breadcrumb(array(l(t('Home'), NULL), l(t('Blogs'), 'blog'), l(t("!name's blog", array('!name' => format_username($node))), 'blog/' . $node->uid)));
  }
  return $node;
}

/**
 * Implements hook_node_view().
 */
function blog_node_view($node, $view_mode) {
  if ($view_mode != 'rss') {
    if ($node->type == 'blog' && (arg(0) != 'blog' || arg(1) != $node->uid)) {
      // This goes to l(), which escapes !username in both title and attributes.
      $links['blog_usernames_blog'] = array(
        'title' => t("!username's blog", array('!username' => format_username($node))),
        'href' => "blog/$node->uid",
        'attributes' => array('title' => t("Read !username's latest blog entries.", array('!username' => format_username($node)))),
      );
      $node->content['links']['blog'] = array(
        '#theme' => 'links__node__blog',
        '#links' => $links,
        '#attributes' => array('class' => array('links', 'inline')),
      );
    }
  }
}

/**
 * Implements hook_menu().
 */
function blog_menu() {
  $items['blog'] = array(
    'title' => 'Blogs',
    'page callback' => 'blog_page_last',
    'access arguments' => array('access content'),
    'type' => MENU_SUGGESTED_ITEM,
    'file' => 'blog.pages.inc',
  );
  $items['blog/%user_uid_optional'] = array(
    'title' => 'My blog',
    'page callback' => 'blog_page_user',
    'page arguments' => array(1),
    'access callback' => 'blog_page_user_access',
    'access arguments' => array(1),
    'file' => 'blog.pages.inc',
  );
  $items['blog/%user/feed'] = array(
    'title' => 'Blogs',
    'page callback' => 'blog_feed_user',
    'page arguments' => array(1),
    'access callback' => 'blog_page_user_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'blog.pages.inc',
  );
  $items['blog/feed'] = array(
    'title' => 'Blogs',
    'page callback' => 'blog_feed_last',
    'access arguments' => array('access content'),
    'type' => MENU_CALLBACK,
    'file' => 'blog.pages.inc',
  );

  return $items;
}

/**
 * Implements hook_menu_local_tasks_alter().
 */
function blog_menu_local_tasks_alter(&$data, $router_item, $root_path) {
  global $user;

  // Add action link to 'node/add/blog' on 'blog' page.
  if ($root_path == 'blog') {
    $item = menu_get_item('node/add/blog');
    if ($item['access']) {
      $item['title'] = t('Create new blog entry');
      $data['actions']['output'][] = array(
        '#theme' => 'menu_local_action',
        '#link' => $item,
      );
    }
  }
  // Provide a helper action link to the author on the 'blog/%' page.
  elseif ($root_path == 'blog/%' && isset($router_item['page_arguments'][0]->uid) && $router_item['page_arguments'][0]->uid == $user->uid) {
    $data['actions']['output']['blog'] = array(
      '#theme' => 'menu_local_action',
    );
    if (user_access('create blog content')) {
      $data['actions']['output']['blog']['#link']['title'] = t('Post new blog entry.');
      $data['actions']['output']['blog']['#link']['href'] = 'node/add/blog';
    }
    else {
      $data['actions']['output']['blog']['#link']['title'] = t('You are not allowed to post a new blog entry.');
    }
  }
}

/**
 * Access callback for user blog pages.
 */
function blog_page_user_access($account) {
  // The visitor must be able to access the site's content.
  // For a blog to 'exist' the user must either be able to
  // create new blog entries, or it must have existing posts.
  return $account->uid && user_access('access content') && (user_access('create blog content', $account) || _blog_post_exists($account));
}

/**
 * Helper function to determine if a user has blog posts already.
 */
function _blog_post_exists($account) {
  return (bool)db_select('node', 'n')
    ->fields('n', array('nid'))
    ->condition('type', 'blog')
    ->condition('uid', $account->uid)
    ->condition('status', 1)
    ->range(0, 1)
    ->addTag('node_access')
    ->execute()
    ->fetchField();
}

/**
 * Implements hook_block_info().
 */
function blog_block_info() {
  $block['recent']['info'] = t('Recent blog posts');
  $block['recent']['properties']['administrative'] = TRUE;
  return $block;
}

/**
 * Implements hook_block_configure().
 */
function blog_block_configure($delta = '') {
  if ($delta == 'recent') {
    $form['blog_block_count'] = array(
      '#type' => 'select',
      '#title' => t('Number of recent blog posts to display'),
      '#default_value' => variable_get('blog_block_count', 10),
      '#options' => drupal_map_assoc(array(2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 25, 30)),
    );
    return $form;
  }
}

/**
 * Implements hook_block_save().
 */
function blog_block_save($delta = '', $edit = array()) {
  if ($delta == 'recent') {
    variable_set('blog_block_count', $edit['blog_block_count']);
  }
}

/**
 * Implements hook_block_view().
 *
 * Displays the most recent 10 blog titles.
 */
function blog_block_view($delta = '') {
  global $user;

  if (user_access('access content')) {
    $result = db_select('node', 'n')
      ->fields('n', array('nid', 'title', 'created'))
      ->condition('type', 'blog')
      ->condition('status', 1)
      ->orderBy('created', 'DESC')
      ->range(0, variable_get('blog_block_count', 10))
      ->addTag('node_access')
      ->execute();

    if ($node_title_list = node_title_list($result)) {
      $block['subject'] = t('Recent blog posts');
      $block['content']['blog_list'] = $node_title_list;
      $block['content']['blog_more'] = array(
        '#theme' => 'more_link',
        '#url' => 'blog',
        '#title' => t('Read the latest blog entries.'),
      );

      return $block;
    }
  }
}

/**
 * Implements hook_rdf_mapping().
 */
function blog_rdf_mapping() {
  return array(
    array(
      'type' => 'node',
      'bundle' => 'blog',
      'mapping' => array(
        'rdftype' => array('sioc:Post', 'sioct:BlogPost'),
      ),
    ),
  );
}
