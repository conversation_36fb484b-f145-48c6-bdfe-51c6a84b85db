<?php

/**
 * @file
 * Tests for php.module.
 */

/**
 * Defines a base PHP test case class.
 */
class PHPTestCase extends DrupalWebTestCase {
  protected $php_code_format;

  function setUp() {
    parent::setUp('php');

    // Create and login admin user.
    $admin_user = $this->drupalCreateUser(array('administer filters'));
    $this->drupalLogin($admin_user);

    // Verify that the PHP code text format was inserted.
    $php_format_id = 'php_code';
    $this->php_code_format = filter_format_load($php_format_id);
    $this->assertEqual($this->php_code_format->name, 'PHP code', 'PHP code text format was created.');

    // Verify that the format has the PHP code filter enabled.
    $filters = filter_list_format($php_format_id);
    $this->assertTrue($filters['php_code']->status, 'PHP code filter is enabled.');

    // Verify that the format exists on the administration page.
    $this->drupalGet('admin/config/content/formats');
    $this->assertText('PHP code', 'PHP code text format was created.');

    // Verify that anonymous and authenticated user roles do not have access.
    $this->drupalGet('admin/config/content/formats/' . $php_format_id);
    $this->assertFieldByName('roles[' . DRUPAL_ANONYMOUS_RID . ']', FALSE, 'Anonymous users do not have access to PHP code format.');
    $this->assertFieldByName('roles[' . DRUPAL_AUTHENTICATED_RID . ']', FALSE, 'Authenticated users do not have access to PHP code format.');
  }

  /**
   * Creates a test node with PHP code in the body.
   *
   * @return stdObject Node object.
   */
  function createNodeWithCode() {
    return $this->drupalCreateNode(array('body' => array(LANGUAGE_NONE => array(array('value' => '<?php print "SimpleTest PHP was executed!"; ?>')))));
  }
}

/**
 * Tests to make sure the PHP filter actually evaluates PHP code when used.
 */
class PHPFilterTestCase extends PHPTestCase {
  public static function getInfo() {
    return array(
      'name' => 'PHP filter functionality',
      'description' => 'Make sure that PHP filter properly evaluates PHP code when enabled.',
      'group' => 'PHP',
    );
  }

  /**
   * Makes sure that the PHP filter evaluates PHP code when used.
   */
  function testPHPFilter() {
    // Log in as a user with permission to use the PHP code text format.
    $php_code_permission = filter_permission_name(filter_format_load('php_code'));
    $web_user = $this->drupalCreateUser(array('access content', 'create page content', 'edit own page content', $php_code_permission));
    $this->drupalLogin($web_user);

    // Create a node with PHP code in it.
    $node = $this->createNodeWithCode();

    // Make sure that the PHP code shows up as text.
    $this->drupalGet('node/' . $node->nid);
    $this->assertText('print "SimpleTest PHP was executed!"', 'PHP code is displayed.');

    // Change filter to PHP filter and see that PHP code is evaluated.
    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["body[$langcode][0][format]"] = $this->php_code_format->format;
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->assertRaw(t('Basic page %title has been updated.', array('%title' => $node->title)), 'PHP code filter turned on.');

    // Make sure that the PHP code shows up as text.
    $this->assertNoText('print "SimpleTest PHP was executed!"', "PHP code isn't displayed.");
    $this->assertText('SimpleTest PHP was executed!', 'PHP code has been evaluated.');
  }
}

/**
 * Tests to make sure access to the PHP filter is properly restricted.
 */
class PHPAccessTestCase extends PHPTestCase {
  public static function getInfo() {
    return array(
      'name' => 'PHP filter access check',
      'description' => 'Make sure that users who don\'t have access to the PHP filter can\'t see it.',
      'group' => 'PHP',
    );
  }

  /**
   * Makes sure that the user can't use the PHP filter when not given access.
   */
  function testNoPrivileges() {
    // Create node with PHP filter enabled.
    $web_user = $this->drupalCreateUser(array('access content', 'create page content', 'edit own page content'));
    $this->drupalLogin($web_user);
    $node = $this->createNodeWithCode();

    // Make sure that the PHP code shows up as text.
    $this->drupalGet('node/' . $node->nid);
    $this->assertText('print', 'PHP code was not evaluated.');

    // Make sure that user doesn't have access to filter.
    $this->drupalGet('node/' . $node->nid . '/edit');
    $this->assertNoRaw('<option value="' . $this->php_code_format->format . '">', 'PHP code format not available.');
  }
}
