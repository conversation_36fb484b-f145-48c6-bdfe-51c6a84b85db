/**
 * @file
 * Right-to-left specific stylesheet for the Color module.
 */

#placeholder {
  left: 0;
  right: auto;
}

/* Palette */
.color-form .form-item {
  padding-left: 0;
  padding-right: 1em;
}
.color-form label {
  float: right;
  clear: right;
}
.color-form .form-text,
.color-form .form-select {
  float: right;
}
.color-form .form-text {
  margin-right: 0;
  margin-left: 5px;
}
#palette .hook {
  float: right;
}
#palette .down,
#palette .up,
#palette .both {
  background: url(images/hook-rtl.png) no-repeat 0 0;
}
#palette .up {
  background-position: 0 -27px;
}
#palette .both {
  background-position: 0 -54px;
}
#palette .lock {
  float: right;
  right: -10px;
}
html.js #preview {
  float: right;
}
