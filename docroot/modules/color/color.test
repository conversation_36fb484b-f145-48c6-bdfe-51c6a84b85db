<?php

/**
 * @file
 * Tests for color module.
 */

/**
 * Tests the Color module functionality.
 */
class ColorTestCase extends DrupalWebTestCase {
  protected $big_user;
  protected $themes;
  protected $colorTests;

  public static function getInfo() {
    return array(
      'name' => 'Color functionality',
      'description' => 'Modify the Bartik and Garland theme colors and make sure the changes are reflected on the frontend',
      'group' => 'Color',
    );
  }

  function setUp() {
    parent::setUp('color');

    // Create users.
    $this->big_user = $this->drupalCreateUser(array('administer themes'));

    // This tests the color module in both Bartik and Garland.
    $this->themes = array(
      'bartik' => array(
        'palette_input' => 'palette[bg]',
        'scheme' => 'slate',
        'scheme_color' => '#3b3b3b',
      ),
      'garland' => array(
        'palette_input' => 'palette[link]',
        'scheme' => 'greenbeam',
        'scheme_color' => '#0c7a00',
      ),
    );
    theme_enable(array_keys($this->themes));

    // Array filled with valid and not valid color values
    $this->colorTests = array(
      '#000' => TRUE,
      '#123456' => TRUE,
      '#abcdef' => TRUE,
      '#0' => FALSE,
      '#00' => FALSE,
      '#0000' => FALSE,
      '#00000' => FALSE,
      '123456' => FALSE,
      '#00000g' => FALSE,
    );
  }

  /**
   * Tests the Color module functionality.
   */
  function testColor() {
    foreach ($this->themes as $theme => $test_values) {
      $this->_testColor($theme, $test_values);
    }
  }

  /**
   * Tests the Color module functionality using the given theme.
   */
  function _testColor($theme, $test_values) {
    variable_set('theme_default', $theme);
    $settings_path = 'admin/appearance/settings/' . $theme;

    $this->drupalLogin($this->big_user);
    $this->drupalGet($settings_path);
    $this->assertResponse(200);
    $edit['scheme'] = '';
    $edit[$test_values['palette_input']] = '#123456';
    $this->drupalPost($settings_path, $edit, t('Save configuration'));

    $this->drupalGet('<front>');
    $stylesheets = variable_get('color_' . $theme . '_stylesheets', array());
    $this->assertPattern('|' . file_create_url($stylesheets[0]) . '|', 'Make sure the color stylesheet is included in the content. (' . $theme . ')');

    $stylesheet_content = join("\n", file($stylesheets[0]));
    $this->assertTrue(strpos($stylesheet_content, 'color: #123456') !== FALSE, 'Make sure the color we changed is in the color stylesheet. (' . $theme . ')');

    $this->drupalGet($settings_path);
    $this->assertResponse(200);
    $edit['scheme'] = $test_values['scheme'];
    $this->drupalPost($settings_path, $edit, t('Save configuration'));

    $this->drupalGet('<front>');
    $stylesheets = variable_get('color_' . $theme . '_stylesheets', array());
    $stylesheet_content = join("\n", file($stylesheets[0]));
    $this->assertTrue(strpos($stylesheet_content, 'color: ' . $test_values['scheme_color']) !== FALSE, 'Make sure the color we changed is in the color stylesheet. (' . $theme . ')');

    // Test with aggregated CSS turned on.
    variable_set('preprocess_css', 1);
    $this->drupalGet('<front>');
    $stylesheets = variable_get('drupal_css_cache_files', array());
    $stylesheet_content = '';
    foreach ($stylesheets as $key => $uri) {
      $stylesheet_content .= join("\n", file(drupal_realpath($uri)));
    }
    $this->assertTrue(strpos($stylesheet_content, 'public://') === FALSE, 'Make sure the color paths have been translated to local paths. (' . $theme . ')');
    variable_set('preprocess_css', 0);
  }

  /**
   * Tests whether the provided color is valid.
   */
  function testValidColor() {
    variable_set('theme_default', 'bartik');
    $settings_path = 'admin/appearance/settings/bartik';

    $this->drupalLogin($this->big_user);
    $edit['scheme'] = '';

    foreach ($this->colorTests as $color => $is_valid) {
      $edit['palette[bg]'] = $color;
      $this->drupalPost($settings_path, $edit, t('Save configuration'));

      if ($is_valid) {
        $this->assertText('The configuration options have been saved.');
      }
      else {
        $this->assertText('Main background must be a valid hexadecimal CSS color value.');
      }
    }
  }
}

/**
 * Unit tests for the color.module
 */
class ColorUnitTestCase extends DrupalUnitTestCase {

  protected $test_values;

  /**
   * {@inheritdoc}
   */
  public static function getInfo() {
    return array(
      'name' => 'Color module unit tests',
      'description' => 'Test color.module functionality.',
      'group' => 'Color',
    );
  }

  /**
   * Set up the test environment.
   */
  public function setUp() {
    drupal_load('module', 'color');
    parent::setUp();

    $this->test_values = array(
      array(array(0.2, 0.4, 0.8), TRUE, '#3366cc'),
      array(array(51, 102, 204), FALSE, '#3366cc'),
      array(array(6, 120, 190), FALSE, '#0678be'),
      array(array(192, 192, 192), FALSE, '#c0c0c0'),
      array(array(255, 255, 0), FALSE, '#ffff00'),
      array(array(128, 0, 128), FALSE, '#800080'),
      array(array(0.6, 0.8, 1), TRUE, '#99ccff'),
      array(array(221, 72, 20), FALSE, '#dd4814'),
    );
  }

  public function testColorPack() {
    foreach ($this->test_values as $test) {
      $this->assertEqual(_color_pack($test[0], $test[1]), $test[2], __FUNCTION__ . ' hex: ' . $test[2] . ' normalize: ' . ($test[1] ? 'TRUE' : 'FALSE'));
    }
  }

  public function testColorUnpack() {
    foreach ($this->test_values as $test) {
      $this->assertEqual(_color_unpack($test[2], $test[1]), $test[0], __FUNCTION__ . ' hex: ' . $test[2] . ' normalize: ' . ($test[1] ? 'TRUE' : 'FALSE'));
    }
  }
}
