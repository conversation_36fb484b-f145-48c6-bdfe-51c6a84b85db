<?php

/**
 * @file
 * Install, update and uninstall functions for the color module.
 */

/**
 * Implements hook_requirements().
 */
function color_requirements($phase) {
  $requirements = array();

  if ($phase == 'runtime') {
    // Check for the PHP GD library.
    if (function_exists('imagegd2')) {
      $info = gd_info();
      $requirements['color_gd'] = array(
        'value' => $info['GD Version'],
      );

      // Check for PNG support.
      if (function_exists('imagecreatefrompng')) {
        $requirements['color_gd']['severity'] = REQUIREMENT_OK;
      }
      else {
        $requirements['color_gd']['severity'] = REQUIREMENT_WARNING;
        $requirements['color_gd']['description'] = t('The GD library for PHP is enabled, but was compiled without PNG support. Check the <a href="@url">PHP image documentation</a> for information on how to correct this.', array('@url' => 'http://www.php.net/manual/ref.image.php'));
      }
    }
    else {
      $requirements['color_gd'] = array(
        'value' => t('Not installed'),
        'severity' => REQUIREMENT_ERROR,
        'description' => t('The GD library for PHP is missing or outdated. Check the <a href="@url">PHP image documentation</a> for information on how to correct this.', array('@url' => 'http://www.php.net/manual/book.image.php')),
      );
    }
    $requirements['color_gd']['title'] = t('GD library PNG support');
  }

  return $requirements;
}

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Warn site administrator if unsafe CSS color codes are found in the database.
 */
function color_update_7001() {
  $theme_palettes = db_query("SELECT name FROM {variable} WHERE name LIKE 'color_%_palette'")->fetchCol();
  foreach ($theme_palettes as $name) {
    $palette = variable_get($name, array());
    foreach ($palette as $key => $color) {
      if (!preg_match('/^#([a-f0-9]{3}){1,2}$/iD', $color)) {
        drupal_set_message('Some of the custom CSS color codes specified via the color module are invalid. Please examine the themes which are making use of the color module at the <a href="'. url('admin/appearance/settings') .'">Appearance settings</a> page to verify their CSS color values.', 'warning');
      }
    }
  }
}

/**
 * @} End of "addtogroup updates-7.x-extra".
 */
