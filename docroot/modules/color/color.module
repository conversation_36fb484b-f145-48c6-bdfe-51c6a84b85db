<?php
/**
 * @file
 * Allows users to change the color scheme of themes.
 */

/**
 * Implements hook_help().
 */
function color_help($path, $arg) {
  switch ($path) {
    case 'admin/help#color':
      $output = '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Color module allows users with the <em>Administer site configuration</em> permission to quickly and easily change the color scheme of themes that have been built to be compatible with it. For more information, see the online handbook entry for <a href="@color">Color module</a>.', array('@color' => 'http://drupal.org/documentation/modules/color')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Changing colors') . '</dt>';
      $output .= '<dd>' . t("Using the Color module allows you to easily change the color of links, backgrounds, text, and other theme elements. To change the color settings for a compatible theme, select the <em>Settings</em> link for your theme on the <a href='@configure'>Themes administration page</a>. If you don't see a color picker on that page, then your theme is not compatible with the color module. If you are sure that the theme does indeed support the color module, but the color picker does not appear, then <a href='@troubleshoot'>follow these troubleshooting procedures</a>.", array('@configure' => url('admin/appearance'), '@troubleshoot' => 'http://drupal.org/node/109457')) . '</dd>';
      $output .= '<dd>' . t("The Color module saves a modified copy of the theme's specified stylesheets in the files directory. This means that if you make any manual changes to your theme's stylesheet, <em>you must save your color settings again, even if they haven't changed</em>. This step is required because the module stylesheets (in the files directory) need to be recreated to include your changes.") . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_theme().
 */
function color_theme() {
  return array(
    'color_scheme_form' => array(
      'render element' => 'form',
    ),
  );
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function color_form_system_theme_settings_alter(&$form, &$form_state) {
  if (isset($form_state['build_info']['args'][0]) && ($theme = $form_state['build_info']['args'][0]) && color_get_info($theme) && function_exists('gd_info')) {
    $form['color'] = array(
      '#type' => 'fieldset',
      '#title' => t('Color scheme'),
      '#weight' => -1,
      '#attributes' => array('id' => 'color_scheme_form'),
      '#theme' => 'color_scheme_form',
    );
    $form['color'] += color_scheme_form($form, $form_state, $theme);
    $form['#validate'][] = 'color_scheme_form_validate';
    $form['#submit'][] = 'color_scheme_form_submit';
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function color_form_system_themes_alter(&$form, &$form_state) {
  _color_theme_select_form_alter($form, $form_state);
}

/**
 * Helper for hook_form_FORM_ID_alter() implementations.
 */
function _color_theme_select_form_alter(&$form, &$form_state) {
  // Use the generated screenshot in the theme list.
  $themes = list_themes();
  foreach (element_children($form) as $theme) {
    if ($screenshot = variable_get('color_' . $theme . '_screenshot')) {
      if (isset($form[$theme]['screenshot'])) {
        $form[$theme]['screenshot']['#markup'] = theme('image', array('path' => $screenshot, 'title' => '', 'attributes' => array('class' => array('screenshot'))));
      }
    }
  }
}

/**
 * Replaces style sheets with color-altered style sheets.
 *
 * A theme that supports the color module should call this function from its
 * THEME_process_html() function, so that the correct style sheets are
 * included when html.tpl.php is rendered.
 *
 * @see theme()
 */
function _color_html_alter(&$vars) {
  global $theme_key;
  $themes = list_themes();

  // Override stylesheets.
  $color_paths = variable_get('color_' . $theme_key . '_stylesheets', array());
  if (!empty($color_paths)) {

    foreach ($themes[$theme_key]->stylesheets['all'] as $base_filename => $old_path) {
      // Loop over the path array with recolored CSS files to find matching
      // paths which could replace the non-recolored paths.
      foreach ($color_paths as $color_path) {
        // Color module currently requires unique file names to be used,
        // which allows us to compare different file paths.
        if (drupal_basename($old_path) == drupal_basename($color_path)) {
          // Replace the path to the new css file.
          // This keeps the order of the stylesheets intact.
          $vars['css'][$old_path]['data'] = $color_path;
        }
      }
    }

    $vars['styles'] = drupal_get_css($vars['css']);
  }
}

/**
 * Replaces the logo with a color-altered logo.
 *
 * A theme that supports the color module should call this function from its
 * THEME_process_page() function, so that the correct logo is included when
 * page.tpl.php is rendered.
 *
 * @see theme()
 */
function _color_page_alter(&$vars) {
  global $theme_key;

  // Override logo.
  $logo = variable_get('color_' . $theme_key . '_logo');
  if ($logo && $vars['logo'] && preg_match('!' . $theme_key . '/logo.png$!', $vars['logo'])) {
    $vars['logo'] = file_create_url($logo);
  }
}

/**
 * Retrieves the Color module information for a particular theme.
 */
function color_get_info($theme) {
  static $theme_info = array();

  if (isset($theme_info[$theme])) {
    return $theme_info[$theme];
  }

  $path = drupal_get_path('theme', $theme);
  $file = DRUPAL_ROOT . '/' . $path . '/color/color.inc';
  if ($path && file_exists($file)) {
    include $file;
    $theme_info[$theme] = $info;
    return $info;
  }
}

/**
 * Retrieves the color palette for a particular theme.
 */
function color_get_palette($theme, $default = FALSE) {
  // Fetch and expand default palette.
  $info = color_get_info($theme);
  $palette = $info['schemes']['default']['colors'];

  // Load variable.
  return $default ? $palette : variable_get('color_' . $theme . '_palette', $palette);
}

/**
 * Form constructor for the color configuration form for a particular theme.
 *
 * @param $theme
 *   The machine name of the theme whose color settings are being configured.
 *
 * @see color_scheme_form_validate()
 * @see color_scheme_form_submit()
 * @ingroup forms
 */
function color_scheme_form($complete_form, &$form_state, $theme) {
  $base = drupal_get_path('module', 'color');
  $info = color_get_info($theme);

  $info['schemes'][''] = array('title' => t('Custom'), 'colors' => array());
  $color_sets = array();
  $schemes = array();
  foreach ($info['schemes'] as $key => $scheme) {
    $color_sets[$key] = $scheme['title'];
    $schemes[$key] = $scheme['colors'];
    $schemes[$key] += $info['schemes']['default']['colors'];
  }

  // See if we're using a predefined scheme.
  // Note: we use the original theme when the default scheme is chosen.
  $current_scheme = variable_get('color_' . $theme . '_palette', array());
  foreach ($schemes as $key => $scheme) {
    if ($current_scheme == $scheme) {
      $scheme_name = $key;
      break;
    }
  }
  if (empty($scheme_name)) {
    if (empty($current_scheme)) {
      $scheme_name = 'default';
    }
    else {
      $scheme_name = '';
    }
  }

  // Add scheme selector.
  $form['scheme'] = array(
    '#type' => 'select',
    '#title' => t('Color set'),
    '#options' => $color_sets,
    '#default_value' => $scheme_name,
    '#attached' => array(
      // Add Farbtastic color picker.
      'library' => array(
        array('system', 'farbtastic'),
      ),
      // Add custom CSS.
      'css' => array(
        $base . '/color.css' => array(),
      ),
      // Add custom JavaScript.
      'js' => array(
        $base . '/color.js',
        array(
          'data' => array(
            'color' => array(
              'reference' => color_get_palette($theme, TRUE),
              'schemes' => $schemes,
            ),
            'gradients' => $info['gradients'],
          ),
          'type' => 'setting',
        ),
      ),
    ),
  );

  // Add palette fields.
  $palette = color_get_palette($theme);
  $names = $info['fields'];
  $form['palette']['#tree'] = TRUE;
  foreach ($palette as $name => $value) {
    if (isset($names[$name])) {
      $form['palette'][$name] = array(
        '#type' => 'textfield',
        '#title' => check_plain($names[$name]),
        '#value_callback' => 'color_palette_color_value',
        '#default_value' => $value,
        '#size' => 8,
      );
    }
  }
  $form['theme'] = array('#type' => 'value', '#value' => $theme);
  $form['info'] = array('#type' => 'value', '#value' => $info);

  return $form;
}

/**
 * Returns HTML for a theme's color form.
 *
 * @param $variables
 *   An associative array containing:
 *   - form: A render element representing the form.
 *
 * @ingroup themeable
 */
function theme_color_scheme_form($variables) {
  $form = $variables['form'];

  $theme = $form['theme']['#value'];
  $info = $form['info']['#value'];
  $path = drupal_get_path('theme', $theme) . '/';
  drupal_add_css($path . $info['preview_css']);

  $preview_js_path = isset($info['preview_js']) ? $path . $info['preview_js'] : drupal_get_path('module', 'color') . '/' . 'preview.js';
  // Add the JS at a weight below color.js.
  drupal_add_js($preview_js_path, array('weight' => -1));

  $output  = '';
  $output .= '<div class="color-form clearfix">';
  // Color schemes
  $output .= drupal_render($form['scheme']);
  // Palette
  $output .= '<div id="palette" class="clearfix">';
  foreach (element_children($form['palette']) as $name) {
    $output .= drupal_render($form['palette'][$name]);
  }
  $output .= '</div>';
  // Preview
  $output .= drupal_render_children($form);
  $output .= '<h2>' . t('Preview') . '</h2>';
  // Attempt to load preview HTML if the theme provides it.
  $preview_html_path = DRUPAL_ROOT . '/' . (isset($info['preview_html']) ? drupal_get_path('theme', $theme) . '/' . $info['preview_html'] : drupal_get_path('module', 'color') . '/preview.html');
  $output .= file_get_contents($preview_html_path);
  // Close the wrapper div.
  $output .= '</div>';

  return $output;
}

/**
 * Determines the value for a palette color field.
 *
 * @param $element
 *   The form element whose value is being populated.
 * @param $input
 *   The incoming input to populate the form element. If this is FALSE,
 *   the element's default value should be returned.
 * @param $form_state
 *   A keyed array containing the current state of the form.
 *
 * @return
 *   The data that will appear in the $form_state['values'] collection for this
 *   element. Return nothing to use the default.
 */
function color_palette_color_value($element, $input = FALSE, $form_state = array()) {
  // If we suspect a possible cross-site request forgery attack, only accept
  // hexadecimal CSS color strings from user input, to avoid problems when this
  // value is used in the JavaScript preview.
  if ($input !== FALSE) {
    // Start with the provided value for this textfield, and validate that if
    // necessary, falling back on the default value.
    $value = form_type_textfield_value($element, $input, $form_state);
    if (!$value || !isset($form_state['complete form']['#token']) || color_valid_hexadecimal_string($value) || drupal_valid_token($form_state['values']['form_token'], $form_state['complete form']['#token'])) {
      return $value;
    }
    else {
      return $element['#default_value'];
    }
  }
}

/**
 * Determines if a hexadecimal CSS color string is valid.
 *
 * @param $color
 *   The string to check.
 *
 * @return
 *   TRUE if the string is a valid hexadecimal CSS color string, or FALSE if it
 *   isn't.
 */
function color_valid_hexadecimal_string($color) {
  return preg_match('/^#([a-f0-9]{3}){1,2}$/iD', $color);
}

/**
 * Form validation handler for color_scheme_form().
 *
 * @see color_scheme_form_submit()
 */
function color_scheme_form_validate($form, &$form_state) {
  // Only accept hexadecimal CSS color strings to avoid XSS upon use.
  foreach ($form_state['values']['palette'] as $key => $color) {
    if (!color_valid_hexadecimal_string($color)) {
      form_set_error('palette][' . $key, t('%name must be a valid hexadecimal CSS color value.', array('%name' => $form['color']['palette'][$key]['#title'])));
    }
  }
}

/**
 * Form submission handler for color_scheme_form().
 *
 * @see color_scheme_form_validate()
 */
function color_scheme_form_submit($form, &$form_state) {
  // Get theme coloring info.
  if (!isset($form_state['values']['info'])) {
    return;
  }
  $theme = $form_state['values']['theme'];
  $info = $form_state['values']['info'];

  // Resolve palette.
  $palette = $form_state['values']['palette'];
  if ($form_state['values']['scheme'] != '') {
    foreach ($palette as $key => $color) {
      if (isset($info['schemes'][$form_state['values']['scheme']]['colors'][$key])) {
        $palette[$key] = $info['schemes'][$form_state['values']['scheme']]['colors'][$key];
      }
    }
    $palette += $info['schemes']['default']['colors'];
  }

  // Make sure enough memory is available, if PHP's memory limit is compiled in.
  if (function_exists('memory_get_usage')) {
    // Fetch source image dimensions.
    $source = drupal_get_path('theme', $theme) . '/' . $info['base_image'];
    list($width, $height) = getimagesize($source);

    // We need at least a copy of the source and a target buffer of the same
    // size (both at 32bpp).
    $required = $width * $height * 8;
    // We intend to prevent color scheme changes if there isn't enough memory
    // available.  memory_get_usage(TRUE) returns a more accurate number than
    // memory_get_usage(), therefore we won't inadvertently reject a color
    // scheme change based on a faulty memory calculation.
    $usage = memory_get_usage(TRUE);
    $memory_limit = ini_get('memory_limit');
    $size = parse_size($memory_limit);
    if (!drupal_check_memory_limit($usage + $required, $memory_limit)) {
      drupal_set_message(t('There is not enough memory available to PHP to change this theme\'s color scheme. You need at least %size more. Check the <a href="@url">PHP documentation</a> for more information.', array('%size' => format_size($usage + $required - $size), '@url' => 'http://www.php.net/manual/ini.core.php#ini.sect.resource-limits')), 'error');
      return;
    }
  }

  // Delete old files.
  foreach (variable_get('color_' . $theme . '_files', array()) as $file) {
    @drupal_unlink($file);
  }
  if (isset($file) && $file = dirname($file)) {
    @drupal_rmdir($file);
  }

  // Don't render the default colorscheme, use the standard theme instead.
  if (implode(',', color_get_palette($theme, TRUE)) == implode(',', $palette)) {
    variable_del('color_' . $theme . '_palette');
    variable_del('color_' . $theme . '_stylesheets');
    variable_del('color_' . $theme . '_logo');
    variable_del('color_' . $theme . '_files');
    variable_del('color_' . $theme . '_screenshot');
    return;
  }

  // Prepare target locations for generated files.
  $id = $theme . '-' . substr(hash('sha256', serialize($palette) . microtime()), 0, 8);
  $paths['color'] = 'public://color';
  $paths['target'] = $paths['color'] . '/' . $id;
  foreach ($paths as $path) {
    file_prepare_directory($path, FILE_CREATE_DIRECTORY);
  }
  $paths['target'] = $paths['target'] . '/';
  $paths['id'] = $id;
  $paths['source'] = drupal_get_path('theme', $theme) . '/';
  $paths['files'] = $paths['map'] = array();

  // Save palette and logo location.
  variable_set('color_' . $theme . '_palette', $palette);
  variable_set('color_' . $theme . '_logo', $paths['target'] . 'logo.png');

  // Copy over neutral images.
  foreach ($info['copy'] as $file) {
    $base = drupal_basename($file);
    $source = $paths['source'] . $file;
    $filepath = file_unmanaged_copy($source, $paths['target'] . $base);
    $paths['map'][$file] = $base;
    $paths['files'][] = $filepath;
  }

  // Render new images, if image has been provided.
  if ($info['base_image']) {
    _color_render_images($theme, $info, $paths, $palette);
  }

  // Rewrite theme stylesheets.
  $css = array();
  foreach ($info['css'] as $stylesheet) {
    // Build a temporary array with LTR and RTL files.
    $files = array();
    if (file_exists($paths['source'] . $stylesheet)) {
      $files[] = $stylesheet;

      $rtl_file = str_replace('.css', '-rtl.css', $stylesheet);
      if (file_exists($paths['source'] . $rtl_file)) {
        $files[] = $rtl_file;
      }
    }

    foreach ($files as $file) {
      // Aggregate @imports recursively for each configured top level CSS file
      // without optimization. Aggregation and optimization will be
      // handled by drupal_build_css_cache() only.
      $style = drupal_load_stylesheet($paths['source'] . $file, FALSE);

      // Return the path to where this CSS file originated from, stripping
      // off the name of the file at the end of the path.
      $base = base_path() . dirname($paths['source'] . $file) . '/';
      _drupal_build_css_path(NULL, $base);

      // Prefix all paths within this CSS file, ignoring absolute paths.
      $style = preg_replace_callback('/url\([\'"]?(?![a-z]+:|\/+)([^\'")]+)[\'"]?\)/i', '_drupal_build_css_path', $style);

      // Rewrite stylesheet with new colors.
      $style = _color_rewrite_stylesheet($theme, $info, $paths, $palette, $style);
      $base_file = drupal_basename($file);
      $css[] = $paths['target'] . $base_file;
      _color_save_stylesheet($paths['target'] . $base_file, $style, $paths);
    }
  }

  // Maintain list of files.
  variable_set('color_' . $theme . '_stylesheets', $css);
  variable_set('color_' . $theme . '_files', $paths['files']);
}

/**
 * Rewrites the stylesheet to match the colors in the palette.
 */
function _color_rewrite_stylesheet($theme, &$info, &$paths, $palette, $style) {
  $themes = list_themes();
  // Prepare color conversion table.
  $conversion = $palette;
  foreach ($conversion as $k => $v) {
    $conversion[$k] = drupal_strtolower($v);
  }
  $default = color_get_palette($theme, TRUE);

  // Split off the "Don't touch" section of the stylesheet.
  $split = "Color Module: Don't touch";
  if (strpos($style, $split) !== FALSE) {
    list($style, $fixed) = explode($split, $style);
  }

  // Find all colors in the stylesheet and the chunks in between.
  $style = preg_split('/(#[0-9a-f]{6}|#[0-9a-f]{3})/i', $style, -1, PREG_SPLIT_DELIM_CAPTURE);
  $is_color = FALSE;
  $output = '';
  $base = 'base';

  // Iterate over all the parts.
  foreach ($style as $chunk) {
    if ($is_color) {
      $chunk = drupal_strtolower($chunk);
      // Check if this is one of the colors in the default palette.
      if ($key = array_search($chunk, $default)) {
        $chunk = $conversion[$key];
      }
      // Not a pre-set color. Extrapolate from the base.
      else {
        $chunk = _color_shift($palette[$base], $default[$base], $chunk, $info['blend_target']);
      }
    }
    else {
      // Determine the most suitable base color for the next color.

      // 'a' declarations. Use link.
      if (preg_match('@[^a-z0-9_-](a)[^a-z0-9_-][^/{]*{[^{]+$@i', $chunk)) {
        $base = 'link';
      }
      // 'color:' styles. Use text.
      elseif (preg_match('/(?<!-)color[^{:]*:[^{#]*$/i', $chunk)) {
        $base = 'text';
      }
      // Reset back to base.
      else {
        $base = 'base';
      }
    }
    $output .= $chunk;
    $is_color = !$is_color;
  }
  // Append fixed colors segment.
  if (isset($fixed)) {
    $output .= $fixed;
  }

  // Replace paths to images.
  foreach ($paths['map'] as $before => $after) {
    $before = base_path() . $paths['source'] . $before;
    $before = preg_replace('`(^|/)(?!../)([^/]+)/../`', '$1', $before);
    $output = str_replace($before, $after, $output);
  }

  return $output;
}

/**
 * Saves the rewritten stylesheet to disk.
 */
function _color_save_stylesheet($file, $style, &$paths) {
  $filepath = file_unmanaged_save_data($style, $file, FILE_EXISTS_REPLACE);
  $paths['files'][] = $filepath;

  // Set standard file permissions for webserver-generated files.
  drupal_chmod($file);
}

/**
 * Renders images that match a given palette.
 */
function _color_render_images($theme, &$info, &$paths, $palette) {
  // Prepare template image.
  $source = $paths['source'] . '/' . $info['base_image'];
  $source = imagecreatefrompng($source);
  $width = imagesx($source);
  $height = imagesy($source);

  // Prepare target buffer.
  $target = imagecreatetruecolor($width, $height);
  imagealphablending($target, TRUE);

  // Fill regions of solid color.
  foreach ($info['fill'] as $color => $fill) {
    imagefilledrectangle($target, $fill[0], $fill[1], $fill[0] + $fill[2], $fill[1] + $fill[3], _color_gd($target, $palette[$color]));
  }

  // Render gradients.
  foreach ($info['gradients'] as $gradient) {
    // Get direction of the gradient.
    if (isset($gradient['direction']) && $gradient['direction'] == 'horizontal') {
      // Horizontal gradient.
      for ($x = 0; $x < $gradient['dimension'][2]; $x++) {
        $color = _color_blend($target, $palette[$gradient['colors'][0]], $palette[$gradient['colors'][1]], $x / ($gradient['dimension'][2] - 1));
        imagefilledrectangle($target, ($gradient['dimension'][0] + $x), $gradient['dimension'][1], ($gradient['dimension'][0] + $x + 1), ($gradient['dimension'][1] + $gradient['dimension'][3]), $color);
      }
    }
    else {
      // Vertical gradient.
      for ($y = 0; $y < $gradient['dimension'][3]; $y++) {
        $color = _color_blend($target, $palette[$gradient['colors'][0]], $palette[$gradient['colors'][1]], $y / ($gradient['dimension'][3] - 1));
        imagefilledrectangle($target, $gradient['dimension'][0], $gradient['dimension'][1] + $y, $gradient['dimension'][0] + $gradient['dimension'][2], $gradient['dimension'][1] + $y + 1, $color);
      }
    }
  }

  // Blend over template.
  imagecopy($target, $source, 0, 0, 0, 0, $width, $height);

  // Clean up template image.
  imagedestroy($source);

  // Cut out slices.
  foreach ($info['slices'] as $file => $coord) {
    list($x, $y, $width, $height) = $coord;
    $base = drupal_basename($file);
    $image = drupal_realpath($paths['target'] . $base);

    // Cut out slice.
    if ($file == 'screenshot.png') {
      $slice = imagecreatetruecolor(150, 90);
      imagecopyresampled($slice, $target, 0, 0, $x, $y, 150, 90, $width, $height);
      variable_set('color_' . $theme . '_screenshot', $image);
    }
    else {
      $slice = imagecreatetruecolor($width, $height);
      imagecopy($slice, $target, 0, 0, $x, $y, $width, $height);
    }

    // Save image.
    imagepng($slice, $image);
    imagedestroy($slice);
    $paths['files'][] = $image;

    // Set standard file permissions for webserver-generated files
    drupal_chmod($image);

    // Build before/after map of image paths.
    $paths['map'][$file] = $base;
  }

  // Clean up target buffer.
  imagedestroy($target);
}

/**
 * Shifts a given color, using a reference pair and a target blend color.
 *
 * Note: this function is significantly different from the JS version, as it
 * is written to match the blended images perfectly.
 *
 * Constraint: if (ref2 == target + (ref1 - target) * delta) for some fraction
 * delta then (return == target + (given - target) * delta).
 *
 * Loose constraint: Preserve relative positions in saturation and luminance
 * space.
 */
function _color_shift($given, $ref1, $ref2, $target) {
  // We assume that ref2 is a blend of ref1 and target and find
  // delta based on the length of the difference vectors.

  // delta = 1 - |ref2 - ref1| / |white - ref1|
  $target = _color_unpack($target, TRUE);
  $ref1 = _color_unpack($ref1, TRUE);
  $ref2 = _color_unpack($ref2, TRUE);
  $numerator = 0;
  $denominator = 0;
  for ($i = 0; $i < 3; ++$i) {
    $numerator += ($ref2[$i] - $ref1[$i]) * ($ref2[$i] - $ref1[$i]);
    $denominator += ($target[$i] - $ref1[$i]) * ($target[$i] - $ref1[$i]);
  }
  $delta = ($denominator > 0) ? (1 - sqrt($numerator / $denominator)) : 0;

  // Calculate the color that ref2 would be if the assumption was true.
  for ($i = 0; $i < 3; ++$i) {
    $ref3[$i] = $target[$i] + ($ref1[$i] - $target[$i]) * $delta;
  }

  // If the assumption is not true, there is a difference between ref2 and ref3.
  // We measure this in HSL space. Notation: x' = hsl(x).
  $ref2 = _color_rgb2hsl($ref2);
  $ref3 = _color_rgb2hsl($ref3);
  for ($i = 0; $i < 3; ++$i) {
    $shift[$i] = $ref2[$i] - $ref3[$i];
  }

  // Take the given color, and blend it towards the target.
  $given = _color_unpack($given, TRUE);
  for ($i = 0; $i < 3; ++$i) {
    $result[$i] = $target[$i] + ($given[$i] - $target[$i]) * $delta;
  }

  // Finally, we apply the extra shift in HSL space.
  // Note: if ref2 is a pure blend of ref1 and target, then |shift| = 0.
  $result = _color_rgb2hsl($result);
  for ($i = 0; $i < 3; ++$i) {
    $result[$i] = min(1, max(0, $result[$i] + $shift[$i]));
  }
  $result = _color_hsl2rgb($result);

  // Return hex color.
  return _color_pack($result, TRUE);
}

/**
 * Converts a hex triplet into a GD color.
 */
function _color_gd($img, $hex) {
  $c = array_merge(array($img), _color_unpack($hex));
  return call_user_func_array('imagecolorallocate', $c);
}

/**
 * Blends two hex colors and returns the GD color.
 */
function _color_blend($img, $hex1, $hex2, $alpha) {
  $in1 = _color_unpack($hex1);
  $in2 = _color_unpack($hex2);
  $out = array($img);
  for ($i = 0; $i < 3; ++$i) {
    $out[] = (int) ($in1[$i] + ($in2[$i] - $in1[$i]) * $alpha);
  }

  return call_user_func_array('imagecolorallocate', $out);
}

/**
 * Converts a hex color into an RGB triplet.
 */
function _color_unpack($hex, $normalize = FALSE) {
  $hex = substr($hex, 1);
  if (strlen($hex) == 3) {
    $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
  }
  $c = hexdec($hex);
  for ($i = 16; $i >= 0; $i -= 8) {
    $out[] = (($c >> $i) & 0xFF) / ($normalize ? 255 : 1);
  }

  return $out;
}

/**
 * Converts an RGB triplet to a hex color.
 */
function _color_pack($rgb, $normalize = FALSE) {
  $out = 0;
  foreach ($rgb as $k => $v) {
    $out |= (((int) ($v * ($normalize ? 255 : 1))) << (16 - $k * 8));
  }

  return '#' . str_pad(dechex($out), 6, 0, STR_PAD_LEFT);
}

/**
 * Converts an HSL triplet into RGB.
 */
function _color_hsl2rgb($hsl) {
  $h = $hsl[0];
  $s = $hsl[1];
  $l = $hsl[2];
  $m2 = ($l <= 0.5) ? $l * ($s + 1) : $l + $s - $l*$s;
  $m1 = $l * 2 - $m2;

  return array(
    _color_hue2rgb($m1, $m2, $h + 0.33333),
    _color_hue2rgb($m1, $m2, $h),
    _color_hue2rgb($m1, $m2, $h - 0.33333),
  );
}

/**
 * Helper function for _color_hsl2rgb().
 */
function _color_hue2rgb($m1, $m2, $h) {
  $h = ($h < 0) ? $h + 1 : (($h > 1) ? $h - 1 : $h);
  if ($h * 6 < 1) return $m1 + ($m2 - $m1) * $h * 6;
  if ($h * 2 < 1) return $m2;
  if ($h * 3 < 2) return $m1 + ($m2 - $m1) * (0.66666 - $h) * 6;

  return $m1;
}

/**
 * Converts an RGB triplet to HSL.
 */
function _color_rgb2hsl($rgb) {
  $r = $rgb[0];
  $g = $rgb[1];
  $b = $rgb[2];
  $min = min($r, min($g, $b));
  $max = max($r, max($g, $b));
  $delta = $max - $min;
  $l = ($min + $max) / 2;
  $s = 0;

  if ($l > 0 && $l < 1) {
    $s = $delta / ($l < 0.5 ? (2 * $l) : (2 - 2 * $l));
  }

  $h = 0;
  if ($delta > 0) {
    if ($max == $r && $max != $g) $h += ($g - $b) / $delta;
    if ($max == $g && $max != $b) $h += (2 + ($b - $r) / $delta);
    if ($max == $b && $max != $r) $h += (4 + ($r - $g) / $delta);
    $h /= 6;
  }

  return array($h, $s, $l);
}
