<?php

/**
 * @file
 * Install, update, and uninstall functions for the Filter module.
 */

/**
 * Implements hook_schema().
 */
function filter_schema() {
  $schema['filter'] = array(
    'description' => 'Table that maps filters (HTML corrector) to text formats (Filtered HTML).',
    'fields' => array(
      'format' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'description' => 'Foreign key: The {filter_format}.format to which this filter is assigned.',
      ),
      'module' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The origin module of the filter.',
      ),
      'name' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Name of the filter being referenced.',
      ),
      'weight' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Weight of filter within format.',
      ),
      'status' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Filter enabled status. (1 = enabled, 0 = disabled)',
      ),
      'settings' => array(
        'type' => 'blob',
        'not null' => FALSE,
        'size' => 'big',
        'serialize' => TRUE,
        'description' => 'A serialized array of name value pairs that store the filter settings for the specific format.',
      ),
    ),
    'primary key' => array('format', 'name'),
    'indexes' => array(
      'list' => array('weight', 'module', 'name'),
    ),
  );
  $schema['filter_format'] = array(
    'description' => 'Stores text formats: custom groupings of filters, such as Filtered HTML.',
    'fields' => array(
      'format' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'description' => 'Primary Key: Unique machine name of the format.',
      ),
      'name' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Name of the text format (Filtered HTML).',
        'translatable' => TRUE,
      ),
      'cache' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Flag to indicate whether format is cacheable. (1 = cacheable, 0 = not cacheable)',
      ),
      'status' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 1,
        'size' => 'tiny',
        'description' => 'The status of the text format. (1 = enabled, 0 = disabled)',
      ),
      'weight' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Weight of text format to use when listing.',
      ),
    ),
    'primary key' => array('format'),
    'unique keys' => array(
      'name' => array('name'),
    ),
    'indexes' => array(
      'status_weight' => array('status', 'weight'),
    ),
  );

  $schema['cache_filter'] = drupal_get_schema_unprocessed('system', 'cache');
  $schema['cache_filter']['description'] = 'Cache table for the Filter module to store already filtered pieces of text, identified by text format and hash of the text.';

  return $schema;
}

/**
 * Implements hook_install().
 */
function filter_install() {
  // All sites require at least one text format (the fallback format) that all
  // users have access to, so add it here. We initialize it as a simple, safe
  // plain text format with very basic formatting, but it can be modified by
  // installation profiles to have other properties.
  $plain_text_format = array(
    'format' => 'plain_text',
    'name' => 'Plain text',
    'weight' => 10,
    'filters' => array(
      // Escape all HTML.
      'filter_html_escape' => array(
        'weight' => 0,
        'status' => 1,
      ),
      // URL filter.
      'filter_url' => array(
        'weight' => 1,
        'status' => 1,
      ),
      // Line break filter.
      'filter_autop' => array(
        'weight' => 2,
        'status' => 1,
      ),
    ),
  );
  $plain_text_format = (object) $plain_text_format;
  filter_format_save($plain_text_format);

  // Set the fallback format to plain text.
  variable_set('filter_fallback_format', $plain_text_format->format);
}

/**
 * Implements hook_update_dependencies().
 */
function filter_update_dependencies() {
  // filter_update_7005() migrates role permissions and therefore must run
  // after the {role} and {role_permission} tables are properly set up, which
  // happens in user_update_7007().
  $dependencies['filter'][7005] = array(
    'user' => 7007,
  );

  return $dependencies;
}
/**
 * @addtogroup updates-6.x-to-7.x
 * @{
 */

/**
 * Upgrade the {filter_formats} table and rename it to {filter_format}.
 */
function filter_update_7000() {
  db_rename_table('filter_formats', 'filter_format');

  // Add the new {filter_format}.status and {filter_format}.weight column.
  db_add_field('filter_format', 'status', array(
    'type' => 'int',
    'unsigned' => TRUE,
    'not null' => TRUE,
    'default' => 1,
    'size' => 'tiny',
    'description' => 'The status of the text format. (1 = enabled, 0 = disabled)',
  ));
  db_add_field('filter_format', 'weight', array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
    'description' => 'Weight of text format to use when listing.',
  ), array(
    'indexes' => array(
      'status_weight' => array('status', 'weight'),
    ),
  ));
}

/**
 * Break out "escape HTML filter" option to its own filter.
 */
function filter_update_7001() {
  $result = db_query("SELECT format FROM {filter_format}")->fetchCol();
  $insert = db_insert('filters')->fields(array('format', 'module', 'delta', 'weight'));

  foreach ($result as $format_id) {
    // Deprecated constants FILTER_HTML_STRIP = 1 and FILTER_HTML_ESCAPE = 2.
    if (variable_get('filter_html_' . $format_id, 1) == 2) {
      $insert->values(array(
        'format' => $format_id,
        'module' => 'filter',
        'delta' => 4,
        'weight' => 0,
      ));
    }
    variable_del('filter_html_' . $format_id);
  }

  $insert->execute();
}

/**
 * Upgrade the {filter} table for core filters.
 */
function filter_update_7003() {
  // Duplicates the {filters} table since core cannot take care of the potential
  // contributed module filters.
  db_rename_table('filters', 'd6_upgrade_filter');
  // Creates the Drupal 7 filter table.
  $filter_table =  array(
    'description' => 'Table that maps filters (HTML corrector) to text formats (Filtered HTML).',
    'fields' => array(
      'format' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Foreign key: The {filter_format}.format to which this filter is assigned.',
      ),
      'module' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The origin module of the filter.',
      ),
      'name' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Name of the filter being referenced.',
      ),
      'weight' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Weight of filter within format.',
      ),
      'status' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Filter enabled status. (1 = enabled, 0 = disabled)',
      ),
      'settings' => array(
        'type' => 'blob',
        'not null' => FALSE,
        'size' => 'big',
        'serialize' => TRUE,
        'description' => 'A serialized array of name value pairs that store the filter settings for the specific format.',
      ),
    ),
    'primary key' => array('format', 'name'),
    'indexes' => array(
      'list' => array('weight', 'module', 'name'),
    ),
  );
  db_create_table('filter', $filter_table);

  // Get an array of the renamed filter deltas, organized by module.
  $renamed_deltas = array(
    'filter' => array(
      '0' => 'filter_html',
      '1' => 'filter_autop',
      '2' => 'filter_url',
      '3' => 'filter_htmlcorrector',
      '4' => 'filter_html_escape',
    ),
    'php' => array(
      '0' => 'php_code',
    ),
  );

  // Loop through each filter and make changes to the core filter table by
  // each record from the old to the new table.
  foreach ($renamed_deltas as $module => $deltas) {
    foreach ($deltas as $old_delta => $new_name) {
      $query = db_select('d6_upgrade_filter')
        ->fields('d6_upgrade_filter', array('format', 'weight'))
        ->condition('module', $module)
        ->condition('delta', $old_delta)
        ->distinct();

      foreach ($query->execute() as $record) {
        // Port the filter settings.
        $settings = array();
        if ($new_name == 'filter_html') {
          if ($setting = variable_get("allowed_html_{$record->format}", NULL)) {
            $settings['allowed_html'] = $setting;
            variable_del("allowed_html_{$record->format}");
          }
          if ($setting = variable_get("filter_html_help_{$record->format}", NULL)) {
            $settings['filter_html_help'] = $setting;
            variable_del("filter_html_help_{$record->format}");
          }
          if ($setting = variable_get("filter_html_nofollow_{$record->format}", NULL)) {
            $settings['filter_html_nofollow'] = $setting;
            variable_del("filter_html_nofollow_{$record->format}");
          }
        }
        elseif ($new_name == 'filter_url') {
          if ($setting = variable_get("filter_url_length_{$record->format}", NULL)) {
            $settings['filter_url_length'] = $setting;
            variable_del("filter_url_length_{$record->format}");
          }
        }

        db_insert('filter')
          ->fields(array(
            'format' => $record->format,
            'module' => $module,
            'name' => $new_name,
            'weight' => $record->weight,
            'settings' => serialize($settings),
            'status' => 1,
          ))
          ->execute();
      }
      db_delete('d6_upgrade_filter')
        ->condition('module', $module)
        ->condition('delta', $old_delta)
        ->execute();
    }
  }
}

/**
 * Integrate text formats with the user permissions system.
 *
 * This function converts text format role assignments to use the new text
 * format permissions introduced in Drupal 7, creates a fallback (plain text)
 * format that is available to all users, and explicitly sets the text format
 * in cases that used to rely on a single site-wide default.
 */
function filter_update_7005() {
  // Move role data from the filter system to the user permission system.
  $all_roles = array_keys(user_roles());
  $default_format = variable_get('filter_default_format', 1);
  $result = db_query("SELECT * FROM {filter_format}");
  foreach ($result as $format) {
    // We need to assign the default format to all roles (regardless of what
    // was stored in the database) to preserve the behavior of the site at the
    // moment of the upgrade.
    $format_roles = ($format->format == $default_format ? $all_roles : explode(',', $format->roles));
    foreach ($format_roles as $format_role) {
      if (in_array($format_role, $all_roles)) {
        _update_7000_user_role_grant_permissions($format_role, array('use text format ' . $format->format), 'filter');
      }
    }
  }

  // Drop the roles field from the {filter_format} table.
  db_drop_field('filter_format', 'roles');

  // Add a fallback text format which outputs plain text and appears last on
  // the list for all users. Generate a unique name for it, starting with
  // "Plain text".
  $start_name = 'Plain text';
  $format_name = $start_name;
  while ($format = db_query('SELECT format FROM {filter_format} WHERE name = :name', array(':name' => $format_name))->fetchField()) {
    $id = empty($id) ? 2 : $id + 1;
    $format_name = $start_name . ' ' . $id;
  }

  // Insert the filter format.
  $format_id = db_insert('filter_format')
    ->fields(array(
      'name' => $format_name,
      'cache' => 1,
      'weight' => 1,
      'status' => 1,
    ))
    ->execute();

  // This format should output plain text, so we escape all HTML and apply the
  // line break and URL filters only.
  db_insert('filter')
    ->fields(array(
      'format',
      'name',
      'weight',
      'status',
      'module',
      'settings',
    ))
    ->values(array(
      'format' => $format_id,
      'name' => 'filter_html_escape',
      'weight' => 0,
      'status' => 1,
      'module' => 'filter',
      'settings' => serialize(array()),
    ))
    ->values(array(
      'format' => $format_id,
      'name' => 'filter_url',
      'weight' => 1,
      'status' => 1,
      'module' => 'filter',
      'settings' => serialize(array()),
    ))
    ->values(array(
      'format' => $format_id,
      'name' => 'filter_autop',
      'weight' => 2,
      'status' => 1,
      'module' => 'filter',
      'settings' => serialize(array()),
    ))
    ->execute();

  variable_set('filter_fallback_format', $format_id);
  drupal_set_message('A new <em>Plain text</em> format has been created which will be available to all users. You can configure this text format on the <a href="' . url('admin/config/content/formats/' . $format) . '">text format configuration page</a>.');

  // Move the former site-wide default text format to the top of the list, so
  // that it continues to be the default text format for all users.
  db_update('filter_format')
    ->fields(array('weight' => -1))
    ->condition('format', $default_format)
    ->execute();

  // We do not delete the 'filter_default_format' variable, since other modules
  // need it in their update functions; for an example, see user_update_7010().
  // @todo This variable can be deleted in Drupal 8.
}

/**
 * Grant usage of all text formats to user roles having the 'administer filters' permission.
 */
function filter_update_7008() {
  // Build the list of permissions to grant.
  $permissions = array();
  foreach (db_query('SELECT format FROM {filter_format}')->fetchCol() as $format_id) {
    if ($format_id != variable_get('filter_fallback_format')) {
      $permissions[] = 'use text format ' . $format_id;
    }
  }
  // Grant text format permissions to all roles that can 'administer filters'.
  // Albeit anonymous users *should not* have the permission, we cannot presume
  // that they do not or must not.
  if ($roles = user_roles(FALSE, 'administer filters')) {
    foreach ($roles as $rid => $name) {
      _update_7000_user_role_grant_permissions($rid, $permissions, 'filter');
    }
  }
}

/**
 * Converts fields that store serialized variables from text to blob.
 */
function filter_update_7009() {
  $schema = system_schema_cache_7054();
  db_drop_table('cache_filter');
  db_create_table('cache_filter', $schema);
}

/**
 * Change {filter_format}.format and {filter}.format into varchar.
 */
function filter_update_7010() {
  db_change_field('filter_format', 'format', 'format', array(
    'type' => 'varchar',
    'length' => 255,
    'not null' => TRUE,
    'description' => 'Primary Key: Unique machine name of the format.',
  ));
  db_change_field('filter', 'format', 'format', array(
    'type' => 'varchar',
    'length' => 255,
    'not null' => TRUE,
    'description' => 'Foreign key: The {filter_format}.format to which this filter is assigned.',
  ));
}

/**
 * @} End of "addtogroup updates-6.x-to-7.x".
 */
