<?php

/**
 * @file
 * Tests for filter.module.
 */

/**
 * Tests for text format and filter CRUD operations.
 */
class FilterCRUDTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Filter CRUD operations',
      'description' => 'Test creation, loading, updating, deleting of text formats and filters.',
      'group' => 'Filter',
    );
  }

  function setUp() {
    parent::setUp('filter_test');
  }

  /**
   * Tests CRUD operations for text formats and filters.
   */
  function testTextFormatCRUD() {
    // Add a text format with minimum data only.
    $format = new stdClass();
    $format->format = 'empty_format';
    $format->name = 'Empty format';
    filter_format_save($format);
    $this->verifyTextFormat($format);
    $this->verifyFilters($format);

    // Add another text format specifying all possible properties.
    $format = new stdClass();
    $format->format = 'custom_format';
    $format->name = 'Custom format';
    $format->filters = array(
      'filter_url' => array(
        'status' => 1,
        'settings' => array(
          'filter_url_length' => 30,
        ),
      ),
    );
    filter_format_save($format);
    $this->verifyTextFormat($format);
    $this->verifyFilters($format);

    // Alter some text format properties and save again.
    $format->name = 'Altered format';
    $format->filters['filter_url']['status'] = 0;
    $format->filters['filter_autop']['status'] = 1;
    filter_format_save($format);
    $this->verifyTextFormat($format);
    $this->verifyFilters($format);

    // Add a uncacheable filter and save again.
    $format->filters['filter_test_uncacheable']['status'] = 1;
    filter_format_save($format);
    $this->verifyTextFormat($format);
    $this->verifyFilters($format);

    // Disable the text format.
    filter_format_disable($format);

    $db_format = db_query("SELECT * FROM {filter_format} WHERE format = :format", array(':format' => $format->format))->fetchObject();
    $this->assertFalse($db_format->status, 'Database: Disabled text format is marked as disabled.');
    $formats = filter_formats();
    $this->assertTrue(!isset($formats[$format->format]), 'filter_formats: Disabled text format no longer exists.');

    // Add a new format to check for Xss in format name.
    $format = new stdClass();
    $format->format = 'xss_format';
    $format->name = '<script>alert(123)</script>';
    filter_format_save($format);
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(filter_permission_name($format) => 1));
    $this->drupalGet('filter/tips');
    $this->assertNoRaw($format->name, 'Text format name contains no xss.');
  }

  /**
   * Verifies that a text format is properly stored.
   */
  function verifyTextFormat($format) {
    $t_args = array('%format' => $format->name);
    // Verify text format database record.
    $db_format = db_select('filter_format', 'ff')
      ->fields('ff')
      ->condition('format', $format->format)
      ->execute()
      ->fetchObject();
    $this->assertEqual($db_format->format, $format->format, format_string('Database: Proper format id for text format %format.', $t_args));
    $this->assertEqual($db_format->name, $format->name, format_string('Database: Proper title for text format %format.', $t_args));
    $this->assertEqual($db_format->cache, $format->cache, format_string('Database: Proper cache indicator for text format %format.', $t_args));
    $this->assertEqual($db_format->weight, $format->weight, format_string('Database: Proper weight for text format %format.', $t_args));

    // Verify filter_format_load().
    $filter_format = filter_format_load($format->format);
    $this->assertEqual($filter_format->format, $format->format, format_string('filter_format_load: Proper format id for text format %format.', $t_args));
    $this->assertEqual($filter_format->name, $format->name, format_string('filter_format_load: Proper title for text format %format.', $t_args));
    $this->assertEqual($filter_format->cache, $format->cache, format_string('filter_format_load: Proper cache indicator for text format %format.', $t_args));
    $this->assertEqual($filter_format->weight, $format->weight, format_string('filter_format_load: Proper weight for text format %format.', $t_args));

    // Verify the 'cache' text format property according to enabled filters.
    $filter_info = filter_get_filters();
    $filters = filter_list_format($filter_format->format);
    $cacheable = TRUE;
    foreach ($filters as $name => $filter) {
      // If this filter is not cacheable, update $cacheable accordingly, so we
      // can verify $format->cache after iterating over all filters.
      if ($filter->status && isset($filter_info[$name]['cache']) && !$filter_info[$name]['cache']) {
        $cacheable = FALSE;
        break;
      }
    }
    $this->assertEqual($filter_format->cache, $cacheable, 'Text format contains proper cache property.');
  }

  /**
   * Verifies that filters are properly stored for a text format.
   */
  function verifyFilters($format) {
    // Verify filter database records.
    $filters = db_query("SELECT * FROM {filter} WHERE format = :format", array(':format' => $format->format))->fetchAllAssoc('name');
    $format_filters = $format->filters;
    foreach ($filters as $name => $filter) {
      $t_args = array('%format' => $format->name, '%filter' => $name);

      // Verify that filter status is properly stored.
      $this->assertEqual($filter->status, $format_filters[$name]['status'], format_string('Database: Proper status for %filter in text format %format.', $t_args));

      // Verify that filter settings were properly stored.
      $this->assertEqual(unserialize($filter->settings), isset($format_filters[$name]['settings']) ? $format_filters[$name]['settings'] : array(), format_string('Database: Proper filter settings for %filter in text format %format.', $t_args));

      // Verify that each filter has a module name assigned.
      $this->assertTrue(!empty($filter->module), format_string('Database: Proper module name for %filter in text format %format.', $t_args));

      // Remove the filter from the copy of saved $format to check whether all
      // filters have been processed later.
      unset($format_filters[$name]);
    }
    // Verify that all filters have been processed.
    $this->assertTrue(empty($format_filters), 'Database contains values for all filters in the saved format.');

    // Verify filter_list_format().
    $filters = filter_list_format($format->format);
    $format_filters = $format->filters;
    foreach ($filters as $name => $filter) {
      $t_args = array('%format' => $format->name, '%filter' => $name);

      // Verify that filter status is properly stored.
      $this->assertEqual($filter->status, $format_filters[$name]['status'], format_string('filter_list_format: Proper status for %filter in text format %format.', $t_args));

      // Verify that filter settings were properly stored.
      $this->assertEqual($filter->settings, isset($format_filters[$name]['settings']) ? $format_filters[$name]['settings'] : array(), format_string('filter_list_format: Proper filter settings for %filter in text format %format.', $t_args));

      // Verify that each filter has a module name assigned.
      $this->assertTrue(!empty($filter->module), format_string('filter_list_format: Proper module name for %filter in text format %format.', $t_args));

      // Remove the filter from the copy of saved $format to check whether all
      // filters have been processed later.
      unset($format_filters[$name]);
    }
    // Verify that all filters have been processed.
    $this->assertTrue(empty($format_filters), 'filter_list_format: Loaded filters contain values for all filters in the saved format.');
  }
}

/**
 * Tests the administrative functionality of the Filter module.
 */
class FilterAdminTestCase extends DrupalWebTestCase {
  protected $admin_user;
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Filter administration functionality',
      'description' => 'Thoroughly test the administrative interface of the filter module.',
      'group' => 'Filter',
    );
  }

  function setUp() {
    parent::setUp();

    // Create users.
    $filtered_html_format = filter_format_load('filtered_html');
    $full_html_format = filter_format_load('full_html');
    $this->admin_user = $this->drupalCreateUser(array(
      'administer filters',
      filter_permission_name($filtered_html_format),
      filter_permission_name($full_html_format),
    ));

    $this->web_user = $this->drupalCreateUser(array('create page content', 'edit own page content'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Tests the format administration functionality.
   */
  function testFormatAdmin() {
    // Add text format.
    $this->drupalGet('admin/config/content/formats');
    $this->clickLink('Add text format');
    $format_id = drupal_strtolower($this->randomName());
    $name = $this->randomName();
    $edit = array(
      'format' => $format_id,
      'name' => $name,
    );
    $this->drupalPost(NULL, $edit, t('Save configuration'));

    // Verify default weight of the text format.
    $this->drupalGet('admin/config/content/formats');
    $this->assertFieldByName("formats[$format_id][weight]", 0, 'Text format weight was saved.');

    // Change the weight of the text format.
    $edit = array(
      "formats[$format_id][weight]" => 5,
    );
    $this->drupalPost('admin/config/content/formats', $edit, t('Save changes'));
    $this->assertFieldByName("formats[$format_id][weight]", 5, 'Text format weight was saved.');

    // Edit text format.
    $this->drupalGet('admin/config/content/formats');
    $this->assertLinkByHref('admin/config/content/formats/' . $format_id);
    $this->drupalGet('admin/config/content/formats/' . $format_id);
    $this->drupalPost(NULL, array(), t('Save configuration'));

    // Verify that the custom weight of the text format has been retained.
    $this->drupalGet('admin/config/content/formats');
    $this->assertFieldByName("formats[$format_id][weight]", 5, 'Text format weight was retained.');

    // Disable text format.
    $this->assertLinkByHref('admin/config/content/formats/' . $format_id . '/disable');
    $this->drupalGet('admin/config/content/formats/' . $format_id . '/disable');
    $this->drupalPost(NULL, array(), t('Disable'));

    // Verify that disabled text format no longer exists.
    $this->drupalGet('admin/config/content/formats/' . $format_id);
    $this->assertResponse(404, 'Disabled text format no longer exists.');

    // Attempt to create a format of the same machine name as the disabled
    // format but with a different human readable name.
    $edit = array(
      'format' => $format_id,
      'name' => 'New format',
    );
    $this->drupalPost('admin/config/content/formats/add', $edit, t('Save configuration'));
    $this->assertText('The machine-readable name is already in use. It must be unique.');

    // Attempt to create a format of the same human readable name as the
    // disabled format but with a different machine name.
    $edit = array(
      'format' => 'new_format',
      'name' => $name,
    );
    $this->drupalPost('admin/config/content/formats/add', $edit, t('Save configuration'));
    $this->assertRaw(t('Text format names must be unique. A format named %name already exists.', array(
      '%name' => $name,
    )));
  }

  /**
   * Tests filter administration functionality.
   */
  function testFilterAdmin() {
    // URL filter.
    $first_filter = 'filter_url';
    // Line filter.
    $second_filter = 'filter_autop';

    $filtered = 'filtered_html';
    $full = 'full_html';
    $plain = 'plain_text';

    // Check that the fallback format exists and cannot be disabled.
    $this->assertTrue($plain == filter_fallback_format(), 'The fallback format is set to plain text.');
    $this->drupalGet('admin/config/content/formats');
    $this->assertNoRaw('admin/config/content/formats/' . $plain . '/disable', 'Disable link for the fallback format not found.');
    $this->drupalGet('admin/config/content/formats/' . $plain . '/disable');
    $this->assertResponse(403, 'The fallback format cannot be disabled.');

    // Verify access permissions to Full HTML format.
    $this->assertTrue(filter_access(filter_format_load($full), $this->admin_user), 'Admin user may use Full HTML.');
    $this->assertFalse(filter_access(filter_format_load($full), $this->web_user), 'Web user may not use Full HTML.');

    // Add an additional tag.
    $edit = array();
    $edit['filters[filter_html][settings][allowed_html]'] = '<a> <em> <strong> <cite> <code> <ul> <ol> <li> <dl> <dt> <dd> <quote>';
    $this->drupalPost('admin/config/content/formats/' . $filtered, $edit, t('Save configuration'));
    $this->assertFieldByName('filters[filter_html][settings][allowed_html]', $edit['filters[filter_html][settings][allowed_html]'], 'Allowed HTML tag added.');

    $result = db_query('SELECT * FROM {cache_filter}')->fetchObject();
    $this->assertFalse($result, 'Cache cleared.');

    $elements = $this->xpath('//select[@name=:first]/following::select[@name=:second]', array(
      ':first' => 'filters[' . $first_filter . '][weight]',
      ':second' => 'filters[' . $second_filter . '][weight]',
    ));
    $this->assertTrue(!empty($elements), 'Order confirmed in admin interface.');

    // Reorder filters.
    $edit = array();
    $edit['filters[' . $second_filter . '][weight]'] = 1;
    $edit['filters[' . $first_filter . '][weight]'] = 2;
    $this->drupalPost(NULL, $edit, t('Save configuration'));
    $this->assertFieldByName('filters[' . $second_filter . '][weight]', 1, 'Order saved successfully.');
    $this->assertFieldByName('filters[' . $first_filter . '][weight]', 2, 'Order saved successfully.');

    $elements = $this->xpath('//select[@name=:first]/following::select[@name=:second]', array(
      ':first' => 'filters[' . $second_filter . '][weight]',
      ':second' => 'filters[' . $first_filter . '][weight]',
    ));
    $this->assertTrue(!empty($elements), 'Reorder confirmed in admin interface.');

    $result = db_query('SELECT * FROM {filter} WHERE format = :format ORDER BY weight ASC', array(':format' => $filtered));
    $filters = array();
    foreach ($result as $filter) {
      if ($filter->name == $second_filter || $filter->name == $first_filter) {
        $filters[] = $filter;
      }
    }
    $this->assertTrue(($filters[0]->name == $second_filter && $filters[1]->name == $first_filter), 'Order confirmed in database.');

    // Add format.
    $edit = array();
    $edit['format'] = drupal_strtolower($this->randomName());
    $edit['name'] = $this->randomName();
    $edit['roles[' . DRUPAL_AUTHENTICATED_RID . ']'] = 1;
    $edit['filters[' . $second_filter . '][status]'] = TRUE;
    $edit['filters[' . $first_filter . '][status]'] = TRUE;
    $this->drupalPost('admin/config/content/formats/add', $edit, t('Save configuration'));
    $this->assertRaw(t('Added text format %format.', array('%format' => $edit['name'])), 'New filter created.');

    drupal_static_reset('filter_formats');
    $format = filter_format_load($edit['format']);
    $this->assertNotNull($format, 'Format found in database.');

    $this->assertFieldByName('roles[' . DRUPAL_AUTHENTICATED_RID . ']', '', 'Role found.');
    $this->assertFieldByName('filters[' . $second_filter . '][status]', '', 'Line break filter found.');
    $this->assertFieldByName('filters[' . $first_filter . '][status]', '', 'Url filter found.');

    // Disable new filter.
    $this->drupalPost('admin/config/content/formats/' . $format->format . '/disable', array(), t('Disable'));
    $this->assertRaw(t('Disabled text format %format.', array('%format' => $edit['name'])), 'Format successfully disabled.');

    // Allow authenticated users on full HTML.
    $format = filter_format_load($full);
    $edit = array();
    $edit['roles[' . DRUPAL_ANONYMOUS_RID . ']'] = 0;
    $edit['roles[' . DRUPAL_AUTHENTICATED_RID . ']'] = 1;
    $this->drupalPost('admin/config/content/formats/' . $full, $edit, t('Save configuration'));
    $this->assertRaw(t('The text format %format has been updated.', array('%format' => $format->name)), 'Full HTML format successfully updated.');

    // Switch user.
    $this->drupalLogout();
    $this->drupalLogin($this->web_user);

    $this->drupalGet('node/add/page');
    $this->assertRaw('<option value="' . $full . '">Full HTML</option>', 'Full HTML filter accessible.');

    // Use filtered HTML and see if it removes tags that are not allowed.
    $body = '<em>' . $this->randomName() . '</em>';
    $extra_text = 'text';
    $text = $body . '<random>' . $extra_text . '</random>';

    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $this->randomName();
    $edit["body[$langcode][0][value]"] = $text;
    $edit["body[$langcode][0][format]"] = $filtered;
    $this->drupalPost('node/add/page', $edit, t('Save'));
    $this->assertRaw(t('Basic page %title has been created.', array('%title' => $edit["title"])), 'Filtered node created.');

    $node = $this->drupalGetNodeByTitle($edit["title"]);
    $this->assertTrue($node, 'Node found in database.');

    $this->drupalGet('node/' . $node->nid);
    $this->assertRaw($body . $extra_text, 'Filter removed invalid tag.');

    // Use plain text and see if it escapes all tags, whether allowed or not.
    $edit = array();
    $edit["body[$langcode][0][format]"] = $plain;
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->drupalGet('node/' . $node->nid);
    $this->assertText(check_plain($text), 'The "Plain text" text format escapes all HTML tags.');

    // Switch user.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);

    // Clean up.
    // Allowed tags.
    $edit = array();
    $edit['filters[filter_html][settings][allowed_html]'] = '<a> <em> <strong> <cite> <code> <ul> <ol> <li> <dl> <dt> <dd>';
    $this->drupalPost('admin/config/content/formats/' . $filtered, $edit, t('Save configuration'));
    $this->assertFieldByName('filters[filter_html][settings][allowed_html]', $edit['filters[filter_html][settings][allowed_html]'], 'Changes reverted.');

    // Full HTML.
    $edit = array();
    $edit['roles[' . DRUPAL_AUTHENTICATED_RID . ']'] = FALSE;
    $this->drupalPost('admin/config/content/formats/' . $full, $edit, t('Save configuration'));
    $this->assertRaw(t('The text format %format has been updated.', array('%format' => $format->name)), 'Full HTML format successfully reverted.');
    $this->assertFieldByName('roles[' . DRUPAL_AUTHENTICATED_RID . ']', $edit['roles[' . DRUPAL_AUTHENTICATED_RID . ']'], 'Changes reverted.');

    // Filter order.
    $edit = array();
    $edit['filters[' . $second_filter . '][weight]'] = 2;
    $edit['filters[' . $first_filter . '][weight]'] = 1;
    $this->drupalPost('admin/config/content/formats/' . $filtered, $edit, t('Save configuration'));
    $this->assertFieldByName('filters[' . $second_filter . '][weight]', $edit['filters[' . $second_filter . '][weight]'], 'Changes reverted.');
    $this->assertFieldByName('filters[' . $first_filter . '][weight]', $edit['filters[' . $first_filter . '][weight]'], 'Changes reverted.');
  }

  /**
   * Tests the URL filter settings form is properly validated.
   */
  function testUrlFilterAdmin() {
    // The form does not save with an invalid filter URL length.
    $edit = array(
      'filters[filter_url][settings][filter_url_length]' => $this->randomName(4),
    );
    $this->drupalPost('admin/config/content/formats/filtered_html', $edit, t('Save configuration'));
    $this->assertNoRaw(t('The text format %format has been updated.', array('%format' => 'Filtered HTML')));
  }
}

/**
 * Tests the filter format access functionality in the Filter module.
 */
class FilterFormatAccessTestCase extends DrupalWebTestCase {
  /**
   * A user with administrative permissions.
   *
   * @var object
   */
  protected $admin_user;

  /**
   * A user with 'administer filters' permission.
   *
   * @var object
   */
  protected $filter_admin_user;

  /**
   * A user with permission to create and edit own content.
   *
   * @var object
   */
  protected $web_user;

  /**
   * An object representing an allowed text format.
   *
   * @var object
   */
  protected $allowed_format;

  /**
   * An object representing a disallowed text format.
   *
   * @var object
   */
  protected $disallowed_format;

  public static function getInfo() {
    return array(
      'name' => 'Filter format access',
      'description' => 'Tests access to text formats.',
      'group' => 'Filter',
    );
  }

  function setUp() {
    parent::setUp();

    // Create a user who can administer text formats, but does not have
    // specific permission to use any of them.
    $this->filter_admin_user = $this->drupalCreateUser(array(
      'administer filters',
      'create page content',
      'edit any page content',
    ));

    // Create two text formats.
    $this->drupalLogin($this->filter_admin_user);
    $formats = array();
    for ($i = 0; $i < 2; $i++) {
      $edit = array(
        'format' => drupal_strtolower($this->randomName()),
        'name' => $this->randomName(),
      );
      $this->drupalPost('admin/config/content/formats/add', $edit, t('Save configuration'));
      $this->resetFilterCaches();
      $formats[] = filter_format_load($edit['format']);
    }
    list($this->allowed_format, $this->disallowed_format) = $formats;
    $this->drupalLogout();

    // Create a regular user with access to one of the formats.
    $this->web_user = $this->drupalCreateUser(array(
      'create page content',
      'edit any page content',
      filter_permission_name($this->allowed_format),
    ));

    // Create an administrative user who has access to use both formats.
    $this->admin_user = $this->drupalCreateUser(array(
      'administer filters',
      'create page content',
      'edit any page content',
      filter_permission_name($this->allowed_format),
      filter_permission_name($this->disallowed_format),
    ));
  }

  /**
   * Tests the Filter format access permissions functionality.
   */
  function testFormatPermissions() {
    // Make sure that a regular user only has access to the text format they
    // were granted access to, as well to the fallback format.
    $this->assertTrue(filter_access($this->allowed_format, $this->web_user), 'A regular user has access to a text format they were granted access to.');
    $this->assertFalse(filter_access($this->disallowed_format, $this->web_user), 'A regular user does not have access to a text format they were not granted access to.');
    $this->assertTrue(filter_access(filter_format_load(filter_fallback_format()), $this->web_user), 'A regular user has access to the fallback format.');

    // Perform similar checks as above, but now against the entire list of
    // available formats for this user.
    $this->assertTrue(in_array($this->allowed_format->format, array_keys(filter_formats($this->web_user))), 'The allowed format appears in the list of available formats for a regular user.');
    $this->assertFalse(in_array($this->disallowed_format->format, array_keys(filter_formats($this->web_user))), 'The disallowed format does not appear in the list of available formats for a regular user.');
    $this->assertTrue(in_array(filter_fallback_format(), array_keys(filter_formats($this->web_user))), 'The fallback format appears in the list of available formats for a regular user.');

    // Make sure that a regular user only has permission to use the format
    // they were granted access to.
    $this->assertTrue(user_access(filter_permission_name($this->allowed_format), $this->web_user), 'A regular user has permission to use the allowed text format.');
    $this->assertFalse(user_access(filter_permission_name($this->disallowed_format), $this->web_user), 'A regular user does not have permission to use the disallowed text format.');

    // Make sure that the allowed format appears on the node form and that
    // the disallowed format does not.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node/add/page');
    $langcode = LANGUAGE_NONE;
    $elements = $this->xpath('//select[@name=:name]/option', array(
      ':name' => "body[$langcode][0][format]",
      ':option' => $this->allowed_format->format,
    ));
    $options = array();
    foreach ($elements as $element) {
      $options[(string) $element['value']] = $element;
    }
    $this->assertTrue(isset($options[$this->allowed_format->format]), 'The allowed text format appears as an option when adding a new node.');
    $this->assertFalse(isset($options[$this->disallowed_format->format]), 'The disallowed text format does not appear as an option when adding a new node.');
    $this->assertTrue(isset($options[filter_fallback_format()]), 'The fallback format appears as an option when adding a new node.');

    // Check regular user access to the filter tips pages.
    $this->drupalGet('filter/tips/' . $this->allowed_format->format);
    $this->assertResponse(200);
    $this->drupalGet('filter/tips/' . $this->disallowed_format->format);
    $this->assertResponse(403);
    $this->drupalGet('filter/tips/' . filter_fallback_format());
    $this->assertResponse(200);
    $this->drupalGet('filter/tips/invalid-format');
    $this->assertResponse(404);

    // Check admin user access to the filter tips pages.
    $this->drupalLogin($this->admin_user);
    $this->drupalGet('filter/tips/' . $this->allowed_format->format);
    $this->assertResponse(200);
    $this->drupalGet('filter/tips/' . $this->disallowed_format->format);
    $this->assertResponse(200);
    $this->drupalGet('filter/tips/' . filter_fallback_format());
    $this->assertResponse(200);
    $this->drupalGet('filter/tips/invalid-format');
    $this->assertResponse(404);
  }

  /**
   * Tests if text format is available to a role.
   */
  function testFormatRoles() {
    // Get the role ID assigned to the regular user; it must be the maximum.
    $rid = max(array_keys($this->web_user->roles));

    // Check that this role appears in the list of roles that have access to an
    // allowed text format, but does not appear in the list of roles that have
    // access to a disallowed text format.
    $this->assertTrue(in_array($rid, array_keys(filter_get_roles_by_format($this->allowed_format))), 'A role which has access to a text format appears in the list of roles that have access to that format.');
    $this->assertFalse(in_array($rid, array_keys(filter_get_roles_by_format($this->disallowed_format))), 'A role which does not have access to a text format does not appear in the list of roles that have access to that format.');

    // Check that the correct text format appears in the list of formats
    // available to that role.
    $this->assertTrue(in_array($this->allowed_format->format, array_keys(filter_get_formats_by_role($rid))), 'A text format which a role has access to appears in the list of formats available to that role.');
    $this->assertFalse(in_array($this->disallowed_format->format, array_keys(filter_get_formats_by_role($rid))), 'A text format which a role does not have access to does not appear in the list of formats available to that role.');

    // Check that the fallback format is always allowed.
    $this->assertEqual(filter_get_roles_by_format(filter_format_load(filter_fallback_format())), user_roles(), 'All roles have access to the fallback format.');
    $this->assertTrue(in_array(filter_fallback_format(), array_keys(filter_get_formats_by_role($rid))), 'The fallback format appears in the list of allowed formats for any role.');
  }

  /**
   * Tests editing a page using a disallowed text format.
   *
   * Verifies that regular users and administrators are able to edit a page, but
   * not allowed to change the fields which use an inaccessible text format.
   * Also verifies that fields which use a text format that does not exist can
   * be edited by administrators only, but that the administrator is forced to
   * choose a new format before saving the page.
   */
  function testFormatWidgetPermissions() {
    $langcode = LANGUAGE_NONE;
    $title_key = "title";
    $body_value_key = "body[$langcode][0][value]";
    $body_format_key = "body[$langcode][0][format]";

    // Create node to edit.
    $this->drupalLogin($this->admin_user);
    $edit = array();
    $edit['title'] = $this->randomName(8);
    $edit[$body_value_key] = $this->randomName(16);
    $edit[$body_format_key] = $this->disallowed_format->format;
    $this->drupalPost('node/add/page', $edit, t('Save'));
    $node = $this->drupalGetNodeByTitle($edit['title']);

    // Try to edit with a less privileged user.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node/' . $node->nid);
    $this->clickLink(t('Edit'));

    // Verify that body field is read-only and contains replacement value.
    $this->assertFieldByXPath("//textarea[@name='$body_value_key' and @disabled='disabled']", t('This field has been disabled because you do not have sufficient permissions to edit it.'), 'Text format access denied message found.');

    // Verify that title can be changed, but preview displays original body.
    $new_edit = array();
    $new_edit['title'] = $this->randomName(8);
    $this->drupalPost(NULL, $new_edit, t('Preview'));
    $this->assertText($edit[$body_value_key], 'Old body found in preview.');

    // Save and verify that only the title was changed.
    $this->drupalPost(NULL, $new_edit, t('Save'));
    $this->assertNoText($edit['title'], 'Old title not found.');
    $this->assertText($new_edit['title'], 'New title found.');
    $this->assertText($edit[$body_value_key], 'Old body found.');

    // Check that even an administrator with "administer filters" permission
    // cannot edit the body field if they do not have specific permission to
    // use its stored format. (This must be disallowed so that the
    // administrator is never forced to switch the text format to something
    // else.)
    $this->drupalLogin($this->filter_admin_user);
    $this->drupalGet('node/' . $node->nid . '/edit');
    $this->assertFieldByXPath("//textarea[@name='$body_value_key' and @disabled='disabled']", t('This field has been disabled because you do not have sufficient permissions to edit it.'), 'Text format access denied message found.');

    // Disable the text format used above.
    filter_format_disable($this->disallowed_format);
    $this->resetFilterCaches();

    // Log back in as the less privileged user and verify that the body field
    // is still disabled, since the less privileged user should not be able to
    // edit content that does not have an assigned format.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node/' . $node->nid . '/edit');
    $this->assertFieldByXPath("//textarea[@name='$body_value_key' and @disabled='disabled']", t('This field has been disabled because you do not have sufficient permissions to edit it.'), 'Text format access denied message found.');

    // Log back in as the filter administrator and verify that the body field
    // can be edited.
    $this->drupalLogin($this->filter_admin_user);
    $this->drupalGet('node/' . $node->nid . '/edit');
    $this->assertNoFieldByXPath("//textarea[@name='$body_value_key' and @disabled='disabled']", NULL, 'Text format access denied message not found.');
    $this->assertFieldByXPath("//select[@name='$body_format_key']", NULL, 'Text format selector found.');

    // Verify that trying to save the node without selecting a new text format
    // produces an error message, and does not result in the node being saved.
    $old_title = $new_edit['title'];
    $new_title = $this->randomName(8);
    $edit = array('title' => $new_title);
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->assertText(t('!name field is required.', array('!name' => t('Text format'))), 'Error message is displayed.');
    $this->drupalGet('node/' . $node->nid);
    $this->assertText($old_title, 'Old title found.');
    $this->assertNoText($new_title, 'New title not found.');

    // Now select a new text format and make sure the node can be saved.
    $edit[$body_format_key] = filter_fallback_format();
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->assertUrl('node/' . $node->nid);
    $this->assertText($new_title, 'New title found.');
    $this->assertNoText($old_title, 'Old title not found.');

    // Switch the text format to a new one, then disable that format and all
    // other formats on the site (leaving only the fallback format).
    $this->drupalLogin($this->admin_user);
    $edit = array($body_format_key => $this->allowed_format->format);
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->assertUrl('node/' . $node->nid);
    foreach (filter_formats() as $format) {
      if ($format->format != filter_fallback_format()) {
        filter_format_disable($format);
      }
    }

    // Since there is now only one available text format, the widget for
    // selecting a text format would normally not display when the content is
    // edited. However, we need to verify that the filter administrator still
    // is forced to make a conscious choice to reassign the text to a different
    // format.
    $this->drupalLogin($this->filter_admin_user);
    $old_title = $new_title;
    $new_title = $this->randomName(8);
    $edit = array('title' => $new_title);
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->assertText(t('!name field is required.', array('!name' => t('Text format'))), 'Error message is displayed.');
    $this->drupalGet('node/' . $node->nid);
    $this->assertText($old_title, 'Old title found.');
    $this->assertNoText($new_title, 'New title not found.');
    $edit[$body_format_key] = filter_fallback_format();
    $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
    $this->assertUrl('node/' . $node->nid);
    $this->assertText($new_title, 'New title found.');
    $this->assertNoText($old_title, 'Old title not found.');
  }

  /**
   * Rebuilds text format and permission caches in the thread running the tests.
   */
  protected function resetFilterCaches() {
    filter_formats_reset();
    $this->checkPermissions(array(), TRUE);
  }
}

/**
 * Tests the default filter functionality in the Filter module.
 */
class FilterDefaultFormatTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Default text format functionality',
      'description' => 'Test the default text formats for different users.',
      'group' => 'Filter',
    );
  }

  /**
   * Tests if the default text format is accessible to users.
   */
  function testDefaultTextFormats() {
    // Create two text formats, and two users. The first user has access to
    // both formats, but the second user only has access to the second one.
    $admin_user = $this->drupalCreateUser(array('administer filters'));
    $this->drupalLogin($admin_user);
    $formats = array();
    for ($i = 0; $i < 2; $i++) {
      $edit = array(
        'format' => drupal_strtolower($this->randomName()),
        'name' => $this->randomName(),
      );
      $this->drupalPost('admin/config/content/formats/add', $edit, t('Save configuration'));
      $this->resetFilterCaches();
      $formats[] = filter_format_load($edit['format']);
    }
    list($first_format, $second_format) = $formats;
    $first_user = $this->drupalCreateUser(array(filter_permission_name($first_format), filter_permission_name($second_format)));
    $second_user = $this->drupalCreateUser(array(filter_permission_name($second_format)));

    // Adjust the weights so that the first and second formats (in that order)
    // are the two lowest weighted formats available to any user.
    $minimum_weight = db_query("SELECT MIN(weight) FROM {filter_format}")->fetchField();
    $edit = array();
    $edit['formats[' . $first_format->format . '][weight]'] = $minimum_weight - 2;
    $edit['formats[' . $second_format->format . '][weight]'] = $minimum_weight - 1;
    $this->drupalPost('admin/config/content/formats', $edit, t('Save changes'));
    $this->resetFilterCaches();

    // Check that each user's default format is the lowest weighted format that
    // the user has access to.
    $this->assertEqual(filter_default_format($first_user), $first_format->format, "The first user's default format is the lowest weighted format that the user has access to.");
    $this->assertEqual(filter_default_format($second_user), $second_format->format, "The second user's default format is the lowest weighted format that the user has access to, and is different than the first user's.");

    // Reorder the two formats, and check that both users now have the same
    // default.
    $edit = array();
    $edit['formats[' . $second_format->format . '][weight]'] = $minimum_weight - 3;
    $this->drupalPost('admin/config/content/formats', $edit, t('Save changes'));
    $this->resetFilterCaches();
    $this->assertEqual(filter_default_format($first_user), filter_default_format($second_user), 'After the formats are reordered, both users have the same default format.');
  }

  /**
   * Rebuilds text format and permission caches in the thread running the tests.
   */
  protected function resetFilterCaches() {
    filter_formats_reset();
    $this->checkPermissions(array(), TRUE);
  }
}

/**
 * Tests the behavior of check_markup() when it is called without text format.
 */
class FilterNoFormatTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Unassigned text format functionality',
      'description' => 'Test the behavior of check_markup() when it is called without a text format.',
      'group' => 'Filter',
    );
  }

  /**
   * Tests text without format.
   *
   * Tests if text with no format is filtered the same way as text in the
   * fallback format.
   */
  function testCheckMarkupNoFormat() {
    // Create some text. Include some HTML and line breaks, so we get a good
    // test of the filtering that is applied to it.
    $text = "<strong>" . $this->randomName(32) . "</strong>\n\n<div>" . $this->randomName(32) . "</div>";

    // Make sure that when this text is run through check_markup() with no text
    // format, it is filtered as though it is in the fallback format.
    $this->assertEqual(check_markup($text), check_markup($text, filter_fallback_format()), 'Text with no format is filtered the same as text in the fallback format.');
  }
}

/**
 * Security tests for missing/vanished text formats or filters.
 */
class FilterSecurityTestCase extends DrupalWebTestCase {
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Security',
      'description' => 'Test the behavior of check_markup() when a filter or text format vanishes.',
      'group' => 'Filter',
    );
  }

  function setUp() {
    parent::setUp('php', 'filter_test');
    $this->admin_user = $this->drupalCreateUser(array('administer modules', 'administer filters', 'administer site configuration'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Tests removal of filtered content when an active filter is disabled.
   *
   * Tests that filtered content is emptied when an actively used filter module
   * is disabled.
   */
  function testDisableFilterModule() {
    // Create a new node.
    $node = $this->drupalCreateNode(array('promote' => 1));
    $body_raw = $node->body[LANGUAGE_NONE][0]['value'];
    $format_id = $node->body[LANGUAGE_NONE][0]['format'];
    $this->drupalGet('node/' . $node->nid);
    $this->assertText($body_raw, 'Node body found.');

    // Enable the filter_test_replace filter.
    $edit = array(
      'filters[filter_test_replace][status]' => 1,
    );
    $this->drupalPost('admin/config/content/formats/' . $format_id, $edit, t('Save configuration'));

    // Verify that filter_test_replace filter replaced the content.
    $this->drupalGet('node/' . $node->nid);
    $this->assertNoText($body_raw, 'Node body not found.');
    $this->assertText('Filter: Testing filter', 'Testing filter output found.');

    // Disable the text format entirely.
    $this->drupalPost('admin/config/content/formats/' . $format_id . '/disable', array(), t('Disable'));

    // Verify that the content is empty, because the text format does not exist.
    $this->drupalGet('node/' . $node->nid);
    $this->assertNoText($body_raw, 'Node body not found.');
  }
}

/**
 * Unit tests for core filters.
 */
class FilterUnitTestCase extends DrupalUnitTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Filter module filters',
      'description' => 'Tests Filter module filters individually.',
      'group' => 'Filter',
    );
  }

  /**
   * Tests the line break filter.
   */
  function testLineBreakFilter() {
    // Setup dummy filter object.
    $filter = new stdClass();
    $filter->callback = '_filter_autop';

    // Since the line break filter naturally needs plenty of newlines in test
    // strings and expectations, we're using "\n" instead of regular newlines
    // here.
    $tests = array(
      // Single line breaks should be changed to <br /> tags, while paragraphs
      // separated with double line breaks should be enclosed with <p></p> tags.
      "aaa\nbbb\n\nccc" => array(
        "<p>aaa<br />\nbbb</p>\n<p>ccc</p>" => TRUE,
      ),
      // Skip contents of certain block tags entirely.
      "<script>aaa\nbbb\n\nccc</script>
<style>aaa\nbbb\n\nccc</style>
<pre>aaa\nbbb\n\nccc</pre>
<object>aaa\nbbb\n\nccc</object>
<iframe>aaa\nbbb\n\nccc</iframe>
<svg>aaa\nbbb\n\nccc</svg>
" => array(
        "<script>aaa\nbbb\n\nccc</script>" => TRUE,
        "<style>aaa\nbbb\n\nccc</style>" => TRUE,
        "<pre>aaa\nbbb\n\nccc</pre>" => TRUE,
        "<object>aaa\nbbb\n\nccc</object>" => TRUE,
        "<iframe>aaa\nbbb\n\nccc</iframe>" => TRUE,
        "<svg>aaa\nbbb\n\nccc</svg>" => TRUE,
      ),
      // Skip comments entirely.
      "One. <!-- comment --> Two.\n<!--\nThree.\n-->\n" => array(
        '<!-- comment -->' => TRUE,
        "<!--\nThree.\n-->" => TRUE,
      ),
      // Resulting HTML should produce matching paragraph tags.
      '<p><div>  </div></p>' => array(
        "<p>\n<div>  </div>\n</p>" => TRUE,
      ),
      '<div><p>  </p></div>' => array(
        "<div>\n</div>" => TRUE,
      ),
      '<blockquote><pre>aaa</pre></blockquote>' => array(
        "<blockquote><pre>aaa</pre></blockquote>" => TRUE,
      ),
      "<pre>aaa\nbbb\nccc</pre>\nddd\neee" => array(
        "<pre>aaa\nbbb\nccc</pre>" => TRUE,
        "<p>ddd<br />\neee</p>" => TRUE,
      ),
      // Comments remain unchanged and subsequent lines/paragraphs are
      // transformed normally.
      "aaa<!--comment-->\n\nbbb\n\nccc\n\nddd<!--comment\nwith linebreak-->\n\neee\n\nfff" => array(
        "<p>aaa</p>\n<!--comment--><p>\nbbb</p>\n<p>ccc</p>\n<p>ddd</p>" => TRUE,
        "<!--comment\nwith linebreak--><p>\neee</p>\n<p>fff</p>" => TRUE,
      ),
      // Check that a comment in a PRE will result that the text after
      // the comment, but still in PRE, is not transformed.
      "<pre>aaa\nbbb<!-- comment -->\n\nccc</pre>\nddd" => array(
        "<pre>aaa\nbbb<!-- comment -->\n\nccc</pre>" => TRUE,
      ),
      // Bug 810824, paragraphs were appearing around iframe tags.
      "<iframe>aaa</iframe>\n\n" => array(
        "<p><iframe>aaa</iframe></p>" => FALSE,
      ),
    );
    $this->assertFilteredString($filter, $tests);

    // Very long string hitting PCRE limits.
    $limit = max(ini_get('pcre.backtrack_limit'), ini_get('pcre.recursion_limit'));
    $source = $this->randomName($limit);
    $result = _filter_autop($source);
    $success = $this->assertEqual($result, '<p>' . $source . "</p>\n", 'Line break filter can process very long strings.');
    if (!$success) {
      $this->verbose("\n" . $source . "\n<hr />\n" . $result);
    }
  }

  /**
   * Tests limiting allowed tags and XSS prevention.
   *
   * XSS tests assume that script is disallowed by default and src is allowed
   * by default, but on* and style attributes are disallowed.
   *
   * Script injection vectors mostly adopted from http://ha.ckers.org/xss.html.
   *
   * Relevant CVEs:
   * - CVE-2002-1806, ~CVE-2005-0682, ~CVE-2005-2106, CVE-2005-3973,
   *   CVE-2006-1226 (= rev. 1.112?), CVE-2008-0273, CVE-2008-3740.
   */
  function testFilterXSS() {
    // Tag stripping, different ways to work around removal of HTML tags.
    $f = filter_xss('<script>alert(0)</script>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping -- simple script without special characters.');

    $f = filter_xss('<script src="http://www.example.com" />');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping -- empty script with source.');

    $f = filter_xss('<ScRipt sRc=http://www.example.com/>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- varying case.');

    $f = filter_xss("<script\nsrc\n=\nhttp://www.example.com/\n>");
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- multiline tag.');

    $f = filter_xss('<script/a src=http://www.example.com/a.js></script>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- non whitespace character after tag name.');

    $f = filter_xss('<script/src=http://www.example.com/a.js></script>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- no space between tag and attribute.');

    // Null between < and tag name works at least with IE6.
    $f = filter_xss("<\0scr\0ipt>alert(0)</script>");
    $this->assertNoNormalized($f, 'ipt', 'HTML tag stripping evasion -- breaking HTML with nulls.');

    $f = filter_xss("<scrscriptipt src=http://www.example.com/a.js>");
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- filter just removing "script".');

    $f = filter_xss('<<script>alert(0);//<</script>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- double opening brackets.');

    $f = filter_xss('<script src=http://www.example.com/a.js?<b>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- no closing tag.');

    // DRUPAL-SA-2008-047: This doesn't seem exploitable, but the filter should
    // work consistently.
    $f = filter_xss('<script>>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- double closing tag.');

    $f = filter_xss('<script src=//www.example.com/.a>');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- no scheme or ending slash.');

    $f = filter_xss('<script src=http://www.example.com/.a');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- no closing bracket.');

    $f = filter_xss('<script src=http://www.example.com/ <');
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- opening instead of closing bracket.');

    $f = filter_xss('<nosuchtag attribute="newScriptInjectionVector">');
    $this->assertNoNormalized($f, 'nosuchtag', 'HTML tag stripping evasion -- unknown tag.');

    $f = filter_xss('<?xml:namespace ns="urn:schemas-microsoft-com:time">');
    $this->assertTrue(stripos($f, '<?xml') === FALSE, 'HTML tag stripping evasion -- starting with a question sign (processing instructions).');

    $f = filter_xss('<t:set attributeName="innerHTML" to="&lt;script defer&gt;alert(0)&lt;/script&gt;">');
    $this->assertNoNormalized($f, 't:set', 'HTML tag stripping evasion -- colon in the tag name (namespaces\' tricks).');

    $f = filter_xss('<img """><script>alert(0)</script>', array('img'));
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- a malformed image tag.');

    $f = filter_xss('<blockquote><script>alert(0)</script></blockquote>', array('blockquote'));
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- script in a blockqoute.');

    $f = filter_xss("<!--[if true]><script>alert(0)</script><![endif]-->");
    $this->assertNoNormalized($f, 'script', 'HTML tag stripping evasion -- script within a comment.');

    // Dangerous attributes removal.
    $f = filter_xss('<p onmouseover="http://www.example.com/">', array('p'));
    $this->assertNoNormalized($f, 'onmouseover', 'HTML filter attributes removal -- events, no evasion.');

    $f = filter_xss('<li style="list-style-image: url(javascript:alert(0))">', array('li'));
    $this->assertNoNormalized($f, 'style', 'HTML filter attributes removal -- style, no evasion.');

    $f = filter_xss('<img onerror   =alert(0)>', array('img'));
    $this->assertNoNormalized($f, 'onerror', 'HTML filter attributes removal evasion -- spaces before equals sign.');

    $f = filter_xss('<img onabort!#$%&()*~+-_.,:;?@[/|\]^`=alert(0)>', array('img'));
    $this->assertNoNormalized($f, 'onabort', 'HTML filter attributes removal evasion -- non alphanumeric characters before equals sign.');

    $f = filter_xss('<img oNmediAError=alert(0)>', array('img'));
    $this->assertNoNormalized($f, 'onmediaerror', 'HTML filter attributes removal evasion -- varying case.');

    // Works at least with IE6.
    $f = filter_xss("<img o\0nfocus\0=alert(0)>", array('img'));
    $this->assertNoNormalized($f, 'focus', 'HTML filter attributes removal evasion -- breaking with nulls.');

    // Only whitelisted scheme names allowed in attributes.
    $f = filter_xss('<img src="javascript:alert(0)">', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing -- no evasion.');

    $f = filter_xss('<img src=javascript:alert(0)>', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- no quotes.');

    // A bit like CVE-2006-0070.
    $f = filter_xss('<img src="javascript:confirm(0)">', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- no alert ;)');

    $f = filter_xss('<img src=`javascript:alert(0)`>', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- grave accents.');

    $f = filter_xss('<img dynsrc="javascript:alert(0)">', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing -- rare attribute.');

    $f = filter_xss('<table background="javascript:alert(0)">', array('table'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing -- another tag.');

    $f = filter_xss('<base href="javascript:alert(0);//">', array('base'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing -- one more attribute and tag.');

    $f = filter_xss('<img src="jaVaSCriPt:alert(0)">', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- varying case.');

    $f = filter_xss('<img src=&#106;&#97;&#118;&#97;&#115;&#99;&#114;&#105;&#112;&#116;&#58;&#97;&#108;&#101;&#114;&#116;&#40;&#48;&#41;>', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- UTF-8 decimal encoding.');

    $f = filter_xss('<img src=&#00000106&#0000097&#00000118&#0000097&#00000115&#0000099&#00000114&#00000105&#00000112&#00000116&#0000058&#0000097&#00000108&#00000101&#00000114&#00000116&#0000040&#0000048&#0000041>', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- long UTF-8 encoding.');

    $f = filter_xss('<img src=&#x6A&#x61&#x76&#x61&#x73&#x63&#x72&#x69&#x70&#x74&#x3A&#x61&#x6C&#x65&#x72&#x74&#x28&#x30&#x29>', array('img'));
    $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- UTF-8 hex encoding.');

    $f = filter_xss("<img src=\"jav\tascript:alert(0)\">", array('img'));
    $this->assertNoNormalized($f, 'script', 'HTML scheme clearing evasion -- an embedded tab.');

    $f = filter_xss('<img src="jav&#x09;ascript:alert(0)">', array('img'));
    $this->assertNoNormalized($f, 'script', 'HTML scheme clearing evasion -- an encoded, embedded tab.');

    $f = filter_xss('<img src="jav&#x000000A;ascript:alert(0)">', array('img'));
    $this->assertNoNormalized($f, 'script', 'HTML scheme clearing evasion -- an encoded, embedded newline.');

    // With &#xD; this test would fail, but the entity gets turned into
    // &amp;#xD;, so it's OK.
    $f = filter_xss('<img src="jav&#x0D;ascript:alert(0)">', array('img'));
    $this->assertNoNormalized($f, 'script', 'HTML scheme clearing evasion -- an encoded, embedded carriage return.');

    $f = filter_xss("<img src=\"\n\n\nj\na\nva\ns\ncript:alert(0)\">", array('img'));
    $this->assertNoNormalized($f, 'cript', 'HTML scheme clearing evasion -- broken into many lines.');

    $f = filter_xss("<img src=\"jav\0a\0\0cript:alert(0)\">", array('img'));
    $this->assertNoNormalized($f, 'cript', 'HTML scheme clearing evasion -- embedded nulls.');

    // @todo This dataset currently fails under 5.4 because of
    //   https://www.drupal.org/node/1210798. Restore after it's fixed.
    if (version_compare(PHP_VERSION, '5.4.0', '<')) {
      $f = filter_xss('<img src=" &#14;  javascript:alert(0)">', array('img'));
      $this->assertNoNormalized($f, 'javascript', 'HTML scheme clearing evasion -- spaces and metacharacters before scheme.');
    }

    $f = filter_xss('<img src="vbscript:msgbox(0)">', array('img'));
    $this->assertNoNormalized($f, 'vbscript', 'HTML scheme clearing evasion -- another scheme.');

    $f = filter_xss('<img src="nosuchscheme:notice(0)">', array('img'));
    $this->assertNoNormalized($f, 'nosuchscheme', 'HTML scheme clearing evasion -- unknown scheme.');

    // Netscape 4.x javascript entities.
    $f = filter_xss('<br size="&{alert(0)}">', array('br'));
    $this->assertNoNormalized($f, 'alert', 'Netscape 4.x javascript entities.');

    // DRUPAL-SA-2008-006: Invalid UTF-8, these only work as reflected XSS with
    // Internet Explorer 6.
    $f = filter_xss("<p arg=\"\xe0\">\" style=\"background-image: url(javascript:alert(0));\"\xe0<p>", array('p'));
    $this->assertNoNormalized($f, 'style', 'HTML filter -- invalid UTF-8.');

    $f = filter_xss("\xc0aaa");
    $this->assertEqual($f, '', 'HTML filter -- overlong UTF-8 sequences.');

    $f = filter_xss("Who&#039;s Online");
    $this->assertNormalized($f, "who's online", 'HTML filter -- html entity number');

    $f = filter_xss("Who&amp;#039;s Online");
    $this->assertNormalized($f, "who&#039;s online", 'HTML filter -- encoded html entity number');

    $f = filter_xss("Who&amp;amp;#039; Online");
    $this->assertNormalized($f, "who&amp;#039; online", 'HTML filter -- double encoded html entity number');
  }

  /**
   * Tests filter settings, defaults, access restrictions and similar.
   *
   * @todo This is for functions like filter_filter and check_markup, whose
   *   functionality is not completely focused on filtering. Some ideas:
   *   restricting formats according to user permissions, proper cache
   *   handling, defaults -- allowed tags/attributes/protocols.
   *
   * @todo It is possible to add script, iframe etc. to allowed tags, but this
   *   makes HTML filter completely ineffective.
   *
   * @todo Class, id, name and xmlns should be added to disallowed attributes,
   *   or better a whitelist approach should be used for that too.
   */
  function testHtmlFilter() {
    // Setup dummy filter object.
    $filter = new stdClass();
    $filter->settings = array(
      'allowed_html' => '<a> <em> <strong> <cite> <blockquote> <code> <ul> <ol> <li> <dl> <dt> <dd> <test-element>',
      'filter_html_help' => 1,
      'filter_html_nofollow' => 0,
    );

    // HTML filter is not able to secure some tags, these should never be
    // allowed.
    $f = _filter_html('<script />', $filter);
    $this->assertNoNormalized($f, 'script', 'HTML filter should always remove script tags.');

    $f = _filter_html('<iframe />', $filter);
    $this->assertNoNormalized($f, 'iframe', 'HTML filter should always remove iframe tags.');

    $f = _filter_html('<object />', $filter);
    $this->assertNoNormalized($f, 'object', 'HTML filter should always remove object tags.');

    $f = _filter_html('<style />', $filter);
    $this->assertNoNormalized($f, 'style', 'HTML filter should always remove style tags.');

    // Some tags make CSRF attacks easier, let the user take the risk herself.
    $f = _filter_html('<img />', $filter);
    $this->assertNoNormalized($f, 'img', 'HTML filter should remove img tags on default.');

    $f = _filter_html('<input />', $filter);
    $this->assertNoNormalized($f, 'img', 'HTML filter should remove input tags on default.');

    // Filtering content of some attributes is infeasible, these shouldn't be
    // allowed too.
    $f = _filter_html('<p style="display: none;" />', $filter);
    $this->assertNoNormalized($f, 'style', 'HTML filter should remove style attribute on default.');

    $f = _filter_html('<p onerror="alert(0);" />', $filter);
    $this->assertNoNormalized($f, 'onerror', 'HTML filter should remove on* attributes on default.');

    $f = _filter_html('<code onerror>&nbsp;</code>', $filter);
    $this->assertNoNormalized($f, 'onerror', 'HTML filter should remove empty on* attributes on default.');

    // Custom tags are supported and should be allowed through.
    $f = _filter_html('<test-element></test-element>', $filter);
    $this->assertNormalized($f, 'test-element', 'HTML filter should allow custom elements.');
  }

  /**
   * Tests the spam deterrent.
   */
  function testNoFollowFilter() {
    // Setup dummy filter object.
    $filter = new stdClass();
    $filter->settings = array(
      'allowed_html' => '<a>',
      'filter_html_help' => 1,
      'filter_html_nofollow' => 1,
    );

    // Test if the rel="nofollow" attribute is added, even if we try to prevent
    // it.
    $f = _filter_html('<a href="http://www.example.com/">text</a>', $filter);
    $this->assertNormalized($f, 'rel="nofollow"', 'Spam deterrent -- no evasion.');

    $f = _filter_html('<A href="http://www.example.com/">text</a>', $filter);
    $this->assertNormalized($f, 'rel="nofollow"', 'Spam deterrent evasion -- capital A.');

    $f = _filter_html("<a/href=\"http://www.example.com/\">text</a>", $filter);
    $this->assertNormalized($f, 'rel="nofollow"', 'Spam deterrent evasion -- non whitespace character after tag name.');

    $f = _filter_html("<\0a\0 href=\"http://www.example.com/\">text</a>", $filter);
    $this->assertNormalized($f, 'rel="nofollow"', 'Spam deterrent evasion -- some nulls.');

    $f = _filter_html('<a href="http://www.example.com/" rel="follow">text</a>', $filter);
    $this->assertNoNormalized($f, 'rel="follow"', 'Spam deterrent evasion -- with rel set - rel="follow" removed.');
    $this->assertNormalized($f, 'rel="nofollow"', 'Spam deterrent evasion -- with rel set - rel="nofollow" added.');
  }

  /**
   * Tests the loose, admin HTML filter.
   */
  function testFilterXSSAdmin() {
    // DRUPAL-SA-2008-044
    $f = filter_xss_admin('<object />');
    $this->assertNoNormalized($f, 'object', 'Admin HTML filter -- should not allow object tag.');

    $f = filter_xss_admin('<script />');
    $this->assertNoNormalized($f, 'script', 'Admin HTML filter -- should not allow script tag.');

    $f = filter_xss_admin('<style /><iframe /><frame /><frameset /><meta /><link /><embed /><applet /><param /><layer />');
    $this->assertEqual($f, '', 'Admin HTML filter -- should never allow some tags.');
  }

  /**
   * Tests the HTML escaping filter.
   *
   * check_plain() is not tested here.
   */
  function testHtmlEscapeFilter() {
    // Setup dummy filter object.
    $filter = new stdClass();
    $filter->callback = '_filter_html_escape';

    $tests = array(
      "   One. <!-- \"comment\" --> Two'.\n<p>Three.</p>\n    " => array(
        "One. &lt;!-- &quot;comment&quot; --&gt; Two&#039;.\n&lt;p&gt;Three.&lt;/p&gt;" => TRUE,
        '   One.' => FALSE,
        "</p>\n    " => FALSE,
      ),
    );
    $this->assertFilteredString($filter, $tests);
  }

  /**
   * Tests the URL filter.
   */
  function testUrlFilter() {
    // Setup dummy filter object.
    $filter = new stdClass();
    $filter->callback = '_filter_url';
    $filter->settings = array(
      'filter_url_length' => 496,
    );
    // @todo Possible categories:
    // - absolute, mail, partial
    // - characters/encoding, surrounding markup, security

    // Create a e-mail that is too long.
    $long_email = str_repeat('a', 254) . '@example.com';
    $too_long_email = str_repeat('b', 255) . '@example.com';
    $email_with_plus_sign = '<EMAIL>';


    // Filter selection/pattern matching.
    $tests = array(
      // HTTP URLs.
      '
http://example.com or www.example.com
' => array(
        '<a href="http://example.com">http://example.com</a>' => TRUE,
        '<a href="http://www.example.com">www.example.com</a>' => TRUE,
      ),
      // MAILTO URLs.
      '
<EMAIL> or mailto:<EMAIL> or ' . $email_with_plus_sign . ' or ' . $long_email . ' but not ' . $too_long_email . '
' => array(
        '<a href="mailto:<EMAIL>"><EMAIL></a>' => TRUE,
        '<a href="mailto:<EMAIL>">mailto:<EMAIL></a>' => TRUE,
        '<a href="mailto:' . $long_email . '">' . $long_email . '</a>' => TRUE,
        '<a href="mailto:' . $too_long_email . '">' . $too_long_email . '</a>' => FALSE,
        '<a href="mailto:' . $email_with_plus_sign . '">' . $email_with_plus_sign . '</a>' => TRUE,
      ),
      // URI parts and special characters.
      '
http://trailingslash.com/ or www.trailingslash.com/
http://host.com/some/path?query=foo&bar[baz]=beer#fragment or www.host.com/some/path?query=foo&bar[baz]=beer#fragment
http://twitter.com/#!/example/status/22376963142324226
ftp://user:<EMAIL>/~home/dir1
sftp://user@nonstandardport:222/dir
ssh://*************/srv/git/drupal.git
' => array(
        '<a href="http://trailingslash.com/">http://trailingslash.com/</a>' => TRUE,
        '<a href="http://www.trailingslash.com/">www.trailingslash.com/</a>' => TRUE,
        '<a href="http://host.com/some/path?query=foo&amp;bar[baz]=beer#fragment">http://host.com/some/path?query=foo&amp;bar[baz]=beer#fragment</a>' => TRUE,
        '<a href="http://www.host.com/some/path?query=foo&amp;bar[baz]=beer#fragment">www.host.com/some/path?query=foo&amp;bar[baz]=beer#fragment</a>' => TRUE,
        '<a href="http://twitter.com/#!/example/status/22376963142324226">http://twitter.com/#!/example/status/22376963142324226</a>' => TRUE,
        '<a href="ftp://user:<EMAIL>/~home/dir1">ftp://user:<EMAIL>/~home/dir1</a>' => TRUE,
        '<a href="sftp://user@nonstandardport:222/dir">sftp://user@nonstandardport:222/dir</a>' => TRUE,
        '<a href="ssh://*************/srv/git/drupal.git">ssh://*************/srv/git/drupal.git</a>' => TRUE,
      ),
      // Encoding.
      '
http://ampersand.com/?a=1&b=2
http://encoded.com/?a=1&amp;b=2
' => array(
        '<a href="http://ampersand.com/?a=1&amp;b=2">http://ampersand.com/?a=1&amp;b=2</a>' => TRUE,
        '<a href="http://encoded.com/?a=1&amp;b=2">http://encoded.com/?a=1&amp;b=2</a>' => TRUE,
      ),
      // Domain name length.
      '
www.ex.ex or www.example.example or www.toolongdomainexampledomainexampledomainexampledomainexampledomain or
<EMAIL>
' => array(
        '<a href="http://www.ex.ex">www.ex.ex</a>' => TRUE,
        '<a href="http://www.example.example">www.example.example</a>' => TRUE,
        'http://www.toolong' => FALSE,
        '<a href="mailto:<EMAIL>"><EMAIL></a>' => TRUE,
      ),
      // Absolute URL protocols.
      // The list to test is found in the beginning of _filter_url() at
      // $protocols = variable_get('filter_allowed_protocols'... (approx line 1325).
      '
https://example.com,
ftp://ftp.example.com,
news://example.net,
telnet://example,
irc://example.host,
ssh://odd.geek,
sftp://secure.host?,
webcal://calendar,
rtsp://127.0.0.1,
not foo://disallowed.com.
' => array(
        'href="https://example.com"' => TRUE,
        'href="ftp://ftp.example.com"' => TRUE,
        'href="news://example.net"' => TRUE,
        'href="telnet://example"' => TRUE,
        'href="irc://example.host"' => TRUE,
        'href="ssh://odd.geek"' => TRUE,
        'href="sftp://secure.host"' => TRUE,
        'href="webcal://calendar"' => TRUE,
        'href="rtsp://127.0.0.1"' => TRUE,
        'href="foo://disallowed.com"' => FALSE,
        'not foo://disallowed.com.' => TRUE,
      ),
    );
    $this->assertFilteredString($filter, $tests);

    // Surrounding text/punctuation.
    $tests = array(
      '
Partial URL with trailing period www.partial.com.
E-mail with <NAME_EMAIL>,
Absolute URL with trailing question http://www.absolute.com?
Query string with trailing exclamation www.query.com/index.php?a=!
Partial URL with 3 trailing www.partial.periods...
E-mail with 3 trailing <EMAIL>!!!
Absolute URL and query string with 2 different punctuation characters (http://www.example.com/q=abc).
' => array(
        'period <a href="http://www.partial.com">www.partial.com</a>.' => TRUE,
        'comma <a href="mailto:<EMAIL>"><EMAIL></a>,' => TRUE,
        'question <a href="http://www.absolute.com">http://www.absolute.com</a>?' => TRUE,
        'exclamation <a href="http://www.query.com/index.php?a=">www.query.com/index.php?a=</a>!' => TRUE,
        'trailing <a href="http://www.partial.periods">www.partial.periods</a>...' => TRUE,
        'trailing <a href="mailto:<EMAIL>"><EMAIL></a>!!!' => TRUE,
        'characters (<a href="http://www.example.com/q=abc">http://www.example.com/q=abc</a>).' => TRUE,
      ),
      '
(www.parenthesis.com/dir?a=1&b=2#a)
' => array(
        '(<a href="http://www.parenthesis.com/dir?a=1&amp;b=2#a">www.parenthesis.com/dir?a=1&amp;b=2#a</a>)' => TRUE,
      ),
    );
    $this->assertFilteredString($filter, $tests);

    // Surrounding markup.
    $tests = array(
      '
<p xmlns="www.namespace.com" />
<p xmlns="http://namespace.com">
An <a href="http://example.com" title="Read more at www.example.info...">anchor</a>.
</p>
' => array(
        '<p xmlns="www.namespace.com" />' => TRUE,
        '<p xmlns="http://namespace.com">' => TRUE,
        'href="http://www.namespace.com"' => FALSE,
        'href="http://namespace.com"' => FALSE,
        'An <a href="http://example.com" title="Read more at www.example.info...">anchor</a>.' => TRUE,
      ),
      '
Not <a href="foo">www.relative.com</a> or <a href="http://absolute.com">www.absolute.com</a>
but <strong>http://www.strong.net</strong> or <em>www.emphasis.info</em>
' => array(
        '<a href="foo">www.relative.com</a>' => TRUE,
        'href="http://www.relative.com"' => FALSE,
        '<a href="http://absolute.com">www.absolute.com</a>' => TRUE,
        '<strong><a href="http://www.strong.net">http://www.strong.net</a></strong>' => TRUE,
        '<em><a href="http://www.emphasis.info">www.emphasis.info</a></em>' => TRUE,
      ),
      '
Test <code>using www.example.com the code tag</code>.
' => array(
        'href' => FALSE,
        'http' => FALSE,
      ),
      '
Intro.
<blockquote>
Quoted text linking to www.example.com, <NAME_EMAIL>, originating from http://origin.example.com. <code>@see www.usage.example.com or <em>www.example.info</em> bla bla</code>.
</blockquote>

Outro.
' => array(
        'href="http://www.example.com"' => TRUE,
        'href="mailto:<EMAIL>"' => TRUE,
        'href="http://origin.example.com"' => TRUE,
        'http://www.usage.example.com' => FALSE,
        'http://www.example.info' => FALSE,
        'Intro.' => TRUE,
        'Outro.' => TRUE,
      ),
      '
Unknown tag <x>containing x and www.example.com</x>? And a tag <pooh>beginning with p and containing www.example.pooh with p?</pooh>
' => array(
        'href="http://www.example.com"' => TRUE,
        'href="http://www.example.pooh"' => TRUE,
      ),
      '
<p>Test &lt;br/&gt;: This is a www.example17.com example <strong>with</strong> various http://www.example18.com tags. *<br/>
 It is important www.example19.com to *<br/>test different URLs and http://www.example20.com in the same paragraph. *<br>
HTML www.example21.com <NAME_EMAIL> can litererally http://www.example23.com contain *img*<img> anything. Just a www.example24.com with http://www.example25.com thrown in. www.example26.<NAME_EMAIL> with extra http://www.example28.com.
' => array(
        'href="http://www.example17.com"' => TRUE,
        'href="http://www.example18.com"' => TRUE,
        'href="http://www.example19.com"' => TRUE,
        'href="http://www.example20.com"' => TRUE,
        'href="http://www.example21.com"' => TRUE,
        'href="mailto:<EMAIL>"' => TRUE,
        'href="http://www.example23.com"' => TRUE,
        'href="http://www.example24.com"' => TRUE,
        'href="http://www.example25.com"' => TRUE,
        'href="http://www.example26.com"' => TRUE,
        'href="mailto:<EMAIL>"' => TRUE,
        'href="http://www.example28.com"' => TRUE,
      ),
      '
<script>
<!--
  // @see www.example.com
  var exampleurl = "http://example.net";
-->
<!--//--><![CDATA[//><!--
  // @see www.example.com
  var exampleurl = "http://example.net";
//--><!]]>
</script>
' => array(
        'href="http://www.example.com"' => FALSE,
        'href="http://example.net"' => FALSE,
      ),
      '
<style>body {
  background: url(http://example.com/pixel.gif);
}</style>
' => array(
        'href' => FALSE,
      ),
      '
<!-- Skip any URLs like www.example.com in comments -->
' => array(
        'href' => FALSE,
      ),
      '
<!-- Skip any URLs like
www.example.com with a newline in comments -->
' => array(
        'href' => FALSE,
      ),
      '
<!-- Skip any URLs like www.comment.com in comments. <p>Also ignore http://commented.out/markup.</p> -->
' => array(
        'href' => FALSE,
      ),
      '
<dl>
<dt>www.example.com</dt>
<dd>http://example.com</dd>
<dd><EMAIL></dd>
<dt>Check www.example.net</dt>
<dd>Some text around http://www.example.<NAME_EMAIL>?</dd>
</dl>
' => array(
        'href="http://www.example.com"' => TRUE,
        'href="http://example.com"' => TRUE,
        'href="mailto:<EMAIL>"' => TRUE,
        'href="http://www.example.net"' => TRUE,
        'href="http://www.example.info"' => TRUE,
        'href="mailto:<EMAIL>"' => TRUE,
      ),
      '
<div>www.div.com</div>
<ul>
<li>http://listitem.com</li>
<li class="odd">www.class.listitem.com</li>
</ul>
' => array(
        '<div><a href="http://www.div.com">www.div.com</a></div>' => TRUE,
        '<li><a href="http://listitem.com">http://listitem.com</a></li>' => TRUE,
        '<li class="odd"><a href="http://www.class.listitem.com">www.class.listitem.com</a></li>' => TRUE,
      ),
    );
    $this->assertFilteredString($filter, $tests);

    // URL trimming.
    $filter->settings['filter_url_length'] = 20;
    $tests = array(
      'www.trimmed.com/d/ff.ext?a=1&b=2#a1' => array(
        '<a href="http://www.trimmed.com/d/ff.ext?a=1&amp;b=2#a1">www.trimmed.com/d/ff...</a>' => TRUE,
      ),
    );
    $this->assertFilteredString($filter, $tests);
  }

  /**
   * Asserts multiple filter output expectations for multiple input strings.
   *
   * @param $filter
   *   A input filter object.
   * @param $tests
   *   An associative array, whereas each key is an arbitrary input string and
   *   each value is again an associative array whose keys are filter output
   *   strings and whose values are Booleans indicating whether the output is
   *   expected or not.
   *
   * For example:
   * @code
   * $tests = array(
   *   'Input string' => array(
   *     '<p>Input string</p>' => TRUE,
   *     'Input string<br' => FALSE,
   *   ),
   * );
   * @endcode
   */
  function assertFilteredString($filter, $tests) {
    foreach ($tests as $source => $tasks) {
      $function = $filter->callback;
      $result = $function($source, $filter);
      foreach ($tasks as $value => $is_expected) {
        // Not using assertIdentical, since combination with strpos() is hard to grok.
        if ($is_expected) {
          $success = $this->assertTrue(strpos($result, $value) !== FALSE, format_string('@source: @value found.', array(
            '@source' => var_export($source, TRUE),
            '@value' => var_export($value, TRUE),
          )));
        }
        else {
          $success = $this->assertTrue(strpos($result, $value) === FALSE, format_string('@source: @value not found.', array(
            '@source' => var_export($source, TRUE),
            '@value' => var_export($value, TRUE),
          )));
        }
        if (!$success) {
          $this->verbose('Source:<pre>' . check_plain(var_export($source, TRUE)) . '</pre>'
            . '<hr />' . 'Result:<pre>' . check_plain(var_export($result, TRUE)) . '</pre>'
            . '<hr />' . ($is_expected ? 'Expected:' : 'Not expected:')
            . '<pre>' . check_plain(var_export($value, TRUE)) . '</pre>'
          );
        }
      }
    }
  }

  /**
   * Tests URL filter on longer content.
   *
   * Filters based on regular expressions should also be tested with a more
   * complex content than just isolated test lines.
   * The most common errors are:
   * - accidental '*' (greedy) match instead of '*?' (minimal) match.
   * - only matching first occurrence instead of all.
   * - newlines not matching '.*'.
   *
   * This test covers:
   * - Document with multiple newlines and paragraphs (two newlines).
   * - Mix of several HTML tags, invalid non-HTML tags, tags to ignore and HTML
   *   comments.
   * - Empty HTML tags (BR, IMG).
   * - Mix of absolute and partial URLs, and e-mail addresses in one content.
   */
  function testUrlFilterContent() {
    // Setup dummy filter object.
    $filter = new stdClass();
    $filter->settings = array(
      'filter_url_length' => 496,
    );
    $path = drupal_get_path('module', 'filter') . '/tests';

    $input = file_get_contents($path . '/filter.url-input.txt');
    $expected = file_get_contents($path . '/filter.url-output.txt');
    $result = _filter_url($input, $filter);
    $this->assertIdentical($result, $expected, 'Complex HTML document was correctly processed.');
  }

  /**
   * Tests the HTML corrector filter.
   *
   * @todo This test could really use some validity checking function.
   */
  function testHtmlCorrectorFilter() {
    // Tag closing.
    $f = _filter_htmlcorrector('<p>text');
    $this->assertEqual($f, '<p>text</p>', 'HTML corrector -- tag closing at the end of input.');

    $f = _filter_htmlcorrector('<p>text<p><p>text');
    $this->assertEqual($f, '<p>text</p><p></p><p>text</p>', 'HTML corrector -- tag closing.');

    $f = _filter_htmlcorrector("<ul><li>e1<li>e2");
    $this->assertEqual($f, "<ul><li>e1</li><li>e2</li></ul>", 'HTML corrector -- unclosed list tags.');

    $f = _filter_htmlcorrector('<div id="d">content');
    $this->assertEqual($f, '<div id="d">content</div>', 'HTML corrector -- unclosed tag with attribute.');

    // XHTML slash for empty elements.
    $f = _filter_htmlcorrector('<hr><br>');
    $this->assertEqual($f, '<hr /><br />', 'HTML corrector -- XHTML closing slash.');

    $f = _filter_htmlcorrector('<P>test</P>');
    $this->assertEqual($f, '<p>test</p>', 'HTML corrector -- Convert uppercased tags to proper lowercased ones.');

    $f = _filter_htmlcorrector('<P>test</p>');
    $this->assertEqual($f, '<p>test</p>', 'HTML corrector -- Convert uppercased tags to proper lowercased ones.');

    $f = _filter_htmlcorrector('test<hr />');
    $this->assertEqual($f, 'test<hr />', 'HTML corrector -- Let proper XHTML pass through.');

    $f = _filter_htmlcorrector('test<hr/>');
    $this->assertEqual($f, 'test<hr />', 'HTML corrector -- Let proper XHTML pass through, but ensure there is a single space before the closing slash.');

    $f = _filter_htmlcorrector('test<hr    />');
    $this->assertEqual($f, 'test<hr />', 'HTML corrector -- Let proper XHTML pass through, but ensure there are not too many spaces before the closing slash.');

    $f = _filter_htmlcorrector('<span class="test" />');
    $this->assertEqual($f, '<span class="test"></span>', 'HTML corrector -- Convert XHTML that is properly formed but that would not be compatible with typical HTML user agents.');

    $f = _filter_htmlcorrector('test1<br class="test">test2');
    $this->assertEqual($f, 'test1<br class="test" />test2', 'HTML corrector -- Automatically close single tags.');

    $f = _filter_htmlcorrector('line1<hr>line2');
    $this->assertEqual($f, 'line1<hr />line2', 'HTML corrector -- Automatically close single tags.');

    $f = _filter_htmlcorrector('line1<HR>line2');
    $this->assertEqual($f, 'line1<hr />line2', 'HTML corrector -- Automatically close single tags.');

    $f = _filter_htmlcorrector('<img src="http://example.com/test.jpg">test</img>');
    $this->assertEqual($f, '<img src="http://example.com/test.jpg" />test', 'HTML corrector -- Automatically close single tags.');

    $f = _filter_htmlcorrector('<br></br>');
    $this->assertEqual($f, '<br />', "HTML corrector -- Transform empty tags to a single closed tag if the tag's content model is EMPTY.");

    $f = _filter_htmlcorrector('<div></div>');
    $this->assertEqual($f, '<div></div>', "HTML corrector -- Do not transform empty tags to a single closed tag if the tag's content model is not EMPTY.");

    $f = _filter_htmlcorrector('<p>line1<br/><hr/>line2</p>');
    $this->assertEqual($f, '<p>line1<br /></p><hr />line2', 'HTML corrector -- Move non-inline elements outside of inline containers.');

    $f = _filter_htmlcorrector('<p>line1<div>line2</div></p>');
    $this->assertEqual($f, '<p>line1</p><div>line2</div>', 'HTML corrector -- Move non-inline elements outside of inline containers.');

    $f = _filter_htmlcorrector('<p>test<p>test</p>\n');
    $this->assertEqual($f, '<p>test</p><p>test</p>\n', 'HTML corrector -- Auto-close improperly nested tags.');

    $f = _filter_htmlcorrector('<p>Line1<br><STRONG>bold stuff</b>');
    $this->assertEqual($f, '<p>Line1<br /><strong>bold stuff</strong></p>', 'HTML corrector -- Properly close unclosed tags, and remove useless closing tags.');

    $f = _filter_htmlcorrector('test <!-- this is a comment -->');
    $this->assertEqual($f, 'test <!-- this is a comment -->', 'HTML corrector -- Do not touch HTML comments.');

    $f = _filter_htmlcorrector('test <!--this is a comment-->');
    $this->assertEqual($f, 'test <!--this is a comment-->', 'HTML corrector -- Do not touch HTML comments.');

    $f = _filter_htmlcorrector('test <!-- comment <p>another
    <strong>multiple</strong> line
    comment</p> -->');
    $this->assertEqual($f, 'test <!-- comment <p>another
    <strong>multiple</strong> line
    comment</p> -->', 'HTML corrector -- Do not touch HTML comments.');

    $f = _filter_htmlcorrector('test <!-- comment <p>another comment</p> -->');
    $this->assertEqual($f, 'test <!-- comment <p>another comment</p> -->', 'HTML corrector -- Do not touch HTML comments.');

    $f = _filter_htmlcorrector('test <!--break-->');
    $this->assertEqual($f, 'test <!--break-->', 'HTML corrector -- Do not touch HTML comments.');

    $f = _filter_htmlcorrector('<p>test\n</p>\n');
    $this->assertEqual($f, '<p>test\n</p>\n', 'HTML corrector -- New-lines are accepted and kept as-is.');

    $f = _filter_htmlcorrector('<p>دروبال');
    $this->assertEqual($f, '<p>دروبال</p>', 'HTML corrector -- Encoding is correctly kept.');

    $f = _filter_htmlcorrector('<script type="text/javascript">alert("test")</script>');
    $this->assertEqual($f, '<script type="text/javascript">
<!--//--><![CDATA[// ><!--
alert("test")
//--><!]]>
</script>', 'HTML corrector -- CDATA added to script element');

    $f = _filter_htmlcorrector('<p><script type="text/javascript">alert("test")</script></p>');
    $this->assertEqual($f, '<p><script type="text/javascript">
<!--//--><![CDATA[// ><!--
alert("test")
//--><!]]>
</script></p>', 'HTML corrector -- CDATA added to a nested script element');

    $f = _filter_htmlcorrector('<p><style> /* Styling */ body {color:red}</style></p>');
    $this->assertEqual($f, '<p><style>
<!--/*--><![CDATA[/* ><!--*/
 /* Styling */ body {color:red}
/*--><!]]>*/
</style></p>', 'HTML corrector -- CDATA added to a style element.');

    $filtered_data = _filter_htmlcorrector('<p><style>
/*<![CDATA[*/
/* Styling */
body {color:red}
/*]]>*/
</style></p>');
    $this->assertEqual($filtered_data, '<p><style>
<!--/*--><![CDATA[/* ><!--*/

/*<![CDATA[*/
/* Styling */
body {color:red}
/*]]]]><![CDATA[>*/

/*--><!]]>*/
</style></p>',
      format_string('HTML corrector -- Existing cdata section @pattern_name properly escaped', array('@pattern_name' => '/*<![CDATA[*/'))
    );

    $filtered_data = _filter_htmlcorrector('<p><style>
  <!--/*--><![CDATA[/* ><!--*/
  /* Styling */
  body {color:red}
  /*--><!]]>*/
</style></p>');
    $this->assertEqual($filtered_data, '<p><style>
<!--/*--><![CDATA[/* ><!--*/

  <!--/*--><![CDATA[/* ><!--*/
  /* Styling */
  body {color:red}
  /*--><!]]]]><![CDATA[>*/

/*--><!]]>*/
</style></p>',
      format_string('HTML corrector -- Existing cdata section @pattern_name properly escaped', array('@pattern_name' => '<!--/*--><![CDATA[/* ><!--*/'))
    );

    $filtered_data = _filter_htmlcorrector('<p><script type="text/javascript">
<!--//--><![CDATA[// ><!--
  alert("test");
//--><!]]>
</script></p>');
    $this->assertEqual($filtered_data, '<p><script type="text/javascript">
<!--//--><![CDATA[// ><!--

<!--//--><![CDATA[// ><!--
  alert("test");
//--><!]]]]><![CDATA[>

//--><!]]>
</script></p>',
      format_string('HTML corrector -- Existing cdata section @pattern_name properly escaped', array('@pattern_name' => '<!--//--><![CDATA[// ><!--'))
    );

    $filtered_data = _filter_htmlcorrector('<p><script type="text/javascript">
// <![CDATA[
  alert("test");
// ]]>
</script></p>');
    $this->assertEqual($filtered_data, '<p><script type="text/javascript">
<!--//--><![CDATA[// ><!--

// <![CDATA[
  alert("test");
// ]]]]><![CDATA[>

//--><!]]>
</script></p>',
      format_string('HTML corrector -- Existing cdata section @pattern_name properly escaped', array('@pattern_name' => '// <![CDATA['))
    );

  }

  /**
   * Asserts that a text transformed to lowercase with HTML entities decoded does contains a given string.
   *
   * Otherwise fails the test with a given message, similar to all the
   * SimpleTest assert* functions.
   *
   * Note that this does not remove nulls, new lines and other characters that
   * could be used to obscure a tag or an attribute name.
   *
   * @param $haystack
   *   Text to look in.
   * @param $needle
   *   Lowercase, plain text to look for.
   * @param $message
   *   (optional) Message to display if failed. Defaults to an empty string.
   * @param $group
   *   (optional) The group this message belongs to. Defaults to 'Other'.
   * @return
   *   TRUE on pass, FALSE on fail.
   */
  function assertNormalized($haystack, $needle, $message = '', $group = 'Other') {
    return $this->assertTrue(strpos(strtolower(decode_entities($haystack)), $needle) !== FALSE, $message, $group);
  }

  /**
   * Asserts that text transformed to lowercase with HTML entities decoded does not contain a given string.
   *
   * Otherwise fails the test with a given message, similar to all the
   * SimpleTest assert* functions.
   *
   * Note that this does not remove nulls, new lines, and other character that
   * could be used to obscure a tag or an attribute name.
   *
   * @param $haystack
   *   Text to look in.
   * @param $needle
   *   Lowercase, plain text to look for.
   * @param $message
   *   (optional) Message to display if failed. Defaults to an empty string.
   * @param $group
   *   (optional) The group this message belongs to. Defaults to 'Other'.
   * @return
   *   TRUE on pass, FALSE on fail.
   */
  function assertNoNormalized($haystack, $needle, $message = '', $group = 'Other') {
    return $this->assertTrue(strpos(strtolower(decode_entities($haystack)), $needle) === FALSE, $message, $group);
  }
}

/**
 * Tests for Filter's hook invocations.
 */
class FilterHooksTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Filter format hooks',
      'description' => 'Test hooks for text formats insert/update/disable.',
      'group' => 'Filter',
    );
  }

  function setUp() {
    parent::setUp('block', 'filter_test');
    $admin_user = $this->drupalCreateUser(array('administer filters', 'administer blocks'));
    $this->drupalLogin($admin_user);
  }

  /**
   * Tests hooks on format management.
   *
   * Tests that hooks run correctly on creating, editing, and deleting a text
   * format.
   */
  function testFilterHooks() {
    // Add a text format.
    $name = $this->randomName();
    $edit = array();
    $edit['format'] = drupal_strtolower($this->randomName());
    $edit['name'] = $name;
    $edit['roles[' . DRUPAL_ANONYMOUS_RID . ']'] = 1;
    $this->drupalPost('admin/config/content/formats/add', $edit, t('Save configuration'));
    $this->assertRaw(t('Added text format %format.', array('%format' => $name)), 'New format created.');
    $this->assertText('hook_filter_format_insert invoked.', 'hook_filter_format_insert was invoked.');

    $format_id = $edit['format'];

    // Update text format.
    $edit = array();
    $edit['roles[' . DRUPAL_AUTHENTICATED_RID . ']'] = 1;
    $this->drupalPost('admin/config/content/formats/' . $format_id, $edit, t('Save configuration'));
    $this->assertRaw(t('The text format %format has been updated.', array('%format' => $name)), 'Format successfully updated.');
    $this->assertText('hook_filter_format_update invoked.', 'hook_filter_format_update() was invoked.');

    // Add a new custom block.
    $custom_block = array();
    $custom_block['info'] = $this->randomName(8);
    $custom_block['title'] = $this->randomName(8);
    $custom_block['body[value]'] = $this->randomName(32);
    // Use the format created.
    $custom_block['body[format]'] = $format_id;
    $this->drupalPost('admin/structure/block/add', $custom_block, t('Save block'));
    $this->assertText(t('The block has been created.'), 'New block successfully created.');

    // Verify the new block is in the database.
    $bid = db_query("SELECT bid FROM {block_custom} WHERE info = :info", array(':info' => $custom_block['info']))->fetchField();
    $this->assertNotNull($bid, 'New block found in database');

    // Disable the text format.
    $this->drupalPost('admin/config/content/formats/' . $format_id . '/disable', array(), t('Disable'));
    $this->assertRaw(t('Disabled text format %format.', array('%format' => $name)), 'Format successfully disabled.');
    $this->assertText('hook_filter_format_disable invoked.', 'hook_filter_format_disable() was invoked.');
  }
}

/**
 * Tests filter settings.
 */
class FilterSettingsTestCase extends DrupalWebTestCase {
  /**
   * The installation profile to use with this test class.
   *
   * @var string
   */
  protected $profile = 'testing';

  public static function getInfo() {
    return array(
      'name' => 'Filter settings',
      'description' => 'Tests filter settings.',
      'group' => 'Filter',
    );
  }

  /**
   * Tests explicit and implicit default settings for filters.
   */
  function testFilterDefaults() {
    $filter_info = filter_filter_info();
    $filters = array_fill_keys(array_keys($filter_info), array());

    // Create text format using filter default settings.
    $filter_defaults_format = (object) array(
      'format' => 'filter_defaults',
      'name' => 'Filter defaults',
      'filters' => $filters,
    );
    filter_format_save($filter_defaults_format);

    // Verify that default weights defined in hook_filter_info() were applied.
    $saved_settings = array();
    foreach ($filter_defaults_format->filters as $name => $settings) {
      $expected_weight = (isset($filter_info[$name]['weight']) ? $filter_info[$name]['weight'] : 0);
      $this->assertEqual($settings['weight'], $expected_weight, format_string('@name filter weight %saved equals %default', array(
        '@name' => $name,
        '%saved' => $settings['weight'],
        '%default' => $expected_weight,
      )));
      $saved_settings[$name]['weight'] = $expected_weight;
    }

    // Re-save the text format.
    filter_format_save($filter_defaults_format);
    // Reload it from scratch.
    filter_formats_reset();
    $filter_defaults_format = filter_format_load($filter_defaults_format->format);
    $filter_defaults_format->filters = filter_list_format($filter_defaults_format->format);

    // Verify that saved filter settings have not been changed.
    foreach ($filter_defaults_format->filters as $name => $settings) {
      $this->assertEqual($settings->weight, $saved_settings[$name]['weight'], format_string('@name filter weight %saved equals %previous', array(
        '@name' => $name,
        '%saved' => $settings->weight,
        '%previous' => $saved_settings[$name]['weight'],
      )));
    }
  }
}

/**
 * Tests DOMDocument serialization.
 */
class FilterDOMSerializeTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Serialization',
      'description' => 'Test serialization of DOMDocument objects.',
      'group' => 'Filter',
    );
  }

  /**
   * Tests empty DOMDocument object.
   */
  function testFilterEmptyDOMSerialization() {
    $document = new DOMDocument();
    $result = filter_dom_serialize($document);
    $this->assertEqual('', $result);
  }
}
