<?php

/**
 * @file
 * Mock module for locale layer tests.
 */

/**
 * Implements hook_locale().
 */
function locale_test_locale($op = 'groups') {
  switch ($op) {
    case 'groups':
      return array('custom' => t('Custom'));
  }
}

/**
 * Implements hook_boot().
 *
 * For testing domain language negotiation, we fake it by setting
 * the HTTP_HOST here
 */
function locale_test_boot() {
  if (variable_get('locale_test_domain')) {
    $_SERVER['HTTP_HOST'] = variable_get('locale_test_domain');
  }
}

/**
 * Implements hook_init().
 */
function locale_test_init() {
  locale_test_store_language_negotiation();
  if (isset($GLOBALS['language']) && isset($GLOBALS['language']->provider)) {
    drupal_set_message(t('Language negotiation provider: @name', array('@name' => $GLOBALS['language']->provider)));
  }
}

/**
 * Implements hook_language_types_info().
 */
function locale_test_language_types_info() {
  if (variable_get('locale_test_language_types', FALSE)) {
    return array(
      'test_language_type' => array(
        'name' => t('Test'),
        'description' => t('A test language type.'),
      ),
      'fixed_test_language_type' => array(
        'fixed' => array('test_language_provider'),
      ),
    );
  }
}

/**
 * Implements hook_menu().
 *
 * @return array
 */
function locale_test_menu() {
  $items = array();
  $items['locale_test_plural_format_page'] = array(
    'page callback' => 'locale_test_plural_format_page',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
  );

  return $items;
}

/**
 * Implements hook_language_types_info_alter().
 */
function locale_test_language_types_info_alter(array &$language_types) {
  if (variable_get('locale_test_content_language_type', FALSE)) {
    unset($language_types[LANGUAGE_TYPE_CONTENT]['fixed']);
  }
}

/**
 * Implements hook_language_negotiation_info().
 */
function locale_test_language_negotiation_info() {
  if (variable_get('locale_test_language_negotiation_info', FALSE)) {
    $info = array(
      'callbacks' => array(
        'language' => 'locale_test_language_provider',
      ),
      'file' => drupal_get_path('module', 'locale_test') .'/locale_test.module',
      'weight' => -10,
      'description' => t('This is a test language provider.'),
    );

    return array(
      'test_language_provider' => array(
        'name' => t('Test'),
        'types' => array(LANGUAGE_TYPE_CONTENT, 'test_language_type', 'fixed_test_language_type'),
      ) + $info,
      'test_language_provider_ts' => array(
        'name' => t('Type-specific test'),
        'types' => array('test_language_type'),
      ) + $info,
    );
  }
}

/**
 * Implements hook_language_negotiation_info_alter().
 */
function locale_test_language_negotiation_info_alter(array &$language_providers) {
  if (variable_get('locale_test_language_negotiation_info_alter', FALSE)) {
    unset($language_providers[LOCALE_LANGUAGE_NEGOTIATION_INTERFACE]);
  }
}

/**
 * Store the last negotiated languages.
 */
function locale_test_store_language_negotiation() {
  $last = array();
  foreach (language_types() as $type) {
    $last[$type] = $GLOBALS[$type]->language;
  }
  variable_set('locale_test_language_negotiation_last', $last);
}

/**
 * Test language provider.
 */
function locale_test_language_provider($languages) {
  return 'it';
}

/**
 * Returns markup for locale_get_plural testing.
 *
 * @return array
 */
function locale_test_plural_format_page() {
  $tests = _locale_test_plural_format_tests();
  $result = array();
  foreach ($tests as $test) {
    $string_param = array(
      '@lang' => $test['language'],
      '@locale_get_plural' => locale_get_plural($test['count'], $test['language'])
    );
    $result[] = array(
      '#prefix' => '<br/>',
      '#markup' => format_string('Language: @lang, locale_get_plural: @locale_get_plural.', $string_param),
    );
  }
  return $result;
}

/**
 * Helper function with list of test cases
 *
 * @return array
 */
function _locale_test_plural_format_tests() {
  return array(
    // Test data for English (no formula present).
    array(
      'count' => 1,
      'language' => 'en',
      'expected-result' => 0,
    ),
    array(
      'count' => 0,
      'language' => 'en',
      'expected-result' => 1,
    ),
    array(
      'count' => 5,
      'language' => 'en',
      'expected-result' => 1,
    ),

    // Test data for French (simpler formula).
    array(
      'count' => 1,
      'language' => 'fr',
      'expected-result' => 0,
    ),
    array(
      'count' => 0,
      'language' => 'fr',
      'expected-result' => 1,
    ),
    array(
      'count' => 5,
      'language' => 'fr',
      'expected-result' => 1,
    ),

    // Test data for Croatian (more complex formula).
    array(
      'count' => 1,
      'language' => 'hr',
      'expected-result' => 0,
    ),
    array(
      'count' => 21,
      'language' => 'hr',
      'expected-result' => 0,
    ),
    array(
      'count' => 0,
      'language' => 'hr',
      'expected-result' => 2,
    ),
    array(
      'count' => 2,
      'language' => 'hr',
      'expected-result' => 1,
    ),
    array(
      'count' => 8,
      'language' => 'hr',
      'expected-result' => 2,
    ),

    // Test data for Hungarian (nonexistent language).
    array(
      'count' => 1,
      'language' => 'hu',
      'expected-result' => -1,
    ),
    array(
      'count' => 21,
      'language' => 'hu',
      'expected-result' => -1,
    ),
    array(
      'count' => 0,
      'language' => 'hu',
      'expected-result' => -1,
    ),
  );
}
