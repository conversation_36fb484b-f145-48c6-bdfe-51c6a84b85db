
.locale-untranslated {
  font-style: normal;
  text-decoration: line-through;
}

#locale-translation-filter-form .form-item-language,
#locale-translation-filter-form .form-item-translation,
#locale-translation-filter-form .form-item-group {
  float: left; /* LTR */
  padding-right: .8em; /* LTR */
  margin: 0.1em;
  /**
   * In Opera 9, DOM elements with the property of "overflow: auto"
   * will partially hide its contents with unnecessary scrollbars when
   * its immediate child is floated without an explicit width set.
   */
  width: 15em;
}
#locale-translation-filter-form .form-type-select select {
  width: 100%;
}
#locale-translation-filter-form .form-actions {
  float: left; /* LTR */
  padding: 3ex 0 0 1em; /* LTR */
}
.language-switcher-locale-session a.active {
  color: #0062A0;
}
.language-switcher-locale-session a.session-active {
  color: #000000;
}
