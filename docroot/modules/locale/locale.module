<?php

/**
 * @file
 *   Add language handling functionality and enables the translation of the
 *   user interface to languages other than English.
 *
 *   When enabled, multiple languages can be set up. The site interface
 *   can be displayed in different languages, as well as nodes can have languages
 *   assigned. The setup of languages and translations is completely web based.
 *   Gettext portable object files are supported.
 */

// ---------------------------------------------------------------------------------
// Hook implementations

/**
 * Implements hook_help().
 */
function locale_help($path, $arg) {
  switch ($path) {
    case 'admin/help#locale':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Locale module allows your Drupal site to be presented in languages other than the default English, and to be multilingual. The Locale module works by maintaining a database of translations, and examining text as it is about to be displayed. When a translation of the text is available in the language to be displayed, the translation is displayed rather than the original text. When a translation is unavailable, the original text is displayed, and then stored for review by a translator. For more information, see the online handbook entry for <a href="@locale">Locale module</a>.', array('@locale' => 'http://drupal.org/documentation/modules/locale/')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Translating interface text') . '</dt>';
      $output .= '<dd>' . t('Translations of text in the Drupal interface may be provided by:');
      $output .= '<ul>';
      $output .= '<li>' . t("Translating within your site, using the Locale module's integrated <a href='@translate'>translation interface</a>.", array('@translate' => url('admin/config/regional/translate'))) . '</li>';
      $output .= '<li>' . t('Importing files from a set of existing translations, known as a translation package. A translation package enables the display of a specific version of Drupal in a specific language, and contains files in the Gettext Portable Object (<em>.po</em>) format. Although not all languages are available for every version of Drupal, translation packages for many languages are available for download from the <a href="@translations">Drupal translations page</a>.', array('@translations' => 'http://localize.drupal.org')) . '</li>';
      $output .= '<li>' . t("If an existing translation package does not meet your needs, the Gettext Portable Object (<em>.po</em>) files within a package may be modified, or new <em>.po</em> files may be created, using a desktop Gettext editor. The Locale module's <a href='@import'>import</a> feature allows the translated strings from a new or modified <em>.po</em> file to be added to your site. The Locale module's <a href='@export'>export</a> feature generates files from your site's translated strings, that can either be shared with others or edited offline by a Gettext translation editor.", array('@import' => url('admin/config/regional/translate/import'), '@export' => url('admin/config/regional/translate/export'))) . '</li>';
      $output .= '</ul></dd>';
      $output .= '<dt>' . t('Configuring a multilingual site') . '</dt>';
      $output .= '<dd>' . t("Language negotiation allows your site to automatically change language based on the domain or path used for each request. Users may (optionally) select their preferred language on their <em>My account</em> page, and your site can be configured to honor a web browser's preferred language settings. Site content can be translated using the <a href='@content-help'>Content translation module</a>.", array('@content-help' => url('admin/help/translation'))) . '</dd>';
      $output .= '</dl>';
      return $output;
    case 'admin/config/regional/language':
      $output = '<p>' . t('With multiple languages enabled, interface text can be translated, registered users may select their preferred language, and authors can assign a specific language to content. <a href="@translations">Download contributed translations</a> from Drupal.org.', array('@translations' => 'http://localize.drupal.org')) . '</p>';
      return $output;
    case 'admin/config/regional/language/add':
      return '<p>' . t('Add a language to be supported by your site. If your desired language is not available in the <em>Language name</em> drop-down, click <em>Custom language</em> and provide a language code and other details manually. When providing a language code manually, be sure to enter a standardized language code, since this code may be used by browsers to determine an appropriate display language.') . '</p>';
    case 'admin/config/regional/language/configure':
      $output = '<p>' . t("Define how to decide which language is used to display page elements (primarily text provided by Drupal and modules, such as field labels and help text). This decision is made by evaluating a series of detection methods for languages; the first detection method that gets a result will determine which language is used for that type of text. Define the order of evaluation of language detection methods on this page.") . '</p>';
      return $output;
    case 'admin/config/regional/language/configure/session':
      $output = '<p>' . t('Determine the language from a request/session parameter. Example: "http://example.com?language=de" sets language to German based on the use of "de" within the "language" parameter.') . '</p>';
      return $output;
    case 'admin/config/regional/translate':
      $output = '<p>' . t('This page provides an overview of available translatable strings. Drupal displays translatable strings in text groups; modules may define additional text groups containing other translatable strings. Because text groups provide a method of grouping related strings, they are often used to focus translation efforts on specific areas of the Drupal interface.') . '</p>';
      $output .= '<p>' . t('See the <a href="@languages">Languages page</a> for more information on adding support for additional languages.', array('@languages' => url('admin/config/regional/language'))) . '</p>';
      return $output;
    case 'admin/config/regional/translate/import':
      $output = '<p>' . t('This page imports the translated strings contained in an individual Gettext Portable Object (<em>.po</em>) file. Normally distributed as part of a translation package (each translation package may contain several <em>.po</em> files), a <em>.po</em> file may need to be imported after offline editing in a Gettext translation editor. Importing an individual <em>.po</em> file may be a lengthy process.') . '</p>';
      $output .= '<p>' . t('Note that the <em>.po</em> files within a translation package are imported automatically (if available) when new modules or themes are enabled, or as new languages are added. Since this page only allows the import of one <em>.po</em> file at a time, it may be simpler to download and extract a translation package into your Drupal installation directory and <a href="@language-add">add the language</a> (which automatically imports all <em>.po</em> files within the package). Translation packages are available for download on the <a href="@translations">Drupal translation page</a>.', array('@language-add' => url('admin/config/regional/language/add'), '@translations' => 'http://localize.drupal.org')) . '</p>';
      return $output;
    case 'admin/config/regional/translate/export':
      return '<p>' . t('This page exports the translated strings used by your site. An export file may be in Gettext Portable Object (<em>.po</em>) form, which includes both the original string and the translation (used to share translations with others), or in Gettext Portable Object Template (<em>.pot</em>) form, which includes the original strings only (used to create new translations with a Gettext translation editor).') . '</p>';
    case 'admin/config/regional/translate/translate':
      return '<p>' . t('This page allows a translator to search for specific translated and untranslated strings, and is used when creating or editing translations. (Note: For translation tasks involving many strings, it may be more convenient to <a href="@export">export</a> strings for offline editing in a desktop Gettext translation editor.) Searches may be limited to strings found within a specific text group or in a specific language.', array('@export' => url('admin/config/regional/translate/export'))) . '</p>';
    case 'admin/structure/block/manage/%/%':
      if ($arg[4] == 'locale' && $arg[5] == 'language') {
        return '<p>' . t('This block is only shown if <a href="@languages">at least two languages are enabled</a> and <a href="@configuration">language negotiation</a> is set to <em>URL</em> or <em>Session</em>.', array('@languages' => url('admin/config/regional/language'), '@configuration' => url('admin/config/regional/language/configure'))) . '</p>';
      }
      break;
  }
}

/**
 * Implements hook_menu().
 */
function locale_menu() {
  // Manage languages
  $items['admin/config/regional/language'] = array(
    'title' => 'Languages',
    'description' => 'Configure languages for content and the user interface.',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_languages_overview_form'),
    'access arguments' => array('administer languages'),
    'file' => 'locale.admin.inc',
    'weight' => -10,
  );
  $items['admin/config/regional/language/overview'] = array(
    'title' => 'List',
    'weight' => 0,
    'type' => MENU_DEFAULT_LOCAL_TASK,
  );
  $items['admin/config/regional/language/add'] = array(
    'title' => 'Add language',
    'page callback' => 'locale_languages_add_screen', // two forms concatenated
    'access arguments' => array('administer languages'),
    'weight' => 5,
    'type' => MENU_LOCAL_ACTION,
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/language/configure'] = array(
    'title' => 'Detection and selection',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_languages_configure_form'),
    'access arguments' => array('administer languages'),
    'weight' => 10,
    'file' => 'locale.admin.inc',
    'type' => MENU_LOCAL_TASK,
  );
  $items['admin/config/regional/language/configure/url'] = array(
    'title' => 'URL language detection configuration',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_language_providers_url_form'),
    'access arguments' => array('administer languages'),
    'file' => 'locale.admin.inc',
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
  );
  $items['admin/config/regional/language/configure/session'] = array(
    'title' => 'Session language detection configuration',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_language_providers_session_form'),
    'access arguments' => array('administer languages'),
    'file' => 'locale.admin.inc',
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
  );
  $items['admin/config/regional/language/edit/%'] = array(
    'title' => 'Edit language',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_languages_edit_form', 5),
    'access arguments' => array('administer languages'),
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/language/delete/%'] = array(
    'title' => 'Confirm',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_languages_delete_form', 5),
    'access arguments' => array('administer languages'),
    'file' => 'locale.admin.inc',
  );

  // Translation functionality
  $items['admin/config/regional/translate'] = array(
    'title' => 'Translate interface',
    'description' => 'Translate the built in interface and optionally other text.',
    'page callback' => 'locale_translate_overview_screen',
    'access arguments' => array('translate interface'),
    'file' => 'locale.admin.inc',
    'weight' => -5,
  );
  $items['admin/config/regional/translate/overview'] = array(
    'title' => 'Overview',
    'weight' => 0,
    'type' => MENU_DEFAULT_LOCAL_TASK,
  );
  $items['admin/config/regional/translate/translate'] = array(
    'title' => 'Translate',
    'weight' => 10,
    'type' => MENU_LOCAL_TASK,
    'page callback' => 'locale_translate_seek_screen', // search results and form concatenated
    'access arguments' => array('translate interface'),
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/translate/import'] = array(
    'title' => 'Import',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_translate_import_form'),
    'access arguments' => array('translate interface'),
    'weight' => 20,
    'type' => MENU_LOCAL_TASK,
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/translate/export'] = array(
    'title' => 'Export',
    'page callback' => 'locale_translate_export_screen',  // possibly multiple forms concatenated
    'access arguments' => array('translate interface'),
    'weight' => 30,
    'type' => MENU_LOCAL_TASK,
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/translate/edit/%'] = array(
    'title' => 'Edit string',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_translate_edit_form', 5),
    'access arguments' => array('translate interface'),
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/translate/delete/%'] = array(
    'title' => 'Delete string',
    'page callback' => 'locale_translate_delete_page',
    'page arguments' => array(5),
    'access arguments' => array('translate interface'),
    'file' => 'locale.admin.inc',
  );

  // Localize date formats.
  $items['admin/config/regional/date-time/locale'] = array(
    'title' => 'Localize',
    'description' => 'Configure date formats for each locale',
    'page callback' => 'locale_date_format_language_overview_page',
    'access arguments' => array('administer site configuration'),
    'type' => MENU_LOCAL_TASK,
    'weight' => -8,
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/date-time/locale/%/edit'] = array(
    'title' => 'Localize date formats',
    'description' => 'Configure date formats for each locale',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_date_format_form', 5),
    'access arguments' => array('administer site configuration'),
    'file' => 'locale.admin.inc',
  );
  $items['admin/config/regional/date-time/locale/%/reset'] = array(
    'title' => 'Reset date formats',
    'description' => 'Reset localized date formats to global defaults',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('locale_date_format_reset_form', 5),
    'access arguments' => array('administer site configuration'),
    'file' => 'locale.admin.inc',
  );

  return $items;
}

/**
 * Implements hook_init().
 *
 * Initialize date formats according to the user's current locale.
 */
function locale_init() {
  global $conf, $language;
  include_once DRUPAL_ROOT . '/includes/locale.inc';

  // For each date type (e.g. long, short), get the localized date format
  // for the user's current language and override the default setting for it
  // in $conf. This should happen on all pages except the date and time formats
  // settings page, where we want to display the site default and not the
  // localized version.
  if (strpos($_GET['q'], 'admin/config/regional/date-time/formats') !== 0) {
    $languages = array($language->language);

    // Setup appropriate date formats for this locale.
    $formats = locale_get_localized_date_format($languages);
    foreach ($formats as $format_type => $format) {
      $conf[$format_type] = $format;
    }
  }
}

/**
 * Implements hook_permission().
 */
function locale_permission() {
  return array(
    'administer languages' => array(
      'title' => t('Administer languages'),
    ),
    'translate interface' => array(
      'title' => t('Translate interface texts'),
      'restrict access' => TRUE,
    ),
  );
}

/**
 * Implements hook_locale().
 */
function locale_locale($op = 'groups') {
  switch ($op) {
    case 'groups':
      return array('default' => t('Built-in interface'));
  }
}

/**
 * Form builder callback to display language selection widget.
 *
 * @ingroup forms
 * @see locale_form_alter()
 */
function locale_language_selector_form(&$form, &$form_state, $user) {
  global $language;
  $languages = language_list('enabled');
  $languages = $languages[1];

  // If the user is being created, we set the user language to the page language.
  $user_preferred_language = $user->uid ? user_preferred_language($user) : $language;

  $names = array();
  foreach ($languages as $langcode => $item) {
    $name = t($item->name);
    $names[$langcode] = $name . ($item->native != $name ? ' (' . $item->native . ')' : '');
  }
  $form['locale'] = array(
    '#type' => 'fieldset',
    '#title' => t('Language settings'),
    '#weight' => 1,
    '#access' => ($form['#user_category'] == 'account' || ($form['#user_category'] == 'register' && user_access('administer users'))),
  );

  // Get language negotiation settings.
  $mode = language_negotiation_get(LANGUAGE_TYPE_INTERFACE) != LANGUAGE_NEGOTIATION_DEFAULT;
  $form['locale']['language'] = array(
    '#type' => (count($names) <= 5 ? 'radios' : 'select'),
    '#title' => t('Language'),
    '#default_value' => $user_preferred_language->language,
    '#options' => $names,
    '#description' => $mode ? t("This account's default language for e-mails, and preferred language for site presentation.") : t("This account's default language for e-mails."),
  );
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function locale_form_path_admin_form_alter(&$form, &$form_state) {
  $form['language'] = array(
    '#type' => 'select',
    '#title' => t('Language'),
    '#options' => array(LANGUAGE_NONE => t('All languages')) + locale_language_list('name'),
    '#default_value' => $form['language']['#value'],
    '#weight' => -10,
    '#description' => t('A path alias set for a specific language will always be used when displaying this page in that language, and takes precedence over path aliases set for <em>All languages</em>.'),
  );
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function locale_form_node_type_form_alter(&$form, &$form_state) {
  if (isset($form['type'])) {
    $form['workflow']['language_content_type'] = array(
      '#type' => 'radios',
      '#title' => t('Multilingual support'),
      '#default_value' => variable_get('language_content_type_' . $form['#node_type']->type, 0),
      '#options' => array(t('Disabled'), t('Enabled')),
      '#description' => t('Enable multilingual support for this content type. If enabled, a language selection field will be added to the editing form, allowing you to select from one of the <a href="!languages">enabled languages</a>. If disabled, new posts are saved with the default language. Existing content will not be affected by changing this option.', array('!languages' => url('admin/config/regional/language'))),
    );
  }
}

/**
 * Return whether the given content type has multilingual support.
 *
 * @return
 *   True if multilingual support is enabled.
 */
function locale_multilingual_node_type($type_name) {
  return (bool) variable_get('language_content_type_' . $type_name, 0);
}

/**
 * Implements hook_form_alter().
 *
 * Adds language fields to user forms.
 */
function locale_form_alter(&$form, &$form_state, $form_id) {
  // Only alter user forms if there is more than one language.
  if (drupal_multilingual()) {
    // Display language selector when either creating a user on the admin
    // interface or editing a user account.
    if ($form_id == 'user_register_form' || ($form_id == 'user_profile_form' && $form['#user_category'] == 'account')) {
      locale_language_selector_form($form, $form_state, $form['#user']);
    }
  }
}

/**
 * Implements hook_form_BASE_FORM_ID_alter().
 */
function locale_form_node_form_alter(&$form, &$form_state) {
  if (isset($form['#node']->type) && locale_multilingual_node_type($form['#node']->type)) {
    $form['language'] = array(
      '#type' => 'select',
      '#title' => t('Language'),
      '#default_value' => (isset($form['#node']->language) ? $form['#node']->language : ''),
      '#options' => array(LANGUAGE_NONE => t('Language neutral')) + locale_language_list('name'),
    );
  }
  // Node type without language selector: assign the default for new nodes
  elseif (!isset($form['#node']->nid)) {
    $default = language_default();
    $form['language'] = array(
      '#type' => 'value',
      '#value' => $default->language
    );
  }
  $form['#submit'][] = 'locale_field_node_form_submit';
}

/**
 * Form submit handler for node_form().
 *
 * This submit handler needs to run before entity_form_submit_build_entity()
 * is invoked by node_form_submit_build_node(), because it alters the values of
 * attached fields. Therefore, it cannot be a hook_node_submit() implementation.
 */
function locale_field_node_form_submit($form, &$form_state) {
  locale_field_entity_form_submit('node', $form, $form_state);
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function locale_form_comment_form_alter(&$form, &$form_state, $form_id) {
  // If a content type has multilingual support we set the content language as
  // comment language.
  if ($form['language']['#value'] == LANGUAGE_NONE && locale_multilingual_node_type($form['#node']->type)) {
    global $language_content;
    $form['language']['#value'] = $language_content->language;
    $submit_callback = 'locale_field_comment_form_submit';
    array_unshift($form['actions']['preview']['#submit'], $submit_callback);
    array_unshift($form['#submit'], $submit_callback);
  }
}

/**
 * Form submit handler for comment_form().
 *
 * This submit handler needs to run before entity_form_submit_build_entity()
 * is invoked by comment_form_submit_build_comment(), because it alters the
 * values of attached fields.
 */
function locale_field_comment_form_submit($form, &$form_state) {
  locale_field_entity_form_submit('comment', $form, $form_state);
}

/**
 * Handles field language on submit for the given entity type.
 *
 * Checks if Locale is registered as a translation handler and handle possible
 * language changes.
 */
function locale_field_entity_form_submit($entity_type, $form, &$form_state ) {
  if (field_has_translation_handler($entity_type, 'locale')) {
    $entity = (object) $form_state['values'];
    $current_language = entity_language($entity_type, $entity);
    list(, , $bundle) = entity_extract_ids($entity_type, $entity);

    foreach (field_info_instances($entity_type, $bundle) as $instance) {
      $field_name = $instance['field_name'];
      $field = field_info_field($field_name);
      $previous_language = $form[$field_name]['#language'];

      // Handle a possible language change: new language values are inserted,
      // previous ones are deleted.
      if ($field['translatable'] && $previous_language != $current_language) {
        $form_state['values'][$field_name][$current_language] = $entity->{$field_name}[$previous_language];
        $form_state['values'][$field_name][$previous_language] = array();
      }
    }
  }
}

/**
 * Implements hook_theme().
 */
function locale_theme() {
  return array(
    'locale_languages_overview_form' => array(
      'render element' => 'form',
    ),
    'locale_languages_configure_form' => array(
      'render element' => 'form',
    ),
    'locale_date_format_form' => array(
      'render element' => 'form',
    ),
  );
}

/**
 * Implements hook_field_language_alter().
 */
function locale_field_language_alter(&$display_language, $context) {
  // Do not apply core language fallback rules if they are disabled or if Locale
  // is not registered as a translation handler.
  if (variable_get('locale_field_language_fallback', TRUE) && field_has_translation_handler($context['entity_type'], 'locale')) {
    locale_field_language_fallback($display_language, $context['entity'], $context['language']);
  }
}

/**
 * Applies language fallback rules to the fields attached to the given entity.
 *
 * Core language fallback rules simply check if fields have a field translation
 * for the requested language code. If so the requested language is returned,
 * otherwise all the fallback candidates are inspected to see if there is a
 * field translation available in another language.
 * By default this is called by locale_field_language_alter(), but this
 * behavior can be disabled by setting the 'locale_field_language_fallback'
 * variable to FALSE.
 *
 * @param $display_language
 *   A reference to an array of language codes keyed by field name.
 * @param $entity
 *   The entity to be displayed.
 * @param $langcode
 *   The language code $entity has to be displayed in.
 */
function locale_field_language_fallback(&$display_language, $entity, $langcode) {
  // Lazily init fallback candidates to avoid unnecessary calls.
  $fallback_candidates = NULL;
  $field_languages = array();

  foreach ($display_language as $field_name => $field_language) {
    // If the requested language is defined for the current field use it,
    // otherwise search for a fallback value among the fallback candidates.
    if (isset($entity->{$field_name}[$langcode])) {
      $display_language[$field_name] = $langcode;
    }
    elseif (!empty($entity->{$field_name})) {
      if (!isset($fallback_candidates)) {
        require_once DRUPAL_ROOT . '/includes/language.inc';
        $fallback_candidates = language_fallback_get_candidates();
      }
      foreach ($fallback_candidates as $fallback_language) {
        if (isset($entity->{$field_name}[$fallback_language])) {
          $display_language[$field_name] = $fallback_language;
          break;
        }
      }
    }
  }
}

/**
 * Implements hook_entity_info_alter().
 */
function locale_entity_info_alter(&$entity_info) {
  $entity_info['node']['translation']['locale'] = TRUE;
  if (isset($entity_info['comment'])) {
    $entity_info['comment']['translation']['locale'] = TRUE;
  }
}

/**
 * Implements hook_language_types_info().
 *
 * Defines the three core language types:
 * - Interface language is the only configurable language type in core. It is
 *   used by t() as the default language if none is specified.
 * - Content language is by default non-configurable and inherits the interface
 *   language negotiated value. It is used by the Field API to determine the
 *   display language for fields if no explicit value is specified.
 * - URL language is by default non-configurable and is determined through the
 *   URL language provider or the URL fallback provider if no language can be
 *   detected. It is used by l() as the default language if none is specified.
 */
function locale_language_types_info() {
  require_once DRUPAL_ROOT . '/includes/locale.inc';
  return array(
    LANGUAGE_TYPE_INTERFACE => array(
      'name' => t('User interface text'),
      'description' => t('Order of language detection methods for user interface text. If a translation of user interface text is available in the detected language, it will be displayed.'),
    ),
    LANGUAGE_TYPE_CONTENT => array(
      'name' => t('Content'),
      'description' => t('Order of language detection methods for content. If a version of content is available in the detected language, it will be displayed.'),
      'fixed' => array(LOCALE_LANGUAGE_NEGOTIATION_INTERFACE),
    ),
    LANGUAGE_TYPE_URL => array(
      'name' => t('URL'),
      'description' => t('Order of language detection methods for URLs. The detected language will be used as the default when generating URLs for internal links on the site.'),
      'fixed' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LOCALE_LANGUAGE_NEGOTIATION_URL_FALLBACK),
    ),
  );
}

/**
 * Implements hook_language_negotiation_info().
 */
function locale_language_negotiation_info() {
  require_once DRUPAL_ROOT . '/includes/locale.inc';
  $file = 'includes/locale.inc';
  $providers = array();

  $providers[LOCALE_LANGUAGE_NEGOTIATION_URL] = array(
    'types' => array(LANGUAGE_TYPE_CONTENT, LANGUAGE_TYPE_INTERFACE, LANGUAGE_TYPE_URL),
    'callbacks' => array(
      'language' => 'locale_language_from_url',
      'switcher' => 'locale_language_switcher_url',
      'url_rewrite' => 'locale_language_url_rewrite_url',
    ),
    'file' => $file,
    'weight' => -8,
    'name' => t('URL'),
    'description' => t('Determine the language from the URL (Path prefix or domain).'),
    'config' => 'admin/config/regional/language/configure/url',
  );

  $providers[LOCALE_LANGUAGE_NEGOTIATION_SESSION] = array(
    'callbacks' => array(
      'language' => 'locale_language_from_session',
      'switcher' => 'locale_language_switcher_session',
      'url_rewrite' => 'locale_language_url_rewrite_session',
    ),
    'file' => $file,
    'weight' => -6,
    'name' => t('Session'),
    'description' => t('Determine the language from a request/session parameter.'),
    'config' => 'admin/config/regional/language/configure/session',
  );

  $providers[LOCALE_LANGUAGE_NEGOTIATION_USER] = array(
    'callbacks' => array('language' => 'locale_language_from_user'),
    'file' => $file,
    'weight' => -4,
    'name' => t('User'),
    'description' => t("Follow the user's language preference."),
  );

  $providers[LOCALE_LANGUAGE_NEGOTIATION_BROWSER] = array(
    'callbacks' => array('language' => 'locale_language_from_browser'),
    'file' => $file,
    'weight' => -2,
    'cache' => 0,
    'name' => t('Browser'),
    'description' => t("Determine the language from the browser's language settings."),
  );

  $providers[LOCALE_LANGUAGE_NEGOTIATION_INTERFACE] = array(
    'types' => array(LANGUAGE_TYPE_CONTENT),
    'callbacks' => array('language' => 'locale_language_from_interface'),
    'file' => $file,
    'weight' => 8,
    'name' => t('Interface'),
    'description' => t('Use the detected interface language.'),
  );

  $providers[LOCALE_LANGUAGE_NEGOTIATION_URL_FALLBACK] = array(
    'types' => array(LANGUAGE_TYPE_URL),
    'callbacks' => array('language' => 'locale_language_url_fallback'),
    'file' => $file,
    'weight' => 8,
    'name' => t('URL fallback'),
    'description' => t('Use an already detected language for URLs if none is found.'),
  );

  return $providers;
}

/**
 * Implements hook_modules_enabled().
 */
function locale_modules_enabled($modules) {
  include_once DRUPAL_ROOT . '/includes/language.inc';
  language_types_set();
  language_negotiation_purge();
}

/**
 * Implements hook_modules_disabled().
 */
function locale_modules_disabled($modules) {
  locale_modules_enabled($modules);
}

// ---------------------------------------------------------------------------------
// Locale core functionality

/**
 * Provides interface translation services.
 *
 * This function is called from t() to translate a string if needed.
 *
 * @param $string
 *   A string to look up translation for. If omitted, all the
 *   cached strings will be returned in all languages already
 *   used on the page.
 * @param $context
 *   The context of this string.
 * @param $langcode
 *   Language code to use for the lookup.
 */
function locale($string = NULL, $context = NULL, $langcode = NULL) {
  global $language;

  // Use the advanced drupal_static() pattern, since this is called very often.
  static $drupal_static_fast;
  if (!isset($drupal_static_fast)) {
    $drupal_static_fast['locale'] = &drupal_static(__FUNCTION__);
  }
  $locale_t = &$drupal_static_fast['locale'];


  if (!isset($string)) {
    // Return all cached strings if no string was specified
    return $locale_t;
  }

  $langcode = isset($langcode) ? $langcode : $language->language;

  // Store database cached translations in a static variable. Only build the
  // cache after $language has been set to avoid an unnecessary cache rebuild.
  if (!isset($locale_t[$langcode]) && isset($language)) {
    $locale_t[$langcode] = array();
    // Disabling the usage of string caching allows a module to watch for
    // the exact list of strings used on a page. From a performance
    // perspective that is a really bad idea, so we have no user
    // interface for this. Be careful when turning this option off!
    if (variable_get('locale_cache_strings', 1) == 1) {
      if ($cache = cache_get('locale:' . $langcode, 'cache')) {
        $locale_t[$langcode] = $cache->data;
      }
      elseif (lock_acquire('locale_cache_' . $langcode)) {
        // Refresh database stored cache of translations for given language.
        // We only store short strings used in current version, to improve
        // performance and consume less memory.
        $result = db_query("SELECT s.source, s.context, t.translation, t.language FROM {locales_source} s LEFT JOIN {locales_target} t ON s.lid = t.lid AND t.language = :language WHERE s.textgroup = 'default' AND s.version = :version AND LENGTH(s.source) < :length", array(':language' => $langcode, ':version' => VERSION, ':length' => variable_get('locale_cache_length', 75)));
        foreach ($result as $data) {
          $locale_t[$langcode][$data->context][$data->source] = (empty($data->translation) ? TRUE : $data->translation);
        }
        cache_set('locale:' . $langcode, $locale_t[$langcode]);
        lock_release('locale_cache_' . $langcode);
      }
    }
  }

  // If we have the translation cached, skip checking the database
  if (!isset($locale_t[$langcode][$context][$string])) {

    // We do not have this translation cached, so get it from the DB.
    $translation = db_query("SELECT s.lid, t.translation, s.version FROM {locales_source} s LEFT JOIN {locales_target} t ON s.lid = t.lid AND t.language = :language WHERE s.source = :source AND s.context = :context AND s.textgroup = 'default'", array(
      ':language' => $langcode,
      ':source' => $string,
      ':context' => (string) $context,
    ))->fetchObject();
    if ($translation) {
      // We have the source string at least.
      // Cache translation string or TRUE if no translation exists.
      $locale_t[$langcode][$context][$string] = (empty($translation->translation) ? TRUE : $translation->translation);

      if ($translation->version != VERSION) {
        // This is the first use of this string under current Drupal version. Save version
        // and clear cache, to include the string into caching next time. Saved version is
        // also a string-history information for later pruning of the tables.
        db_update('locales_source')
          ->fields(array('version' => VERSION))
          ->condition('lid', $translation->lid)
          ->execute();
        cache_clear_all('locale:', 'cache', TRUE);
      }
    }
    else {
      // We don't have the source string, cache this as untranslated.
      db_merge('locales_source')
        ->insertFields(array(
          'location' => request_uri(),
          'version' => VERSION,
        ))
        ->key(array(
          'source' => $string,
          'context' => (string) $context,
          'textgroup' => 'default',
        ))
        ->execute();
      $locale_t[$langcode][$context][$string] = TRUE;
      // Clear locale cache so this string can be added in a later request.
      cache_clear_all('locale:', 'cache', TRUE);
    }
  }

  return ($locale_t[$langcode][$context][$string] === TRUE ? $string : $locale_t[$langcode][$context][$string]);
}

/**
 * Reset static variables used by locale().
 */
function locale_reset() {
  drupal_static_reset('locale');
}

/**
 * Returns plural form index for a specific number.
 *
 * The index is computed from the formula of this language.
 *
 * @param $count
 *   Number to return plural for.
 * @param $langcode
 *   Optional language code to translate to a language other than
 *   what is used to display the page.
 *
 * @return
 *   The numeric index of the plural variant to use for this $langcode and
 *   $count combination or -1 if the language was not found or does not have a
 *   plural formula.
 */
function locale_get_plural($count, $langcode = NULL) {
  global $language;

  // Used to locally cache the plural formulas for all languages.
  $plural_formulas = &drupal_static(__FUNCTION__, array());

  // Used to store precomputed plural indexes corresponding to numbers
  // individually for each language.
  $plural_indexes = &drupal_static(__FUNCTION__ . ':plurals', array());

  $langcode = $langcode ? $langcode : $language->language;

  if (!isset($plural_indexes[$langcode][$count])) {
    // Retrieve and statically cache the plural formulas for all languages.
    if (empty($plural_formulas)) {
      foreach (language_list() as $installed_language) {
        $plural_formulas[$installed_language->language] = $installed_language->formula;
      }
    }
    // If there is a plural formula for the language, evaluate it for the given
    // $count and statically cache the result for the combination of language
    // and count, since the result will always be identical.
    if (!empty($plural_formulas[$langcode])) {
      // $n is used inside the expression in the eval().
      $n = $count;
      $plural_indexes[$langcode][$count] = @eval('return intval(' . $plural_formulas[$langcode] . ');');
    }
    // In case there is no plural formula for English (no imported translation
    // for English), use a default formula.
    elseif ($langcode == 'en') {
      $plural_indexes[$langcode][$count] = (int) ($count != 1);
    }
    // Otherwise, return -1 (unknown).
    else {
      $plural_indexes[$langcode][$count] = -1;
    }
  }
  return $plural_indexes[$langcode][$count];
}


/**
 * Returns a language name
 */
function locale_language_name($lang) {
  $list = &drupal_static(__FUNCTION__);
  if (!isset($list)) {
    $list = locale_language_list();
  }
  return ($lang && isset($list[$lang])) ? $list[$lang] : t('All');
}

/**
 * Returns array of language names
 *
 * @param $field
 *   'name' => names in current language, localized
 *   'native' => native names
 * @param $all
 *   Boolean to return all languages or only enabled ones
 */
function locale_language_list($field = 'name', $all = FALSE) {
  if ($all) {
    $languages = language_list();
  }
  else {
    $languages = language_list('enabled');
    $languages = $languages[1];
  }
  $list = array();
  foreach ($languages as $language) {
    $list[$language->language] = ($field == 'name') ? t($language->name) : $language->$field;
  }
  return $list;
}

/**
 * Implements hook_modules_installed().
 */
function locale_modules_installed($modules) {
  locale_system_update($modules);
}

/**
 * Implements hook_themes_enabled().
 *
 * @todo This is technically wrong. We must not import upon enabling, but upon
 *   initial installation. The theme system is missing an installation hook.
 */
function locale_themes_enabled($themes) {
  locale_system_update($themes);
}

/**
 * Imports translations when new modules or themes are installed.
 *
 * This function will either import translation for the component change
 * right away, or start a batch if more files need to be imported.
 *
 * @param $components
 *   An array of component (theme and/or module) names to import
 *   translations for.
 */
function locale_system_update($components) {
  include_once DRUPAL_ROOT . '/includes/locale.inc';
  if ($batch = locale_batch_by_component($components)) {
    batch_set($batch);
  }
}

/**
 * Implements hook_js_alter().
 *
 * This function checks all JavaScript files currently added via drupal_add_js()
 * and invokes parsing if they have not yet been parsed for Drupal.t()
 * and Drupal.formatPlural() calls. Also refreshes the JavaScript translation
 * file if necessary, and adds it to the page.
 */
function locale_js_alter(&$javascript) {
  global $language;

  $dir = 'public://' . variable_get('locale_js_directory', 'languages');
  $parsed = variable_get('javascript_parsed', array());
  $files = $new_files = FALSE;

  // Require because locale_js_alter() could be called without locale_init().
  require_once DRUPAL_ROOT . '/includes/locale.inc';

  foreach ($javascript as $item) {
    if ($item['type'] == 'file') {
      $files = TRUE;
      $filepath = $item['data'];
      if (!in_array($filepath, $parsed)) {
        // Don't parse our own translations files.
        if (substr($filepath, 0, strlen($dir)) != $dir) {
          _locale_parse_js_file($filepath);
          $parsed[] = $filepath;
          $new_files = TRUE;
        }
      }
    }
  }

  // If there are any new source files we parsed, invalidate existing
  // JavaScript translation files for all languages, adding the refresh
  // flags into the existing array.
  if ($new_files) {
    $parsed += _locale_invalidate_js();
  }

  // If necessary, rebuild the translation file for the current language.
  if (!empty($parsed['refresh:' . $language->language])) {
    // Don't clear the refresh flag on failure, so that another try will
    // be performed later.
    if (_locale_rebuild_js()) {
      unset($parsed['refresh:' . $language->language]);
    }
    // Store any changes after refresh was attempted.
    variable_set('javascript_parsed', $parsed);
  }
  // If no refresh was attempted, but we have new source files, we need
  // to store them too. This occurs if current page is in English.
  elseif ($new_files) {
    variable_set('javascript_parsed', $parsed);
  }

  // Add the translation JavaScript file to the page.
  if ($files && !empty($language->javascript)) {
    // Add the translation JavaScript file to the page.
    $file = $dir . '/' . $language->language . '_' . $language->javascript . '.js';
    $javascript[$file] = drupal_js_defaults($file);
  }
}

/**
 * Implements hook_css_alter().
 *
 * This function checks all CSS files currently added via drupal_add_css() and
 * and checks to see if a related right to left CSS file should be included.
 */
function locale_css_alter(&$css) {
  global $language;

  // If the current language is RTL, add the CSS file with the RTL overrides.
  if ($language->direction == LANGUAGE_RTL) {
    foreach ($css as $data => $item) {
      // Only provide RTL overrides for files.
      if ($item['type'] == 'file') {
        $rtl_path = str_replace('.css', '-rtl.css', $item['data']);
        if (file_exists($rtl_path) && !isset($css[$rtl_path])) {
          // Replicate the same item, but with the RTL path and a little larger
          // weight so that it appears directly after the original CSS file.
          $item['data'] = $rtl_path;
          $item['weight'] += 0.0001;
          $css[$rtl_path] = $item;
        }
      }
    }
  }
}

 /**
 * Implement hook_library_alter().
 *
 * Provides the language support for the jQuery UI Date Picker.
 */
function locale_library_alter(&$libraries, $module) {
  if ($module == 'system' && isset($libraries['ui.datepicker'])) {
    global $language;
    // locale.datepicker.js should be added in the JS_LIBRARY group, so that
    // this attach behavior will execute early. JS_LIBRARY is the default for
    // hook_library_info_alter(), thus does not have to be specified explicitly.
    $datepicker = drupal_get_path('module', 'locale') . '/locale.datepicker.js';
    $libraries['ui.datepicker']['js'][$datepicker] = array();
    $libraries['ui.datepicker']['js'][] = array(
      'data' => array(
        'jquery' => array(
          'ui' => array(
            'datepicker' => array(
              'isRTL' => $language->direction == LANGUAGE_RTL,
              'firstDay' => variable_get('date_first_day', 0),
            ),
          ),
        ),
      ),
      'type' => 'setting',
    );
  }
}

// ---------------------------------------------------------------------------------
// Language switcher block

/**
 * Implements hook_block_info().
 */
function locale_block_info() {
  include_once DRUPAL_ROOT . '/includes/language.inc';
  $block = array();
  $info = language_types_info();
  foreach (language_types_configurable(FALSE) as $type) {
    $block[$type] = array(
      'info' => t('Language switcher (@type)', array('@type' => $info[$type]['name'])),
      // Not worth caching.
      'cache' => DRUPAL_NO_CACHE,
    );
  }
  return $block;
}

/**
 * Implements hook_block_view().
 *
 * Displays a language switcher. Only show if we have at least two languages.
 */
function locale_block_view($type) {
  if (drupal_multilingual()) {
    $path = drupal_is_front_page() ? '<front>' : $_GET['q'];
    $links = language_negotiation_get_switch_links($type, $path);

    if (isset($links->links)) {
      drupal_add_css(drupal_get_path('module', 'locale') . '/locale.css');
      $class = "language-switcher-{$links->provider}";
      $variables = array('links' => $links->links, 'attributes' => array('class' => array($class)));
      $block['content'] = theme('links__locale_block', $variables);
      $block['subject'] = t('Languages');
      return $block;
    }
  }
}

/**
 * Implements hook_url_outbound_alter().
 *
 * Rewrite outbound URLs with language based prefixes.
 */
function locale_url_outbound_alter(&$path, &$options, $original_path) {
  // Only modify internal URLs.
  if (!$options['external'] && drupal_multilingual()) {
    static $drupal_static_fast;
    if (!isset($drupal_static_fast)) {
      $drupal_static_fast['callbacks'] = &drupal_static(__FUNCTION__);
    }
    $callbacks = &$drupal_static_fast['callbacks'];

    if (!isset($callbacks)) {
      $callbacks = array();
      include_once DRUPAL_ROOT . '/includes/language.inc';

      foreach (language_types_configurable() as $type) {
        // Get URL rewriter callbacks only from enabled language providers.
        $negotiation = variable_get("language_negotiation_$type", array());

        foreach ($negotiation as $id => $provider) {
          if (isset($provider['callbacks']['url_rewrite'])) {
            if (isset($provider['file'])) {
              require_once DRUPAL_ROOT . '/' . $provider['file'];
            }
            // Avoid duplicate callback entries.
            $callbacks[$provider['callbacks']['url_rewrite']] = TRUE;
          }
        }
      }

      $callbacks = array_keys($callbacks);
    }

    foreach ($callbacks as $callback) {
      $callback($path, $options);
    }

    // No language dependent path allowed in this mode.
    if (empty($callbacks)) {
      unset($options['language']);
    }
  }
}
