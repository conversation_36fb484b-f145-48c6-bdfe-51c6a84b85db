<?php

/**
 * @file
 * Tests for locale.module.
 *
 * The test file includes:
 *  - a functional test for the language configuration forms;
 *  - functional tests for the translation functionalities, including searching;
 *  - a functional test for the PO files import feature, including validation;
 *  - functional tests for translations and templates export feature;
 *  - functional tests for the uninstall process;
 *  - a functional test for the language switching feature;
 *  - a functional test for a user's ability to change their default language;
 *  - a functional test for configuring a different path alias per language;
 *  - a functional test for configuring a different path alias per language;
 *  - a functional test for multilingual support by content type and on nodes.
 *  - a functional test for multilingual fields.
 *  - a functional test for comment language.
 *  - a functional test fot language types/negotiation info.
 */


/**
 * Functional tests for the language configuration forms.
 */
class LocaleConfigurationTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Language configuration',
      'description' => 'Adds a new locale and tests changing its status and the default language.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
  }

  /**
   * Functional tests for adding, editing and deleting languages.
   */
  function testLanguageConfiguration() {
    global $base_url;

    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'access administration pages'));
    $this->drupalLogin($admin_user);

    // Add predefined language.
    $edit = array(
      'langcode' => 'fr',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));
    $this->assertText('fr', 'Language added successfully.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');

    // Add custom language.
    // Code for the language.
    $langcode = 'xx';
    // The English name for the language.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText($langcode, 'Language code found.');
    $this->assertText($name, 'Name found.');
    $this->assertText($native, 'Native found.');
    $this->assertText($native, 'Test language added.');

    // Check if we can change the default language.
    $path = 'admin/config/regional/language';
    $this->drupalGet($path);
    $this->assertFieldChecked('edit-site-default-en', 'English is the default language.');
    // Change the default language.
    $edit = array(
      'site_default' => $langcode,
    );
    $this->drupalPost(NULL, $edit, t('Save configuration'));
    $this->assertNoFieldChecked('edit-site-default-en', 'Default language updated.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');

    // Check if a valid language prefix is added after changing the default
    // language.
    $this->drupalGet('admin/config/regional/language/edit/en');
    $this->assertFieldByXPath('//input[@name="prefix"]', 'en', 'A valid path prefix has been added to the previous default language.');

    // Ensure we can't delete the default language.
    $this->drupalGet('admin/config/regional/language/delete/' . $langcode);
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText(t('The default language cannot be deleted.'), 'Failed to delete the default language.');

    // Check if we can disable a language.
    $edit = array(
      'enabled[en]' => FALSE,
    );
    $this->drupalPost($path, $edit, t('Save configuration'));
    $this->assertNoFieldChecked('edit-enabled-en', 'Language disabled.');

    // Set disabled language to be the default and ensure it is re-enabled.
    $edit = array(
      'site_default' => 'en',
    );
    $this->drupalPost(NULL, $edit, t('Save configuration'));
    $this->assertFieldChecked('edit-enabled-en', 'Default language re-enabled.');

    // Ensure 'edit' link works.
    $this->clickLink(t('edit'));
    $this->assertTitle(t('Edit language | Drupal'), 'Page title is "Edit language".');
    // Edit a language.
    $name = $this->randomName(16);
    $edit = array(
      'name' => $name,
    );
    $this->drupalPost('admin/config/regional/language/edit/' . $langcode, $edit, t('Save language'));
    $this->assertRaw($name, 'The language has been updated.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');

    // Ensure 'delete' link works.
    $this->drupalGet('admin/config/regional/language');
    $this->clickLink(t('delete'));
    $this->assertText(t('Are you sure you want to delete the language'), '"delete" link is correct.');
    // Delete an enabled language.
    $this->drupalGet('admin/config/regional/language/delete/' . $langcode);
    // First test the 'cancel' link.
    $this->clickLink(t('Cancel'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertRaw($name, 'The language was not deleted.');
    // Delete the language for real. This a confirm form, we do not need any
    // fields changed.
    $this->drupalPost('admin/config/regional/language/delete/' . $langcode, array(), t('Delete'));
    // We need raw here because %locale will add HTML.
    $this->assertRaw(t('The language %locale has been removed.', array('%locale' => $name)), 'The test language has been removed.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');
    // Verify that language is no longer found.
    $this->drupalGet('admin/config/regional/language/delete/' . $langcode);
    $this->assertResponse(404, 'Language no longer found.');
    // Make sure the "language_count" variable has been updated correctly.
    drupal_static_reset('language_list');
    $enabled = language_list('enabled');
    $this->assertEqual(variable_get('language_count', 1), count($enabled[1]), 'Language count is correct.');
    // Delete a disabled language.
    // Disable an enabled language.
    $edit = array(
      'enabled[fr]' => FALSE,
    );
    $this->drupalPost($path, $edit, t('Save configuration'));
    $this->assertNoFieldChecked('edit-enabled-fr', 'French language disabled.');
    // Get the count of enabled languages.
    drupal_static_reset('language_list');
    $enabled = language_list('enabled');
    // Delete the disabled language.
    $this->drupalPost('admin/config/regional/language/delete/fr', array(), t('Delete'));
    // We need raw here because %locale will add HTML.
    $this->assertRaw(t('The language %locale has been removed.', array('%locale' => 'French')), 'Disabled language has been removed.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');
    // Verify that language is no longer found.
    $this->drupalGet('admin/config/regional/language/delete/fr');
    $this->assertResponse(404, 'Language no longer found.');
    // Make sure the "language_count" variable has not changed.
    $this->assertEqual(variable_get('language_count', 1), count($enabled[1]), 'Language count is correct.');


    // Ensure we can't delete the English language.
    $this->drupalGet('admin/config/regional/language/delete/en');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText(t('The English language cannot be deleted.'), 'Failed to delete English language.');
  }

}

/**
 * Tests localization of the JavaScript libraries.
 *
 * Currently, only the jQuery datepicker is localized using Drupal translations.
 */
class LocaleLibraryInfoAlterTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Javascript library localisation',
      'description' => 'Tests the localisation of JavaScript libraries.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale', 'locale_test');
  }

  /**
   * Verifies that the datepicker can be localized.
   *
   * @see locale_library_info_alter()
   */
  public function testLibraryInfoAlter() {
    drupal_add_library('system', 'ui.datepicker');
    $scripts = drupal_get_js();
    $this->assertTrue(strpos($scripts, 'locale.datepicker.js'), 'locale.datepicker.js added to scripts.');
  }
}

/**
 * Functional tests for JavaScript parsing for translatable strings.
 */
class LocaleJavascriptTranslationTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Javascript translation',
      'description' => 'Tests parsing js files for translatable strings',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale', 'locale_test');
  }

  function testFileParsing() {

    $filename = drupal_get_path('module', 'locale_test') . '/locale_test.js';

    // Parse the file to look for source strings.
    _locale_parse_js_file($filename);

    // Get all of the source strings that were found.
    $source_strings = db_select('locales_source', 's')
      ->fields('s', array('source', 'context'))
      ->condition('s.location', $filename)
      ->execute()
      ->fetchAllKeyed();

    // List of all strings that should be in the file.
    $test_strings = array(
      "Standard Call t" => '',
      "Whitespace Call t" => '',

      "Single Quote t" => '',
      "Single Quote \\'Escaped\\' t" => '',
      "Single Quote Concat strings t" => '',

      "Double Quote t" => '',
      "Double Quote \\\"Escaped\\\" t" => '',
      "Double Quote Concat strings t" => '',

      "Context !key Args t" => "Context string",

      "Context Unquoted t" => "Context string unquoted",
      "Context Single Quoted t" => "Context string single quoted",
      "Context Double Quoted t" => "Context string double quoted",

      "Standard Call plural" => '',
      "Standard Call @count plural" => '',
      "Whitespace Call plural" => '',
      "Whitespace Call @count plural" => '',

      "Single Quote plural" => '',
      "Single Quote @count plural" => '',
      "Single Quote \\'Escaped\\' plural" => '',
      "Single Quote \\'Escaped\\' @count plural" => '',

      "Double Quote plural" => '',
      "Double Quote @count plural" => '',
      "Double Quote \\\"Escaped\\\" plural" => '',
      "Double Quote \\\"Escaped\\\" @count plural" => '',

      "Context !key Args plural" => "Context string",
      "Context !key Args @count plural" => "Context string",

      "Context Unquoted plural" => "Context string unquoted",
      "Context Unquoted @count plural" => "Context string unquoted",
      "Context Single Quoted plural" => "Context string single quoted",
      "Context Single Quoted @count plural" => "Context string single quoted",
      "Context Double Quoted plural" => "Context string double quoted",
      "Context Double Quoted @count plural" => "Context string double quoted",
    );

    // Assert that all strings were found properly.
    foreach ($test_strings as $str => $context) {
      $args = array('%source' => $str, '%context' => $context);

      // Make sure that the string was found in the file.
      $this->assertTrue(isset($source_strings[$str]), format_string('Found source string: %source', $args));

      // Make sure that the proper context was matched.
      $this->assertTrue(isset($source_strings[$str]) && $source_strings[$str] === $context, strlen($context) > 0 ? format_string('Context for %source is %context', $args) : format_string('Context for %source is blank', $args));
    }

    $this->assertEqual(count($source_strings), count($test_strings), 'Found correct number of source strings.');
  }

  /**
   * Test handling of null values in JS parsing for PHP8.0+ deprecations.
   */
  function testNullValuesLocalesSource() {
    db_insert('locales_source')
      ->fields(array(
        'location' => NULL,
        'source' => 'Standard Call t',
        'context' => '',
        'textgroup' => 'default',
      ))
      ->execute();

    $filename = drupal_get_path('module', 'locale_test') . '/locale_test.js';

    // Parse the file to look for source strings.
    _locale_parse_js_file($filename);

    $num_records = db_select('locales_source')->fields(NULL, array('lid'))->countQuery()->execute()->fetchField();
    $this->assertEqual($num_records, 32, 'Correct number of strings parsed from JS file');
  }
}
/**
 * Functional test for string translation and validation.
 */
class LocaleTranslationFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'String translate, search and validate',
      'description' => 'Adds a new locale and translates its name. Checks the validation of translation strings and search results.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
  }

  /**
   * Adds a language and tests string translation by users with the appropriate permissions.
   */
  function testStringTranslation() {
    global $base_url;

    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'access administration pages'));
    // User to translate and delete string.
    $translate_user = $this->drupalCreateUser(array('translate interface', 'access administration pages'));
    // Code for the language.
    $langcode = 'xx';
    // The English name for the language. This will be translated.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;
    // This is the language indicator on the translation search screen for
    // untranslated strings. Copied straight from locale.inc.
    $language_indicator = "<em class=\"locale-untranslated\">$langcode</em> ";
    // This will be the translation of $name.
    $translation = $this->randomName(16);

    // Add custom language.
    $this->drupalLogin($admin_user);
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));
    // Add string.
    t($name, array(), array('langcode' => $langcode));
    // Reset locale cache.
    locale_reset();
    $this->assertText($langcode, 'Language code found.');
    $this->assertText($name, 'Name found.');
    $this->assertText($native, 'Native found.');
    // No t() here, we do not want to add this string to the database and it's
    // surely not translated yet.
    $this->assertText($native, 'Test language added.');
    $this->drupalLogout();

    // Search for the name and translate it.
    $this->drupalLogin($translate_user);
    $search = array(
      'string' => $name,
      'language' => 'all',
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    // assertText() seems to remove the input field where $name always could be
    // found, so this is not a false assert. See how assertNoText succeeds
    // later.
    $this->assertText($name, 'Search found the name.');
    $this->assertRaw($language_indicator, 'Name is untranslated.');
    // Assume this is the only result, given the random name.
    $this->clickLink(t('edit'));
    // We save the lid from the path.
    $matches = array();
    preg_match('!admin/config/regional/translate/edit/(\d+)!', $this->getUrl(), $matches);
    $lid = $matches[1];
    // No t() here, it's surely not translated yet.
    $this->assertText($name, 'name found on edit screen.');
    $edit = array(
      "translations[$langcode]" => $translation,
    );
    $this->drupalPost(NULL, $edit, t('Save translations'));
    $this->assertText(t('The string has been saved.'), 'The string has been saved.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/translate/translate', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertTrue($name != $translation && t($name, array(), array('langcode' => $langcode)) == $translation, 't() works.');
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    // The indicator should not be here.
    $this->assertNoRaw($language_indicator, 'String is translated.');

    // Verify that a translation set which has an empty target string can be
    // updated without any database error.
    db_update('locales_target')
      ->fields(array('translation' => ''))
      ->condition('language', $langcode, '=')
      ->condition('lid', $lid, '=')
      ->execute();
    $this->drupalPost('admin/config/regional/translate/edit/' . $lid, $edit, t('Save translations'));
    $this->assertText(t('The string has been saved.'), 'The string has been saved.');

    // Try to edit a non-existent string and ensure we're redirected correctly.
    // Assuming we don't have 999,999 strings already.
    $random_lid = 999999;
    $this->drupalGet('admin/config/regional/translate/edit/' . $random_lid);
    $this->assertText(t('String not found'), 'String not found.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/translate/translate', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->drupalLogout();

    // Delete the language.
    $this->drupalLogin($admin_user);
    $path = 'admin/config/regional/language/delete/' . $langcode;
    // This a confirm form, we do not need any fields changed.
    $this->drupalPost($path, array(), t('Delete'));
    // We need raw here because %locale will add HTML.
    $this->assertRaw(t('The language %locale has been removed.', array('%locale' => $name)), 'The test language has been removed.');
    // Reload to remove $name.
    $this->drupalGet($path);
    // Verify that language is no longer found.
    $this->assertResponse(404, 'Language no longer found.');
    $this->drupalLogout();

    // Delete the string.
    $this->drupalLogin($translate_user);
    $search = array(
      'string' => $name,
      'language' => 'all',
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    // Assume this is the only result, given the random name.
    $this->clickLink(t('delete'));
    $this->assertText(t('Are you sure you want to delete the string'), '"delete" link is correct.');
    // Delete the string.
    $path = 'admin/config/regional/translate/delete/' . $lid;
    $this->drupalGet($path);
    // First test the 'cancel' link.
    $this->clickLink(t('Cancel'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/translate/translate', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertRaw($name, 'The string was not deleted.');
    // Delete the name string.
    $this->drupalPost('admin/config/regional/translate/delete/' . $lid, array(), t('Delete'));
    $this->assertText(t('The string has been removed.'), 'The string has been removed message.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/translate/translate', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertNoText($name, 'Search now can not find the name.');
  }

  /*
   * Adds a language and checks that the JavaScript translation files are
   * properly created and rebuilt on deletion.
   */
  function testJavaScriptTranslation() {
    $user = $this->drupalCreateUser(array('translate interface', 'administer languages', 'access administration pages'));
    $this->drupalLogin($user);

    $langcode = 'xx';
    // The English name for the language. This will be translated.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;

    // Add custom language.
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));
    drupal_static_reset('language_list');

    // Build the JavaScript translation file.
    $this->drupalGet('admin/config/regional/translate/translate');

    // Retrieve the id of the first string available in the {locales_source}
    // table and translate it.
    $query = db_select('locales_source', 'l');
    $query->addExpression('min(l.lid)', 'lid');
    $result = $query->condition('l.location', '%.js%', 'LIKE')
      ->condition('l.textgroup', 'default')
      ->execute();
    $url = 'admin/config/regional/translate/edit/' . $result->fetchObject()->lid;
    $edit = array('translations['. $langcode .']' => $this->randomName());
    $this->drupalPost($url, $edit, t('Save translations'));

    // Trigger JavaScript translation parsing and building.
    require_once DRUPAL_ROOT . '/includes/locale.inc';
    _locale_rebuild_js($langcode);

    // Retrieve the JavaScript translation hash code for the custom language to
    // check that the translation file has been properly built.
    $file = db_select('languages', 'l')
      ->fields('l', array('javascript'))
      ->condition('language', $langcode)
      ->execute()
      ->fetchObject();
    $js_file = 'public://' . variable_get('locale_js_directory', 'languages') . '/' . $langcode . '_' . $file->javascript . '.js';
    $this->assertTrue($result = file_exists($js_file), format_string('JavaScript file created: %file', array('%file' => $result ? $js_file : 'not found')));

    // Test JavaScript translation rebuilding.
    file_unmanaged_delete($js_file);
    $this->assertTrue($result = !file_exists($js_file), format_string('JavaScript file deleted: %file', array('%file' => $result ? $js_file : 'found')));
    cache_clear_all();
    _locale_rebuild_js($langcode);
    $this->assertTrue($result = file_exists($js_file), format_string('JavaScript file rebuilt: %file', array('%file' => $result ? $js_file : 'not found')));
  }

  /**
   * Tests the validation of the translation input.
   */
  function testStringValidation() {
    global $base_url;

    // User to add language and strings.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'access administration pages', 'translate interface'));
    $this->drupalLogin($admin_user);
    $langcode = 'xx';
    // The English name for the language. This will be translated.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;
    // This is the language indicator on the translation search screen for
    // untranslated strings. Copied straight from locale.inc.
    $language_indicator = "<em class=\"locale-untranslated\">$langcode</em> ";
    // These will be the invalid translations of $name.
    $key = $this->randomName(16);
    $bad_translations[$key] = "<script>alert('xss');</script>" . $key;
    $key = $this->randomName(16);
    $bad_translations[$key] = '<img SRC="javascript:alert(\'xss\');">' . $key;
    $key = $this->randomName(16);
    $bad_translations[$key] = '<<SCRIPT>alert("xss");//<</SCRIPT>' . $key;
    $key = $this->randomName(16);
    $bad_translations[$key] ="<BODY ONLOAD=alert('xss')>" . $key;

    // Add custom language.
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));
    // Add string.
    t($name, array(), array('langcode' => $langcode));
    // Reset locale cache.
    $search = array(
      'string' => $name,
      'language' => 'all',
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    // Find the edit path.
    $content = $this->drupalGetContent();
    $this->assertTrue(preg_match('@(admin/config/regional/translate/edit/[0-9]+)@', $content, $matches), 'Found the edit path.');
    $path = $matches[0];
    foreach ($bad_translations as $key => $translation) {
      $edit = array(
        "translations[$langcode]" => $translation,
      );
      $this->drupalPost($path, $edit, t('Save translations'));
      // Check for a form error on the textarea.
      $form_class = $this->xpath('//form[@id="locale-translate-edit-form"]//textarea/@class');
      $this->assertNotIdentical(FALSE, strpos($form_class[0], 'error'), 'The string was rejected as unsafe.');
      $this->assertNoText(t('The string has been saved.'), 'The string was not saved.');
    }
  }

  /**
   * Tests translation search form.
   */
  function testStringSearch() {
    global $base_url;

    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'access administration pages'));
    // User to translate and delete string.
    $translate_user = $this->drupalCreateUser(array('translate interface', 'access administration pages'));

    // Code for the language.
    $langcode = 'xx';
    // The English name for the language. This will be translated.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;
    // This is the language indicator on the translation search screen for
    // untranslated strings. Copied straight from locale.inc.
    $language_indicator = "<em class=\"locale-untranslated\">$langcode</em> ";
    // This will be the translation of $name.
    $translation = $this->randomName(16);

    // Add custom language.
    $this->drupalLogin($admin_user);
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));
    // Add string.
    t($name, array(), array('langcode' => $langcode));
    // Reset locale cache.
    locale_reset();
    $this->drupalLogout();

    // Search for the name.
    $this->drupalLogin($translate_user);
    $search = array(
      'string' => $name,
      'language' => 'all',
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    // assertText() seems to remove the input field where $name always could be
    // found, so this is not a false assert. See how assertNoText succeeds
    // later.
    $this->assertText($name, 'Search found the string.');

    // Ensure untranslated string doesn't appear if searching on 'only
    // translated strings'.
    $search = array(
      'string' => $name,
      'language' => 'all',
      'translation' => 'translated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertText(t('No strings available.'), "Search didn't find the string.");

    // Ensure untranslated string appears if searching on 'only untranslated
    // strings' in "all" (hasn't been translated to any language).
    $search = array(
      'string' => $name,
      'language' => 'all',
      'translation' => 'untranslated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertNoText(t('No strings available.'), 'Search found the string.');

    // Ensure untranslated string appears if searching on 'only untranslated
    // strings' in the custom language (hasn't been translated to that specific language).
    $search = array(
      'string' => $name,
      'language' => $langcode,
      'translation' => 'untranslated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertNoText(t('No strings available.'), 'Search found the string.');

    // Add translation.
    // Assume this is the only result, given the random name.
    $this->clickLink(t('edit'));
    // We save the lid from the path.
    $matches = array();
    preg_match('!admin/config/regional/translate/edit/(\d)+!', $this->getUrl(), $matches);
    $lid = $matches[1];
    $edit = array(
      "translations[$langcode]" => $translation,
    );
    $this->drupalPost(NULL, $edit, t('Save translations'));

    // Ensure translated string does appear if searching on 'only
    // translated strings'.
    $search = array(
      'string' => $translation,
      'language' => 'all',
      'translation' => 'translated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertNoText(t('No strings available.'), 'Search found the translation.');

    // Ensure translated source string doesn't appear if searching on 'only
    // untranslated strings'.
    $search = array(
      'string' => $name,
      'language' => 'all',
      'translation' => 'untranslated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertText(t('No strings available.'), "Search didn't find the source string.");

    // Ensure translated string doesn't appear if searching on 'only
    // untranslated strings'.
    $search = array(
      'string' => $translation,
      'language' => 'all',
      'translation' => 'untranslated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertText(t('No strings available.'), "Search didn't find the translation.");

    // Ensure translated string does appear if searching on the custom language.
    $search = array(
      'string' => $translation,
      'language' => $langcode,
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertNoText(t('No strings available.'), 'Search found the translation.');

    // Ensure translated string doesn't appear if searching on English.
    $search = array(
      'string' => $translation,
      'language' => 'en',
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertText(t('No strings available.'), "Search didn't find the translation.");

    // Search for a string that isn't in the system.
    $unavailable_string = $this->randomName(16);
    $search = array(
      'string' => $unavailable_string,
      'language' => 'all',
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertText(t('No strings available.'), "Search didn't find the invalid string.");
  }
}

/**
 * Tests plural index computation functionality.
 */
class LocalePluralFormatTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Plural formula evaluation',
      'description' => 'Tests plural formula evaluation for various languages.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale', 'locale_test');

    $admin_user = $this->drupalCreateUser(array('administer languages', 'translate interface', 'access administration pages'));
    $this->drupalLogin($admin_user);

    // Import some .po files with formulas to set up the environment.
    // These will also add the languages to the system and enable them.
    $this->importPoFile($this->getPoFileWithSimplePlural(), array(
      'langcode' => 'fr',
    ));
    $this->importPoFile($this->getPoFileWithComplexPlural(), array(
      'langcode' => 'hr',
    ));
  }

  /**
   * Tests locale_get_plural() functionality.
   */
  function testGetPluralFormat() {
    $this->drupalGet('locale_test_plural_format_page');
    $tests = _locale_test_plural_format_tests();
    $result = array();
    foreach ($tests as $test) {
      $this->assertPluralFormat($test['count'], $test['language'], $test['expected-result']);
    }
  }

  /**
   * Helper assert to test locale_get_plural page.
   *
   * @param $count
   *  Number for testing.
   * @param $lang
   *  Language for testing
   * @param $expected_result
   *   Expected result.
   * @param $message
   */
  function assertPluralFormat($count, $lang, $expected_result) {
    $message_param =  array(
      '@lang' => $lang,
      '@count' => $count,
      '@expected_result' => $expected_result,
    );
    $message = t("Computed plural index for '@lang' with count @count is @expected_result.", $message_param);

    $message_param = array(
      '@lang' => $lang,
      '@expected_result' => $expected_result,
    );
    $this->assertText(format_string('Language: @lang, locale_get_plural: @expected_result.', $message_param, $message));
  }

  /**
   * Imports a standalone .po file in a given language.
   *
   * @param $contents
   *   Contents of the .po file to import.
   * @param $options
   *   Additional options to pass to the translation import form.
   */
  function importPoFile($contents, array $options = array()) {
    $name = drupal_tempnam('temporary://', "po_") . '.po';
    file_put_contents($name, $contents);
    $options['files[file]'] = $name;
    $this->drupalPost('admin/config/regional/translate/import', $options, t('Import'));
    drupal_unlink($name);
  }

  /**
   * Returns a .po file with a simple plural formula.
   */
  function getPoFileWithSimplePlural() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\\n"

msgid "One sheep"
msgid_plural "@count sheep"
msgstr[0] "un mouton"
msgstr[1] "@count moutons"

msgid "Monday"
msgstr "lundi"
EOF;
  }

  /**
   * Returns a .po file with a complex plural formula.
   */
  function getPoFileWithComplexPlural() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\\n"

msgid "1 hour"
msgid_plural "@count hours"
msgstr[0] "@count sat"
msgstr[1] "@count sata"
msgstr[2] "@count sati"

msgid "Monday"
msgstr "Ponedjeljak"
EOF;
  }
}

/**
 * Functional tests for the import of translation files.
 */
class LocaleImportFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Translation import',
      'description' => 'Tests the import of locale files.',
      'group' => 'Locale',
    );
  }

  /**
   * A user able to create languages and import translations.
   */
  protected $admin_user = NULL;

  function setUp() {
    parent::setUp('locale', 'locale_test');

    $this->admin_user = $this->drupalCreateUser(array('administer languages', 'translate interface', 'access administration pages'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Test import of standalone .po files.
   */
  function testStandalonePoFile() {
    // Try importing a .po file.
    $this->importPoFile($this->getPoFile(), array(
      'langcode' => 'fr',
    ));

    // The import should automatically create the corresponding language.
    $this->assertRaw(t('The language %language has been created.', array('%language' => 'French')), 'The language has been automatically created.');

    // The import should have created 7 strings.
    $this->assertRaw(t('The translation was successfully imported. There are %number newly created translated strings, %update strings were updated and %delete strings were removed.', array('%number' => 9, '%update' => 0, '%delete' => 0)), 'The translation file was successfully imported.');

    // This import should have saved plural forms to have 2 variants.
    $this->assert(db_query("SELECT plurals FROM {languages} WHERE language = 'fr'")->fetchField() == 2, 'Plural number initialized.');

    // Ensure we were redirected correctly.
    $this->assertEqual($this->getUrl(), url('admin/config/regional/translate', array('absolute' => TRUE)), 'Correct page redirection.');


    // Try importing a .po file with invalid tags in the default text group.
    $this->importPoFile($this->getBadPoFile(), array(
      'langcode' => 'fr',
    ));

    // The import should have created 1 string and rejected 2.
    $this->assertRaw(t('The translation was successfully imported. There are %number newly created translated strings, %update strings were updated and %delete strings were removed.', array('%number' => 1, '%update' => 0, '%delete' => 0)), 'The translation file was successfully imported.');
    $skip_message = format_plural(2, 'One translation string was skipped because it contains disallowed HTML.', '@count translation strings were skipped because they contain disallowed HTML.');
    $this->assertRaw($skip_message, 'Unsafe strings were skipped.');


    // Try importing a .po file with invalid tags in a non default text group.
    $this->importPoFile($this->getBadPoFile(), array(
      'langcode' => 'fr',
      'group' => 'custom',
    ));

    // The import should have created 3 strings.
    $this->assertRaw(t('The translation was successfully imported. There are %number newly created translated strings, %update strings were updated and %delete strings were removed.', array('%number' => 3, '%update' => 0, '%delete' => 0)), 'The translation file was successfully imported.');


    // Try importing a .po file which doesn't exist.
    $name = $this->randomName(16);
    $this->drupalPost('admin/config/regional/translate/import', array(
      'langcode' => 'fr',
      'files[file]' => $name,
      'group' => 'custom',
    ), t('Import'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/translate/import', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText(t('File to import not found.'), 'File to import not found message.');


    // Try importing a .po file with overriding strings, and ensure existing
    // strings are kept.
    $this->importPoFile($this->getOverwritePoFile(), array(
      'langcode' => 'fr',
      'mode' => 1, // Existing strings are kept, only new strings are added.
    ));

    // The import should have created 1 string.
    $this->assertRaw(t('The translation was successfully imported. There are %number newly created translated strings, %update strings were updated and %delete strings were removed.', array('%number' => 1, '%update' => 0, '%delete' => 0)), 'The translation file was successfully imported.');
    // Ensure string wasn't overwritten.
    $search = array(
      'string' => 'Montag',
      'language' => 'fr',
      'translation' => 'translated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertText(t('No strings available.'), 'String not overwritten by imported string.');

    // This import should not have changed number of plural forms.
    $this->assert(db_query("SELECT plurals FROM {languages} WHERE language = 'fr'")->fetchField() == 2, 'Plural numbers untouched.');

    $this->importPoFile($this->getPoFileWithBrokenPlural(), array(
      'langcode' => 'fr',
      'mode' => 1, // Existing strings are kept, only new strings are added.
    ));

    // Attempt to import broken .po file as well to prove that this
    // will not overwrite the proper plural formula imported above.
    $this->assert(db_query("SELECT plurals FROM {languages} WHERE language = 'fr'")->fetchField() == 2, 'Broken plurals: plural numbers untouched.');

    $this->importPoFile($this->getPoFileWithMissingPlural(), array(
      'langcode' => 'fr',
      'mode' => 1, // Existing strings are kept, only new strings are added.
    ));

    // Attempt to import .po file which has no plurals and prove that this
    // will not overwrite the proper plural formula imported above.
    $this->assert(db_query("SELECT plurals FROM {languages} WHERE language = 'fr'")->fetchField() == 2, 'No plurals: plural numbers untouched.');


    // Try importing a .po file with overriding strings, and ensure existing
    // strings are overwritten.
    $this->importPoFile($this->getOverwritePoFile(), array(
      'langcode' => 'fr',
      'mode' => 0, // Strings in the uploaded file replace existing ones, new ones are added.
    ));

    // The import should have updated 2 strings.
    $this->assertRaw(t('The translation was successfully imported. There are %number newly created translated strings, %update strings were updated and %delete strings were removed.', array('%number' => 0, '%update' => 2, '%delete' => 0)), 'The translation file was successfully imported.');
    // Ensure string was overwritten.
    $search = array(
      'string' => 'Montag',
      'language' => 'fr',
      'translation' => 'translated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertNoText(t('No strings available.'), 'String overwritten by imported string.');
    // This import should have changed number of plural forms.
    $this->assert(db_query("SELECT plurals FROM {languages} WHERE language = 'fr'")->fetchField() == 3, 'Plural numbers changed.');
  }

  /**
   * Test automatic import of a module's translation files when a language is
   * enabled.
   */
  function testAutomaticModuleTranslationImportLanguageEnable() {
    // Code for the language - manually set to match the test translation file.
    $langcode = 'xx';
    // The English name for the language.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;

    // Create a custom language.
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));

    // Ensure the translation file was automatically imported when language was
    // added.
    $this->assertText(t('One translation file imported for the enabled modules.'), 'Language file automatically imported.');

    // Ensure strings were successfully imported.
    $search = array(
      'string' => 'lundi',
      'language' => $langcode,
      'translation' => 'translated',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    $this->assertNoText(t('No strings available.'), 'String successfully imported.');
  }

  /**
   * Test msgctxt context support.
   */
  function testLanguageContext() {
    // Try importing a .po file.
    $this->importPoFile($this->getPoFileWithContext(), array(
      'langcode' => 'hr',
    ));

    $this->assertIdentical(t('May', array(), array('langcode' => 'hr', 'context' => 'Long month name')), 'Svibanj', 'Long month name context is working.');
    $this->assertIdentical(t('May', array(), array('langcode' => 'hr')), 'Svi.', 'Default context is working.');
  }

  /**
   * Test empty msgstr at end of .po file see #611786.
   */
  function testEmptyMsgstr() {
    $langcode = 'hu';

    // Try importing a .po file.
    $this->importPoFile($this->getPoFileWithMsgstr(), array(
      'langcode' => $langcode,
    ));

    $this->assertRaw(t('The translation was successfully imported. There are %number newly created translated strings, %update strings were updated and %delete strings were removed.', array('%number' => 1, '%update' => 0, '%delete' => 0)), 'The translation file was successfully imported.');
    $this->assertIdentical(t('Operations', array(), array('langcode' => $langcode)), 'Műveletek', 'String imported and translated.');

    // Try importing a .po file.
    $this->importPoFile($this->getPoFileWithEmptyMsgstr(), array(
      'langcode' => $langcode,
      'mode' => 0,
    ));
    $this->assertRaw(t('The translation was successfully imported. There are %number newly created translated strings, %update strings were updated and %delete strings were removed.', array('%number' => 0, '%update' => 0, '%delete' => 1)), 'The translation file was successfully imported.');
    // This is the language indicator on the translation search screen for
    // untranslated strings. Copied straight from locale.inc.
    $language_indicator = "<em class=\"locale-untranslated\">$langcode</em> ";
    $str = "Operations";
    $search = array(
      'string' => $str,
      'language' => 'all',
      'translation' => 'all',
      'group' => 'all',
    );
    $this->drupalPost('admin/config/regional/translate/translate', $search, t('Filter'));
    // assertText() seems to remove the input field where $str always could be
    // found, so this is not a false assert.
    $this->assertText($str, 'Search found the string.');
    $this->assertRaw($language_indicator, 'String is untranslated again.');
  }

  /**
   * Helper function: import a standalone .po file in a given language.
   *
   * @param $contents
   *   Contents of the .po file to import.
   * @param $options
   *   Additional options to pass to the translation import form.
   */
  function importPoFile($contents, array $options = array()) {
    $name = drupal_tempnam('temporary://', "po_") . '.po';
    file_put_contents($name, $contents);
    $options['files[file]'] = $name;
    $this->drupalPost('admin/config/regional/translate/import', $options, t('Import'));
    drupal_unlink($name);
  }

  /**
   * Helper function that returns a proper .po file.
   */
  function getPoFile() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\\n"

msgid "One sheep"
msgid_plural "@count sheep"
msgstr[0] "un mouton"
msgstr[1] "@count moutons"

msgid "Monday"
msgstr "lundi"

msgid "Tuesday"
msgstr "mardi"

msgid "Wednesday"
msgstr "mercredi"

msgid "Thursday"
msgstr "jeudi"

msgid "Friday"
msgstr "vendredi"

msgid "Saturday"
msgstr "samedi"

msgid "Sunday"
msgstr "dimanche"
EOF;
  }

  /**
   * Helper function that returns a bad .po file.
   */
  function getBadPoFile() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\\n"

msgid "Save configuration"
msgstr "Enregistrer la configuration"

msgid "edit"
msgstr "modifier<img SRC="javascript:alert(\'xss\');">"

msgid "delete"
msgstr "supprimer<script>alert('xss');</script>"

EOF;
  }

  /**
   * Helper function that returns a proper .po file, for testing overwriting
   * existing translations.
   */
  function getOverwritePoFile() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\\n"

msgid "Monday"
msgstr "Montag"

msgid "Day"
msgstr "Jour"
EOF;
  }

  /**
   * Helper function that returns a .po file with context.
   */
  function getPoFileWithContext() {
    // Croatian (code hr) is one of the languages that have a different
    // form for the full name and the abbreviated name for the month May.
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\\n"

msgctxt "Long month name"
msgid "May"
msgstr "Svibanj"

msgid "May"
msgstr "Svi."
EOF;
  }

  /**
   * Helper function that returns a .po file with an empty last item.
   */
  function getPoFileWithEmptyMsgstr() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\\n"

msgid "Operations"
msgstr ""

EOF;
  }
  /**
   * Helper function that returns a .po file with an empty last item.
   */
  function getPoFileWithMsgstr() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\\n"

msgid "Operations"
msgstr "Műveletek"

msgid "Will not appear in Drupal core, so we can ensure the test passes"
msgstr ""

EOF;
  }


  /**
   * Returns a .po file with a missing plural formula.
   */
  function getPoFileWithMissingPlural() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"

msgid "Monday"
msgstr "Ponedjeljak"
EOF;
  }

  /**
   * Returns a .po file with a broken plural formula.
   */
  function getPoFileWithBrokenPlural() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 7\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: broken, will not parse\\n"

msgid "Monday"
msgstr "lundi"
EOF;
  }

}

/**
 * Functional tests for the export of translation files.
 */
class LocaleExportFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Translation export',
      'description' => 'Tests the exportation of locale files.',
      'group' => 'Locale',
    );
  }

  /**
   * A user able to create languages and export translations.
   */
  protected $admin_user = NULL;

  function setUp() {
    parent::setUp('locale', 'locale_test');

    $this->admin_user = $this->drupalCreateUser(array('administer languages', 'translate interface', 'access administration pages'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Test exportation of translations.
   */
  function testExportTranslation() {
    // First import some known translations.
    // This will also automatically enable the 'fr' language.
    $name = drupal_tempnam('temporary://', "po_") . '.po';
    file_put_contents($name, $this->getPoFile());
    $this->drupalPost('admin/config/regional/translate/import', array(
      'langcode' => 'fr',
      'files[file]' => $name,
    ), t('Import'));
    drupal_unlink($name);

    // Get the French translations.
    $this->drupalPost('admin/config/regional/translate/export', array(
      'langcode' => 'fr',
    ), t('Export'));

    // Ensure we have a translation file.
    $this->assertRaw('# French translation of Drupal', 'Exported French translation file.');
    // Ensure our imported translations exist in the file.
    $this->assertRaw('msgstr "lundi"', 'French translations present in exported file.');
  }

  /**
   * Test exportation of translation template file.
   */
  function testExportTranslationTemplateFile() {
    // Get the translation template file.
    // There are two 'Export' buttons on this page, but it somehow works.  It'd
    // be better if we could use the submit button id like documented but that
    // doesn't work.
    $this->drupalPost('admin/config/regional/translate/export', array(), t('Export'));
    // Ensure we have a translation file.
    $this->assertRaw('# LANGUAGE translation of PROJECT', 'Exported translation template file.');
  }

  /**
   * Helper function that returns a proper .po file.
   */
  function getPoFile() {
    return <<< EOF
msgid ""
msgstr ""
"Project-Id-Version: Drupal 6\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\\n"

msgid "Monday"
msgstr "lundi"
EOF;
  }

}

/**
 * Tests for the st() function.
 */
class LocaleInstallTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'String translation using st()',
      'description' => 'Tests that st() works like t().',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');

    // st() lives in install.inc, so ensure that it is loaded for all tests.
    require_once DRUPAL_ROOT . '/includes/install.inc';
  }

  /**
   * Verify that function signatures of t() and st() are equal.
   */
  function testFunctionSignatures() {
    $reflector_t = new ReflectionFunction('t');
    $reflector_st = new ReflectionFunction('st');
    $this->assertEqual($reflector_t->getParameters(), $reflector_st->getParameters(), 'Function signatures of t() and st() are equal.');
  }
}

/**
 * Locale uninstall with English UI functional test.
 */
class LocaleUninstallFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Locale uninstall (EN)',
      'description' => 'Tests the uninstall process using the built-in UI language.',
      'group' => 'Locale',
    );
  }

  /**
   * The default language set for the UI before uninstall.
   */
  protected $language;

  function setUp() {
    parent::setUp('locale');
    $this->language = 'en';
  }

  /**
   * Check if the values of the Locale variables are correct after uninstall.
   */
  function testUninstallProcess() {
    $locale_module = array('locale');

    // Add a new language and optionally set it as default.
    require_once DRUPAL_ROOT . '/includes/locale.inc';
    locale_add_language('fr', 'French', 'Français', LANGUAGE_LTR, '', '', TRUE, $this->language == 'fr');

    // Check the UI language.
    drupal_language_initialize();
    global $language;
    $this->assertEqual($language->language, $this->language, format_string('Current language: %lang', array('%lang' => $language->language)));

    // Enable multilingual workflow option for articles.
    variable_set('language_content_type_article', 1);

    // Change JavaScript translations directory.
    variable_set('locale_js_directory', 'js_translations');

    // Build the JavaScript translation file for French.
    $user = $this->drupalCreateUser(array('translate interface', 'access administration pages'));
    $this->drupalLogin($user);
    $this->drupalGet('admin/config/regional/translate/translate');
    $string = db_query('SELECT min(lid) AS lid FROM {locales_source} WHERE location LIKE :location AND textgroup = :textgroup', array(
      ':location' => '%.js%',
      ':textgroup' => 'default',
    ))->fetchObject();
    $edit = array('translations[fr]' => 'french translation');
    $this->drupalPost('admin/config/regional/translate/edit/' . $string->lid, $edit, t('Save translations'));
    _locale_rebuild_js('fr');
    $file = db_query('SELECT javascript FROM {languages} WHERE language = :language', array(':language' => 'fr'))->fetchObject();
    $js_file = 'public://' . variable_get('locale_js_directory', 'languages') . '/fr_' . $file->javascript . '.js';
    $this->assertTrue($result = file_exists($js_file), format_string('JavaScript file created: %file', array('%file' => $result ? $js_file : 'none')));

    // Disable string caching.
    variable_set('locale_cache_strings', 0);

    // Change language negotiation options.
    drupal_load('module', 'locale');
    variable_set('language_types', drupal_language_types() + array('language_custom' => TRUE));
    variable_set('language_negotiation_' . LANGUAGE_TYPE_INTERFACE, locale_language_negotiation_info());
    variable_set('language_negotiation_' . LANGUAGE_TYPE_CONTENT, locale_language_negotiation_info());
    variable_set('language_negotiation_' . LANGUAGE_TYPE_URL, locale_language_negotiation_info());

    // Change language providers settings.
    variable_set('locale_language_negotiation_url_part', LOCALE_LANGUAGE_NEGOTIATION_URL_PREFIX);
    variable_set('locale_language_negotiation_session_param', TRUE);

    // Uninstall Locale.
    module_disable($locale_module);
    drupal_uninstall_modules($locale_module);

    // Visit the front page.
    $this->drupalGet('');

    // Check the init language logic.
    drupal_language_initialize();
    $this->assertEqual($language->language, 'en', format_string('Language after uninstall: %lang', array('%lang' => $language->language)));

    // Check JavaScript files deletion.
    $this->assertTrue($result = !file_exists($js_file), format_string('JavaScript file deleted: %file', array('%file' => $result ? $js_file : 'found')));

    // Check language count.
    $language_count = variable_get('language_count', 1);
    $this->assertEqual($language_count, 1, format_string('Language count: %count', array('%count' => $language_count)));

    // Check language negotiation.
    require_once DRUPAL_ROOT . '/includes/language.inc';
    $this->assertTrue(count(language_types()) == count(drupal_language_types()), 'Language types reset');
    $language_negotiation = language_negotiation_get(LANGUAGE_TYPE_INTERFACE) == LANGUAGE_NEGOTIATION_DEFAULT;
    $this->assertTrue($language_negotiation, format_string('Interface language negotiation: %setting', array('%setting' => $language_negotiation ? 'none' : 'set')));
    $language_negotiation = language_negotiation_get(LANGUAGE_TYPE_CONTENT) == LANGUAGE_NEGOTIATION_DEFAULT;
    $this->assertTrue($language_negotiation, format_string('Content language negotiation: %setting', array('%setting' => $language_negotiation ? 'none' : 'set')));
    $language_negotiation = language_negotiation_get(LANGUAGE_TYPE_URL) == LANGUAGE_NEGOTIATION_DEFAULT;
    $this->assertTrue($language_negotiation, format_string('URL language negotiation: %setting', array('%setting' => $language_negotiation ? 'none' : 'set')));

    // Check language providers settings.
    $this->assertFalse(variable_get('locale_language_negotiation_url_part', FALSE), 'URL language provider indicator settings cleared.');
    $this->assertFalse(variable_get('locale_language_negotiation_session_param', FALSE), 'Visit language provider settings cleared.');

    // Check JavaScript parsed.
    $javascript_parsed_count = count(variable_get('javascript_parsed', array()));
    $this->assertEqual($javascript_parsed_count, 0, format_string('JavaScript parsed count: %count', array('%count' => $javascript_parsed_count)));

    // Check multilingual workflow option for articles.
    $multilingual = variable_get('language_content_type_article', 0);
    $this->assertEqual($multilingual, 0, format_string('Multilingual workflow option: %status', array('%status' => $multilingual ? 'enabled': 'disabled')));

    // Check JavaScript translations directory.
    $locale_js_directory = variable_get('locale_js_directory', 'languages');
    $this->assertEqual($locale_js_directory, 'languages', format_string('JavaScript translations directory: %dir', array('%dir' => $locale_js_directory)));

    // Check string caching.
    $locale_cache_strings = variable_get('locale_cache_strings', 1);
    $this->assertEqual($locale_cache_strings, 1, format_string('String caching: %status', array('%status' => $locale_cache_strings ? 'enabled': 'disabled')));
  }
}

/**
 * Locale uninstall with French UI functional test.
 *
 * Because this class extends LocaleUninstallFunctionalTest, it doesn't require a new
 * test of its own. Rather, it switches the default UI language in setUp and then
 * runs the testUninstallProcess (which it inherits from LocaleUninstallFunctionalTest)
 * to test with this new language.
 */
class LocaleUninstallFrenchFunctionalTest extends LocaleUninstallFunctionalTest {
  public static function getInfo() {
    return array(
      'name' => 'Locale uninstall (FR)',
      'description' => 'Tests the uninstall process using French as interface language.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp();
    $this->language = 'fr';
  }
}

/**
 * Functional tests for the language switching feature.
 */
class LocaleLanguageSwitchingFunctionalTest extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Language switching',
      'description' => 'Tests for the language switching feature.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');

    // Create and login user.
    $admin_user = $this->drupalCreateUser(array('administer blocks', 'administer languages', 'translate interface', 'access administration pages'));
    $this->drupalLogin($admin_user);
  }

  /**
   * Functional tests for the language switcher block.
   */
  function testLanguageBlock() {
    // Enable the language switching block.
    $language_type = LANGUAGE_TYPE_INTERFACE;
    $edit = array(
      "blocks[locale_{$language_type}][region]" => 'sidebar_first',
    );
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));

    // Add language.
    $edit = array(
      'langcode' => 'fr',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Enable URL language detection and selection.
    $edit = array('language[enabled][locale-url]' => '1');
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Assert that the language switching block is displayed on the frontpage.
    $this->drupalGet('');
    $this->assertText(t('Languages'), 'Language switcher block found.');

    // Assert that only the current language is marked as active.
    list($language_switcher) = $this->xpath('//div[@id=:id]/div[@class="content"]', array(':id' => 'block-locale-' . $language_type));
    $links = array(
      'active' => array(),
      'inactive' => array(),
    );
    $anchors = array(
      'active' => array(),
      'inactive' => array(),
    );
    foreach ($language_switcher->ul->li as $link) {
      $classes = explode(" ", (string) $link['class']);
      list($language) = array_intersect($classes, array('en', 'fr'));
      if (in_array('active', $classes)) {
        $links['active'][] = $language;
      }
      else {
        $links['inactive'][] = $language;
      }
      $anchor_classes = explode(" ", (string) $link->a['class']);
      if (in_array('active', $anchor_classes)) {
        $anchors['active'][] = $language;
      }
      else {
        $anchors['inactive'][] = $language;
      }
    }
    $this->assertIdentical($links, array('active' => array('en'), 'inactive' => array('fr')), 'Only the current language list item is marked as active on the language switcher block.');
    $this->assertIdentical($anchors, array('active' => array('en'), 'inactive' => array('fr')), 'Only the current language anchor is marked as active on the language switcher block.');
  }

  /**
   * Tests that languages can be added as disabled.
   */
  function testNewDisabledLanguage() {
    // Add new language which will be disabled.
    locale_add_language('de', 'German', 'Deutsch', LANGUAGE_LTR, '', '', FALSE, FALSE);
  }
}

/**
 * Test browser language detection.
 */
class LocaleBrowserDetectionTest extends DrupalUnitTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Browser language detection',
      'description' => 'Tests for the browser language detection.',
      'group' => 'Locale',
    );
  }

  /**
   * Unit tests for the locale_language_from_browser() function.
   */
  function testLanguageFromBrowser() {
    // Load the required functions.
    require_once DRUPAL_ROOT . '/includes/locale.inc';

    $languages = array(
      // In our test case, 'en' has priority over 'en-US'.
      'en' => (object) array(
        'language' => 'en',
      ),
      'en-US' => (object) array(
        'language' => 'en-US',
      ),
      // But 'fr-CA' has priority over 'fr'.
      'fr-CA' => (object) array(
        'language' => 'fr-CA',
      ),
      'fr' => (object) array(
        'language' => 'fr',
      ),
      // 'es-MX' is alone.
      'es-MX' => (object) array(
        'language' => 'es-MX',
      ),
      // 'pt' is alone.
      'pt' => (object) array(
        'language' => 'pt',
      ),
      // Language codes with more then one dash are actually valid.
      // eh-oh-laa-laa is the official language code of the Teletubbies.
      'eh-oh-laa-laa' => (object) array(
        'language' => 'eh-oh-laa-laa',
      ),
    );

    $test_cases = array(
      // Equal qvalue for each language, choose the site preferred one.
      'en,en-US,fr-CA,fr,es-MX' => 'en',
      'en-US,en,fr-CA,fr,es-MX' => 'en',
      'fr,en' => 'en',
      'en,fr' => 'en',
      'en-US,fr' => 'en',
      'fr,en-US' => 'en',
      'fr,fr-CA' => 'fr-CA',
      'fr-CA,fr' => 'fr-CA',
      'fr' => 'fr-CA',
      'fr;q=1' => 'fr-CA',
      'fr,es-MX' => 'fr-CA',
      'fr,es' => 'fr-CA',
      'es,fr' => 'fr-CA',
      'es-MX,de' => 'es-MX',
      'de,es-MX' => 'es-MX',

      // Different cases and whitespace.
      'en' => 'en',
      'En' => 'en',
      'EN' => 'en',
      ' en' => 'en',
      'en ' => 'en',
      'en, fr' => 'en',

      // A less specific language from the browser matches a more specific one
      // from the website, and the other way around for compatibility with
      // some versions of Internet Explorer.
      'es' => 'es-MX',
      'es-MX' => 'es-MX',
      'pt' => 'pt',
      'pt-PT' => 'pt',
      'pt-PT;q=0.5,pt-BR;q=1,en;q=0.7' => 'en',
      'pt-PT;q=1,pt-BR;q=0.5,en;q=0.7' => 'en',
      'pt-PT;q=0.4,pt-BR;q=0.1,en;q=0.7' => 'en',
      'pt-PT;q=0.1,pt-BR;q=0.4,en;q=0.7' => 'en',

      // Language code with several dashes are valid. The less specific language
      // from the browser matches the more specific one from the website.
      'eh-oh-laa-laa' => 'eh-oh-laa-laa',
      'eh-oh-laa' => 'eh-oh-laa-laa',
      'eh-oh' => 'eh-oh-laa-laa',
      'eh' => 'eh-oh-laa-laa',

      // Different qvalues.
      'fr,en;q=0.5' => 'fr-CA',
      'fr,en;q=0.5,fr-CA;q=0.25' => 'fr',

      // Silly wildcards are also valid.
      '*,fr-CA;q=0.5' => 'en',
      '*,en;q=0.25' => 'fr-CA',
      'en,en-US;q=0.5,fr;q=0.25' => 'en',
      'en-US,en;q=0.5,fr;q=0.25' => 'en-US',

      // Unresolvable cases.
      '' => FALSE,
      'de,pl' => FALSE,
      'iecRswK4eh' => FALSE,
      $this->randomName(10) => FALSE,
    );

    foreach ($test_cases as $accept_language => $expected_result) {
      $_SERVER['HTTP_ACCEPT_LANGUAGE'] = $accept_language;
      $result = locale_language_from_browser($languages);
      $this->assertIdentical($result, $expected_result, format_string("Language selection '@accept-language' selects '@result', result = '@actual'", array('@accept-language' => $accept_language, '@result' => $expected_result, '@actual' => isset($result) ? $result : 'none')));
    }
  }
}

/**
 * Functional tests for a user's ability to change their default language.
 */
class LocaleUserLanguageFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'User language settings',
      'description' => "Tests user's ability to change their default language.",
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
  }

  /**
   * Test if user can change their default language.
   */
  function testUserLanguageConfiguration() {
    global $base_url;

    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'access administration pages'));
    // User to change their default language.
    $web_user = $this->drupalCreateUser();

    // Add custom language.
    $this->drupalLogin($admin_user);
    // Code for the language.
    $langcode = 'xx';
    // The English name for the language.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = 'xx';
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));

    // Add custom language and disable it.
    // Code for the language.
    $langcode_disabled = 'xx-yy';
    // The English name for the language. This will be translated.
    $name_disabled = $this->randomName(16);
    // The native name for the language.
    $native_disabled = $this->randomName(16);
    // The domain prefix.
    $prefix_disabled = $langcode_disabled;
    $edit = array(
      'langcode' => $langcode_disabled,
      'name' => $name_disabled,
      'native' => $native_disabled,
      'prefix' => $prefix_disabled,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));
    // Disable the language.
    $edit = array(
      'enabled[' . $langcode_disabled . ']' => FALSE,
    );
    $this->drupalPost('admin/config/regional/language', $edit, t('Save configuration'));
    $this->drupalLogout();

    // Login as normal user and edit account settings.
    $this->drupalLogin($web_user);
    $path = 'user/' . $web_user->uid . '/edit';
    $this->drupalGet($path);
    // Ensure language settings fieldset is available.
    $this->assertText(t('Language settings'), 'Language settings available.');
    // Ensure custom language is present.
    $this->assertText($name, 'Language present on form.');
    // Ensure disabled language isn't present.
    $this->assertNoText($name_disabled, 'Disabled language not present on form.');
    // Switch to our custom language.
    $edit = array(
      'language' => $langcode,
    );
    $this->drupalPost($path, $edit, t('Save'));
    // Ensure form was submitted successfully.
    $this->assertText(t('The changes have been saved.'), 'Changes were saved.');
    // Check if language was changed.
    $elements = $this->xpath('//input[@id=:id]', array(':id' => 'edit-language-' . $langcode));
    $this->assertTrue(isset($elements[0]) && !empty($elements[0]['checked']), 'Default language successfully updated.');

    $this->drupalLogout();
  }
}

/**
 * Functional test for language handling during user creation.
 */
class LocaleUserCreationTest extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'User creation',
      'description' => 'Tests whether proper language is stored for new users and access to language selector.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
    variable_set('user_register', USER_REGISTER_VISITORS);
  }

  /**
   * Functional test for language handling during user creation.
   */
  function testLocalUserCreation() {
    // User to add and remove language and create new users.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'access administration pages', 'administer users'));
    $this->drupalLogin($admin_user);

    // Add predefined language.
    $langcode = 'fr';
    $edit = array(
      'langcode' => 'fr',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));
    $this->assertText($langcode, 'Language added successfully.');
    $this->assertEqual($this->getUrl(), url('admin/config/regional/language', array('absolute' => TRUE)), 'Correct page redirection.');

    // Set language negotiation.
    $edit = array(
      'language[enabled][locale-url]' => TRUE,
    );
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));
    $this->assertText(t('Language negotiation configuration saved.'), 'Set language negotiation.');

    // Check if the language selector is available on admin/people/create and
    // set to the currently active language.
    $this->drupalGet($langcode . '/admin/people/create');
    $this->assertFieldChecked("edit-language-$langcode", 'Global language set in the language selector.');

    // Create a user with the admin/people/create form and check if the correct
    // language is set.
    $username = $this->randomName(10);
    $edit = array(
      'name' => $username,
      'mail' => $this->randomName(4) . '@example.com',
      'pass[pass1]' => $username,
      'pass[pass2]' => $username,
    );

    $this->drupalPost($langcode . '/admin/people/create', $edit, t('Create new account'));

    $user = user_load_by_name($username);
    $this->assertEqual($user->language, $langcode, 'New user has correct language set.');

    // Register a new user and check if the language selector is hidden.
    $this->drupalLogout();

    $this->drupalGet($langcode . '/user/register');
    $this->assertNoFieldByName('language[fr]', 'Language selector is not accessible.');

    $username = $this->randomName(10);
    $edit = array(
      'name' => $username,
      'mail' => $this->randomName(4) . '@example.com',
    );

    $this->drupalPost($langcode . '/user/register', $edit, t('Create new account'));

    $user = user_load_by_name($username);
    $this->assertEqual($user->language, $langcode, 'New user has correct language set.');

    // Test if the admin can use the language selector and if the
    // correct language is was saved.
    $user_edit = $langcode . '/user/' . $user->uid . '/edit';

    $this->drupalLogin($admin_user);
    $this->drupalGet($user_edit);
    $this->assertFieldChecked("edit-language-$langcode", 'Language selector is accessible and correct language is selected.');

    // Set pass_raw so we can login the new user.
    $user->pass_raw = $this->randomName(10);
    $edit = array(
      'pass[pass1]' => $user->pass_raw,
      'pass[pass2]' => $user->pass_raw,
    );

    $this->drupalPost($user_edit, $edit, t('Save'));

    $this->drupalLogin($user);
    $this->drupalGet($user_edit);
    $this->assertFieldChecked("edit-language-$langcode", 'Language selector is accessible and correct language is selected.');
  }
}

/**
 * Functional tests for configuring a different path alias per language.
 */
class LocalePathFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Path language settings',
      'description' => 'Checks you can configure a language for individual URL aliases.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale', 'path');
  }

  /**
   * Test if a language can be associated with a path alias.
   */
  function testPathLanguageConfiguration() {
    global $base_url;

    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'create page content', 'administer url aliases', 'create url aliases', 'access administration pages'));

    // Add custom language.
    $this->drupalLogin($admin_user);
    // Code for the language.
    $langcode = 'xx';
    // The English name for the language.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));

    // Check that the "xx" front page is not available when path prefixes are
    // not enabled yet.
    $this->drupalPost('admin/config/regional/language/configure', array(), t('Save settings'));
    $this->drupalGet($prefix);
    $this->assertResponse(404, 'The "xx" front page is not available yet.');

    // Enable URL language detection and selection.
    $edit = array('language[enabled][locale-url]' => 1);
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Create a node.
    $node = $this->drupalCreateNode(array('type' => 'page'));

    // Create a path alias in default language (English).
    $path = 'admin/config/search/path/add';
    $english_path = $this->randomName(8);
    $edit = array(
      'source'   => 'node/' . $node->nid,
      'alias'    => $english_path,
      'language' => 'en',
    );
    $this->drupalPost($path, $edit, t('Save'));

    // Create a path alias in new custom language.
    $custom_language_path = $this->randomName(8);
    $edit = array(
      'source'   => 'node/' . $node->nid,
      'alias'    => $custom_language_path,
      'language' => $langcode,
    );
    $this->drupalPost($path, $edit, t('Save'));

    // Confirm English language path alias works.
    $this->drupalGet($english_path);
    $this->assertText($node->title, 'English alias works.');

    // Confirm custom language path alias works.
    $this->drupalGet($prefix . '/' . $custom_language_path);
    $this->assertText($node->title, 'Custom language alias works.');

    // Create a custom path.
    $custom_path = $this->randomName(8);

    // Check priority of language for alias by source path.
    $edit = array(
      'source'   => 'node/' . $node->nid,
      'alias'    => $custom_path,
      'language' => LANGUAGE_NONE,
    );
    path_save($edit);
    $lookup_path = drupal_lookup_path('alias', 'node/' . $node->nid, 'en');
    $this->assertEqual($english_path, $lookup_path, 'English language alias has priority.');
    // Same check for language 'xx'.
    $lookup_path = drupal_lookup_path('alias', 'node/' . $node->nid, $prefix);
    $this->assertEqual($custom_language_path, $lookup_path, 'Custom language alias has priority.');
    path_delete($edit);

    // Create language nodes to check priority of aliases.
    $first_node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $second_node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));

    // Assign a custom path alias to the first node with the English language.
    $edit = array(
      'source'   => 'node/' . $first_node->nid,
      'alias'    => $custom_path,
      'language' => 'en',
    );
    path_save($edit);

    // Assign a custom path alias to second node with LANGUAGE_NONE.
    $edit = array(
      'source'   => 'node/' . $second_node->nid,
      'alias'    => $custom_path,
      'language' => LANGUAGE_NONE,
    );
    path_save($edit);

    // Test that both node titles link to our path alias.
    $this->drupalGet('<front>');
    $custom_path_url = base_path() . (variable_get('clean_url', 0) ? $custom_path : '?q=' . $custom_path);
    $elements = $this->xpath('//a[@href=:href and .=:title]', array(':href' => $custom_path_url, ':title' => $first_node->title));
    $this->assertTrue(!empty($elements), 'First node links to the path alias.');
    $elements = $this->xpath('//a[@href=:href and .=:title]', array(':href' => $custom_path_url, ':title' => $second_node->title));
    $this->assertTrue(!empty($elements), 'Second node links to the path alias.');

    // Confirm that the custom path leads to the first node.
    $this->drupalGet($custom_path);
    $this->assertText($first_node->title, 'Custom alias returns first node.');

    // Confirm that the custom path with prefix leads to the second node.
    $this->drupalGet($prefix . '/' . $custom_path);
    $this->assertText($second_node->title, 'Custom alias with prefix returns second node.');
  }
}

/**
 * Functional tests for multilingual support on nodes.
 */
class LocaleContentFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Content language settings',
      'description' => 'Checks you can enable multilingual support on content types and configure a language for a node.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
  }

  /**
   * Verifies that machine name fields are always LTR.
   */
  function testMachineNameLTR() {
    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'administer content types', 'access administration pages'));

    // Log in as admin.
    $this->drupalLogin($admin_user);

    // Verify that the machine name field is LTR for a new content type.
    $this->drupalGet('admin/structure/types/add');
    $this->assertFieldByXpath('//input[@name="type" and @dir="ltr"]', NULL, 'The machine name field is LTR when no additional language is configured.');

    // Install the Arabic language (which is RTL) and configure as the default.
    $edit = array();
    $edit['langcode'] = 'ar';
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    $edit = array();
    $edit['site_default'] = 'ar';
    $this->drupalPost(NULL, $edit, t('Save configuration'));

    // Verify that the machine name field is still LTR for a new content type.
    $this->drupalGet('admin/structure/types/add');
    $this->assertFieldByXpath('//input[@name="type" and @dir="ltr"]', NULL, 'The machine name field is LTR when the default language is RTL.');
  }

  /**
   * Test if a content type can be set to multilingual and language setting is
   * present on node add and edit forms.
   */
  function testContentTypeLanguageConfiguration() {
    global $base_url;

    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'administer content types', 'access administration pages'));
    // User to create a node.
    $web_user = $this->drupalCreateUser(array('create article content', 'create page content', 'edit any page content'));

    // Add custom language.
    $this->drupalLogin($admin_user);
    // Code for the language.
    $langcode = 'xx';
    // The English name for the language.
    $name = $this->randomName(16);
    // The native name for the language.
    $native = $this->randomName(16);
    // The domain prefix.
    $prefix = $langcode;
    $edit = array(
      'langcode' => $langcode,
      'name' => $name,
      'native' => $native,
      'prefix' => $prefix,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));

    // Add disabled custom language.
    // Code for the language.
    $langcode_disabled = 'xx-yy';
    // The English name for the language.
    $name_disabled = $this->randomName(16);
    // The native name for the language.
    $native_disabled = $this->randomName(16);
    // The domain prefix.
    $prefix_disabled = $langcode_disabled;
    $edit = array(
      'langcode' => $langcode_disabled,
      'name' => $name_disabled,
      'native' => $native_disabled,
      'prefix' => $prefix_disabled,
      'direction' => '0',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add custom language'));
    // Disable second custom language.
    $path = 'admin/config/regional/language';
    $edit = array(
      'enabled[' . $langcode_disabled . ']' => FALSE,
    );
    $this->drupalPost($path, $edit, t('Save configuration'));

    // Set "Basic page" content type to use multilingual support.
    $this->drupalGet('admin/structure/types/manage/page');
    $this->assertText(t('Multilingual support'), 'Multilingual support fieldset present on content type configuration form.');
    $edit = array(
      'language_content_type' => 1,
    );
    $this->drupalPost('admin/structure/types/manage/page', $edit, t('Save content type'));
    $this->assertRaw(t('The content type %type has been updated.', array('%type' => 'Basic page')), 'Basic page content type has been updated.');
    $this->drupalLogout();

    // Verify language selection is not present on add article form.
    $this->drupalLogin($web_user);
    $this->drupalGet('node/add/article');
    // Verify language select list is not present.
    $this->assertNoFieldByName('language', NULL, 'Language select not present on add article form.');

    // Verify language selection appears on add "Basic page" form.
    $this->drupalGet('node/add/page');
    // Verify language select list is present.
    $this->assertFieldByName('language', NULL, 'Language select present on add Basic page form.');
    // Ensure enabled language appears.
    $this->assertText($name, 'Enabled language present.');
    // Ensure disabled language doesn't appear.
    $this->assertNoText($name_disabled, 'Disabled language not present.');

    // Create "Basic page" content.
    $node_title = $this->randomName();
    $node_body =  $this->randomName();
    $edit = array(
      'type' => 'page',
      'title' => $node_title,
      'body' => array($langcode => array(array('value' => $node_body))),
      'language' => $langcode,
    );
    $node = $this->drupalCreateNode($edit);
    // Edit the content and ensure correct language is selected.
    $path = 'node/' . $node->nid . '/edit';
    $this->drupalGet($path);
    $this->assertRaw('<option value="' . $langcode . '" selected="selected">' .  $name . '</option>', 'Correct language selected.');
    // Ensure we can change the node language.
    $edit = array(
      'language' => 'en',
    );
    $this->drupalPost($path, $edit, t('Save'));
    $this->assertRaw(t('%title has been updated.', array('%title' => $node_title)), 'Basic page content updated.');

    $this->drupalLogout();
  }

  /**
   * Verifies that nodes may be created with different languages.
   */
  function testNodeCreationWithLanguage() {
    // Create an admin user and log them in.
    $perms = array(
      // Standard node permissions.
      'create page content',
      'administer content types',
      'administer nodes',
      'bypass node access',
      // Locale.
      'administer languages',
    );
    $web_user = $this->drupalCreateUser($perms);
    $this->drupalLogin($web_user);

    // Create some test nodes using different langcodes.
    foreach (array(LANGUAGE_NONE, 'en', 'fr') as $langcode) {
      $node_args = array(
        'type' => 'page',
        'promote' => 1,
        'language' => $langcode,
      );
      $node = $this->drupalCreateNode($node_args);
      $node_reloaded = node_load($node->nid, NULL, TRUE);
      $this->assertEqual($node_reloaded->language, $langcode, format_string('The language code of the node was successfully set to @langcode.', array('@langcode' => $langcode)));
    }
  }

}

/**
 * Test UI language negotiation
 * 1. URL (PATH) > DEFAULT
 *    UI Language base on URL prefix, browser language preference has no
 *    influence:
 *      admin/config
 *        UI in site default language
 *      zh-hans/admin/config
 *        UI in Chinese
 *      blah-blah/admin/config
 *        404
 * 2. URL (PATH) > BROWSER > DEFAULT
 *        admin/config
 *          UI in user's browser language preference if the site has that
 *          language enabled, if not, the default language
 *        zh-hans/admin/config
 *          UI in Chinese
 *        blah-blah/admin/config
 *          404
 * 3. URL (DOMAIN) > DEFAULT
 *        http://example.com/admin/config
 *          UI language in site default
 *        http://example.cn/admin/config
 *          UI language in Chinese
 */
class LocaleUILanguageNegotiationTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'UI language negotiation',
      'description' => 'Test UI language switching by URL path prefix and domain.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale', 'locale_test');
    require_once DRUPAL_ROOT . '/includes/language.inc';
    drupal_load('module', 'locale');
    $admin_user = $this->drupalCreateUser(array('administer languages', 'translate interface', 'access administration pages', 'administer blocks'));
    $this->drupalLogin($admin_user);
  }

  /**
   * Tests for language switching by URL path.
   */
  function testUILanguageNegotiation() {
    // A few languages to switch to.
    // This one is unknown, should get the default lang version.
    $language_unknown = 'blah-blah';
    // For testing browser lang preference.
    $language_browser_fallback = 'vi';
    // For testing path prefix.
    $language = 'zh-hans';
    // For setting browser language preference to 'vi'.
    $http_header_browser_fallback = array("Accept-Language: $language_browser_fallback;q=1");
    // For setting browser language preference to some unknown.
    $http_header_blah = array("Accept-Language: blah;q=1");

    // This domain should switch the UI to Chinese.
    $language_domain = 'example.cn';

    // Setup the site languages by installing two languages.
    require_once DRUPAL_ROOT . '/includes/locale.inc';
    locale_add_language($language_browser_fallback);
    locale_add_language($language);

    // We will look for this string in the admin/config screen to see if the
    // corresponding translated string is shown.
    $default_string = 'Configure languages for content and the user interface';

    // Set the default language in order for the translated string to be registered
    // into database when seen by t(). Without doing this, our target string
    // is for some reason not found when doing translate search. This might
    // be some bug.
    drupal_static_reset('language_list');
    $languages = language_list('enabled');
    variable_set('language_default', $languages[1]['vi']);
    // First visit this page to make sure our target string is searchable.
    $this->drupalGet('admin/config');
    // Now the t()'ed string is in db so switch the language back to default.
    variable_del('language_default');

    // Translate the string.
    $language_browser_fallback_string = "In $language_browser_fallback In $language_browser_fallback In $language_browser_fallback";
    $language_string = "In $language In $language In $language";
    // Do a translate search of our target string.
    $edit = array( 'string' => $default_string);
    $this->drupalPost('admin/config/regional/translate/translate', $edit, t('Filter'));
    // Should find the string and now click edit to post translated string.
    $this->clickLink('edit');
    $edit = array(
      "translations[$language_browser_fallback]" => $language_browser_fallback_string,
      "translations[$language]" => $language_string,
    );
    $this->drupalPost(NULL, $edit, t('Save translations'));

    // Configure URL language rewrite.
    variable_set('locale_language_negotiation_url_type', LANGUAGE_TYPE_INTERFACE);

    $tests = array(
      // Default, browser preference should have no influence.
      array(
        'language_negotiation' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LANGUAGE_NEGOTIATION_DEFAULT),
        'path' => 'admin/config',
        'expect' => $default_string,
        'expected_provider' => LANGUAGE_NEGOTIATION_DEFAULT,
        'http_header' => $http_header_browser_fallback,
        'message' => 'URL (PATH) > DEFAULT: no language prefix, UI language is default and the browser language preference setting is not used.',
      ),
      // Language prefix.
      array(
        'language_negotiation' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LANGUAGE_NEGOTIATION_DEFAULT),
        'path' => "$language/admin/config",
        'expect' => $language_string,
        'expected_provider' => LOCALE_LANGUAGE_NEGOTIATION_URL,
        'http_header' => $http_header_browser_fallback,
        'message' => 'URL (PATH) > DEFAULT: with language prefix, UI language is switched based on path prefix',
      ),
      // Default, go by browser preference.
      array(
        'language_negotiation' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LOCALE_LANGUAGE_NEGOTIATION_BROWSER),
        'path' => 'admin/config',
        'expect' => $language_browser_fallback_string,
        'expected_provider' => LOCALE_LANGUAGE_NEGOTIATION_BROWSER,
        'http_header' => $http_header_browser_fallback,
        'message' => 'URL (PATH) > BROWSER: no language prefix, UI language is determined by browser language preference',
      ),
      // Prefix, switch to the language.
      array(
        'language_negotiation' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LOCALE_LANGUAGE_NEGOTIATION_BROWSER),
        'path' => "$language/admin/config",
        'expect' => $language_string,
        'expected_provider' => LOCALE_LANGUAGE_NEGOTIATION_URL,
        'http_header' => $http_header_browser_fallback,
        'message' => 'URL (PATH) > BROWSER: with langage prefix, UI language is based on path prefix',
      ),
      // Default, browser language preference is not one of site's lang.
      array(
        'language_negotiation' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LOCALE_LANGUAGE_NEGOTIATION_BROWSER, LANGUAGE_NEGOTIATION_DEFAULT),
        'path' => 'admin/config',
        'expect' => $default_string,
        'expected_provider' => LANGUAGE_NEGOTIATION_DEFAULT,
        'http_header' => $http_header_blah,
        'message' => 'URL (PATH) > BROWSER > DEFAULT: no language prefix and browser language preference set to unknown language should use default language',
      ),
    );

    foreach ($tests as $test) {
      $this->runTest($test);
    }

    // Unknown language prefix should return 404.
    variable_set('language_negotiation_' . LANGUAGE_TYPE_INTERFACE, locale_language_negotiation_info());
    $this->drupalGet("$language_unknown/admin/config", array(), $http_header_browser_fallback);
    $this->assertResponse(404, "Unknown language path prefix should return 404");

    // Setup for domain negotiation, first configure the language to have domain
    // URL. We use HTTPS and a port to make sure that only the domain name is used.
    $edit = array('prefix' => '', 'domain' => "https://$language_domain:99");
    $this->drupalPost("admin/config/regional/language/edit/$language", $edit, t('Save language'));
    // Set the site to use domain language negotiation.

    $tests = array(
      // Default domain, browser preference should have no influence.
      array(
        'language_negotiation' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LANGUAGE_NEGOTIATION_DEFAULT),
        'locale_language_negotiation_url_part' => LOCALE_LANGUAGE_NEGOTIATION_URL_DOMAIN,
        'path' => 'admin/config',
        'expect' => $default_string,
        'expected_provider' => LANGUAGE_NEGOTIATION_DEFAULT,
        'http_header' => $http_header_browser_fallback,
        'message' => 'URL (DOMAIN) > DEFAULT: default domain should get default language',
      ),
      // Language domain specific URL, we set the $_SERVER['HTTP_HOST'] in
      // locale_test.module hook_boot() to simulate this.
      array(
        'language_negotiation' => array(LOCALE_LANGUAGE_NEGOTIATION_URL, LANGUAGE_NEGOTIATION_DEFAULT),
        'locale_language_negotiation_url_part' => LOCALE_LANGUAGE_NEGOTIATION_URL_DOMAIN,
        'locale_test_domain' => $language_domain . ':88',
        'path' => 'admin/config',
        'expect' => $language_string,
        'expected_provider' => LOCALE_LANGUAGE_NEGOTIATION_URL,
        'http_header' => $http_header_browser_fallback,
        'message' => 'URL (DOMAIN) > DEFAULT: domain example.cn should switch to Chinese',
      ),
    );

    foreach ($tests as $test) {
      $this->runTest($test);
    }
  }

  private function runTest($test) {
    if (!empty($test['language_negotiation'])) {
      $negotiation = array_flip($test['language_negotiation']);
      language_negotiation_set(LANGUAGE_TYPE_INTERFACE, $negotiation);
    }
    if (!empty($test['locale_language_negotiation_url_part'])) {
      variable_set('locale_language_negotiation_url_part', $test['locale_language_negotiation_url_part']);
    }
    if (!empty($test['locale_test_domain'])) {
      variable_set('locale_test_domain', $test['locale_test_domain']);
    }
    $this->drupalGet($test['path'], array(), $test['http_header']);
    $this->assertText($test['expect'], $test['message']);
    $this->assertText(t('Language negotiation provider: @name', array('@name' => $test['expected_provider'])));
  }

  /**
   * Test URL language detection when the requested URL has no language.
   */
  function testUrlLanguageFallback() {
    // Add the Italian language.
    $language_browser_fallback = 'it';
    locale_add_language($language_browser_fallback);
    $languages = language_list();

    // Enable the path prefix for the default language: this way any unprefixed
    // URL must have a valid fallback value.
    $edit = array('prefix' => 'en');
    $this->drupalPost('admin/config/regional/language/edit/en', $edit, t('Save language'));

    // Enable browser and URL language detection.
    $edit = array(
      'language[enabled][locale-browser]' => TRUE,
      'language[enabled][locale-url]' => TRUE,
      'language[weight][locale-browser]' => -8,
      'language[weight][locale-url]' => -10,
    );
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));
    $this->drupalGet('admin/config/regional/language/configure');

    // Enable the language switcher block.
    $edit = array('blocks[locale_language][region]' => 'sidebar_first');
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));

    // Access the front page without specifying any valid URL language prefix
    // and having as browser language preference a non-default language.
    $http_header = array("Accept-Language: $language_browser_fallback;q=1");
    $this->drupalGet('', array(), $http_header);

    // Check that the language switcher active link matches the given browser
    // language.
    $args = array(':url' => base_path() . (!empty($GLOBALS['conf']['clean_url']) ? $language_browser_fallback : "?q=$language_browser_fallback"));
    $fields = $this->xpath('//div[@id="block-locale-language"]//a[@class="language-link active" and @href=:url]', $args);
    $this->assertTrue($fields[0] == $languages[$language_browser_fallback]->native, 'The browser language is the URL active language');

    // Check that URLs are rewritten using the given browser language.
    $fields = $this->xpath('//div[@id="site-name"]//a[@rel="home" and @href=:url]//span', $args);
    $this->assertTrue($fields[0] == 'Drupal', 'URLs are rewritten using the browser language.');
  }

  /**
   * Tests url() when separate domains are used for multiple languages.
   */
  function testLanguageDomain() {
    // Add the Italian language, without protocol.
    $langcode = 'it';
    locale_add_language($langcode, 'Italian', 'Italian', LANGUAGE_LTR, 'it.example.com', '', TRUE, FALSE);

    // Add the French language, with protocol.
    $langcode = 'fr';
    locale_add_language($langcode, 'French', 'French', LANGUAGE_LTR, 'http://fr.example.com', '', TRUE, FALSE);

    // Enable language URL detection.
    $negotiation = array_flip(array(LOCALE_LANGUAGE_NEGOTIATION_URL, LANGUAGE_NEGOTIATION_DEFAULT));
    language_negotiation_set(LANGUAGE_TYPE_INTERFACE, $negotiation);

    variable_set('locale_language_negotiation_url_part', 1);

    global $is_https;
    $languages = language_list();

    foreach (array('it', 'fr') as $langcode) {
      // Build the link we're going to test based on the clean URL setting.
      $link = (!empty($GLOBALS['conf']['clean_url'])) ? $langcode . '.example.com/admin' : $langcode . '.example.com/?q=admin';

      // Test URL in another language.
      // Base path gives problems on the testbot, so $correct_link is hard-coded.
      // @see UrlAlterFunctionalTest::assertUrlOutboundAlter (path.test).
      $url = url('admin', array('language' => $languages[$langcode]));
      $url_scheme = ($is_https) ? 'https://' : 'http://';
      $correct_link = $url_scheme . $link;
      $this->assertTrue($url == $correct_link, format_string('The url() function returns the right url (@url) in accordance with the chosen language', array('@url' => $url . " == " . $correct_link)));

      // Test HTTPS via options.
      variable_set('https', TRUE);
      $url = url('admin', array('https' => TRUE, 'language' => $languages[$langcode]));
      $correct_link = 'https://' . $link;
      $this->assertTrue($url == $correct_link, format_string('The url() function returns the right https url (via options) (@url) in accordance with the chosen language', array('@url' => $url . " == " . $correct_link)));
      variable_set('https', FALSE);

      // Test HTTPS via current URL scheme.
      $temp_https = $is_https;
      $is_https = TRUE;
      $url = url('admin', array('language' => $languages[$langcode]));
      $correct_link = 'https://' . $link;
      $this->assertTrue($url == $correct_link, format_string('The url() function returns the right url (via current url scheme) (@url) in accordance with the chosen language', array('@url' => $url . " == " . $correct_link)));
      $is_https = $temp_https;
    }
  }
}

/**
 * Test that URL rewriting works as expected.
 */
class LocaleUrlRewritingTest extends DrupalWebTestCase {
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'URL rewriting',
      'description' => 'Test that URL rewriting works as expected.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');

    // Create and login user.
    $this->web_user = $this->drupalCreateUser(array('administer languages', 'access administration pages'));
    $this->drupalLogin($this->web_user);

    // Install French language.
    $edit = array();
    $edit['langcode'] = 'fr';
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Install Italian language.
    $edit = array();
    $edit['langcode'] = 'it';
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Disable Italian language.
    $edit = array('enabled[it]' => FALSE);
    $this->drupalPost('admin/config/regional/language', $edit, t('Save configuration'));

    // Enable URL language detection and selection.
    $edit = array('language[enabled][locale-url]' => 1);
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Reset static caching.
    drupal_static_reset('language_list');
    drupal_static_reset('locale_url_outbound_alter');
    drupal_static_reset('locale_language_url_rewrite_url');
  }

  /**
   * Check that disabled or non-installed languages are not considered.
   */
  function testUrlRewritingEdgeCases() {
    // Check URL rewriting with a disabled language.
    $languages = language_list();
    $this->checkUrl($languages['it'], 'Path language is ignored if language is disabled.', 'URL language negotiation does not work with disabled languages');

    // Check URL rewriting with a non-installed language.
    $non_existing = language_default();
    $non_existing->language = $this->randomName();
    $non_existing->prefix = $this->randomName();
    $this->checkUrl($non_existing, 'Path language is ignored if language is not installed.', 'URL language negotiation does not work with non-installed languages');
  }

  /**
   * Check URL rewriting for the given language.
   *
   * The test is performed with a fixed URL (the default front page) to simply
   * check that language prefixes are not added to it and that the prefixed URL
   * is actually not working.
   *
   * @param string $language
   *   The language prefix, e.g. 'es'.
   * @param string $message1
   *   Message to display in assertion that language prefixes are not added.
   * @param string $message2
   *   The message to display confirming prefixed URL is not working.
   */
  private function checkUrl($language, $message1, $message2) {
    $options = array('language' => $language);
    $base_path = trim(base_path(), '/');
    $rewritten_path = trim(str_replace(array('?q=', $base_path), '', url('node', $options)), '/');
    $segments = explode('/', $rewritten_path, 2);
    $prefix = $segments[0];
    $path = isset($segments[1]) ? $segments[1] : $prefix;
    // If the rewritten URL has not a language prefix we pick the right one from
    // the language object so we can always check the prefixed URL.
    if ($this->assertNotEqual($language->prefix, $prefix, $message1)) {
      $prefix = $language->prefix;
    }
    $this->drupalGet("$prefix/$path");
    $this->assertResponse(404, $message2);
  }

  /**
   * Check URL rewriting when using a domain name and a non-standard port.
   */
  function testDomainNameNegotiationPort() {
    $language_domain = 'example.fr';
    $edit = array(
      'locale_language_negotiation_url_part' => 1,
    );
    $this->drupalPost('admin/config/regional/language/configure/url', $edit, t('Save configuration'));
    $edit = array(
      'prefix' => '',
      'domain' => $language_domain
    );
    $this->drupalPost('admin/config/regional/language/edit/fr', $edit, t('Save language'));

    // Enable domain configuration.
    variable_set('locale_language_negotiation_url_part', LOCALE_LANGUAGE_NEGOTIATION_URL_DOMAIN);

    // Reset static caching.
    drupal_static_reset('language_list');
    drupal_static_reset('language_url_outbound_alter');
    drupal_static_reset('language_url_rewrite_url');

    // In case index.php is part of the URLs, we need to adapt the asserted
    // URLs as well.
    $index_php = strpos(url('', array('absolute' => TRUE)), 'index.php') !== FALSE;

    // Remember current HTTP_HOST.
    $http_host = $_SERVER['HTTP_HOST'];

    // Fake a different port.
    $_SERVER['HTTP_HOST'] .= ':88';

    // Create an absolute French link.
    $languages = language_list();
    $language = $languages['fr'];
    $url = url('', array(
      'absolute' => TRUE,
      'language' => $language
    ));

    $expected = 'http://example.fr:88/';
    $expected .= $index_php ? 'index.php/' : '';

    $this->assertEqual($url, $expected, 'The right port is used.');

    // If we set the port explicitly in url(), it should not be overriden.
    $url = url('', array(
      'absolute' => TRUE,
      'language' => $language,
      'base_url' => $GLOBALS['base_url'] . ':90',
    ));

    $expected = 'http://example.fr:90/';
    $expected .= $index_php ? 'index.php/' : '';

    $this->assertEqual($url, $expected, 'A given port is not overriden.');

    // Restore HTTP_HOST.
    $_SERVER['HTTP_HOST'] = $http_host;
  }
}

/**
 * Functional test for multilingual fields.
 */
class LocaleMultilingualFieldsFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Multilingual fields',
      'description' => 'Test multilingual support for fields.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
    // Setup users.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'administer content types', 'access administration pages', 'create page content', 'edit own page content'));
    $this->drupalLogin($admin_user);

    // Add a new language.
    require_once DRUPAL_ROOT . '/includes/locale.inc';
    locale_add_language('it', 'Italian', 'Italiano', LANGUAGE_LTR, '', '', TRUE, FALSE);

    // Enable URL language detection and selection.
    $edit = array('language[enabled][locale-url]' => '1');
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Set "Basic page" content type to use multilingual support.
    $edit = array(
      'language_content_type' => 1,
    );
    $this->drupalPost('admin/structure/types/manage/page', $edit, t('Save content type'));
    $this->assertRaw(t('The content type %type has been updated.', array('%type' => 'Basic page')), 'Basic page content type has been updated.');

    // Make node body translatable.
    $field = field_info_field('body');
    $field['translatable'] = TRUE;
    field_update_field($field);
  }

  /**
   * Test if field languages are correctly set through the node form.
   */
  function testMultilingualNodeForm() {
    // Create "Basic page" content.
    $langcode = LANGUAGE_NONE;
    $title_key = "title";
    $title_value = $this->randomName(8);
    $body_key = "body[$langcode][0][value]";
    $body_value = $this->randomName(16);

    // Create node to edit.
    $edit = array();
    $edit[$title_key] = $title_value;
    $edit[$body_key] = $body_value;
    $edit['language'] = 'en';
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the node exists in the database.
    $node = $this->drupalGetNodeByTitle($edit[$title_key]);
    $this->assertTrue($node, 'Node found in database.');

    $assert = isset($node->body['en']) && !isset($node->body[LANGUAGE_NONE]) && $node->body['en'][0]['value'] == $body_value;
    $this->assertTrue($assert, 'Field language correctly set.');

    // Change node language.
    $this->drupalGet("node/$node->nid/edit");
    $edit = array(
      $title_key => $this->randomName(8),
      'language' => 'it'
    );
    $this->drupalPost(NULL, $edit, t('Save'));
    $node = $this->drupalGetNodeByTitle($edit[$title_key]);
    $this->assertTrue($node, 'Node found in database.');

    $assert = isset($node->body['it']) && !isset($node->body['en']) && $node->body['it'][0]['value'] == $body_value;
    $this->assertTrue($assert, 'Field language correctly changed.');

    // Enable content language URL detection.
    language_negotiation_set(LANGUAGE_TYPE_CONTENT, array(LOCALE_LANGUAGE_NEGOTIATION_URL => 0));

    // Test multilingual field language fallback logic.
    $this->drupalGet("it/node/$node->nid");
    $this->assertRaw($body_value, 'Body correctly displayed using Italian as requested language');

    $this->drupalGet("node/$node->nid");
    $this->assertRaw($body_value, 'Body correctly displayed using English as requested language');
  }

  /*
   * Test multilingual field display settings.
   */
  function testMultilingualDisplaySettings() {
    // Create "Basic page" content.
    $langcode = LANGUAGE_NONE;
    $title_key = "title";
    $title_value = $this->randomName(8);
    $body_key = "body[$langcode][0][value]";
    $body_value = $this->randomName(16);

    // Create node to edit.
    $edit = array();
    $edit[$title_key] = $title_value;
    $edit[$body_key] = $body_value;
    $edit['language'] = 'en';
    $this->drupalPost('node/add/page', $edit, t('Save'));

    // Check that the node exists in the database.
    $node = $this->drupalGetNodeByTitle($edit[$title_key]);
    $this->assertTrue($node, 'Node found in database.');

    // Check if node body is showed.
    $this->drupalGet("node/$node->nid");
    $body = $this->xpath('//div[@id=:id]//div[@property="content:encoded"]/p', array(':id' => 'node-' . $node->nid));
    $this->assertEqual(current($body), $node->body['en'][0]['value'], 'Node body is correctly showed.');
  }
}

/**
 * Functional tests for comment language.
 */
class LocaleCommentLanguageFunctionalTest extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Comment language',
      'description' => 'Tests for comment language.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale', 'locale_test');

    // Create and login user.
    $admin_user = $this->drupalCreateUser(array('administer site configuration', 'administer languages', 'access administration pages', 'administer content types', 'administer comments', 'create article content'));
    $this->drupalLogin($admin_user);

    // Add language.
    $edit = array('langcode' => 'fr');
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Set "Article" content type to use multilingual support.
    $edit = array('language_content_type' => 1);
    $this->drupalPost('admin/structure/types/manage/article', $edit, t('Save content type'));

    // Enable content language negotiation UI.
    variable_set('locale_test_content_language_type', TRUE);

    // Set interface language detection to user and content language detection
    // to URL. Disable inheritance from interface language to ensure content
    // language will fall back to the default language if no URL language can be
    // detected.
    $edit = array(
      'language[enabled][locale-user]' => TRUE,
      'language_content[enabled][locale-url]' => TRUE,
      'language_content[enabled][locale-interface]' => FALSE,
    );
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Change user language preference, this way interface language is always
    // French no matter what path prefix the URLs have.
    $edit = array('language' => 'fr');
    $this->drupalPost("user/{$admin_user->uid}/edit", $edit, t('Save'));

    // Make comment body translatable.
    $field = field_info_field('comment_body');
    $field['translatable'] = TRUE;
    field_update_field($field);
    $this->assertTrue(field_is_translatable('comment', $field), 'Comment body is translatable.');
  }

  /**
   * Test that comment language is properly set.
   */
  function testCommentLanguage() {
    drupal_static_reset('language_list');

    // Create two nodes, one for english and one for french, and comment each
    // node using both english and french as content language by changing URL
    // language prefixes. Meanwhile interface language is always French, which
    // is the user language preference. This way we can ensure that node
    // language and interface language do not influence comment language, as
    // only content language has to.
    foreach (language_list() as $node_langcode => $node_language) {
      $language_none = LANGUAGE_NONE;

      // Create "Article" content.
      $title = $this->randomName();
      $edit = array(
        "title" => $title,
        "body[$language_none][0][value]" => $this->randomName(),
        "language" => $node_langcode,
      );
      $this->drupalPost("node/add/article", $edit, t('Save'));
      $node = $this->drupalGetNodeByTitle($title);

      foreach (language_list() as $langcode => $language) {
        // Post a comment with content language $langcode.
        $prefix = empty($language->prefix) ? '' : $language->prefix . '/';
        $comment_values[$node_langcode][$langcode] = $this->randomName();
        // Initially field form widgets have no language.
        $edit = array(
          'subject' => $this->randomName(),
          "comment_body[$language_none][0][value]" => $comment_values[$node_langcode][$langcode],
        );
        $this->drupalPost("{$prefix}node/{$node->nid}", $edit, t('Preview'));
        // After the first submit the submitted entity language is taken into
        // account.
        $edit = array(
          'subject' => $edit['subject'],
          "comment_body[$langcode][0][value]" => $comment_values[$node_langcode][$langcode],
        );
        $this->drupalPost(NULL, $edit, t('Save'));

        // Check that comment language matches the current content language.
        $cid = db_select('comment', 'c')
          ->fields('c', array('cid'))
          ->condition('nid', $node->nid)
          ->orderBy('cid', 'DESC')
          ->range(0, 1)
          ->execute()
          ->fetchField();
        $comment = comment_load($cid);
        $comment_langcode = entity_language('comment', $comment);
        $args = array('%node_language' => $node_langcode, '%comment_language' => $comment_langcode, '%langcode' => $langcode);
        $this->assertEqual($comment_langcode, $langcode, format_string('The comment posted with content language %langcode and belonging to the node with language %node_language has language %comment_language', $args));
        $this->assertEqual($comment->comment_body[$langcode][0]['value'], $comment_values[$node_langcode][$langcode], 'Comment body correctly stored.');
      }
    }

    // Check that comment bodies appear in the administration UI.
    $this->drupalGet('admin/content/comment');
    foreach ($comment_values as $node_values) {
      foreach ($node_values as $value) {
        $this->assertRaw($value);
      }
    }
  }

}

/**
 * Functional tests for localizing date formats.
 */
class LocaleDateFormatsFunctionalTest extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Localize date formats',
      'description' => 'Tests for the localization of date formats.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');

    // Create and login user.
    $admin_user = $this->drupalCreateUser(array('administer site configuration', 'administer languages', 'access administration pages', 'create article content'));
    $this->drupalLogin($admin_user);
  }

  /**
   * Functional tests for localizing date formats.
   */
  function testLocalizeDateFormats() {
    // Add language.
    $edit = array(
      'langcode' => 'fr',
    );
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Set language negotiation.
    $language_type = LANGUAGE_TYPE_INTERFACE;
    $edit = array(
      "{$language_type}[enabled][locale-url]" => TRUE,
    );
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Configure date formats.
    $this->drupalGet('admin/config/regional/date-time/locale');
    $this->assertText('Français', 'Configured languages appear.');
    $edit = array(
      'date_format_long' => 'd.m.Y - H:i',
      'date_format_medium' => 'd.m.Y - H:i',
      'date_format_short' => 'd.m.Y - H:i',
    );
    $this->drupalPost('admin/config/regional/date-time/locale/fr/edit', $edit, t('Save configuration'));
    $this->assertText(t('Configuration saved.'), 'French date formats updated.');
    $edit = array(
      'date_format_long' => 'j M Y - g:ia',
      'date_format_medium' => 'j M Y - g:ia',
      'date_format_short' => 'j M Y - g:ia',
    );
    $this->drupalPost('admin/config/regional/date-time/locale/en/edit', $edit, t('Save configuration'));
    $this->assertText(t('Configuration saved.'), 'English date formats updated.');

    // Create node content.
    $node = $this->drupalCreateNode(array('type' => 'article'));

    // Configure format for the node posted date changes with the language.
    $this->drupalGet('node/' . $node->nid);
    $english_date = format_date($node->created, 'custom', 'j M Y');
    $this->assertText($english_date, 'English date format appears');
    $this->drupalGet('fr/node/' . $node->nid);
    $french_date = format_date($node->created, 'custom', 'd.m.Y');
    $this->assertText($french_date, 'French date format appears');
  }
}

/**
 * Functional test for language types/negotiation info.
 */
class LocaleLanguageNegotiationInfoFunctionalTest extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Language negotiation info',
      'description' => 'Tests alterations to language types/negotiation info.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
    require_once DRUPAL_ROOT .'/includes/language.inc';
    $admin_user = $this->drupalCreateUser(array('administer languages', 'access administration pages', 'view the administration theme'));
    $this->drupalLogin($admin_user);
    $this->drupalPost('admin/config/regional/language/add', array('langcode' => 'it'), t('Add language'));
  }

  /**
   * Tests alterations to language types/negotiation info.
   */
  function testInfoAlterations() {
    // Enable language type/negotiation info alterations.
    variable_set('locale_test_language_types', TRUE);
    variable_set('locale_test_language_negotiation_info', TRUE);
    $this->languageNegotiationUpdate();

    // Check that fixed language types are properly configured without the need
    // of saving the language negotiation settings.
    $this->checkFixedLanguageTypes();

    // Make the content language type configurable by updating the language
    // negotiation settings with the proper flag enabled.
    variable_set('locale_test_content_language_type', TRUE);
    $this->languageNegotiationUpdate();
    $type = LANGUAGE_TYPE_CONTENT;
    $language_types = variable_get('language_types', drupal_language_types());
    $this->assertTrue($language_types[$type], 'Content language type is configurable.');

    // Enable some core and custom language providers. The test language type is
    // supposed to be configurable.
    $test_type = 'test_language_type';
    $provider = LOCALE_LANGUAGE_NEGOTIATION_INTERFACE;
    $test_provider = 'test_language_provider';
    $form_field = $type . '[enabled]['. $provider .']';
    $edit = array(
      $form_field => TRUE,
      $type . '[enabled][' . $test_provider . ']' => TRUE,
      $test_type . '[enabled][' . $test_provider . ']' => TRUE,
    );
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Remove the interface language provider by updating the language
    // negotiation settings with the proper flag enabled.
    variable_set('locale_test_language_negotiation_info_alter', TRUE);
    $this->languageNegotiationUpdate();
    $negotiation = variable_get("language_negotiation_$type", array());
    $this->assertFalse(isset($negotiation[$provider]), 'Interface language provider removed from the stored settings.');
    $this->assertNoFieldByXPath("//input[@name=\"$form_field\"]", NULL, 'Interface language provider unavailable.');

    // Check that type-specific language providers can be assigned only to the
    // corresponding language types.
    foreach (language_types_configurable() as $type) {
      $form_field = $type . '[enabled][test_language_provider_ts]';
      if ($type == $test_type) {
        $this->assertFieldByXPath("//input[@name=\"$form_field\"]", NULL, format_string('Type-specific test language provider available for %type.', array('%type' => $type)));
      }
      else {
        $this->assertNoFieldByXPath("//input[@name=\"$form_field\"]", NULL, format_string('Type-specific test language provider unavailable for %type.', array('%type' => $type)));
      }
    }

    // Check language negotiation results.
    $this->drupalGet('');
    $last = variable_get('locale_test_language_negotiation_last', array());
    foreach (language_types() as $type) {
      $langcode = $last[$type];
      $value = $type == LANGUAGE_TYPE_CONTENT || strpos($type, 'test') !== FALSE ? 'it' : 'en';
      $this->assertEqual($langcode, $value, format_string('The negotiated language for %type is %language', array('%type' => $type, '%language' => $langcode)));
    }

    // Disable locale_test and check that everything is set back to the original
    // status.
    $this->languageNegotiationUpdate('disable');

    // Check that only the core language types are available.
    foreach (language_types() as $type) {
      $this->assertTrue(strpos($type, 'test') === FALSE, format_string('The %type language is still available', array('%type' => $type)));
    }

    // Check that fixed language types are properly configured, even those
    // previously set to configurable.
    $this->checkFixedLanguageTypes();

    // Check that unavailable language providers are not present in the
    // negotiation settings.
    $negotiation = variable_get("language_negotiation_$type", array());
    $this->assertFalse(isset($negotiation[$test_provider]), 'The disabled test language provider is not part of the content language negotiation settings.');

    // Check that configuration page presents the correct options and settings.
    $this->assertNoRaw(t('Test language detection'), 'No test language type configuration available.');
    $this->assertNoRaw(t('This is a test language provider'), 'No test language provider available.');
  }

  /**
   * Update language types/negotiation information.
   *
   * Manually invoke locale_modules_enabled()/locale_modules_disabled() since
   * they would not be invoked after enabling/disabling locale_test the first
   * time.
   */
  private function languageNegotiationUpdate($op = 'enable') {
    static $last_op = NULL;
    $modules = array('locale_test');

    // Enable/disable locale_test only if we did not already before.
    if ($last_op != $op) {
      $function = "module_{$op}";
      $function($modules);
      // Reset hook implementation cache.
      module_implements(NULL, FALSE, TRUE);
    }

    drupal_static_reset('language_types_info');
    drupal_static_reset('language_negotiation_info');
    $function = "locale_modules_{$op}d";
    if (function_exists($function)) {
      $function($modules);
    }

    $this->drupalGet('admin/config/regional/language/configure');
  }

  /**
   * Check that language negotiation for fixed types matches the stored one.
   */
  private function checkFixedLanguageTypes() {
    drupal_static_reset('language_types_info');
    foreach (language_types_info() as $type => $info) {
      if (isset($info['fixed'])) {
        $negotiation = variable_get("language_negotiation_$type", array());
        $equal = array_keys($negotiation) === array_values($info['fixed']);
        $this->assertTrue($equal, format_string('language negotiation for %type is properly set up', array('%type' => $type)));
      }
    }
  }
}

/**
 * Functional tests for CSS alter functions.
 */
class LocaleCSSAlterTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'CSS altering',
      'description' => 'Test CSS alter functions.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
  }

  /**
   * Verifies that -rtl.css file is added directly after LTR .css file.
   */
  function testCSSFilesOrderInRTLMode() {
    global $base_url;

    // User to add and remove language.
    $admin_user = $this->drupalCreateUser(array('administer languages', 'administer content types', 'access administration pages'));

    // Log in as admin.
    $this->drupalLogin($admin_user);

    // Install the Arabic language (which is RTL) and configure as the default.
    $edit = array();
    $edit['langcode'] = 'ar';
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    $edit = array();
    $edit['site_default'] = 'ar';
    $this->drupalPost(NULL, $edit, t('Save configuration'));

    // Verify that the -rtl.css file is added directly after LTR file.
    $this->drupalGet('');
    $query_string = '?' . variable_get('css_js_query_string', '0');
    $this->assertRaw('@import url("' . $base_url . '/modules/system/system.base.css' . $query_string . '");' . "\n" . '@import url("' . $base_url . '/modules/system/system.base-rtl.css' . $query_string . '");' . "\n", 'CSS: system.base-rtl.css is added directly after system.base.css.');
    $this->assertRaw('@import url("' . $base_url . '/modules/system/system.menus.css' . $query_string . '");' . "\n" . '@import url("' . $base_url . '/modules/system/system.menus-rtl.css' . $query_string . '");' . "\n", 'CSS: system.menus-rtl.css is added directly after system.menus.css.');
    $this->assertRaw('@import url("' . $base_url . '/modules/system/system.messages.css' . $query_string . '");' . "\n" . '@import url("' . $base_url . '/modules/system/system.messages-rtl.css' . $query_string . '");' . "\n", 'CSS: system.messages-rtl.css is added directly after system.messages.css.');
  }
}

/**
 * Tests locale translation safe string handling.
 */
class LocaleStringIsSafeTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Test if a string is safe',
      'description' => 'Tests locale translation safe string handling.',
      'group' => 'Locale',
    );
  }

  function setUp() {
    parent::setUp('locale');
  }

  /**
   * Tests for locale_string_is_safe().
   */
  public function testLocaleStringIsSafe() {
    // Check a translatable string without HTML.
    $string = 'Hello world!';
    $result = locale_string_is_safe($string);
    $this->assertTrue($result);

    // Check a translatable string which includes trustable HTML.
    $string = 'Hello <strong>world</strong>!';
    $result = locale_string_is_safe($string);
    $this->assertTrue($result);

    // Check an untranslatable string which includes untrustable HTML (according
    // to the locale_string_is_safe() function definition).
    $string = 'Hello <img src="world.png" alt="world" />!';
    $result = locale_string_is_safe($string);
    $this->assertFalse($result);

    // Check a translatable string which includes a token in an href attribute.
    $string = 'Hi <a href="[current-user:url]">user</a>';
    $result = locale_string_is_safe($string);
    $this->assertTrue($result);
  }
}
