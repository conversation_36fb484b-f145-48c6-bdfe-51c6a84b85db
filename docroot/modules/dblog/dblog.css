/**
 * @file
 * Admin styles for the Database Logging module.
 */

#dblog-filter-form .form-item-type,
#dblog-filter-form .form-item-severity {
  float: left; /* LTR */
  padding-right: .8em; /* LTR */
  margin: 0.1em;
  /**
   * In Opera 9, DOM elements with the property of "overflow: auto"
   * will partially hide its contents with unnecessary scrollbars when
   * its immediate child is floated without an explicit width set.
   */
  width: 15em;
}
#dblog-filter-form .form-type-select select {
  width: 100%;
}
#dblog-filter-form .form-actions {
  float: left;
  padding: 3ex 0 0 1em;
}

tr.dblog-user {
  background: #ffd;
}
tr.dblog-user .active {
  background: #eed;
}
tr.dblog-content {
  background: #ddf;
}
tr.dblog-content .active {
  background: #cce;
}
tr.dblog-page-not-found,
tr.dblog-access-denied {
  background: #dfd;
}
tr.dblog-page-not-found .active,
tr.dblog-access-denied .active {
  background: #cec;
}
tr.dblog-error {
  background: #ffc9c9;
}
tr.dblog-error .active {
  background: #eeb9b9;
}
table#admin-dblog td.icon {
  background: no-repeat center;
  width: 16px;
}
table#admin-dblog tr.dblog-warning td.icon {
  background-image: url(../../misc/message-16-warning.png);
}
table#admin-dblog tr.dblog-error td.icon,
table#admin-dblog tr.dblog-critical td.icon,
table#admin-dblog tr.dblog-alert td.icon,
table#admin-dblog tr.dblog-emerg td.icon {
  background-image: url(../../misc/message-16-error.png);
}
