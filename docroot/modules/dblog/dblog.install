<?php

/**
 * @file
 * Install, update and uninstall functions for the dblog module.
 */

/**
 * Implements hook_schema().
 */
function dblog_schema() {
  $schema['watchdog'] = array(
    'description' => 'Table that contains logs of all system events.',
    'fields' => array(
      'wid' => array(
        'type' => 'serial',
        'not null' => TRUE,
        'description' => 'Primary Key: Unique watchdog event ID.',
      ),
      'uid' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {users}.uid of the user who triggered the event.',
      ),
      'type' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Type of log message, for example "user" or "page not found."',
      ),
      'message' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'Text of log message to be passed into the t() function.',
      ),
      'variables' => array(
        'type' => 'blob',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'Serialized array of variables that match the message string and that is passed into the t() function.',
      ),
      'severity' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'The severity level of the event; ranges from 0 (Emergency) to 7 (Debug)',
      ),
      'link' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => FALSE,
        'default' => '',
        'description' => 'Link to view the result of the event.',
      ),
      'location'  => array(
        'type' => 'text',
        'not null' => TRUE,
        'description' => 'URL of the origin of the event.',
      ),
      'referer' => array(
        'type' => 'text',
        'not null' => FALSE,
        'description' => 'URL of referring page.',
      ),
      'hostname' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Hostname of the user who triggered the event.',
      ),
      'timestamp' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Unix timestamp of when event occurred.',
      ),
    ),
    'primary key' => array('wid'),
    'indexes' => array(
      'type' => array('type'),
      'uid' => array('uid'),
      'severity' => array('severity'),
    ),
  );

  return $schema;
}

/**
 * Implements hook_uninstall().
 */
function dblog_uninstall() {
  variable_del('dblog_row_limit');
}

/**
 * @addtogroup updates-6.x-to-7.x
 * @{
 */

/**
 * Update the {watchdog} table.
 */
function dblog_update_7001() {
  // Allow NULL values for links.
  db_change_field('watchdog', 'link', 'link', array(
    'type' => 'varchar',
    'length' => 255,
    'not null' => FALSE,
    'default' => '',
    'description' => 'Link to view the result of the event.',
  ));

  // Add an index on uid.
  db_add_index('watchdog', 'uid', array('uid'));

  // Allow longer type values.
  db_change_field('watchdog', 'type', 'type', array(
    'type' => 'varchar',
    'length' => 64,
    'not null' => TRUE,
    'default' => '',
    'description' => 'Type of log message, for example "user" or "page not found."',
  ));

  // Convert the variables field (that stores serialized variables) from text to blob.
  db_change_field('watchdog', 'variables', 'variables', array(
    'type' => 'blob',
    'not null' => TRUE,
    'size' => 'big',
    'description' => 'Serialized array of variables that match the message string and that is passed into the t() function.',
  ));
}

/**
 * @} End of "addtogroup updates-6.x-to-7.x".
 */

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Add an index to the severity column in the watchdog database table.
 */
function dblog_update_7002() {
  db_add_index('watchdog', 'severity', array('severity'));
}

/**
 * Account for possible legacy systems where dblog was not installed.
 */
function dblog_update_7003() {
  if (!db_table_exists('watchdog')) {
    db_create_table('watchdog', drupal_get_schema_unprocessed('dblog', 'watchdog'));
  }
}

/**
 * @} End of "addtogroup updates-7.x-extra".
 */
