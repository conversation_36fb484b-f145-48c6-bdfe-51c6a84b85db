<?php

/**
 * @file
 * Tests for help.module.
 */

/**
 * Tests help display and user access for all modules implementing help.
 */
class HelpTestCase extends DrupalWebTestCase {
  /**
   * The admin user that will be created.
   */
  protected $big_user;

  /**
   * The anonymous user that will be created.
   */
  protected $any_user;

  /**
   * The list of modules.
   */
  protected $modules;

  public static function getInfo() {
    return array(
      'name' => 'Help functionality',
      'description' => 'Verify help display and user access to help based on permissions.',
      'group' => 'Help',
    );
  }

  function setUp() {
    parent::setUp('blog', 'poll');

    $this->getModuleList();

    // Create users.
    $this->big_user = $this->drupalCreateUser(array('access administration pages', 'view the administration theme', 'administer permissions'));
    $this->any_user = $this->drupalCreateUser(array());
  }

  /**
   * Logs in users, creates dblog events, and tests dblog functionality.
   */
  function testHelp() {
    // Login the admin user.
    $this->drupalLogin($this->big_user);
    $this->verifyHelp();

    // Login the regular user.
    $this->drupalLogin($this->any_user);
    $this->verifyHelp(403);

    // Check for css on admin/help.
    $this->drupalLogin($this->big_user);
    $this->drupalGet('admin/help');
    $this->assertRaw(drupal_get_path('module', 'help') . '/help.css', 'The help.css file is present in the HTML.');

    // Verify that introductory help text exists, goes for 100% module coverage.
    $this->assertRaw(t('For more information, refer to the specific topics listed in the next section or to the <a href="@drupal">online Drupal handbooks</a>.', array('@drupal' => 'http://drupal.org/documentation')), 'Help intro text correctly appears.');

    // Verify that help topics text appears.
    $this->assertRaw('<h2>' . t('Help topics') . '</h2><p>' . t('Help is available on the following items:') . '</p>', 'Help topics text correctly appears.');

    // Make sure links are properly added for modules implementing hook_help().
    foreach ($this->modules as $module => $name) {
      $this->assertLink($name, 0, format_string('Link properly added to @name (admin/help/@module)', array('@module' => $module, '@name' => $name)));
    }
  }

  /**
   * Verifies the logged in user has access to the various help nodes.
   *
   * @param integer $response
   *   An HTTP response code.
   */
  protected function verifyHelp($response = 200) {
    foreach ($this->modules as $module => $name) {
      // View module help node.
      $this->drupalGet('admin/help/' . $module);
      $this->assertResponse($response);
      if ($response == 200) {
        $this->assertTitle($name . ' | Drupal', format_string('%module title was displayed', array('%module' => $module)));
        $this->assertRaw('<h1 class="page-title">' . t($name) . '</h1>', format_string('%module heading was displayed', array('%module' => $module)));
       }
    }
  }

  /**
   * Gets the list of enabled modules that implement hook_help().
   *
   * @return array
   *   A list of enabled modules.
   */
  protected function getModuleList() {
    $this->modules = array();
    $result = db_query("SELECT name, filename, info FROM {system} WHERE type = 'module' AND status = 1 ORDER BY weight ASC, filename ASC");
    foreach ($result as $module) {
      if (file_exists($module->filename) && function_exists($module->name . '_help')) {
        $fullname = unserialize($module->info);
        $this->modules[$module->name] = $fullname['name'];
      }
    }
  }
}

/**
 * Tests a module without help to verify it is not listed in the help page.
 */
class NoHelpTestCase extends DrupalWebTestCase {
  /**
   * The user who will be created.
   */
  protected $big_user;

  public static function getInfo() {
    return array(
      'name' => 'No help',
      'description' => 'Verify no help is displayed for modules not providing any help.',
      'group' => 'Help',
    );
  }

  function setUp() {
    // Use one of the test modules that do not implement hook_help().
    parent::setUp('menu_test');
    $this->big_user = $this->drupalCreateUser(array('access administration pages'));
  }

  /**
   * Ensures modules not implementing help do not appear on admin/help.
   */
  function testMainPageNoHelp() {
    $this->drupalLogin($this->big_user);

    $this->drupalGet('admin/help');
    $this->assertNoText('Hook menu tests', 'Making sure the test module menu_test does not display a help link in admin/help');
  }
}
