<?php

/**
 * @file
 * Manages displaying online help.
 */

/**
 * Implements hook_menu().
 */
function help_menu() {
  $items['admin/help'] = array(
    'title' => 'Help',
    'description' => 'Reference for usage, configuration, and modules.',
    'page callback' => 'help_main',
    'access arguments' => array('access administration pages'),
    'weight' => 9,
    'file' => 'help.admin.inc',
  );

  foreach (module_implements('help', TRUE) as $module) {
    $items['admin/help/' . $module] = array(
      'title' => $module,
      'page callback' => 'help_page',
      'page arguments' => array(2),
      'access arguments' => array('access administration pages'),
      'type' => MENU_VISIBLE_IN_BREADCRUMB,
      'file' => 'help.admin.inc',
    );
  }

  return $items;
}

/**
 * Implements hook_help().
 */
function help_help($path, $arg) {
  switch ($path) {
    case 'admin/help':
      $output = '<p>' . t('Follow these steps to set up and start using your website:') . '</p>';
      $output .= '<ol>';
      $output .= '<li>' . t('<strong>Configure your website</strong> Once logged in, visit the <a href="@admin">administration section</a>, where you can <a href="@config">customize and configure</a> all aspects of your website.', array('@admin' => url('admin'), '@config' => url('admin/config'))) . '</li>';
      $output .= '<li>' . t('<strong>Enable additional functionality</strong> Next, visit the <a href="@modules">module list</a> and enable features which suit your specific needs. You can find additional modules in the <a href="@download_modules">Drupal modules download section</a>.', array('@modules' => url('admin/modules'), '@download_modules' => 'http://drupal.org/project/modules')) . '</li>';
      $output .= '<li>' . t('<strong>Customize your website design</strong> To change the "look and feel" of your website, visit the <a href="@themes">themes section</a>. You may choose from one of the included themes or download additional themes from the <a href="@download_themes">Drupal themes download section</a>.', array('@themes' => url('admin/appearance'), '@download_themes' => 'http://drupal.org/project/themes')) . '</li>';
      $output .= '<li>' . t('<strong>Start posting content</strong> Finally, you can <a href="@content">add new content</a> for your website.', array('@content' => url('node/add'))) . '</li>';
      $output .= '</ol>';
      $output .= '<p>' . t('For more information, refer to the specific topics listed in the next section or to the <a href="@handbook">online Drupal handbooks</a>. You may also post at the <a href="@forum">Drupal forum</a> or view the wide range of <a href="@support">other support options</a> available.', array('@help' => url('admin/help'), '@handbook' => 'http://drupal.org/documentation', '@forum' => 'http://drupal.org/forum', '@support' => 'http://drupal.org/support')) . '</p>';
      return $output;
    case 'admin/help#help':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Help module provides <a href="@help-page">Help reference pages</a> and context-sensitive advice to guide you through the use and configuration of modules. It is a starting point for the online <a href="@handbook">Drupal handbooks</a>. The handbooks contain more extensive and up-to-date information, are annotated with user-contributed comments, and serve as the definitive reference point for all Drupal documentation. For more information, see the online handbook entry for the <a href="@help">Help module</a>.', array('@help' => 'http://drupal.org/documentation/modules/help/', '@handbook' => 'http://drupal.org/documentation', '@help-page' => url('admin/help'))) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Providing a help reference') . '</dt>';
      $output .= '<dd>' . t('The Help module displays explanations for using each module listed on the main <a href="@help">Help reference page</a>.', array('@help' => url('admin/help'))) . '</dd>';
      $output .= '<dt>' . t('Providing context-sensitive help') . '</dt>';
      $output .= '<dd>' . t('The Help module displays context-sensitive advice and explanations on various pages.') . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}
