
/**
 * @file
 * Basic styling for the Overlay child pages.
 */

html.js {
  background: transparent !important;
  overflow-y: scroll;
}
html.js body {
  background: transparent !important;
  margin-left: 0;
  margin-right: 0;
  padding: 20px 0;
}

#overlay {
  display: table;
  margin: 0 auto;
  min-height: 100px;
  min-width: 700px;
  position: relative;
  padding: .2em;
  padding-bottom: 2em;
  padding-right: 26px; /* LTR */
  width: 88%;
}
#overlay-titlebar {
  padding: 0 20px;
  position: relative;
  white-space: nowrap;
  z-index: 100;
}
#overlay-content {
  background: #fff;
  clear: both;
  color: #000;
  padding: .5em 1em;
  position: relative;
}

#overlay-title-wrapper {
  overflow: hidden;
}
#overlay-title {
  color: #fff;
  float: left; /* LTR */
  font-size: 20px;
  margin: 0;
  padding: 0.3em 0;
}
#overlay-title:active,
#overlay-title:focus {
  outline: 0;
}

.overlay #skip-link {
  margin-top: -20px;
}
.overlay #skip-link a {
  color: #fff; /* This is white to contrast with the dark background behind it. */
}

#overlay-close-wrapper {
  position: absolute;
  right: 0; /* LTR */
}
#overlay-close,
#overlay-close:hover {
  background: transparent url(images/close.png) no-repeat; /* LTR */
  -moz-border-radius-topleft: 0; /* LTR */
  -webkit-border-top-left-radius: 0; /* LTR */
  border-top-left-radius: 0; /* LTR */
  display: block;
  height: 26px;
  margin: 0;
  padding: 0;
  /* Replace with position:fixed to get a scrolling close button. */
  position: absolute;
  width: 26px;
}

/**
 * Tabs on the overlay.
 */
#overlay-tabs {
  line-height: 27px;
  margin: -28px 0 0 0;
  position: absolute;
  right: 20px; /* LTR */
  text-transform: uppercase;
}
#overlay-tabs li {
  display: inline;
  list-style: none;
  margin: 0 0 0 -3px; /* LTR */
  padding: 0;
}
#overlay-tabs li a,
#overlay-tabs li a:active,
#overlay-tabs li a:visited,
#overlay-tabs li a:hover {
  background-color: #a6a7a2;
  -moz-border-radius: 8px 8px 0 0;
  -webkit-border-top-left-radius: 8px;
  -webkit-border-top-right-radius: 8px;
  border-radius: 8px 8px 0 0;
  color: #000;
  display: inline-block;
  font-size: 11px;
  font-weight: bold;
  margin: 0 0 2px 0;
  outline: 0;
  padding: 0 14px;
  text-decoration: none;
}
#overlay-tabs li.active a,
#overlay-tabs li.active a.active,
#overlay-tabs li.active a:active,
#overlay-tabs li.active a:visited {
  background-color: #fff;
  margin: 0;
  padding-bottom: 2px;
}
#overlay-tabs li a:focus,
#overlay-tabs li a:hover {
  color: #fff;
}
#overlay-tabs li.active a:focus,
#overlay-tabs li.active a:hover {
  color: #000;
}

/**
 * Add to shortcuts link
 */
#overlay-titlebar .add-or-remove-shortcuts {
  padding-top: 0.9em;
}

/**
 * IE6 shows elements with position:fixed as position:static so replace
 * it with position:absolute;
 */
* html #overlay-close,
* html #overlay-close:hover {
  position: absolute;
}

/**
 * Disable message.
 */
#overlay-disable-message {
  background-color: #fff;
  margin: -20px auto 20px;
  width: 80%;
  -moz-border-radius: 0 0 8px 8px;
  -webkit-border-bottom-left-radius: 8px;
  -webkit-border-bottom-right-radius: 8px;
  border-radius: 0 0 8px 8px;
}
.overlay-disable-message-focused {
  padding: 0.5em;
}
.overlay-disable-message-focused a {
  display: block;
  float: left;
}
.overlay-disable-message-focused #overlay-dismiss-message {
  float: right;
}
