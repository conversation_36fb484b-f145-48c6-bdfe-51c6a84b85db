<?php

/**
 * @file
 * Displays the Drupal administration interface in an overlay.
 */

/**
 * Implements hook_help().
 */
function overlay_help($path, $arg) {
  switch ($path) {
    case 'admin/help#overlay':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Overlay module makes the administration pages on your site display in a JavaScript overlay of the page you were viewing when you clicked the administrative link, instead of replacing the page in your browser window. Use the close link on the overlay to return to the page you were viewing when you clicked the link. For more information, see the online handbook entry for <a href="@overlay">Overlay module</a>.', array('@overlay' => 'http://drupal.org/documentation/modules/overlay')) . '</p>';
      return $output;
  }
}

/**
 * Implements hook_menu().
 */
function overlay_menu() {
  $items['overlay-ajax/%'] = array(
    'title' => '',
    'page callback' => 'overlay_ajax_render_region',
    'page arguments' => array(1),
    'access arguments' => array('access overlay'),
    'type' => MENU_CALLBACK,
  );
  $items['overlay/dismiss-message'] = array(
    'title' => '',
    'page callback' => 'overlay_user_dismiss_message',
    'access arguments' => array('access overlay'),
    'type' => MENU_CALLBACK,
  );
  return $items;
}

/**
 * Implements hook_admin_paths().
 */
function overlay_admin_paths() {
  $paths = array(
    // This is marked as an administrative path so that if it is visited from
    // within the overlay, the user will stay within the overlay while the
    // callback is being processed.
    'overlay/dismiss-message' => TRUE,
  );
  return $paths;
}

/**
 * Implements hook_permission().
 */
function overlay_permission() {
  return array(
    'access overlay' => array(
      'title' => t('Access the administrative overlay'),
      'description' => t('View administrative pages in the overlay.'),
    ),
  );
}

/**
 * Implements hook_theme().
 */
function overlay_theme() {
  return array(
    'overlay' => array(
      'render element' => 'page',
      'template' => 'overlay',
    ),
    'overlay_disable_message' => array(
      'render element' => 'element',
    ),
  );
}

/**
 * Implements hook_form_alter().
 */
function overlay_form_alter(&$form, &$form_state) {
  // Add a hidden element to prevent dropping out of the overlay when a form is
  // submitted inside the overlay using a GET method.
  if (isset($form['#method']) && $form['#method'] == 'get' && isset($_REQUEST['render']) && $_REQUEST['render'] == 'overlay' && !isset($form['render'])) {
    $form['render'] = array(
      '#type' => 'hidden',
      '#value' => 'overlay',
    );
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function overlay_form_user_profile_form_alter(&$form, &$form_state) {
  if ($form['#user_category'] == 'account') {
    $account = $form['#user'];
    if (user_access('access overlay', $account)) {
      $form['overlay_control'] = array(
        '#type' => 'fieldset',
        '#title' => t('Administrative overlay'),
        '#weight' => 4,
        '#collapsible' => TRUE,
      );

      $form['overlay_control']['overlay'] = array(
        '#type' => 'checkbox',
        '#title' => t('Use the overlay for administrative pages.'),
        '#description' => t('Show administrative pages on top of the page you started from.'),
        '#default_value' => isset($account->data['overlay']) ? $account->data['overlay'] : 1,
      );
    }
  }
}

/**
 * Implements hook_user_presave().
 */
function overlay_user_presave(&$edit, $account, $category) {
  if (isset($edit['overlay'])) {
    $edit['data']['overlay'] = $edit['overlay'];
  }
}

/**
 * Implements hook_init().
 *
 * Determine whether the current page request is destined to appear in the
 * parent window or in the overlay window, and format the page accordingly.
 *
 * @see overlay_set_mode()
 */
function overlay_init() {
  global $user;

  $mode = overlay_get_mode();

  // Only act if the user has access to the overlay and a mode was not already
  // set. Other modules can also enable the overlay directly for other uses.
  $use_overlay = !isset($user->data['overlay']) || $user->data['overlay'];
  if (empty($mode) && user_access('access overlay') && $use_overlay) {
    $current_path = current_path();
    // After overlay is enabled on the modules page, redirect to
    // <front>#overlay=admin/modules to actually enable the overlay.
    if (isset($_SESSION['overlay_enable_redirect']) && $_SESSION['overlay_enable_redirect']) {
      unset($_SESSION['overlay_enable_redirect']);
      drupal_goto('<front>', array('fragment' => 'overlay=' . $current_path));
    }

    if (isset($_GET['render']) && $_GET['render'] == 'overlay') {
      // If a previous page requested that we close the overlay, close it and
      // redirect to the final destination.
      if (isset($_SESSION['overlay_close_dialog'])) {
        call_user_func_array('overlay_close_dialog', $_SESSION['overlay_close_dialog']);
        unset($_SESSION['overlay_close_dialog']);
      }
      // If this page shouldn't be rendered inside the overlay, redirect to the
      // parent.
      elseif (!path_is_admin($current_path)) {
        // Prevent open redirects by ensuring the current path is not an absolute URL.
        if (url_is_external($current_path)) {
          $current_path = '<front>';
        }
        overlay_close_dialog($current_path, array('query' => drupal_get_query_parameters(NULL, array('q', 'render'))));
      }

      // Indicate that we are viewing an overlay child page.
      overlay_set_mode('child');

      // Unset the render parameter to avoid it being included in URLs on the page.
      unset($_GET['render']);
    }
    // Do not enable the overlay if we already are on an admin page.
    elseif (!path_is_admin($current_path)) {
      // Otherwise add overlay parent code and our behavior.
      overlay_set_mode('parent');
    }
  }
}

/**
 * Implements hook_exit().
 *
 * When viewing an overlay child page, check if we need to trigger a refresh of
 * the supplemental regions of the overlay on the next page request.
 */
function overlay_exit() {
  // Check that we are in an overlay child page. Note that this should never
  // return TRUE on a cached page view, since the child mode is not set until
  // overlay_init() is called.
  if (overlay_get_mode() == 'child') {
    // Load any markup that was stored earlier in the page request, via calls
    // to overlay_store_rendered_content(). If none was stored, this is not a
    // page request where we expect any changes to the overlay supplemental
    // regions to have occurred, so we do not need to proceed any further.
    $original_markup = overlay_get_rendered_content();
    if (!empty($original_markup)) {
      // Compare the original markup to the current markup that we get from
      // rendering each overlay supplemental region now. If they don't match,
      // something must have changed, so we request a refresh of that region
      // within the parent window on the next page request.
      foreach (overlay_supplemental_regions() as $region) {
        if (!isset($original_markup[$region]) || $original_markup[$region] != overlay_render_region($region)) {
          overlay_request_refresh($region);
        }
      }
    }
  }
}

/**
 * Implements hook_library().
 */
function overlay_library() {
  $module_path = drupal_get_path('module', 'overlay');

  // Overlay parent.
  $libraries['parent'] = array(
    'title' => 'Overlay: Parent',
    'website' => 'http://drupal.org/documentation/modules/overlay',
    'version' => '1.0',
    'js' => array(
      $module_path . '/overlay-parent.js' => array(),
    ),
    'css' => array(
      $module_path . '/overlay-parent.css' => array(),
    ),
    'dependencies' => array(
      array('system', 'ui'),
      array('system', 'jquery.bbq'),
    ),
  );
  // Overlay child.
  $libraries['child'] = array(
    'title' => 'Overlay: Child',
    'website' => 'http://drupal.org/documentation/modules/overlay',
    'version' => '1.0',
    'js' => array(
      $module_path . '/overlay-child.js' => array(),
    ),
    'css' => array(
      $module_path . '/overlay-child.css' => array(),
    ),
  );

  return $libraries;
}

/**
 * Implements hook_drupal_goto_alter().
 */
function overlay_drupal_goto_alter(&$path, &$options, &$http_response_code) {
  if (overlay_get_mode() == 'child') {
    // The authorize.php script bootstraps Drupal to a very low level, where
    // the PHP code that is necessary to close the overlay properly will not be
    // loaded. Therefore, if we are redirecting to authorize.php inside the
    // overlay, instead redirect back to the current page with instructions to
    // close the overlay there before redirecting to the final destination; see
    // overlay_init().
    if ($path == system_authorized_get_url() || $path == system_authorized_batch_processing_url()) {
      $_SESSION['overlay_close_dialog'] = array($path, $options);
      $path = current_path();
      $options = drupal_get_query_parameters();
    }

    // If the current page request is inside the overlay, add ?render=overlay
    // to the new path, so that it appears correctly inside the overlay.
    if (isset($options['query'])) {
      $options['query'] += array('render' => 'overlay');
    }
    else {
      $options['query'] = array('render' => 'overlay');
    }
  }
}

/**
 * Implements hook_batch_alter().
 *
 * If the current page request is inside the overlay, add ?render=overlay to
 * the success callback URL, so that it appears correctly within the overlay.
 *
 * @see overlay_get_mode()
 */
function overlay_batch_alter(&$batch) {
  if (overlay_get_mode() == 'child') {
    if (isset($batch['url_options']['query'])) {
      $batch['url_options']['query']['render'] = 'overlay';
    }
    else {
      $batch['url_options']['query'] = array('render' => 'overlay');
    }
  }
}

/**
 * Implements hook_page_alter().
 */
function overlay_page_alter(&$page) {
  // If we are limiting rendering to a subset of page regions, deny access to
  // all other regions so that they will not be processed.
  if ($regions_to_render = overlay_get_regions_to_render()) {
    $skipped_regions = array_diff(element_children($page), $regions_to_render);
    foreach ($skipped_regions as $skipped_region) {
      $page[$skipped_region]['#access'] = FALSE;
    }
  }

  $mode = overlay_get_mode();
  if ($mode == 'child') {
    // Add the overlay wrapper before the html wrapper.
    array_unshift($page['#theme_wrappers'], 'overlay');
  }
  elseif ($mode == 'parent' && ($message = overlay_disable_message())) {
    $page['page_top']['disable_overlay'] = $message;
  }
}

/**
 * Page callback: Dismisses the overlay accessibility message for this user.
 *
 * @return
 *   A render array for a page containing a list of content.
 */
function overlay_user_dismiss_message() {
  global $user;
  // It's unlikely, but possible that "access overlay" permission is granted to
  // the anonymous role. In this case, we do not display the message to disable
  // the overlay, so there is nothing to dismiss. Also, protect against
  // cross-site request forgeries by validating a token.
  if (empty($user->uid) || !isset($_GET['token']) || !drupal_valid_token($_GET['token'], 'overlay')) {
    return MENU_ACCESS_DENIED;
  }
  else {
    user_save(user_load($user->uid), array('data' => array('overlay_message_dismissed' => 1)));
    drupal_set_message(t('The message has been dismissed. You can change your overlay settings at any time by visiting your profile page.'));
    // Destination is normally given. Go to the user profile as a fallback.
    drupal_goto('user/' . $user->uid . '/edit');
  }
}

/**
 * Returns a renderable array representing a message for disabling the overlay.
 *
 * If the current user can access the overlay and has not previously indicated
 * that this message should be dismissed, this function returns a message
 * containing a link to disable the overlay. Nothing is returned for anonymous
 * users, because the links control per-user settings. Because some screen
 * readers are unable to properly read overlay contents, site builders are
 * discouraged from granting the "access overlay" permission to the anonymous
 * role.
 *
 * @see http://drupal.org/node/890284
 */
function overlay_disable_message() {
  global $user;

  if (!empty($user->uid) && empty($user->data['overlay_message_dismissed']) && (!isset($user->data['overlay']) || $user->data['overlay']) && user_access('access overlay')) {
    $build = array(
      '#theme' => 'overlay_disable_message',
      '#weight' => -99,
      // Link to the user's profile page, where the overlay can be disabled.
      'profile_link' => array(
        '#type' => 'link',
        '#title' => t('If you have problems accessing administrative pages on this site, disable the overlay on your profile page.'),
        '#href' => 'user/' . $user->uid . '/edit',
        '#options' => array(
          'query' => drupal_get_destination(),
          'fragment' => 'edit-overlay-control',
          'attributes' => array(
            'id' => 'overlay-profile-link',
            // Prevent the target page from being opened in the overlay.
            'class' => array('overlay-exclude'),
          ),
        ),
      ),
      // Link to a menu callback that allows this message to be permanently
      // dismissed for the current user.
      'dismiss_message_link' => array(
        '#type' => 'link',
        '#title' => t('Dismiss this message.'),
        '#href' => 'overlay/dismiss-message',
        '#options' => array(
          'query' => drupal_get_destination() + array(
            // Add a token to protect against cross-site request forgeries.
            'token' => drupal_get_token('overlay'),
          ),
          'attributes' => array(
            'id' => 'overlay-dismiss-message',
            // If this message is being displayed outside the overlay, prevent
            // this link from opening the overlay.
            'class' => (overlay_get_mode() == 'parent') ? array('overlay-exclude') : array(),
          ),
        ),
      )
    );
  }
  else {
    $build = array();
  }

  return $build;
}

/**
 * Returns the HTML for the message about how to disable the overlay.
 *
 * @param $variables
 *   An associative array with an 'element' element, which itself is an
 *   associative array containing:
 *   - profile_link: The link to this user's account.
 *   - dismiss_message_link: The link to dismiss the overlay.
 *
 * @ingroup themeable
 */
function theme_overlay_disable_message($variables) {
  $element = $variables['element'];

  // Add CSS classes to hide the links from most sighted users, while keeping
  // them accessible to screen-reader users and keyboard-only users. To assist
  // screen-reader users, this message appears in both the parent and child
  // documents, but only the one in the child document is part of the tab order.
  foreach (array('profile_link', 'dismiss_message_link') as $key) {
    $element[$key]['#options']['attributes']['class'][] = 'element-invisible';
    if (overlay_get_mode() == 'child') {
      $element[$key]['#options']['attributes']['class'][] = 'element-focusable';
    }
  }

  // Render the links.
  $output = drupal_render($element['profile_link']) . ' ' . drupal_render($element['dismiss_message_link']);

  // Add a heading for screen-reader users. The heading doesn't need to be seen
  // by sighted users.
  $output = '<h3 class="element-invisible">' . t('Options for the administrative overlay') . '</h3>' . $output;

  // Wrap in a container for styling.
  $output = '<div id="overlay-disable-message" class="clearfix">' . $output . '</div>';

  return $output;
}

/**
 * Implements hook_block_list_alter().
 */
function overlay_block_list_alter(&$blocks) {
  // If we are limiting rendering to a subset of page regions, hide all blocks
  // which appear in regions not on that list. Note that overlay_page_alter()
  // does a more comprehensive job of preventing unwanted regions from being
  // displayed (regardless of whether they contain blocks or not), but the
  // reason for duplicating effort here is performance; we do not even want
  // these blocks to be built if they are not going to be displayed.
  if ($regions_to_render = overlay_get_regions_to_render()) {
    foreach ($blocks as $bid => $block) {
      if (!in_array($block->region, $regions_to_render)) {
        unset($blocks[$bid]);
      }
    }
  }
}

/**
 * Implements hook_system_info_alter().
 *
 * Add default regions for the overlay.
 */
function overlay_system_info_alter(&$info, $file, $type) {
  if ($type == 'theme') {
    $info['overlay_regions'][] = 'content';
    $info['overlay_regions'][] = 'help';
  }
}

/**
 * Implements hook_preprocess_html().
 *
 * If the current page request is inside the overlay, add appropriate classes
 * to the <body> element, and simplify the page title.
 *
 * @see overlay_get_mode()
 */
function overlay_preprocess_html(&$variables) {
  if (overlay_get_mode() == 'child') {
    // Add overlay class, so themes can react to being displayed in the overlay.
    $variables['classes_array'][] = 'overlay';
  }
}

/**
 * Implements hook_preprocess_maintenance_page().
 *
 * If the current page request is inside the overlay, add appropriate classes
 * to the <body> element, and simplify the page title.
 *
 * @see overlay_preprocess_maintenance_page()
 */
function overlay_preprocess_maintenance_page(&$variables) {
  overlay_preprocess_html($variables);
}

/**
 * Implements template_preprocess_HOOK() for overlay.tpl.php
 *
 * If the current page request is inside the overlay, add appropriate classes
 * to the <body> element, and simplify the page title.
 *
 * @see template_process_overlay()
 * @see overlay.tpl.php
 */
function template_preprocess_overlay(&$variables) {
  $variables['tabs'] = menu_primary_local_tasks();
  $variables['title'] = drupal_get_title();
  $variables['disable_overlay'] = overlay_disable_message();
  $variables['content_attributes_array']['class'][] = 'clearfix';
}

/**
 * Implements template_process_HOOK() for overlay.tpl.php
 *
 * Places the rendered HTML for the page body into a top level variable.
 *
 * @see template_preprocess_overlay()
 * @see overlay.tpl.php
 */
function template_process_overlay(&$variables) {
  $variables['page'] = $variables['page']['#children'];
}

/**
 * Implements hook_preprocess_page().
 *
 * If the current page request is inside the overlay, hide the tabs.
 *
 * @see overlay_get_mode()
 */
function overlay_preprocess_page(&$variables) {
  if (overlay_get_mode() == 'child') {
    unset($variables['tabs']['#primary']);
  }
}

/**
 * Stores and returns whether an empty page override is needed.
 *
 * This is used to prevent a page request which closes the overlay (for
 * example, a form submission) from being fully re-rendered before the overlay
 * is closed. Instead, we store a variable which will cause the page to be
 * rendered by a delivery callback function that does not actually print
 * visible HTML (but rather only the bare minimum scripts and styles necessary
 * to trigger the overlay to close), thereby allowing the dialog to be closed
 * faster and with less interruption, and also allowing the display of messages
 * to be deferred to the parent window (rather than displaying them in the
 * child window, which will close before the user has had a chance to read
 * them).
 *
 * @param $value
 *   By default, an empty page will not be displayed. Set to TRUE to request
 *   an empty page display, or FALSE to disable the empty page display (if it
 *   was previously enabled on this page request).
 *
 * @return
 *   TRUE if the current behavior is to display an empty page, or FALSE if not.
 *
 * @see overlay_page_delivery_callback_alter()
 */
function overlay_display_empty_page($value = NULL) {
  $display_empty_page = &drupal_static(__FUNCTION__, FALSE);
  if (isset($value)) {
    $display_empty_page = $value;
  }
  return $display_empty_page;
}

/**
 * Implements hook_page_delivery_callback_alter().
 */
function overlay_page_delivery_callback_alter(&$callback) {
  if (overlay_display_empty_page()) {
    $callback = 'overlay_deliver_empty_page';
  }
}

/**
 * Prints an empty page.
 *
 * This function is used to print out a bare minimum empty page which still has
 * the scripts and styles necessary in order to trigger the overlay to close.
 */
function overlay_deliver_empty_page() {
  $empty_page = '<html><head><title></title>' . drupal_get_css() . drupal_get_js() . '</head><body class="overlay"></body></html>';
  print $empty_page;
  drupal_exit();
}

/**
 * Gets the current overlay mode.
 *
 * @see overlay_set_mode()
 */
function overlay_get_mode() {
  return overlay_set_mode(NULL);
}

/**
 * Sets the overlay mode and adds proper JavaScript and styles to the page.
 *
 * Note that since setting the overlay mode triggers a variety of behaviors
 * (including hooks being invoked), it can only be done once per page request.
 * Therefore, the first call to this function which passes along a value of the
 * $mode parameter controls the overlay mode that will be used.
 *
 * @param $mode
 *   To set the mode, pass in one of the following values:
 *   - 'parent': This is used in the context of a parent window (a regular
 *     browser window). If set, JavaScript is added so that administrative
 *     links in the parent window will open in an overlay.
 *   - 'child': This is used in the context of the child overlay window (the
 *     page actually appearing within the overlay iframe). If set, JavaScript
 *     and CSS are added so that Drupal behaves nicely from within the overlay.
 *   - 'none': This is used to avoid adding any overlay-related code to the
 *     page at all. Modules can set this to explicitly prevent the overlay from
 *     being used. For example, since the overlay module itself sets the mode
 *     to 'parent' or 'child' in overlay_init() when certain conditions are
 *     met, other modules which want to override that behavior can do so by
 *     setting the mode to 'none' earlier in the page request - e.g., in their
 *     own hook_init() implementations, if they have a lower weight.
 *   This parameter is optional, and if omitted, the current mode will be
 *   returned with no action taken.
 *
 * @return
 *   The current mode, if any has been set, or NULL if no mode has been set.
 *
 * @ingroup overlay_api
 * @see overlay_init()
 */
function overlay_set_mode($mode = NULL) {
  global $base_path;
  $overlay_mode = &drupal_static(__FUNCTION__);

  // Make sure external resources are not included more than once. Also return
  // the current mode, if no mode was specified.
  if (isset($overlay_mode) || !isset($mode)) {
    return $overlay_mode;
  }
  $overlay_mode = $mode;

  switch ($overlay_mode) {
    case 'parent':
      drupal_add_library('overlay', 'parent');

      // Allow modules to act upon overlay events.
      module_invoke_all('overlay_parent_initialize');
      break;

    case 'child':
      drupal_add_library('overlay', 'child');

      // Allow modules to act upon overlay events.
      module_invoke_all('overlay_child_initialize');
      break;
  }
  return $overlay_mode;
}

/**
 * Implements hook_overlay_parent_initialize().
 */
function overlay_overlay_parent_initialize() {
  // Let the client side know which paths are administrative.
  $paths = path_get_admin_paths();
  foreach ($paths as &$type) {
    $type = str_replace('<front>', variable_get('site_frontpage', 'node'), $type);
  }
  drupal_add_js(array('overlay' => array('paths' => $paths)), 'setting');
  $path_prefixes = array();
  if (module_exists('locale') && variable_get('locale_language_negotiation_url_part', LOCALE_LANGUAGE_NEGOTIATION_URL_PREFIX) == LOCALE_LANGUAGE_NEGOTIATION_URL_PREFIX) {
    // Get languages grouped by status and select only the enabled ones.
    $languages = language_list('enabled');
    $languages = $languages[1];

    $path_prefixes = array();
    foreach ($languages as $language) {
      if ($language->prefix) {
        $path_prefixes[] = $language->prefix;
      }
    }
  }
  drupal_add_js(array('overlay' => array('pathPrefixes' => $path_prefixes)), 'setting');
  // Pass along the Ajax callback for rerendering sections of the parent window.
  drupal_add_js(array('overlay' => array('ajaxCallback' => 'overlay-ajax')), 'setting');
}

/**
 * Implements hook_overlay_child_initialize().
 */
function overlay_overlay_child_initialize() {
  // Check if the parent window needs to refresh any page regions on this page
  // request.
  overlay_trigger_refresh();
  // If this is a POST request, or a GET request with a token parameter, we
  // have an indication that something in the supplemental regions of the
  // overlay might change during the current page request. We therefore store
  // the initial rendered content of those regions here, so that we can compare
  // it to the same content rendered in overlay_exit(), at the end of the page
  // request. This allows us to check if anything actually did change, and, if
  // so, trigger an immediate Ajax refresh of the parent window.
  if (!empty($_POST) || isset($_GET['token'])) {
    foreach (overlay_supplemental_regions() as $region) {
      overlay_store_rendered_content($region, overlay_render_region($region));
    }
    // In addition, notify the parent window that when the overlay closes,
    // the entire parent window should be refreshed.
    overlay_request_page_refresh();
  }
  // Indicate that when the main page rendering occurs later in the page
  // request, only the regions that appear within the overlay should be
  // rendered.
  overlay_set_regions_to_render(overlay_regions());
}

/**
 * Requests that the overlay closes when the page is displayed.
 *
 * @param $redirect
 *   (optional) The path that should open in the parent window after the
 *   overlay closes. If not set, no redirect will be performed on the parent
 *   window.
 *
 * @param $redirect_options
 *   (optional) An associative array of options to use when generating the
 *   redirect URL.
 */
function overlay_close_dialog($redirect = NULL, $redirect_options = array()) {
  $settings = array(
    'overlayChild' => array(
      'closeOverlay' => TRUE,
    ),
  );

  // Tell the child window to perform the redirection when requested to.
  if (isset($redirect)) {
    $settings['overlayChild']['redirect'] = url($redirect, $redirect_options);
  }

  drupal_add_js($settings, array('type' => 'setting'));

  // Since we are closing the overlay as soon as the page is displayed, we do
  // not want to show any of the page's actual content.
  overlay_display_empty_page(TRUE);
}

/**
 * Returns a list of page regions that appear in the overlay.
 *
 * Overlay regions correspond to the entire contents of the overlay child
 * window and are refreshed each time a new page request is made within the
 * overlay.
 *
 * @return
 *   An array of region names that correspond to those which appear in the
 *   overlay, within the theme that is being used to display the current page.
 *
 * @see overlay_supplemental_regions()
 */
function overlay_regions() {
  return _overlay_region_list('overlay_regions');
}

/**
 * Returns a list of supplemental page regions for the overlay.
 *
 * Supplemental overlay regions are those which are technically part of the
 * parent window, but appear to the user as being related to the overlay
 * (usually because they are displayed next to, rather than underneath, the
 * main overlay regions) and therefore need to be dynamically refreshed if any
 * administrative actions taken within the overlay change their contents.
 *
 * An example of a typical overlay supplemental region would be the 'page_top'
 * region, in the case where a toolbar is being displayed there.
 *
 * @return
 *   An array of region names that correspond to supplemental overlay regions,
 *   within the theme that is being used to display the current page.
 *
 * @see overlay_regions()
 */
function overlay_supplemental_regions() {
  return _overlay_region_list('overlay_supplemental_regions');
}

/**
 * Returns a list of page regions related to the overlay.
 *
 * @param $type
 *   The type of regions to return. This can either be 'overlay_regions' or
 *   'overlay_supplemental_regions'.
 *
 * @return
 *   An array of region names of the given type, within the theme that is being
 *   used to display the current page.
 *
 * @see overlay_regions()
 * @see overlay_supplemental_regions()
 */
function _overlay_region_list($type) {
  // Obtain the current theme. We need to first make sure the theme system is
  // initialized, since this function can be called early in the page request.
  drupal_theme_initialize();
  $themes = list_themes();
  $theme = $themes[$GLOBALS['theme']];
  // Return the list of regions stored within the theme's info array, or an
  // empty array if no regions of the appropriate type are defined.
  return !empty($theme->info[$type]) ? $theme->info[$type] : array();
}

/**
 * Returns a list of page regions that rendering should be limited to.
 *
 * @return
 *   An array containing the names of the regions that will be rendered when
 *   drupal_render_page() is called. If empty, then no limits will be imposed,
 *   and all regions of the page will be rendered.
 *
 * @see overlay_page_alter()
 * @see overlay_block_list_alter()
 * @see overlay_set_regions_to_render()
 */
function overlay_get_regions_to_render() {
  return overlay_set_regions_to_render();
}

/**
 * Sets the regions of the page that rendering will be limited to.
 *
 * @param $regions
 *   (Optional) An array containing the names of the regions that should be
 *   rendered when drupal_render_page() is called. Pass in an empty array to
 *   remove all limits and cause drupal_render_page() to render all page
 *   regions (the default behavior). If this parameter is omitted, no change
 *   will be made to the current list of regions to render.
 *
 * @return
 *   The current list of regions to render, or an empty array if the regions
 *   are not being limited.
 *
 * @see overlay_page_alter()
 * @see overlay_block_list_alter()
 * @see overlay_get_regions_to_render()
 */
function overlay_set_regions_to_render($regions = NULL) {
  $regions_to_render = &drupal_static(__FUNCTION__, array());
  if (isset($regions)) {
    $regions_to_render = $regions;
  }
  return $regions_to_render;
}

/**
 * Renders an individual page region.
 *
 * This function is primarily intended to be used for checking the content of
 * supplemental overlay regions (e.g., a region containing a toolbar). Passing
 * in a region that is intended to display the main page content is not
 * supported; the region will be rendered by this function, but the main page
 * content will not appear in it. In addition, although this function returns
 * the rendered HTML for the provided region, it does not place it on the final
 * page, nor add any of its associated JavaScript or CSS to the page.
 *
 * @param $region
 *   The name of the page region that should be rendered.
 *
 * @return
 *   The rendered HTML of the provided region.
 */
function overlay_render_region($region) {
  // Indicate the region that we will be rendering, so that other regions will
  // be hidden by overlay_page_alter() and overlay_block_list_alter().
  overlay_set_regions_to_render(array($region));
  // Do what is necessary to force drupal_render_page() to only display HTML
  // from the requested region. Specifically, declare that the main page
  // content does not need to automatically be added to the page, and pass in
  // a page array that has all theme functions removed (so that overall HTML
  // for the page will not be added either).
  $system_main_content_added = &drupal_static('system_main_content_added');
  $system_main_content_added = TRUE;
  $page = array(
    '#type' => 'page',
    '#theme' => NULL,
    '#theme_wrappers' => array(),
  );
  // Render the region, but do not cache any JavaScript or CSS associated with
  // it. This region might not be included the next time drupal_render_page()
  // is called, and we do not want its JavaScript or CSS to erroneously appear
  // on the final rendered page.
  $original_js = drupal_add_js();
  $original_css = drupal_add_css();
  $original_libraries = drupal_static('drupal_add_library');
  $js = &drupal_static('drupal_add_js');
  $css = &drupal_static('drupal_add_css');
  $libraries = &drupal_static('drupal_add_library');
  $markup = drupal_render_page($page);
  $js = $original_js;
  $css = $original_css;
  $libraries = $original_libraries;
  // Indicate that the main page content has not, in fact, been displayed, so
  // that future calls to drupal_render_page() will be able to render it
  // correctly.
  $system_main_content_added = FALSE;
  // Restore the original behavior of rendering all regions for the next time
  // drupal_render_page() is called.
  overlay_set_regions_to_render(array());
  return $markup;
}

/**
 * Returns any rendered content that was stored earlier in the page request.
 *
 * @return
 *   An array of all rendered HTML that was stored earlier in the page request,
 *   keyed by the identifier with which it was stored. If no content was
 *   stored, an empty array is returned.
 *
 * @see overlay_store_rendered_content()
 */
function overlay_get_rendered_content() {
  return overlay_store_rendered_content();
}

/**
 * Stores strings representing rendered HTML content.
 *
 * This function is used to keep a static cache of rendered content that can be
 * referred to later in the page request.
 *
 * @param $id
 *   (Optional) An identifier for the content which is being stored, which will
 *   be used as an array key in the returned array. If omitted, no change will
 *   be made to the current stored data.
 * @param $content
 *   (Optional) A string representing the rendered data to store. This only has
 *   an effect if $id is also provided.
 *
 * @return
 *   An array representing all data that is currently being stored, or an empty
 *   array if there is none.
 *
 * @see overlay_get_rendered_content()
 */
function overlay_store_rendered_content($id = NULL, $content = NULL) {
  $rendered_content = &drupal_static(__FUNCTION__, array());
  if (isset($id)) {
    $rendered_content[$id] = $content;
  }
  return $rendered_content;
}

/**
 * Requests that the parent window refreshes a particular page region.
 *
 * @param $region
 *   The name of the page region to refresh. The parent window will trigger a
 *   refresh of this region on the next page load.
 *
 * @see overlay_trigger_refresh()
 * @see Drupal.overlay.refreshRegions()
 */
function overlay_request_refresh($region) {
  $class = drupal_region_class($region);
  $_SESSION['overlay_regions_to_refresh'][] = array($class => $region);
}

/**
 * Requests that the entire parent window is reloaded when the overlay closes.
 *
 * @see overlay_trigger_refresh()
 */
function overlay_request_page_refresh() {
  $_SESSION['overlay_refresh_parent'] = TRUE;
}

/**
 * Checks if the parent window needs to be refreshed on this page load.
 *
 * If the previous page load requested that any page regions be refreshed, or
 * if it requested that the entire page be refreshed when the overlay closes,
 * pass that request via JavaScript to the child window, so it can in turn pass
 * the request to the parent window.
 *
 * @see overlay_request_refresh()
 * @see overlay_request_page_refresh()
 * @see Drupal.overlay.refreshRegions()
 */
function overlay_trigger_refresh() {
  if (!empty($_SESSION['overlay_regions_to_refresh'])) {
    $settings = array(
      'overlayChild' => array(
        'refreshRegions' => $_SESSION['overlay_regions_to_refresh'],
      ),
    );
    drupal_add_js($settings, array('type' => 'setting'));
    unset($_SESSION['overlay_regions_to_refresh']);
  }
  if (!empty($_SESSION['overlay_refresh_parent'])) {
    drupal_add_js(array('overlayChild' => array('refreshPage' => TRUE)), array('type' => 'setting'));
    unset($_SESSION['overlay_refresh_parent']);
  }
}

/**
 * Prints the markup obtained by rendering a single region of the page.
 *
 * This function is intended to be called via Ajax.
 *
 * @param $region
 *   The name of the page region to render.
 *
 * @see Drupal.overlay.refreshRegions()
 */
function overlay_ajax_render_region($region) {
  print overlay_render_region($region);
}
