
/**
 * @file
 * RTL styling for Overlay child pages.
 */

html {
  direction: rtl;
}

#overlay-title {
  float: right;
  left: auto;
}
#overlay {
  padding: 0.2em;
  padding-left: 26px;
}
#overlay-close-wrapper {
  left: 0;
  right: auto;
}
#overlay-close,
#overlay-close:hover {
  background: transparent url(images/close-rtl.png) no-repeat;
  -moz-border-radius-topright: 0;
  -webkit-border-top-right-radius: 0;
  border-top-right-radius: 0;
}

/**
 * Tabs on the overlay.
 */
#overlay-tabs {
  left: 20px;
  right: auto;
}
#overlay-tabs li {
  margin: 0 -3px 0 0;
}
