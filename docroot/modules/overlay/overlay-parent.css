
/**
 * @file
 * Basic styling for the Overlay module.
 */

html.overlay-open,
html.overlay-open body {
  height: 100%;
  overflow: hidden;
}

#overlay-container,
.overlay-modal-background,
.overlay-element {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 500;
}

.overlay-modal-background {
  /* Using a transparent png renders faster than using opacity */
  background: transparent url(images/background.png) repeat;
}

.overlay-element {
  background: transparent;
  left: -200%;
  z-index: 501;
}
.overlay-element.overlay-active {
  left: 0;
}

html.overlay-open .displace-top,
html.overlay-open .displace-bottom {
  z-index: 600;
}

/**
 * Within the overlay parent, the message about disabling the overlay is for
 * screen-reader users only. It is always kept invisible with the
 * element-invisible class, and removed from the tab order. Overlay-child.css
 * contains styling for the same message appearing within the overlay, and
 * intended for sighted users.
 */
#overlay-disable-message {
  display: none;
}
html.overlay-open #overlay-disable-message {
  display: block;
}
