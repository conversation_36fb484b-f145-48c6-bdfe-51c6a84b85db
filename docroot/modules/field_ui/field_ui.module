<?php
/**
 * @file
 * Allows administrators to attach custom fields to fieldable types.
 */

/**
 * Implements hook_help().
 */
function field_ui_help($path, $arg) {
  switch ($path) {
    case 'admin/help#field_ui':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Field UI module provides an administrative user interface (UI) for attaching and managing fields. Fields can be defined at the content-type level for content items and comments, at the vocabulary level for taxonomy terms, and at the site level for user accounts. Other modules may also enable fields to be defined for their data. Field types (text, image, number, etc.) are defined by modules, and collected and managed by the <a href="@field">Field module</a>. For more information, see the online handbook entry for <a href="@field_ui" target="_blank">Field UI module</a>.', array('@field' => url('admin/help/field'), '@field_ui' => 'http://drupal.org/documentation/modules/field-ui')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Planning fields') . '</dt>';
      $output .= '<dd>' . t('There are several decisions you will need to make before defining a field for content, comments, etc.:') . '<dl>';
      $output .= '<dt>' . t('What the field will be called') . '</dt>';
      $output .= '<dd>' . t('A field has a <em>label</em> (the name displayed in the user interface) and a <em>machine name</em> (the name used internally). The label can be changed after you create the field, if needed, but the machine name cannot be changed after you have created the field.') . '</li>';
      $output .= '<dt>' . t('What type of data the field will store') . '</dt>';
      $output .= '<dd>' . t('Each field can store one type of data (text, number, file, etc.). When you define a field, you choose a particular <em>field type</em>, which corresponds to the type of data you want to store. The field type cannot be changed after you have created the field.') . '</dd>';
      $output .= '<dt>' . t('How the data will be input and displayed') . '</dt>';
      $output .= '<dd>' . t('Each field type has one or more available <em>widgets</em> associated with it; each widget provides a mechanism for data input when you are editing (text box, select list, file upload, etc.). Each field type also has one or more display options, which determine how the field is displayed to site visitors. The widget and display options can be changed after you have created the field.') . '</dd>';
      $output .= '<dt>' . t('How many values the field will store') . '</dt>';
      $output .= '<dd>' . t('You can store one value, a specific maximum number of values, or an unlimited number of values in each field. For example, an employee identification number field might store a single number, whereas a phone number field might store multiple phone numbers. This setting can be changed after you have created the field, but if you reduce the maximum number of values, you may lose information.') . '</dd>';
      $output .= '</dl>';
      $output .= '<dt>' . t('Reusing fields') . '</dt>';
      $output .= '<dd>' . t('Once you have defined a field, you can reuse it. For example, if you define a custom image field for one content type, and you need to have an image field with the same parameters on another content type, you can add the same field to the second content type, in the <em>Add existing field</em> area of the user interface. You could also add this field to a taxonomy vocabulary, comments, user accounts, etc.') . '</dd>';
      $output .= '<dd>' . t('Some settings of a reused field are unique to each use of the field; others are shared across all places you use the field. For example, the label of a text field is unique to each use, while the setting for the number of values is shared.') . '</dd>';
      $output .= '<dd>' . t('There are two main reasons for reusing fields. First, reusing fields can save you time over defining new fields. Second, reusing fields also allows you to display, filter, group, and sort content together by field across content types. For example, the contributed Views module allows you to create lists and tables of content. So if you use the same field on multiple content types, you can create a View containing all of those content types together displaying that field, sorted by that field, and/or filtered by that field.') . '</dd>';
      $output .= '<dt>' . t('Fields on content items') . '</dt>';
      $output .= '<dd>' . t('Fields on content items are defined at the content-type level, on the <em>Manage fields</em> tab of the content type edit page (which you can reach from the <a href="@types">Content types page</a>). When you define a field for a content type, each content item of that type will have that field added to it. Some fields, such as the Title and Body, are provided for you when you create a content type, or are provided on content types created by your installation profile.', array('@types' => url('admin/structure/types'))) . '</dd>';
      $output .= '<dt>' . t('Fields on taxonomy terms') . '</dt>';
      $output .= '<dd>' . t('Fields on taxonomy terms are defined at the taxonomy vocabulary level, on the <em>Manage fields</em> tab of the vocabulary edit page (which you can reach from the <a href="@taxonomy">Taxonomy page</a>). When you define a field for a vocabulary, each term in that vocabulary will have that field added to it. For example, you could define an image field for a vocabulary to store an icon with each term.', array('@taxonomy' => url('admin/structure/taxonomy'))) . '</dd>';
      $output .= '<dt>' . t('Fields on user accounts') . '</dt>';
      $output .= '<dd>' . t('Fields on user accounts are defined on a site-wide basis on the <a href="@fields">Manage fields tab</a> of the <a href="@accounts">Account settings</a> page. When you define a field for user accounts, each user account will have that field added to it. For example, you could add a long text field to allow users to include a biography.', array('@fields' => url('admin/config/people/accounts/fields'), '@accounts' => url('admin/config/people/accounts'))) . '</dd>';
      $output .= '<dt>' . t('Fields on comments') . '</dt>';
      $output .= '<dd>' . t('Fields on comments are defined at the content-type level, on the <em>Comment fields</em> tab of the content type edit page (which you can reach from the <a href="@types">Content types page</a>). When you add a field for comments, each comment on a content item of that type will have that field added to it. For example, you could add a website field to the comments on forum posts, to allow forum commenters to add a link to their website.', array('@types' => url('admin/structure/types'))) . '</dd>';
      $output .= '</dl>';
      return $output;

    case 'admin/reports/fields':
      return '<p>' . t('This list shows all fields currently in use for easy reference.') . '</p>';
  }
}

/**
 * Implements hook_field_attach_rename_bundle().
 */
function field_ui_field_attach_rename_bundle($entity_type, $bundle_old, $bundle_new) {
  // The Field UI relies on entity_get_info() to build menu items for entity
  // field administration pages. Clear the entity info cache and ensure that
  // the menu is rebuilt.
  entity_info_cache_clear();
  menu_rebuild();
}

/**
 * Implements hook_menu().
 */
function field_ui_menu() {
  $items['admin/reports/fields'] = array(
    'title' => 'Field list',
    'description' => 'Overview of fields on all entity types.',
    'page callback' => 'field_ui_fields_list',
    'access arguments' => array('administer content types'),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'field_ui.admin.inc',
  );

  // Ensure the following is not executed until field_bundles is working and
  // tables are updated. Needed to avoid errors on initial installation.
  if (defined('MAINTENANCE_MODE')) {
    return $items;
  }

  // Create tabs for all possible bundles.
  foreach (entity_get_info() as $entity_type => $entity_info) {
    if ($entity_info['fieldable']) {
      foreach ($entity_info['bundles'] as $bundle_name => $bundle_info) {
        if (isset($bundle_info['admin'])) {
          // Extract path information from the bundle.
          $path = $bundle_info['admin']['path'];
          // Different bundles can appear on the same path (e.g. %node_type and
          // %comment_node_type). To allow field_ui_menu_load() to extract the
          // actual bundle object from the translated menu router path
          // arguments, we need to identify the argument position of the bundle
          // name string ('bundle argument') and pass that position to the menu
          // loader. The position needs to be casted into a string; otherwise it
          // would be replaced with the bundle name string.
          if (isset($bundle_info['admin']['bundle argument'])) {
            $bundle_arg = $bundle_info['admin']['bundle argument'];
            $bundle_pos = (string) $bundle_arg;
          }
          else {
            $bundle_arg = $bundle_name;
            $bundle_pos = '0';
          }
          // This is the position of the %field_ui_menu placeholder in the
          // items below.
          $field_position = count(explode('/', $path)) + 1;

          // Extract access information, providing defaults.
          $access = array_intersect_key($bundle_info['admin'], drupal_map_assoc(array('access callback', 'access arguments')));
          $access += array(
            'access callback' => 'user_access',
            'access arguments' => array('administer fields'),
          );

          // Add the "administer fields" permission on top of the access
          // restriction because the field UI should only be accessible to
          // trusted users.
          if ($access['access callback'] != 'user_access' || $access['access arguments'] != array('administer fields')) {
            $access = array(
              'access callback' => 'field_ui_admin_access',
              'access arguments' => array($access['access callback'], $access['access arguments']),
            );
          }

          $items["$path/fields"] = array(
            'title' => 'Manage fields',
            'page callback' => 'drupal_get_form',
            'page arguments' => array('field_ui_field_overview_form', $entity_type, $bundle_arg),
            'type' => MENU_LOCAL_TASK,
            'weight' => 1,
            'file' => 'field_ui.admin.inc',
          ) + $access;
          $items["$path/fields/%field_ui_menu"] = array(
            'load arguments' => array($entity_type, $bundle_arg, $bundle_pos, '%map'),
            'title callback' => 'field_ui_menu_title',
            'title arguments' => array($field_position),
            'page callback' => 'drupal_get_form',
            'page arguments' => array('field_ui_field_edit_form', $field_position),
            'file' => 'field_ui.admin.inc',
          ) + $access;
          $items["$path/fields/%field_ui_menu/edit"] = array(
            'load arguments' => array($entity_type, $bundle_arg, $bundle_pos, '%map'),
            'title' => 'Edit',
            'page callback' => 'drupal_get_form',
            'page arguments' => array('field_ui_field_edit_form', $field_position),
            'type' => MENU_DEFAULT_LOCAL_TASK,
            'file' => 'field_ui.admin.inc',
          ) + $access;
          $items["$path/fields/%field_ui_menu/field-settings"] = array(
            'load arguments' => array($entity_type, $bundle_arg, $bundle_pos, '%map'),
            'title' => 'Field settings',
            'page callback' => 'drupal_get_form',
            'page arguments' => array('field_ui_field_settings_form', $field_position),
            'type' => MENU_LOCAL_TASK,
            'file' => 'field_ui.admin.inc',
          ) + $access;
          $items["$path/fields/%field_ui_menu/widget-type"] = array(
            'load arguments' => array($entity_type, $bundle_arg, $bundle_pos, '%map'),
            'title' => 'Widget type',
            'page callback' => 'drupal_get_form',
            'page arguments' => array('field_ui_widget_type_form', $field_position),
            'type' => MENU_LOCAL_TASK,
            'file' => 'field_ui.admin.inc',
          ) + $access;
          $items["$path/fields/%field_ui_menu/delete"] = array(
            'load arguments' => array($entity_type, $bundle_arg, $bundle_pos, '%map'),
            'title' => 'Delete',
            'page callback' => 'drupal_get_form',
            'page arguments' => array('field_ui_field_delete_form', $field_position),
            'type' => MENU_LOCAL_TASK,
            'weight' => 10,
            'file' => 'field_ui.admin.inc',
          ) + $access;

          // 'Manage display' tab.
          $items["$path/display"] = array(
            'title' => 'Manage display',
            'page callback' => 'drupal_get_form',
            'page arguments' => array('field_ui_display_overview_form', $entity_type, $bundle_arg, 'default'),
            'type' => MENU_LOCAL_TASK,
            'weight' => 2,
            'file' => 'field_ui.admin.inc',
          ) + $access;

          // View modes secondary tabs.
          // The same base $path for the menu item (with a placeholder) can be
          // used for all bundles of a given entity type; but depending on
          // administrator settings, each bundle has a different set of view
          // modes available for customisation. So we define menu items for all
          // view modes, and use an access callback to determine which ones are
          // actually visible for a given bundle.
          $weight = 0;
          $view_modes = array('default' => array('label' => t('Default'))) + $entity_info['view modes'];
          foreach ($view_modes as $view_mode => $view_mode_info) {
            $items["$path/display/$view_mode"] = array(
              'title' => $view_mode_info['label'],
              'page arguments' => array('field_ui_display_overview_form', $entity_type, $bundle_arg, $view_mode),
              // The access callback needs to check both the current 'custom
              // display' setting for the view mode, and the overall access
              // rules for the bundle admin pages.
              'access callback' => '_field_ui_view_mode_menu_access',
              'access arguments' => array_merge(array($entity_type, $bundle_arg, $view_mode, $access['access callback']), $access['access arguments']),
              'type' => ($view_mode == 'default' ? MENU_DEFAULT_LOCAL_TASK : MENU_LOCAL_TASK),
              'weight' => ($view_mode == 'default' ? -10 : $weight++),
              'file' => 'field_ui.admin.inc',
            );
          }
        }
      }
    }
  }
  return $items;
}

/**
 * Menu loader; Load a field instance based on field and bundle name.
 *
 * @param $field_name
 *   The name of the field, as contained in the path.
 * @param $entity_type
 *   The name of the entity.
 * @param $bundle_name
 *   The name of the bundle, as contained in the path.
 * @param $bundle_pos
 *   The position of $bundle_name in $map.
 * @param $map
 *   The translated menu router path argument map.
 *
 * @return
 *   The field instance array.
 *
 * @ingroup field
 */
function field_ui_menu_load($field_name, $entity_type, $bundle_name, $bundle_pos, $map) {
  // Extract the actual bundle name from the translated argument map.
  // The menu router path to manage fields of an entity can be shared among
  // multiple bundles. For example:
  // - admin/structure/types/manage/%node_type/fields/%field_ui_menu
  // - admin/structure/types/manage/%comment_node_type/fields/%field_ui_menu
  // The menu system will automatically load the correct bundle depending on the
  // actual path arguments, but this menu loader function only receives the node
  // type string as $bundle_name, which is not the bundle name for comments.
  // We therefore leverage the dynamically translated $map provided by the menu
  // system to retrieve the actual bundle and bundle name for the current path.
  if ($bundle_pos > 0) {
    $bundle = $map[$bundle_pos];
    $bundle_name = field_extract_bundle($entity_type, $bundle);
  }
  // Check whether the field exists at all.
  if ($field = field_info_field($field_name)) {
    // Only return the field if a field instance exists for the given entity
    // type and bundle.
    if ($instance = field_info_instance($entity_type, $field_name, $bundle_name)) {
      return $instance;
    }
  }
  return FALSE;
}

/**
 * Menu title callback.
 */
function field_ui_menu_title($instance) {
  return $instance['label'];
}

/**
 * Menu access callback for the 'view mode display settings' pages.
 */
function _field_ui_view_mode_menu_access($entity_type, $bundle, $view_mode, $access_callback) {
  // It's good practice to call func_get_args() at the beginning of a function
  // to avoid problems with function parameters being modified later. The
  // behavior of func_get_args() changed in PHP7.
  // @see https://www.php.net/manual/en/migration70.incompatible.php#migration70.incompatible.other.func-parameter-modified
  $all_args = func_get_args();

  // First, determine visibility according to the 'use custom display'
  // setting for the view mode.
  $bundle = field_extract_bundle($entity_type, $bundle);
  $view_mode_settings = field_view_mode_settings($entity_type, $bundle);
  $visibility = ($view_mode == 'default') || !empty($view_mode_settings[$view_mode]['custom_settings']);

  // Then, determine access according to the $access parameter. This duplicates
  // part of _menu_check_access().
  if ($visibility) {
    // Grab the variable 'access arguments' part.
    $args = array_slice($all_args, 4);
    $callback = empty($access_callback) ? 0 : trim($access_callback);
    if (is_numeric($callback)) {
      return (bool) $callback;
    }
    else {
      // As call_user_func_array() is quite slow and user_access is a very
      // common callback, it is worth making a special case for it.
      if ($access_callback == 'user_access') {
        return (count($args) == 1) ? user_access($args[0]) : user_access($args[0], $args[1]);
      }
      elseif (function_exists($access_callback)) {
        return call_user_func_array($access_callback, $args);
      }
    }
  }
}

/**
 * Implements hook_theme().
 */
function field_ui_theme() {
  return array(
    'field_ui_table' => array(
      'render element' => 'elements',
    ),
  );
}

/**
 * Implements hook_element_info().
 */
function field_ui_element_info() {
  return array(
    'field_ui_table' => array(
      '#theme' => 'field_ui_table',
      '#pre_render' => array('field_ui_table_pre_render'),
      '#regions' => array('' => array()),
    ),
  );
}

/**
 * Implements hook_field_attach_create_bundle().
 */
function field_ui_field_attach_create_bundle($entity_type, $bundle) {
  // When a new bundle is created, the menu needs to be rebuilt to add our
  // menu item tabs.
  variable_set('menu_rebuild_needed', TRUE);
}

/**
 * Determines the administration path for a bundle.
 */
function _field_ui_bundle_admin_path($entity_type, $bundle_name) {
  $bundles = field_info_bundles($entity_type);
  $bundle_info = $bundles[$bundle_name];
  if (isset($bundle_info['admin'])) {
    return isset($bundle_info['admin']['real path']) ? $bundle_info['admin']['real path'] : $bundle_info['admin']['path'];
  }
}

/**
 * Identifies inactive fields within a bundle.
 */
function field_ui_inactive_instances($entity_type, $bundle_name = NULL) {
  $params = array('entity_type' => $entity_type);

  if (empty($bundle_name)) {
    $active = field_info_instances($entity_type);
    $inactive = array();
  }
  else {
    // Restrict to the specified bundle. For consistency with the case where
    // $bundle_name is NULL, the $active and  $inactive arrays are keyed by
    // bundle name first.
    $params['bundle'] = $bundle_name;
    $active = array($bundle_name => field_info_instances($entity_type, $bundle_name));
    $inactive = array($bundle_name => array());
  }

  // Iterate on existing definitions, and spot those that do not appear in the
  // $active list collected earlier.
  $all_instances = field_read_instances($params, array('include_inactive' => TRUE));
  foreach ($all_instances as $instance) {
    if (!isset($active[$instance['bundle']][$instance['field_name']])) {
      $inactive[$instance['bundle']][$instance['field_name']] = $instance;
    }
  }

  if (!empty($bundle_name)) {
    return $inactive[$bundle_name];
  }
  return $inactive;
}

/**
 * Implements hook_form_FORM_ID_alter().
 *
 * Adds a button 'Save and add fields' to the 'Create content type' form.
 *
 * @see node_type_form()
 * @see field_ui_form_node_type_form_submit()
 */
function field_ui_form_node_type_form_alter(&$form, $form_state) {
  // We want to display the button only on add page.
  if (empty($form['#node_type']->type)) {
    $form['actions']['save_continue'] = array(
      '#type' => 'submit',
      '#value' => t('Save and add fields'),
      '#weight' => 45,
    );
    $form['#submit'][] = 'field_ui_form_node_type_form_submit';
  }
}

/**
 * Form submission handler for the 'Save and add fields' button.
 *
 * @see field_ui_form_node_type_form_alter()
 */
function field_ui_form_node_type_form_submit($form, &$form_state) {
  if ($form_state['triggering_element']['#parents'][0] === 'save_continue') {
    $form_state['redirect'] = _field_ui_bundle_admin_path('node', $form_state['values']['type']) .'/fields';
  }
}

/**
 * Access callback to determine if a user is allowed to use the field UI.
 *
 * Only grant access if the user has both the "administer fields" permission and
 * is granted access by the entity specific restrictions.
 */
function field_ui_admin_access($access_callback, $access_arguments) {
  return user_access('administer fields') && call_user_func_array($access_callback, $access_arguments);
}
