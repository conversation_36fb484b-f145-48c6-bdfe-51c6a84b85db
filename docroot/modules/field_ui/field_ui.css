/**
 * @file
 * Stylesheet for the Field UI module.
 */
 
/* 'Manage fields' and 'Manage display' overviews */
table.field-ui-overview tr.add-new .label-input {
  float: left; /* LTR */
}
table.field-ui-overview tr.add-new .tabledrag-changed {
  display: none;
}
table.field-ui-overview tr.add-new .description {
  margin-bottom: 0;
  max-width: 250px;
}
table.field-ui-overview tr.add-new .form-type-machine-name .description {
  white-space: normal;
}
table.field-ui-overview tr.add-new .add-new-placeholder {
  font-weight: bold;
  padding-bottom: .5em;
}
table.field-ui-overview tr.region-title td {
  font-weight: bold;
}
table.field-ui-overview tr.region-message td {
  font-style: italic;
}
table.field-ui-overview tr.region-populated {
  display: none;
}
table.field-ui-overview tr.region-add-new-title {
  display: none;
}
table.field-ui-overview tr.add-new td {
  vertical-align: top;
  white-space: nowrap;
}

/* 'Manage display' overview */
#field-display-overview .field-formatter-summary-cell {
  line-height: 1em;
}
#field-display-overview .field-formatter-summary {
  float: left;
  font-size: 0.9em;
}
#field-display-overview td.field-formatter-summary-cell span.warning {
  display: block;
  float: left;
  margin-right: .5em;
}
#field-display-overview .field-formatter-settings-edit-wrapper {
  float: right;
}
#field-display-overview .field-formatter-settings-edit {
  float: right;
}
#field-display-overview tr.field-formatter-settings-editing td {
  vertical-align: top;
}
#field-display-overview tr.field-formatter-settings-editing .field-formatter-type {
  display: none;
}
#field-display-overview .field-formatter-settings-edit-form .formatter-name{
  font-weight: bold;
}
#field-ui-display-overview-form #edit-refresh {
  display:none;
}
