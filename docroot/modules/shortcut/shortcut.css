div#toolbar a#edit-shortcuts {
  float: right;
  padding: 5px 10px 5px 5px;
  line-height: 24px;
  color: #fefefe;
}
div#toolbar a#edit-shortcuts:focus,
div#toolbar a#edit-shortcuts:hover,
div#toolbar a#edit-shortcuts.active {
  color: #fff;
  text-decoration: underline;
}

div#toolbar div.toolbar-shortcuts ul {
  padding: 5px 0 2px 0;
  height: 28px;
  line-height: 24px;
  float: left; /* LTR */
  margin-left:5px; /* LTR */
}

div#toolbar div.toolbar-shortcuts ul li a {
  padding: 0 5px 0 5px;
  margin-right: 5px; /* LTR */
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
}

div#toolbar div.toolbar-shortcuts ul li a:focus,
div#toolbar div.toolbar-shortcuts ul li a:hover,
div#toolbar div.toolbar-shortcuts ul li a.active:focus {
  background: #555;
}

div#toolbar div.toolbar-shortcuts ul li a.active:hover,
div#toolbar div.toolbar-shortcuts ul li a.active {
  background: #000;
}

div#toolbar div.toolbar-shortcuts span.icon {
  float: left; /* LTR */
  background: #444;
  width: 30px;
  height: 30px;
  margin-right: 5px; /* LTR */
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
}

div.add-or-remove-shortcuts {
  padding-top: 5px;
}

div.add-or-remove-shortcuts a span.icon {
  display: block;
  width: 12px;
  background: transparent url(shortcut.png) no-repeat scroll 0 0;
  height: 12px;
  float: left;
  margin-left:8px;
}

div.add-shortcut a:focus span.icon,
div.add-shortcut a:hover span.icon {
  background-position: 0 -12px;
}
div.remove-shortcut a span.icon {
  background-position: -12px 0;
}
div.remove-shortcut a:focus span.icon,
div.remove-shortcut a:hover span.icon {
  background-position: -12px -12px;
}

div.add-or-remove-shortcuts a span.text {
  float: left;
  padding-left:10px;
  display: none;
}

div.add-or-remove-shortcuts a:focus span.text,
div.add-or-remove-shortcuts a:hover span.text {
  font-size: 10px;
  line-height: 12px;
  color: #fff;
  background-color: #5f605b;
  display: block;
  padding-right: 6px; /* LTR */
  cursor: pointer;
  -moz-border-radius: 0 5px 5px 0; /* LTR */
  -webkit-border-top-right-radius: 5px; /* LTR */
  -webkit-border-bottom-right-radius: 5px; /* LTR */
  border-radius: 0 5px 5px 0; /* LTR */
}

#shortcut-set-switch .form-type-radios {
  padding-bottom: 0;
  margin-bottom: 0;
}

#shortcut-set-switch .form-item-new {
  padding-top: 0;
  padding-left: 17px; /* LTR */
}
