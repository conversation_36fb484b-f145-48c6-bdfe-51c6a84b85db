
div#toolbar a#edit-shortcuts {
  position: absolute;
  left: 0;
  top: 0;
  padding: 5px 5px 5px 10px;
}
div#toolbar div.toolbar-shortcuts ul {
  float: none;
  margin-right: 5px;
  margin-left: 10em;
}
div#toolbar div.toolbar-shortcuts ul li a {
  margin-left: 5px;
  margin-right: 0;
  padding: 0 5px;
}
div#toolbar div.toolbar-shortcuts span.icon {
  float: right;
}
div.add-or-remove-shortcuts a span.icon {
  float: right;
  margin-right: 8px;
  margin-left: 0;
}
div.add-or-remove-shortcuts a span.text {
  float: right;
  padding-right: 10px;
  padding-left: 0;
}
div.add-or-remove-shortcuts a:focus span.text,
div.add-or-remove-shortcuts a:hover span.text {
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-top-left-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  border-radius: 5px 0 0 5px;
  padding-left: 6px;
}
#shortcut-set-switch .form-item-new {
  padding-right: 17px;
  padding-left: 0;
}
div.add-shortcut a:hover span.icon {
  background-position: 0 -24px;
}
div.remove-shortcut a:hover span.icon {
  background-position: -12px -24px;
}
