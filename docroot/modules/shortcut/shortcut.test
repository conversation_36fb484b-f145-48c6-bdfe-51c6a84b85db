<?php

/**
 * @file
 * Tests for shortcut.module.
 */

/**
 * Defines base class for shortcut test cases.
 */
class ShortcutTestCase extends DrupalWebTestCase {

  /**
   * User with permission to administer shortcuts.
   */
  protected $admin_user;

  /**
   * User with permission to use shortcuts, but not administer them.
   */
  protected $shortcut_user;

  /**
   * Generic node used for testing.
   */
  protected $node;

  /**
   * Site-wide default shortcut set.
   */
  protected $set;

  function setUp() {
    parent::setUp('toolbar', 'shortcut');
    // Create users.
    $this->admin_user = $this->drupalCreateUser(array('access toolbar', 'administer shortcuts', 'view the administration theme', 'create article content', 'create page content', 'access content overview'));
    $this->shortcut_user = $this->drupalCreateUser(array('customize shortcut links', 'switch shortcut sets'));

    // Create a node.
    $this->node = $this->drupalCreateNode(array('type' => 'article'));

    // Log in as admin and grab the default shortcut set.
    $this->drupalLogin($this->admin_user);
    $this->set = shortcut_set_load(SHORTCUT_DEFAULT_SET_NAME);
    shortcut_set_assign_user($this->set, $this->admin_user);
  }

  /**
   * Creates a generic shortcut set.
   */
  function generateShortcutSet($title = '', $default_links = TRUE) {
    $set = new stdClass();
    $set->title = empty($title) ? $this->randomName(10) : $title;
    if ($default_links) {
      $set->links = array();
      $set->links[] = $this->generateShortcutLink('node/add');
      $set->links[] = $this->generateShortcutLink('admin/content');
    }
    shortcut_set_save($set);

    return $set;
  }

  /**
   * Creates a generic shortcut link.
   */
  function generateShortcutLink($path, $title = '') {
    $link = array(
      'link_path' => $path,
      'link_title' => !empty($title) ? $title : $this->randomName(10),
    );

    return $link;
  }

  /**
   * Extracts information from shortcut set links.
   * 
   * @param object $set
   *   The shortcut set object to extract information from.
   * @param string $key
   *   The array key indicating what information to extract from each link:
   *    - 'link_path': Extract link paths.
   *    - 'link_title': Extract link titles.
   *    - 'mlid': Extract the menu link item ID numbers.
   *
   * @return array
   *   Array of the requested information from each link.
   */
  function getShortcutInformation($set, $key) {
    $info = array();
    foreach ($set->links as $link) {
      $info[] = $link[$key];
    }
    return $info;
  }
}

/**
 * Defines shortcut links test cases.
 */
class ShortcutLinksTestCase extends ShortcutTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Shortcut link functionality',
      'description' => 'Create, view, edit, delete, and change shortcut links.',
      'group' => 'Shortcut',
    );
  }

  /**
   * Tests that creating a shortcut works properly.
   */
  function testShortcutLinkAdd() {
    $set = $this->set;

    // Create an alias for the node so we can test aliases.
    $path = array(
      'source' => 'node/' . $this->node->nid,
      'alias' => $this->randomName(8),
    );
    path_save($path);

    // Create some paths to test.
    $test_cases = array(
      array('path' => ''),
      array('path' => 'admin'),
      array('path' => 'admin/config/system/site-information'),
      array('path' => "node/{$this->node->nid}/edit"),
      array('path' => $path['alias']),
    );

    // Check that each new shortcut links where it should.
    foreach ($test_cases as $test) {
      $title = $this->randomName(10);
      $form_data = array(
        'shortcut_link[link_title]' => $title,
        'shortcut_link[link_path]'  => $test['path'],
      );
      $this->drupalPost('admin/config/user-interface/shortcut/' . $set->set_name . '/add-link', $form_data, t('Save'));
      $this->assertResponse(200);
      $saved_set = shortcut_set_load($set->set_name);
      $paths = $this->getShortcutInformation($saved_set, 'link_path');
      $test_path = empty($test['path']) ? '<front>' : $test['path'];
      $this->assertTrue(in_array(drupal_get_normal_path($test_path), $paths), 'Shortcut created: '. $test['path']);
      $this->assertLink($title, 0, 'Shortcut link found on the page.');
    }
  }

  /**
   * Tests that the "add to shortcut" link changes to "remove shortcut".
   */
  function testShortcutQuickLink() {
    $this->drupalGet($this->set->links[0]['link_path']);
    $this->assertRaw(t('Remove from %title shortcuts', array('%title' => $this->set->title)), '"Add to shortcuts" link properly switched to "Remove from shortcuts".');
  }

  /**
   * Tests that shortcut links can be renamed.
   */
  function testShortcutLinkRename() {
    $set = $this->set;

    // Attempt to rename shortcut link.
    $new_link_name = $this->randomName(10);

    $this->drupalPost('admin/config/user-interface/shortcut/link/' . $set->links[0]['mlid'], array('shortcut_link[link_title]' => $new_link_name, 'shortcut_link[link_path]' => $set->links[0]['link_path']), t('Save'));
    $saved_set = shortcut_set_load($set->set_name);
    $titles = $this->getShortcutInformation($saved_set, 'link_title');
    $this->assertTrue(in_array($new_link_name, $titles), 'Shortcut renamed: ' . $new_link_name);
    $this->assertLink($new_link_name, 0, 'Renamed shortcut link appears on the page.');
  }

  /**
   * Tests that changing the path of a shortcut link works.
   */
  function testShortcutLinkChangePath() {
    $set = $this->set;

    // Tests changing a shortcut path.
    $new_link_path = 'admin/config';

    $this->drupalPost('admin/config/user-interface/shortcut/link/' . $set->links[0]['mlid'], array('shortcut_link[link_title]' => $set->links[0]['link_title'], 'shortcut_link[link_path]' => $new_link_path), t('Save'));
    $saved_set = shortcut_set_load($set->set_name);
    $paths = $this->getShortcutInformation($saved_set, 'link_path');
    $this->assertTrue(in_array($new_link_path, $paths), 'Shortcut path changed: ' . $new_link_path);
    $this->assertLinkByHref($new_link_path, 0, 'Shortcut with new path appears on the page.');
  }

  /**
   * Tests deleting a shortcut link.
   */
  function testShortcutLinkDelete() {
    $set = $this->set;

    $this->drupalPost('admin/config/user-interface/shortcut/link/' . $set->links[0]['mlid'] . '/delete', array(), 'Delete');
    $saved_set = shortcut_set_load($set->set_name);
    $mlids = $this->getShortcutInformation($saved_set, 'mlid');
    $this->assertFalse(in_array($set->links[0]['mlid'], $mlids), 'Successfully deleted a shortcut.');
  }

  /**
   * Tests that the add shortcut link is not displayed for 404/403 errors.
   *
   * Tests that the "Add to shortcuts" link is not displayed on a page not
   * found or a page the user does not have access to.
   */
  function testNoShortcutLink() {
    // Change to a theme that displays shortcuts.
    variable_set('theme_default', 'seven');

    $this->drupalGet('page-that-does-not-exist');
    $this->assertNoRaw('add-shortcut', 'Add to shortcuts link was not shown on a page not found.');

    // The user does not have access to this path.
    $this->drupalGet('admin/modules');
    $this->assertNoRaw('add-shortcut', 'Add to shortcuts link was not shown on a page the user does not have access to.');

    // Verify that the testing mechanism works by verifying the shortcut
    // link appears on admin/content/node.
    $this->drupalGet('admin/content/node');
    $this->assertRaw('add-shortcut', 'Add to shortcuts link was shown on a page the user does have access to.');
  }
}

/**
 * Defines shortcut set test cases.
 */
class ShortcutSetsTestCase extends ShortcutTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Shortcut set functionality',
      'description' => 'Create, view, edit, delete, and change shortcut sets.',
      'group' => 'Shortcut',
    );
  }

  /**
   * Tests creating a shortcut set.
   */
  function testShortcutSetAdd() {
    $new_set = $this->generateShortcutSet($this->randomName(10));
    $sets = shortcut_sets();
    $this->assertTrue(isset($sets[$new_set->set_name]), 'Successfully created a shortcut set.');
    $this->drupalGet('user/' . $this->admin_user->uid . '/shortcuts');
    $this->assertText($new_set->title, 'Generated shortcut set was listed as a choice on the user account page.');
  }

  /**
   * Tests switching a user's own shortcut set.
   */
  function testShortcutSetSwitchOwn() {
    $new_set = $this->generateShortcutSet($this->randomName(10));

    // Attempt to switch the default shortcut set to the newly created shortcut
    // set.
    $this->drupalPost('user/' . $this->admin_user->uid . '/shortcuts', array('set' => $new_set->set_name), t('Change set'));
    $this->assertResponse(200);
    $current_set = shortcut_current_displayed_set($this->admin_user);
    $this->assertTrue($new_set->set_name == $current_set->set_name, 'Successfully switched own shortcut set.');
  }

  /**
   * Tests switching another user's shortcut set.
   */
  function testShortcutSetAssign() {
    $new_set = $this->generateShortcutSet($this->randomName(10));

    shortcut_set_assign_user($new_set, $this->shortcut_user);
    $current_set = shortcut_current_displayed_set($this->shortcut_user);
    $this->assertTrue($new_set->set_name == $current_set->set_name, "Successfully switched another user's shortcut set.");
  }

  /**
   * Tests switching a user's shortcut set and creating one at the same time.
   */
  function testShortcutSetSwitchCreate() {
    $edit = array(
      'set' => 'new',
      'new' => $this->randomName(10),
    );
    $this->drupalPost('user/' . $this->admin_user->uid . '/shortcuts', $edit, t('Change set'));
    $current_set = shortcut_current_displayed_set($this->admin_user);
    $this->assertNotEqual($current_set->set_name, $this->set->set_name, 'A shortcut set can be switched to at the same time as it is created.');
    $this->assertEqual($current_set->title, $edit['new'], 'The new set is correctly assigned to the user.');
  }

  /**
   * Tests switching a user's shortcut set without providing a new set name.
   */
  function testShortcutSetSwitchNoSetName() {
    $edit = array('set' => 'new');
    $this->drupalPost('user/' . $this->admin_user->uid . '/shortcuts', $edit, t('Change set'));
    $this->assertText(t('The new set name is required.'));
    $current_set = shortcut_current_displayed_set($this->admin_user);
    $this->assertEqual($current_set->set_name, $this->set->set_name, 'Attempting to switch to a new shortcut set without providing a set name does not succeed.');
  }

  /**
   * Tests that shortcut_set_save() correctly updates existing links.
   */
  function testShortcutSetSave() {
    $set = $this->set;
    $old_mlids = $this->getShortcutInformation($set, 'mlid');

    $set->links[] = $this->generateShortcutLink('admin', $this->randomName(10));
    shortcut_set_save($set);
    $saved_set = shortcut_set_load($set->set_name);

    $new_mlids = $this->getShortcutInformation($saved_set, 'mlid');
    $this->assertTrue(count(array_intersect($old_mlids, $new_mlids)) == count($old_mlids), 'shortcut_set_save() did not inadvertently change existing mlids.');
  }

  /**
   * Tests renaming a shortcut set.
   */
  function testShortcutSetRename() {
    $set = $this->set;
    
    $new_title = $this->randomName(10);
    $this->drupalPost('admin/config/user-interface/shortcut/' . $set->set_name . '/edit', array('title' => $new_title), t('Save'));
    $set = shortcut_set_load($set->set_name);
    $this->assertTrue($set->title == $new_title, 'Shortcut set has been successfully renamed.');
  }

  /**
   * Tests renaming a shortcut set to the same name as another set.
   */
  function testShortcutSetRenameAlreadyExists() {
    $set = $this->generateShortcutSet($this->randomName(10));
    $existing_title = $this->set->title;
    $this->drupalPost('admin/config/user-interface/shortcut/' . $set->set_name . '/edit', array('title' => $existing_title), t('Save'));
    $this->assertRaw(t('The shortcut set %name already exists. Choose another name.', array('%name' => $existing_title)));
    $set = shortcut_set_load($set->set_name);
    $this->assertNotEqual($set->title, $existing_title, format_string('The shortcut set %title cannot be renamed to %new-title because a shortcut set with that title already exists.', array('%title' => $set->title, '%new-title' => $existing_title)));
  }

  /**
   * Tests unassigning a shortcut set.
   */
  function testShortcutSetUnassign() {
    $new_set = $this->generateShortcutSet($this->randomName(10));

    shortcut_set_assign_user($new_set, $this->shortcut_user);
    shortcut_set_unassign_user($this->shortcut_user);
    $current_set = shortcut_current_displayed_set($this->shortcut_user);
    $default_set = shortcut_default_set($this->shortcut_user);
    $this->assertTrue($current_set->set_name == $default_set->set_name, "Successfully unassigned another user's shortcut set.");
  }

  /**
   * Tests deleting a shortcut set.
   */
  function testShortcutSetDelete() {
    $new_set = $this->generateShortcutSet($this->randomName(10));

    $this->drupalPost('admin/config/user-interface/shortcut/' . $new_set->set_name . '/delete', array(), t('Delete'));
    $sets = shortcut_sets();
    $this->assertFalse(isset($sets[$new_set->set_name]), 'Successfully deleted a shortcut set.');
  }

  /**
   * Tests deleting the default shortcut set.
   */
  function testShortcutSetDeleteDefault() {
    $this->drupalGet('admin/config/user-interface/shortcut/' . SHORTCUT_DEFAULT_SET_NAME . '/delete');
    $this->assertResponse(403);
  }
}
