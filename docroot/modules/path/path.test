<?php

/**
 * @file
 * Tests for the Path module.
 */

/**
 * Provides a base class for testing the Path module.
 */
class PathTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Path alias functionality',
      'description' => 'Add, edit, delete, and change alias and verify its consistency in the database.',
      'group' => 'Path',
    );
  }

  function setUp() {
    parent::setUp('path');

    // Create test user and login.
    $web_user = $this->drupalCreateUser(array('create page content', 'edit own page content', 'administer url aliases', 'create url aliases', 'access content overview'));
    $this->drupalLogin($web_user);
  }

  /**
   * Tests the path cache.
   */
  function testPathCache() {
    // Create test node.
    $node1 = $this->drupalCreateNode();

    // Create alias.
    $edit = array();
    $edit['source'] = 'node/' . $node1->nid;
    $edit['alias'] = $this->randomName(8);
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));

    // Visit the system path for the node and confirm a cache entry is
    // created.
    cache_clear_all('*', 'cache_path', TRUE);
    $this->drupalGet($edit['source']);
    $this->assertTrue(cache_get($edit['source'], 'cache_path'), 'Cache entry was created.');

    // Visit the alias for the node and confirm a cache entry is created.
    cache_clear_all('*', 'cache_path', TRUE);
    $this->drupalGet($edit['alias']);
    $this->assertTrue(cache_get($edit['source'], 'cache_path'), 'Cache entry was created.');
  }

  /**
   * Tests alias functionality through the admin interfaces.
   */
  function testAdminAlias() {
    // Create test node.
    $node1 = $this->drupalCreateNode();

    // Create alias.
    $edit = array();
    $edit['source'] = 'node/' . $node1->nid;
    $edit['alias'] = $this->randomName(8);
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));

    // Confirm that the alias works.
    $this->drupalGet($edit['alias']);
    $this->assertText($node1->title, 'Alias works.');
    $this->assertResponse(200);

    // Change alias to one containing "exotic" characters.
    $pid = $this->getPID($edit['alias']);

    $previous = $edit['alias'];
    $edit['alias'] = "- ._~!$'\"()*@[]?&+%#,;=:" . // "Special" ASCII characters.
      "%23%25%26%2B%2F%3F" . // Characters that look like a percent-escaped string.
      "éøïвβ中國書۞"; // Characters from various non-ASCII alphabets.
    $this->drupalPost('admin/config/search/path/edit/' . $pid, $edit, t('Save'));

    // Confirm that the alias works.
    $this->drupalGet($edit['alias']);
    $this->assertText($node1->title, 'Changed alias works.');
    $this->assertResponse(200);

    drupal_static_reset('drupal_lookup_path');
    // Confirm that previous alias no longer works.
    $this->drupalGet($previous);
    $this->assertNoText($node1->title, 'Previous alias no longer works.');
    $this->assertResponse(404);

    // Create second test node.
    $node2 = $this->drupalCreateNode();

    // Set alias to second test node.
    $edit['source'] = 'node/' . $node2->nid;
    // leave $edit['alias'] the same
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));

    // Confirm no duplicate was created.
    $this->assertRaw(t('The alias %alias is already in use in this language.', array('%alias' => $edit['alias'])), 'Attempt to move alias was rejected.');

    // Delete alias.
    $this->drupalPost('admin/config/search/path/edit/' . $pid, array(), t('Delete'));
    $this->drupalPost(NULL, array(), t('Confirm'));

    // Confirm that the alias no longer works.
    $this->drupalGet($edit['alias']);
    $this->assertNoText($node1->title, 'Alias was successfully deleted.');
    $this->assertResponse(404);

    // Create third and fourth test node.
    $node3 = $this->drupalCreateNode();
    $node4 = $this->drupalCreateNode();

    // Give the node aliases a common first part.
    $name = $this->randomName(4);

    // Create aliases containing a slash.
    $edit = array();
    $edit['source'] = 'node/' . $node3->nid;
    $alias3 = $name . '/' . $this->randomName(5);
    $edit['alias'] = $alias3;
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));
    $edit['source'] = 'node/' . $node4->nid;
    $alias4 = $name . '/' . $this->randomName(4);
    $edit['alias'] = $alias4;
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));

    // Confirm that the aliases work.
    $this->drupalGet($alias3);
    $this->assertText($node3->title, 'Alias works.');
    $this->assertResponse(200);
    $this->drupalGet($alias4);
    $this->assertText($node4->title, 'Alias works.');
    $this->assertResponse(200);

    // Confirm that filters containing slashes work.
    $this->drupalGet('admin/config/search/path/list/' . $alias3);
    $this->assertFieldByName('filter', $alias3);
    $this->assertText($alias3, 'Searched-for alias with slash found.');
    $this->assertNoText($alias4, 'Different alias with slash not found.');
    $this->assertResponse(200);

    // Delete aliases.
    $pid = $this->getPID($alias3);
    $this->drupalPost('admin/config/search/path/edit/' . $pid, array(), t('Delete'));
    $this->drupalPost(NULL, array(), t('Confirm'));
    $pid = $this->getPID($alias4);
    $this->drupalPost('admin/config/search/path/edit/' . $pid, array(), t('Delete'));
    $this->drupalPost(NULL, array(), t('Confirm'));

    // Confirm that the aliases no longer work.
    $this->drupalGet($alias3);
    $this->assertNoText($node3->title, 'Alias was successfully deleted.');
    $this->assertResponse(404);
    $this->drupalGet($alias4);
    $this->assertNoText($node4->title, 'Alias was successfully deleted.');
    $this->assertResponse(404);
  }

  /**
   * Tests alias functionality through the node interfaces.
   */
  function testNodeAlias() {
    // Create test node.
    $node1 = $this->drupalCreateNode();

    // Create alias.
    $edit = array();
    $edit['path[alias]'] = $this->randomName(8);
    $this->drupalPost('node/' . $node1->nid . '/edit', $edit, t('Save'));

    // Confirm that the alias works.
    $this->drupalGet($edit['path[alias]']);
    $this->assertText($node1->title, 'Alias works.');
    $this->assertResponse(200);

    // Change alias to one containing "exotic" characters.
    $previous = $edit['path[alias]'];
    $edit['path[alias]'] = "- ._~!$'\"()*@[]?&+%#,;=:" . // "Special" ASCII characters.
      "%23%25%26%2B%2F%3F" . // Characters that look like a percent-escaped string.
      "éøïвβ中國書۞"; // Characters from various non-ASCII alphabets.
    $this->drupalPost('node/' . $node1->nid . '/edit', $edit, t('Save'));

    // Confirm that the alias works.
    $this->drupalGet($edit['path[alias]']);
    $this->assertText($node1->title, 'Changed alias works.');
    $this->assertResponse(200);

    // Make sure that previous alias no longer works.
    $this->drupalGet($previous);
    $this->assertNoText($node1->title, 'Previous alias no longer works.');
    $this->assertResponse(404);

    // Create second test node.
    $node2 = $this->drupalCreateNode();

    // Set alias to second test node.
    // Leave $edit['path[alias]'] the same.
    $this->drupalPost('node/' . $node2->nid . '/edit', $edit, t('Save'));

    // Confirm that the alias didn't make a duplicate.
    $this->assertText(t('The alias is already in use.'), 'Attempt to moved alias was rejected.');

    // Delete alias.
    $this->drupalPost('node/' . $node1->nid . '/edit', array('path[alias]' => ''), t('Save'));

    // Confirm that the alias no longer works.
    $this->drupalGet($edit['path[alias]']);
    $this->assertNoText($node1->title, 'Alias was successfully deleted.');
    $this->assertResponse(404);

    // Create third test node.
    $node3 = $this->drupalCreateNode();

    // Create an invalid alias with a leading slash and verify that the slash
    // is removed when the link is generated. This ensures that URL aliases
    // cannot be used to inject external URLs.
    // @todo The user interface should either display an error message or
    //   automatically trim these invalid aliases, rather than allowing them to
    //   be silently created, at which point the functional aspects of this
    //   test will need to be moved elsewhere and switch to using a
    //   programmatically-created alias instead.
    $alias = $this->randomName(8);
    $edit = array('path[alias]' => '/' . $alias);
    $this->drupalPost('node/' . $node3->nid . '/edit', $edit, t('Save'));
    $this->drupalGet('admin/content');
    // This checks the link href before clicking it, rather than using
    // DrupalWebTestCase::assertUrl() after clicking it, because the test
    // browser does not always preserve the correct number of slashes in the
    // URL when it visits internal links; using DrupalWebTestCase::assertUrl()
    // would actually make the test pass unconditionally on the testbot (or
    // anywhere else where Drupal is installed in a subdirectory).
    $link_xpath = $this->xpath('//a[normalize-space(text())=:label]', array(':label' => $node3->title));
    $link_href = (string) $link_xpath[0]['href'];
    $link_prefix = base_path() . (variable_get('clean_url', 0) ? '' : '?q=');
    $this->assertEqual($link_href, $link_prefix . $alias);
    $this->clickLink($node3->title);
    $this->assertResponse(404);
  }

  /**
   * Returns the path ID.
   *
   * @param $alias
   *   A string containing an aliased path.
   *
   * @return int
   *   Integer representing the path ID.
   */
  function getPID($alias) {
    return db_query("SELECT pid FROM {url_alias} WHERE alias = :alias", array(':alias' => $alias))->fetchField();
  }

  /**
   * Tests that duplicate aliases fail validation.
   */
  function testDuplicateNodeAlias() {
    // Create one node with a random alias.
    $node_one = $this->drupalCreateNode();
    $edit = array();
    $edit['path[alias]'] = $this->randomName();
    $this->drupalPost('node/' . $node_one->nid . '/edit', $edit, t('Save'));

    // Now create another node and try to set the same alias.
    $node_two = $this->drupalCreateNode();
    $this->drupalPost('node/' . $node_two->nid . '/edit', $edit, t('Save'));
    $this->assertText(t('The alias is already in use.'));
    $this->assertFieldByXPath("//input[@name='path[alias]' and contains(@class, 'error')]", $edit['path[alias]'], 'Textfield exists and has the error class.');
  }
}

/**
 * Tests URL aliases for taxonomy terms.
 */
class PathTaxonomyTermTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Taxonomy term URL aliases',
      'description' => 'Tests URL aliases for taxonomy terms.',
      'group' => 'Path',
    );
  }

  function setUp() {
    parent::setUp('path', 'taxonomy');

    // Create and login user.
    $web_user = $this->drupalCreateUser(array('administer url aliases', 'administer taxonomy', 'access administration pages'));
    $this->drupalLogin($web_user);
  }

  /**
   * Tests alias functionality through the admin interfaces.
   */
  function testTermAlias() {
    // Create a term in the default 'Tags' vocabulary with URL alias.
    $vocabulary = taxonomy_vocabulary_load(1);
    $description = $this->randomName();;
    $edit = array();
    $edit['name'] = $this->randomName();
    $edit['description[value]'] = $description;
    $edit['path[alias]'] = $this->randomName();
    $this->drupalPost('admin/structure/taxonomy/' . $vocabulary->machine_name . '/add', $edit, t('Save'));

    // Confirm that the alias works.
    $this->drupalGet($edit['path[alias]']);
    $this->assertText($description, 'Term can be accessed on URL alias.');

    // Change the term's URL alias.
    $tid = db_query("SELECT tid FROM {taxonomy_term_data} WHERE name = :name", array(':name' => $edit['name']))->fetchField();
    $edit2 = array();
    $edit2['path[alias]'] = $this->randomName();
    $this->drupalPost('taxonomy/term/' . $tid . '/edit', $edit2, t('Save'));

    // Confirm that the changed alias works.
    $this->drupalGet($edit2['path[alias]']);
    $this->assertText($description, 'Term can be accessed on changed URL alias.');

    // Confirm that the old alias no longer works.
    $this->drupalGet($edit['path[alias]']);
    $this->assertNoText($description, 'Old URL alias has been removed after altering.');
    $this->assertResponse(404, 'Old URL alias returns 404.');

    // Remove the term's URL alias.
    $edit3 = array();
    $edit3['path[alias]'] = '';
    $this->drupalPost('taxonomy/term/' . $tid . '/edit', $edit3, t('Save'));

    // Confirm that the alias no longer works.
    $this->drupalGet($edit2['path[alias]']);
    $this->assertNoText($description, 'Old URL alias has been removed after altering.');
    $this->assertResponse(404, 'Old URL alias returns 404.');
  }
}

/**
 * Tests URL aliases for translated nodes.
 */
class PathLanguageTestCase extends DrupalWebTestCase {
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Path aliases with translated nodes',
      'description' => 'Confirm that paths work with translated nodes',
      'group' => 'Path',
    );
  }

  function setUp() {
    parent::setUp('path', 'locale', 'translation');

    // Create and login user.
    $this->web_user = $this->drupalCreateUser(array('edit any page content', 'create page content', 'administer url aliases', 'create url aliases', 'administer languages', 'translate content', 'access administration pages'));
    $this->drupalLogin($this->web_user);

    // Enable French language.
    $edit = array();
    $edit['langcode'] = 'fr';

    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Enable URL language detection and selection.
    $edit = array('language[enabled][locale-url]' => 1);
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));
  }

  /**
   * Test alias functionality through the admin interfaces.
   */
  function testAliasTranslation() {
    // Set 'page' content type to enable translation.
    variable_set('language_content_type_page', 2);

    $english_node = $this->drupalCreateNode(array('type' => 'page'));
    $english_alias = $this->randomName();

    // Edit the node to set language and path.
    $edit = array();
    $edit['language'] = 'en';
    $edit['path[alias]'] = $english_alias;
    $this->drupalPost('node/' . $english_node->nid . '/edit', $edit, t('Save'));

    // Confirm that the alias works.
    $this->drupalGet($english_alias);
    $this->assertText($english_node->title, 'Alias works.');

    // Translate the node into French.
    $this->drupalGet('node/' . $english_node->nid . '/translate');
    $this->clickLink(t('add translation'));
    $edit = array();
    $langcode = LANGUAGE_NONE;
    $edit["title"] = $this->randomName();
    $edit["body[$langcode][0][value]"] = $this->randomName();
    $french_alias = $this->randomName();
    $edit['path[alias]'] = $french_alias;
    $this->drupalPost(NULL, $edit, t('Save'));

    // Clear the path lookup cache.
    drupal_lookup_path('wipe');

    // Ensure the node was created.
    $french_node = $this->drupalGetNodeByTitle($edit["title"]);
    $this->assertTrue(($french_node), 'Node found in database.');

    // Confirm that the alias works.
    $this->drupalGet('fr/' . $edit['path[alias]']);
    $this->assertText($french_node->title, 'Alias for French translation works.');

    // Confirm that the alias is returned by url().
    drupal_static_reset('language_list');
    drupal_static_reset('locale_url_outbound_alter');
    $languages = language_list();
    $url = url('node/' . $french_node->nid, array('language' => $languages[$french_node->language]));
    $this->assertTrue(strpos($url, $edit['path[alias]']), 'URL contains the path alias.');

    // Confirm that the alias works even when changing language negotiation
    // options. Enable User language detection and selection over URL one.
    $edit = array(
      'language[enabled][locale-user]' => 1,
      'language[weight][locale-user]' => -9,
      'language[enabled][locale-url]' => 1,
      'language[weight][locale-url]' => -8,
    );
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Change user language preference.
    $edit = array('language' => 'fr');
    $this->drupalPost("user/{$this->web_user->uid}/edit", $edit, t('Save'));

    // Check that the English alias works. In this situation French is the
    // current UI and content language, while URL language is English (since we
    // do not have a path prefix we fall back to the site's default language).
    // We need to ensure that the user language preference is not taken into
    // account while determining the path alias language, because if this
    // happens we have no way to check that the path alias is valid: there is no
    // path alias for French matching the english alias. So drupal_lookup_path()
    // needs to use the URL language to check whether the alias is valid.
    $this->drupalGet($english_alias);
    $this->assertText($english_node->title, 'Alias for English translation works.');

    // Check that the French alias works.
    $this->drupalGet("fr/$french_alias");
    $this->assertText($french_node->title, 'Alias for French translation works.');

    // Disable URL language negotiation.
    $edit = array('language[enabled][locale-url]' => FALSE);
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Check that the English alias still works.
    $this->drupalGet($english_alias);
    $this->assertText($english_node->title, 'Alias for English translation works.');

    // Check that the French alias is not available. We check the unprefixed
    // alias because we disabled URL language negotiation above. In this
    // situation only aliases in the default language and language neutral ones
    // should keep working.
    $this->drupalGet($french_alias);
    $this->assertResponse(404, 'Alias for French translation is unavailable when URL language negotiation is disabled.');

    // drupal_lookup_path() has an internal static cache. Check to see that
    // it has the appropriate contents at this point.
    drupal_lookup_path('wipe');
    $french_node_path = drupal_lookup_path('source', $french_alias, $french_node->language);
    $this->assertEqual($french_node_path, 'node/' . $french_node->nid, 'Normal path works.');
    // Second call should return the same path.
    $french_node_path = drupal_lookup_path('source', $french_alias, $french_node->language);
    $this->assertEqual($french_node_path, 'node/' . $french_node->nid, 'Normal path is the same.');

    // Confirm that the alias works.
    $french_node_alias = drupal_lookup_path('alias', 'node/' . $french_node->nid, $french_node->language);
    $this->assertEqual($french_node_alias, $french_alias, 'Alias works.');
    // Second call should return the same alias.
    $french_node_alias = drupal_lookup_path('alias', 'node/' . $french_node->nid, $french_node->language);
    $this->assertEqual($french_node_alias, $french_alias, 'Alias is the same.');
  }
}

/**
 * Tests the user interface for creating path aliases, with languages.
 */
class PathLanguageUITestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Path aliases with languages',
      'description' => 'Confirm that the Path module user interface works with languages.',
      'group' => 'Path',
    );
  }

  function setUp() {
    parent::setUp('path', 'locale');

    // Create and login user.
    $web_user = $this->drupalCreateUser(array('edit any page content', 'create page content', 'administer url aliases', 'create url aliases', 'administer languages', 'access administration pages'));
    $this->drupalLogin($web_user);

    // Enable French language.
    $edit = array();
    $edit['langcode'] = 'fr';

    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Enable URL language detection and selection.
    $edit = array('language[enabled][locale-url]' => 1);
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));
  }

  /**
   * Tests that a language-neutral URL alias works.
   */
  function testLanguageNeutralURLs() {
    $name = $this->randomName(8);
    $edit = array();
    $edit['source'] = 'admin/config/search/path';
    $edit['alias'] = $name;
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));

    $this->drupalGet($name);
    $this->assertText(t('Filter aliases'), 'Language-neutral URL alias works');
  }

  /**
   * Tests that a default language URL alias works.
   */
  function testDefaultLanguageURLs() {
    $name = $this->randomName(8);
    $edit = array();
    $edit['source'] = 'admin/config/search/path';
    $edit['alias'] = $name;
    $edit['language'] = 'en';
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));

    $this->drupalGet($name);
    $this->assertText(t('Filter aliases'), 'English URL alias works');
  }

  /**
   * Tests that a non-default language URL alias works.
   */
  function testNonDefaultURLs() {
    $name = $this->randomName(8);
    $edit = array();
    $edit['source'] = 'admin/config/search/path';
    $edit['alias'] = $name;
    $edit['language'] = 'fr';
    $this->drupalPost('admin/config/search/path/add', $edit, t('Save'));

    $this->drupalGet('fr/' . $name);
    $this->assertText(t('Filter aliases'), 'Foreign URL alias works');
  }

}

/**
 * Tests that paths are not prefixed on a monolingual site.
 */
class PathMonolingualTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Paths on non-English monolingual sites',
      'description' => 'Confirm that paths are not changed on monolingual non-English sites',
      'group' => 'Path',
    );
  }

  function setUp() {
    global $language;
    parent::setUp('path', 'locale', 'translation');

    // Create and login user.
    $web_user = $this->drupalCreateUser(array('administer languages', 'access administration pages'));
    $this->drupalLogin($web_user);

    // Enable French language.
    $edit = array();
    $edit['langcode'] = 'fr';
    $this->drupalPost('admin/config/regional/language/add', $edit, t('Add language'));

    // Make French the default language.
    $edit = array('site_default' => 'fr');
    $this->drupalPost('admin/config/regional/language', $edit, t('Save configuration'));

    // Disable English.
    $edit = array('enabled[en]' => FALSE);
    $this->drupalPost('admin/config/regional/language', $edit, t('Save configuration'));

    // Verify that French is the only language.
    $this->assertFalse(drupal_multilingual(), 'Site is mono-lingual');
    $this->assertEqual(language_default('language'), 'fr', 'French is the default language');

    // Set language detection to URL.
    $edit = array('language[enabled][locale-url]' => TRUE);
    $this->drupalPost('admin/config/regional/language/configure', $edit, t('Save settings'));

    // Force languages to be initialized.
    drupal_language_initialize();
  }

  /**
   * Verifies that links do not have language prefixes in them.
   */
  function testPageLinks() {
    // Navigate to 'admin/config' path.
    $this->drupalGet('admin/config');

    // Verify that links in this page do not have a 'fr/' prefix.
    $this->assertNoLinkByHref('/fr/', 'Links do not contain language prefix');

    // Verify that links in this page can be followed and work.
    $this->clickLink(t('Languages'));
    $this->assertResponse(200, 'Clicked link results in a valid page');
    $this->assertText(t('Add language'), 'Page contains the add language text');
  }
}
