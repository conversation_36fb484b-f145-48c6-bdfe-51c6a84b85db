<?php

/**
 * @file
 * Enables users to rename URLs.
 */

/**
 * Implements hook_help().
 */
function path_help($path, $arg) {
  switch ($path) {
    case 'admin/help#path':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Path module allows you to specify an alias, or custom URL, for any existing internal system path. Aliases should not be confused with URL redirects, which allow you to forward a changed or inactive URL to a new URL. In addition to making URLs more readable, aliases also help search engines index content more effectively. Multiple aliases may be used for a single internal system path. To automate the aliasing of paths, you can install the contributed module <a href="@pathauto">Pathauto</a>. For more information, see the online handbook entry for the <a href="@path">Path module</a>.', array('@path' => 'http://drupal.org/documentation/modules/path', '@pathauto' => 'http://drupal.org/project/pathauto')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Creating aliases') . '</dt>';
      $output .= '<dd>' . t('Users with sufficient <a href="@permissions">permissions</a> can create aliases under the <em>URL path settings</em> section when they create or edit content. Some examples of aliases are: ', array('@permissions' => url('admin/people/permissions', array('fragment' => 'module-path'))));
      $output .= '<ul><li>' . t('<em>member/jane-smith</em> aliased to internal path <em>user/123</em>') . '</li>';
      $output .= '<li>' . t('<em>about-us/team</em> aliased to internal path <em>node/456</em>') . '</li>';
      $output .= '</ul></dd>';
      $output .= '<dt>' . t('Managing aliases') . '</dt>';
      $output .= '<dd>' . t('The Path module provides a way to search and view a <a href="@aliases">list of all aliases</a> that are in use on your website. Aliases can be added, edited and deleted through this list.', array('@aliases' => url('admin/config/search/path'))) . '</dd>';
      $output .= '</dl>';
      return $output;

    case 'admin/config/search/path':
      return '<p>' . t("An alias defines a different name for an existing URL path - for example, the alias 'about' for the URL path 'node/1'. A URL path can have multiple aliases.") . '</p>';

    case 'admin/config/search/path/add':
      return '<p>' . t('Enter the path you wish to create the alias for, followed by the name of the new alias.') . '</p>';
  }
}

/**
 * Implements hook_permission().
 */
function path_permission() {
  return array(
    'administer url aliases' => array(
      'title' => t('Administer URL aliases'),
    ),
    'create url aliases' => array(
      'title' => t('Create and edit URL aliases'),
    ),
  );
}

/**
 * Implements hook_menu().
 */
function path_menu() {
  $items['admin/config/search/path'] = array(
    'title' => 'URL aliases',
    'description' => "Change your site's URL paths by aliasing them.",
    'page callback' => 'path_admin_overview',
    'access arguments' => array('administer url aliases'),
    'weight' => -5,
    'file' => 'path.admin.inc',
  );
  $items['admin/config/search/path/edit/%path'] = array(
    'title' => 'Edit alias',
    'page callback' => 'path_admin_edit',
    'page arguments' => array(5),
    'access arguments' => array('administer url aliases'),
    'file' => 'path.admin.inc',
  );
  $items['admin/config/search/path/delete/%path'] = array(
    'title' => 'Delete alias',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('path_admin_delete_confirm', 5),
    'access arguments' => array('administer url aliases'),
    'file' => 'path.admin.inc',
  );
  $items['admin/config/search/path/list'] = array(
    'title' => 'List',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );
  $items['admin/config/search/path/add'] = array(
    'title' => 'Add alias',
    'page callback' => 'path_admin_edit',
    'access arguments' => array('administer url aliases'),
    'type' => MENU_LOCAL_ACTION,
    'file' => 'path.admin.inc',
  );

  return $items;
}

/**
 * Implements hook_form_BASE_FORM_ID_alter() for node_form().
 *
 * @see path_form_element_validate()
 */
function path_form_node_form_alter(&$form, $form_state) {
  $path = array();
  if (!empty($form['#node']->nid)) {
    $conditions = array('source' => 'node/' . $form['#node']->nid);
    $langcode = entity_language('node', $form['#node']);
    if ($langcode != LANGUAGE_NONE) {
      $conditions['language'] = $langcode;
    }
    $path = path_load($conditions);
    if ($path === FALSE) {
      $path = array();
    }
  }
  $path += array(
    'pid' => NULL,
    'source' => isset($form['#node']->nid) ? 'node/' . $form['#node']->nid : NULL,
    'alias' => '',
    'language' => isset($langcode) ? $langcode : LANGUAGE_NONE,
  );

  $form['path'] = array(
    '#type' => 'fieldset',
    '#title' => t('URL path settings'),
    '#collapsible' => TRUE,
    '#collapsed' => empty($path['alias']),
    '#group' => 'additional_settings',
    '#attributes' => array(
      'class' => array('path-form'),
    ),
    '#attached' => array(
      'js' => array(drupal_get_path('module', 'path') . '/path.js'),
    ),
    '#access' => user_access('create url aliases') || user_access('administer url aliases'),
    '#weight' => 30,
    '#tree' => TRUE,
    '#element_validate' => array('path_form_element_validate'),
  );
  $form['path']['alias'] = array(
    '#type' => 'textfield',
    '#title' => t('URL alias'),
    '#default_value' => $path['alias'],
    '#maxlength' => 255,
    '#description' => t('Optionally specify an alternative URL by which this content can be accessed. For example, type "about" when writing an about page. Use a relative path and don\'t add a trailing slash or the URL alias won\'t work.'),
  );
  $form['path']['pid'] = array('#type' => 'value', '#value' => $path['pid']);
  $form['path']['source'] = array('#type' => 'value', '#value' => $path['source']);
  $form['path']['language'] = array('#type' => 'value', '#value' => $path['language']);
}

/**
 * Form element validation handler for URL alias form element.
 *
 * @see path_form_node_form_alter()
 */
function path_form_element_validate($element, &$form_state, $complete_form) {
  // Trim the submitted value.
  $alias = trim($form_state['values']['path']['alias']);
  if (!empty($alias)) {
    form_set_value($element['alias'], $alias, $form_state);
    // Node language (Locale module) needs special care. Since the language of
    // the URL alias depends on the node language, and the node language can be
    // switched right within the same form, we need to conditionally overload
    // the originally assigned URL alias language.
    // @todo Remove this after converting Path module to a field, and, after
    //   stopping Locale module from abusing the content language system.
    if (isset($form_state['values']['language'])) {
      form_set_value($element['language'], $form_state['values']['language'], $form_state);
    }

    $path = $form_state['values']['path'];

    // Ensure that the submitted alias does not exist yet.
    $query = db_select('url_alias')
      ->condition('alias', $path['alias'])
      ->condition('language', $path['language']);
    if (!empty($path['source'])) {
      $query->condition('source', $path['source'], '<>');
    }
    $query->addExpression('1');
    $query->range(0, 1);
    if ($query->execute()->fetchField()) {
      form_error($element, t('The alias is already in use.'));
    }
  }
}

/**
 * Implements hook_node_insert().
 */
function path_node_insert($node) {
  if (isset($node->path) && isset($node->path['alias'])) {
    $path = $node->path;
    $path['alias'] = trim($path['alias']);
    // Only save a non-empty alias.
    if (!empty($path['alias'])) {
      // Ensure fields for programmatic executions.
      $langcode = entity_language('node', $node);
      $path['source'] = 'node/' . $node->nid;
      $path['language'] = isset($langcode) ? $langcode : LANGUAGE_NONE;
      path_save($path);
    }
  }
}

/**
 * Implements hook_node_update().
 */
function path_node_update($node) {
  if (isset($node->path)) {
    $path = $node->path;
    $path['alias'] = isset($path['alias']) ? trim($path['alias']) : '';
    // Delete old alias if user erased it.
    if (!empty($path['pid']) && !$path['alias']) {
      path_delete($path['pid']);
    }
    path_node_insert($node);
  }
}

/**
 * Implements hook_node_delete().
 */
function path_node_delete($node) {
  // Delete all aliases associated with this node.
  path_delete(array('source' => 'node/' . $node->nid));
}

/**
 * Implements hook_form_FORM_ID_alter() for taxonomy_form_term().
 */
function path_form_taxonomy_form_term_alter(&$form, $form_state) {
  // Make sure this does not show up on the delete confirmation form.
  if (empty($form_state['confirm_delete'])) {
    $langcode = entity_language('taxonomy_term', (object) $form['#term']);
    $langcode = !empty($langcode) ? $langcode : LANGUAGE_NONE;
    $conditions = array('source' => 'taxonomy/term/' . $form['#term']['tid'], 'language' => $langcode);
    $path = (isset($form['#term']['tid']) ? path_load($conditions) : array());
    if ($path === FALSE) {
      $path = array();
    }
    $path += array(
      'pid' => NULL,
      'source' => isset($form['#term']['tid']) ? 'taxonomy/term/' . $form['#term']['tid'] : NULL,
      'alias' => '',
      'language' => $langcode,
    );
    $form['path'] = array(
      '#access' => user_access('create url aliases') || user_access('administer url aliases'),
      '#tree' => TRUE,
      '#element_validate' => array('path_form_element_validate'),
    );
    $form['path']['alias'] = array(
      '#type' => 'textfield',
      '#title' => t('URL alias'),
      '#default_value' => $path['alias'],
      '#maxlength' => 255,
      '#weight' => 0,
      '#description' => t("Optionally specify an alternative URL by which this term can be accessed. Use a relative path and don't add a trailing slash or the URL alias won't work."),
    );
    $form['path']['pid'] = array('#type' => 'value', '#value' => $path['pid']);
    $form['path']['source'] = array('#type' => 'value', '#value' => $path['source']);
    $form['path']['language'] = array('#type' => 'value', '#value' => $path['language']);
  }
}

/**
 * Implements hook_taxonomy_term_insert().
 */
function path_taxonomy_term_insert($term) {
  if (isset($term->path)) {
    $path = $term->path;
    $path['alias'] = trim($path['alias']);
    // Only save a non-empty alias.
    if (!empty($path['alias'])) {
      // Ensure fields for programmatic executions.
      $path['source'] = 'taxonomy/term/' . $term->tid;
      // Core does not provide a way to store the term language but contrib
      // modules can do it so we need to take this into account.
      $langcode = entity_language('taxonomy_term', $term);
      $path['language'] = !empty($langcode) ? $langcode : LANGUAGE_NONE;
      path_save($path);
    }
  }
}

/**
 * Implements hook_taxonomy_term_update().
 */
function path_taxonomy_term_update($term) {
  if (isset($term->path)) {
    $path = $term->path;
    $path['alias'] = trim($path['alias']);
    // Delete old alias if user erased it.
    if (!empty($path['pid']) && empty($path['alias'])) {
      path_delete($path['pid']);
    }
    // Only save a non-empty alias.
    if (!empty($path['alias'])) {
      // Ensure fields for programmatic executions.
      $path['source'] = 'taxonomy/term/' . $term->tid;
      // Core does not provide a way to store the term language but contrib
      // modules can do it so we need to take this into account.
      $langcode = entity_language('taxonomy_term', $term);
      $path['language'] = !empty($langcode) ? $langcode : LANGUAGE_NONE;
      path_save($path);
    }
  }
}

/**
 * Implements hook_taxonomy_term_delete().
 */
function path_taxonomy_term_delete($term) {
  // Delete all aliases associated with this term.
  path_delete(array('source' => 'taxonomy/term/' . $term->tid));
}
