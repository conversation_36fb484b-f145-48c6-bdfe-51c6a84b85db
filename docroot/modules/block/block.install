<?php

/**
 * @file
 * Install, update and uninstall functions for the block module.
 */

/**
 * Implements hook_schema().
 */
function block_schema() {
  $schema['block'] = array(
    'description' => 'Stores block settings, such as region and visibility settings.',
    'fields' => array(
      'bid' => array(
        'type' => 'serial',
        'not null' => TRUE,
        'description' => 'Primary Key: Unique block ID.',
      ),
      'module' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'default' => '',
        'description' => "The module from which the block originates; for example, 'user' for the Who's Online block, and 'block' for any custom blocks.",
      ),
      'delta' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'default' => '0',
        'description' => 'Unique ID for block within a module.',
      ),
      'theme' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The theme under which the block settings apply.',
      ),
      'status' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Block enabled status. (1 = enabled, 0 = disabled)',
      ),
      'weight' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Block weight within region.',
      ),
      'region' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Theme region within which the block is set.',
      ),
      'custom' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Flag to indicate how users may control visibility of the block. (0 = Users cannot control, 1 = On by default, but can be hidden, 2 = Hidden by default, but can be shown)',
      ),
      'visibility' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Flag to indicate how to show blocks on pages. (0 = Show on all pages except listed pages, 1 = Show only on listed pages, 2 = Use custom PHP code to determine visibility)',
      ),
      'pages' => array(
        'type' => 'text',
        'not null' => TRUE,
        'description' => 'Contents of the "Pages" block; contains either a list of paths on which to include/exclude the block or PHP code, depending on "visibility" setting.',
      ),
      'title' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Custom title for the block. (Empty string will use block default title, <none> will remove the title, text will cause block to use specified title.)',
        'translatable' => TRUE,
      ),
      'cache' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 1,
        'size' => 'tiny',
        'description' => 'Binary flag to indicate block cache mode. (-2: Custom cache, -1: Do not cache, 1: Cache per role, 2: Cache per user, 4: Cache per page, 8: Block cache global) See DRUPAL_CACHE_* constants in ../includes/common.inc for more detailed information.',
      ),
    ),
    'primary key' => array('bid'),
    'unique keys' => array(
      'tmd' => array('theme', 'module', 'delta'),
    ),
    'indexes' => array(
      'list' => array('theme', 'status', 'region', 'weight', 'module'),
    ),
  );

  $schema['block_role'] = array(
    'description' => 'Sets up access permissions for blocks based on user roles',
    'fields' => array(
      'module' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'description' => "The block's origin module, from {block}.module.",
      ),
      'delta' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'description' => "The block's unique delta within module, from {block}.delta.",
      ),
      'rid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => "The user's role ID from {users_roles}.rid.",
      ),
    ),
    'primary key' => array('module', 'delta', 'rid'),
    'indexes' => array(
      'rid' => array('rid'),
    ),
  );

  $schema['block_custom'] = array(
    'description' => 'Stores contents of custom-made blocks.',
    'fields' => array(
      'bid' => array(
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => "The block's {block}.bid.",
      ),
      'body' => array(
        'type' => 'text',
        'not null' => FALSE,
        'size' => 'big',
        'description' => 'Block contents.',
        'translatable' => TRUE,
      ),
      'info' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Block description.',
      ),
      'format' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => FALSE,
        'description' => 'The {filter_format}.format of the block body.',
      ),
    ),
    'unique keys' => array(
      'info' => array('info'),
    ),
    'primary key' => array('bid'),
  );

  $schema['cache_block'] = drupal_get_schema_unprocessed('system', 'cache');
  $schema['cache_block']['description'] = 'Cache table for the Block module to store already built blocks, identified by module, delta, and various contexts which may change the block, such as theme, locale, and caching mode defined for the block.';

  return $schema;
}

/**
 * Implements hook_install().
 */
function block_install() {

  // Block should go first so that other modules can alter its output
  // during hook_page_alter(). Almost everything on the page is a block,
  // so before block module runs, there will not be much to alter.
  db_update('system')
    ->fields(array('weight' => -5))
    ->condition('name', 'block')
    ->execute();
}

/**
 * Implements hook_update_dependencies().
 */
function block_update_dependencies() {
  // block_update_7005() needs to query the {filter_format} table to get a list
  // of existing text formats, so it must run after filter_update_7000(), which
  // creates that table.
  $dependencies['block'][7005] = array(
    'filter' => 7000,
  );

  return $dependencies;
}

/**
 * @addtogroup updates-6.x-to-7.x
 * @{
 */

/**
 * Set system.weight to a low value for block module.
 *
 * Block should go first so that other modules can alter its output
 * during hook_page_alter(). Almost everything on the page is a block,
 * so before block module runs, there will not be much to alter.
 */
function block_update_7000() {
  db_update('system')
    ->fields(array('weight' => '-5'))
    ->condition('name', 'block')
    ->execute();
}

/**
 * Rename {blocks} table to {block}, {blocks_roles} to {block_role} and
 * {boxes} to {block_custom}.
 */
function block_update_7002() {
  db_drop_index('blocks', 'list');
  db_rename_table('blocks', 'block');
  db_rename_table('blocks_roles', 'block_role');
  db_rename_table('boxes', 'block_custom');
}

/**
 * Change the weight column to normal int.
 */
function block_update_7003() {
  db_change_field('block', 'weight', 'weight', array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
    'description' => 'Block weight within region.',
  ), array(
    'indexes' => array(
      'list' => array('theme', 'status', 'region', 'weight', 'module'),
    ),
  ));
}

/**
 * Add new blocks to new regions, migrate custom variables to blocks.
 */
function block_update_7004() {
  // Collect a list of themes with blocks.
  $themes_with_blocks = array();
  $result = db_query("SELECT s.name FROM {system} s INNER JOIN {block} b ON s.name = b.theme WHERE s.type = 'theme' GROUP by s.name");

  $insert = db_insert('block')->fields(array('module', 'delta', 'theme', 'status', 'weight', 'region', 'pages', 'cache'));
  foreach ($result as $theme) {
    $themes_with_blocks[] = $theme->name;
    // Add new system generated help block.
    $insert->values(array(
      'module' => 'system',
      'delta' => 'help',
      'theme' => $theme->name,
      'status' => 1,
      'weight' => 0,
      'region' => 'help',
      'pages' => '',
      'cache' => DRUPAL_CACHE_PER_ROLE,
    ));
    // Add new system generated main page content block.
    $insert->values(array(
      'module' => 'system',
      'delta' => 'main',
      'theme' => $theme->name,
      'status' => 1,
      'weight' => 0,
      'region' => 'content',
      'pages' => '',
      'cache' => DRUPAL_NO_CACHE,
    ));
  }
  $insert->execute();

  // Migrate blocks from left/right regions to first/second regions.
  db_update('block')
    ->fields(array('region' => 'sidebar_first'))
    ->condition('region', 'left')
    ->execute();
  db_update('block')
    ->fields(array('region' => 'sidebar_second'))
    ->condition('region', 'right')
    ->execute();

  // Migrate contact form information.
  $default_format = variable_get('filter_default_format', 1);
  if ($contact_help = variable_get('contact_form_information', '')) {
    $bid = db_insert('block_custom')
      ->fields(array(
        'body' => $contact_help,
        'info' => 'Contact page help',
        'format' => $default_format,
      ))
      ->execute();

    $insert = db_insert('block')->fields(array('module', 'delta', 'theme', 'status', 'weight', 'region', 'visibility', 'pages', 'cache'));
    foreach ($themes_with_blocks as $theme) {
      // Add contact help block for themes, which had blocks.
      $insert->values(array(
        'module' => 'block',
        'delta' => $bid,
        'theme' => $theme,
        'status' => 1,
        'weight' => 5,
        'region' => 'help',
        'visibility' => BLOCK_VISIBILITY_LISTED,
        'pages' => 'contact',
        'cache' => DRUPAL_NO_CACHE,
      ));
    }
    drupal_set_message('The contact form information setting was migrated to <a href="' . url('admin/structure/block/manage/block/' . $bid . '/configure') . '">a custom block</a> and set up to only show on the site-wide contact page. The block was set to use the default text format, which might differ from the HTML based format used before. Check the block and ensure that the output is right.');
  }
  $insert->execute();

  // Migrate user help setting.
  if ($user_help = variable_get('user_registration_help', '')) {
    $bid = db_insert('block_custom')->fields(array('body' => $user_help, 'info' => 'User registration guidelines', 'format' => $default_format))->execute();

    $insert = db_insert('block')->fields(array('module', 'delta', 'theme', 'status', 'weight', 'region', 'visibility', 'pages', 'cache'));
    foreach ($themes_with_blocks as $theme) {
      // Add user registration help block for themes, which had blocks.
      $insert->values(array(
        'module' => 'block',
        'delta' => $bid,
        'theme' => $theme,
        'status' => 1,
        'weight' => 5,
        'region' => 'help',
        'visibility' => BLOCK_VISIBILITY_LISTED,
        'pages' => 'user/register',
        'cache' => DRUPAL_NO_CACHE,
      ));
    }
    drupal_set_message('The user registration guidelines were migrated to <a href="' . url('admin/structure/block/manage/block/' . $bid . '/configure') . '">a custom block</a> and set up to only show on the user registration page. The block was set to use the default text format, which might differ from the HTML based format used before. Check the block and ensure that the output is right.');
    $insert->execute();
  }

  // Migrate site mission setting.
  if ($mission = variable_get('site_mission')) {
    $bid = db_insert('block_custom')->fields(array('body' => $mission, 'info' => 'Site mission', 'format' => $default_format))->execute();

    $insert = db_insert('block')->fields(array('module', 'delta', 'theme', 'status', 'weight', 'region', 'visibility', 'pages', 'cache'));
    foreach ($themes_with_blocks as $theme) {
      // Add mission block for themes, which had blocks.
      $insert->values(array(
        'module' => 'block',
        'delta' => $bid,
        'theme' => $theme,
        'status' => 1,
        'weight' => 0,
        'region' => 'highlighted',
        'visibility' => BLOCK_VISIBILITY_LISTED,
        'pages' => '<front>',
        'cache' => DRUPAL_NO_CACHE,
      ));
    }
    drupal_set_message('The site mission was migrated to <a href="' . url('admin/structure/block/manage/block/' . $bid . '/configure') . '">a custom block</a> and set up to only show on the front page in the highlighted content region. The block was set to use the default text format, which might differ from the HTML based format used before. Check the block and ensure that the output is right. If your theme does not have a highlighted content region, you might need to <a href="' . url('admin/structure/block') . '">relocate the block</a>.');
    $insert->execute();
    // Migrate mission to RSS site description.
    variable_set('feed_description', $mission);
  }

  // Migrate site footer message to a custom block.
  if ($footer_message = variable_get('site_footer', '')) {
    $bid = db_insert('block_custom')->fields(array('body' => $footer_message, 'info' => 'Footer message', 'format' => $default_format))->execute();

    $insert = db_insert('block')->fields(array('module', 'delta', 'theme', 'status', 'weight', 'region', 'pages', 'cache'));
    foreach ($themes_with_blocks as $theme) {
      // Add site footer block for themes, which had blocks.
      // Set low weight, so the block comes early (it used to be
      // before the other blocks).
      $insert->values(array(
        'module' => 'block',
        'delta' => $bid,
        'theme' => $theme,
        'status' => 1,
        'weight' => -10,
        'region' => 'footer',
        'pages' => '',
        'cache' => DRUPAL_NO_CACHE,
      ));
    }
    drupal_set_message('The footer message was migrated to <a href="' . url('admin/structure/block/manage/block/' . $bid . '/configure') . '">a custom block</a> and set up to appear in the footer. The block was set to use the default text format, which might differ from the HTML based format used before. Check the block and ensure that the output is right. If your theme does not have a footer region, you might need to <a href="' . url('admin/structure/block') . '">relocate the block</a>.');
    $insert->execute();
  }

  // Remove the variables (even if they were saved empty on the admin interface),
  // to avoid keeping clutter in the variables table.
  variable_del('contact_form_information');
  variable_del('user_registration_help');
  variable_del('site_mission');
  variable_del('site_footer');

  // Rebuild theme data, so the new 'help' region is identified.
  system_rebuild_theme_data();
}

/**
 * Update the {block_custom}.format column.
 */
function block_update_7005() {
  // For an explanation of these updates, see the code comments in
  // user_update_7010().
  db_change_field('block_custom', 'format', 'format', array(
    'type' => 'int',
    'unsigned' => TRUE,
    'not null' => FALSE,
    'description' => 'The {filter_format}.format of the block body.',
  ));
  db_update('block_custom')
    ->fields(array('format' => NULL))
    ->condition('body', '')
    ->condition('format', 0)
    ->execute();
  $existing_formats = db_query("SELECT format FROM {filter_format}")->fetchCol();
  $default_format = variable_get('filter_default_format', 1);
  db_update('block_custom')
    ->fields(array('format' => $default_format))
    ->isNotNull('format')
    ->condition('format', $existing_formats, 'NOT IN')
    ->execute();
}

/**
 * Recreates cache_block table.
 *
 * Converts fields that hold serialized variables from text to blob.
 * Removes 'headers' column.
 */
function block_update_7006() {
  $schema = system_schema_cache_7054();

  db_drop_table('cache_block');
  db_create_table('cache_block', $schema);
}

/**
 * Change {block_custom}.format into varchar.
 */
function block_update_7007() {
  db_change_field('block_custom', 'format', 'format', array(
    'type' => 'varchar',
    'length' => 255,
    'not null' => FALSE,
    'description' => 'The {filter_format}.format of the block body.',
  ));
}

/**
 * @} End of "addtogroup updates-6.x-to-7.x".
 */

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Update database to match Drupal 7 schema.
 */
function block_update_7008() {
  db_drop_field('block', 'throttle');
}

/**
 * Increase {block}.title length to 255 characters.
 */
function block_update_7009() {
  db_change_field('block', 'title', 'title',
    array(
      'type' => 'varchar',
      'length' => 255,
      'not null' => TRUE,
      'default' => '',
      'description' => 'Custom title for the block. (Empty string will use block default title, <none> will remove the title, text will cause block to use specified title.)',
      'translatable' => TRUE,
    )
  );
}

/**
 * @} End of "addtogroup updates-7.x-extra".
 */
