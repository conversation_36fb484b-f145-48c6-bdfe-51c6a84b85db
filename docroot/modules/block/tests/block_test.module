<?php

/**
 * @file
 *   Provide test blocks.
 */

/**
 * Implements hook_system_theme_info().
 */
function block_test_system_theme_info() {
  $themes['block_test_theme'] = drupal_get_path('module', 'block_test') . '/themes/block_test_theme/block_test_theme.info';
  return $themes;
}

/**
 * Implements hook_block_info().
 */
function block_test_block_info() {
  $blocks['test_cache'] = array(
    'info' => t('Test block caching'),
    'cache' => variable_get('block_test_caching', DRUPAL_CACHE_PER_ROLE),
  );

  $blocks['test_underscore'] = array(
    'info' => t('Test underscore'),
  );

  $blocks['test-hyphen'] = array(
    'info' => t('Test hyphen'),
  );

  $blocks['test_html_id'] = array(
    'info' => t('Test block html id'),
  );
  return $blocks;
}

/**
 * Implements hook_block_view().
 */
function block_test_block_view($delta = 0) {
  return array('content' => variable_get('block_test_content', ''));
}

/**
 * Implements hook_block_view_MODULE_DELTA_alter().
 */
function block_test_block_view_block_test_test_underscore_alter(&$data, $block) {
  $data['content'] = 'hook_block_view_MODULE_DELTA_alter';
}

/**
 * Implements hook_block_view_MODULE_DELTA_alter().
 */
function block_test_block_view_block_test_test_hyphen_alter(&$data, $block) {
  $data['content'] = 'hook_block_view_MODULE_DELTA_alter';
}

/**
 * Implements hook_block_info_alter().
 */
function block_test_block_info_alter(&$blocks) {
  if (variable_get('block_test_info_alter')) {
    $blocks['block_test']['test_html_id']['weight'] = 10000;
  }
}
