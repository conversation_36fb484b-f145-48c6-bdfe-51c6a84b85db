<?php

/**
 * @file
 * Controls the visual building blocks a page is constructed with.
 */

/**
 * Denotes that a block is not enabled in any region and should not be shown.
 */
define('BLOCK_REGION_NONE', -1);

/**
 * Users cannot control whether or not they see this block.
 */
define('BLOCK_CUSTOM_FIXED', 0);

/**
 * Shows this block by default, but lets individual users hide it.
 */
define('BLOCK_CUSTOM_ENABLED', 1);

/**
 * Hides this block by default but lets individual users show it.
 */
define('BLOCK_CUSTOM_DISABLED', 2);

/**
 * Shows this block on every page except the listed pages.
 */
define('BLOCK_VISIBILITY_NOTLISTED', 0);

/**
 * Shows this block on only the listed pages.
 */
define('BLOCK_VISIBILITY_LISTED', 1);

/**
 * Shows this block if the associated PHP code returns TRUE.
 */
define('BLOCK_VISIBILITY_PHP', 2);

/**
 * Implements hook_help().
 */
function block_help($path, $arg) {
  switch ($path) {
    case 'admin/help#block':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Block module allows you to create boxes of content, which are rendered into an area, or region, of one or more pages of a website. The core Seven administration theme, for example, implements the regions "Content", "Help", "Dashboard main", and "Dashboard sidebar", and a block may appear in any one of these regions. The <a href="@blocks">Blocks administration page</a> provides a drag-and-drop interface for assigning a block to a region, and for controlling the order of blocks within regions. For more information, see the online handbook entry for <a href="@block">Block module</a>.', array('@block' => 'http://drupal.org/documentation/modules/block/', '@blocks' => url('admin/structure/block'))) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Positioning content') . '</dt>';
      $output .= '<dd>' . t('When working with blocks, remember that all themes do <em>not</em> implement the same regions, or display regions in the same way. Blocks are positioned on a per-theme basis. Users with the <em>Administer blocks</em> permission can disable blocks. Disabled blocks are listed on the <a href="@blocks">Blocks administration page</a>, but are not displayed in any region.', array('@block' => 'http://drupal.org/documentation/modules/block/', '@blocks' => url('admin/structure/block'))) . '</dd>';
      $output .= '<dt>' . t('Controlling visibility') . '</dt>';
      $output .= '<dd>' . t('Blocks can be configured to be visible only on certain pages, only to users of certain roles, or only on pages displaying certain <a href="@content-type">content types</a>. Administrators can also allow specific blocks to be enabled or disabled by users when they edit their <a href="@user">My account</a> page. Some dynamic blocks, such as those generated by modules, will be displayed only on certain pages.', array('@content-type' => url('admin/structure/types'), '@user' => url('user'))) . '</dd>';
      $output .= '<dt>' . t('Creating custom blocks') . '</dt>';
      $output .= '<dd>' . t('Users with the <em>Administer blocks</em> permission can <a href="@block-add">add custom blocks</a>, which are then listed on the <a href="@blocks">Blocks administration page</a>. Once created, custom blocks behave just like default and module-generated blocks.', array('@blocks' => url('admin/structure/block'), '@block-add' => url('admin/structure/block/add'))) . '</dd>';
      $output .= '</dl>';
      return $output;

    case 'admin/structure/block/add':
      return '<p>' . t('Use this page to create a new custom block.') . '</p>';
  }
  if ($arg[0] == 'admin' && $arg[1] == 'structure' && $arg['2'] == 'block' && (empty($arg[3]) || $arg[3] == 'list')) {
    $demo_theme = !empty($arg[4]) ? $arg[4] : variable_get('theme_default', 'bartik');
    $themes = list_themes();
    $output = '<p>' . t('This page provides a drag-and-drop interface for assigning a block to a region, and for controlling the order of blocks within regions. Since not all themes implement the same regions, or display regions in the same way, blocks are positioned on a per-theme basis. Remember that your changes will not be saved until you click the <em>Save blocks</em> button at the bottom of the page. Click the <em>configure</em> link next to each block to configure its specific title and visibility settings.') . '</p>';
    $output .= '<p>' . l(t('Demonstrate block regions (!theme)', array('!theme' => $themes[$demo_theme]->info['name'])), 'admin/structure/block/demo/' . $demo_theme) . '</p>';
    return $output;
  }
}

/**
 * Implements hook_theme().
 */
function block_theme() {
  return array(
    'block' => array(
      'render element' => 'elements',
      'template' => 'block',
    ),
    'block_admin_display_form' => array(
      'template' => 'block-admin-display-form',
      'file' => 'block.admin.inc',
      'render element' => 'form',
    ),
  );
}

/**
 * Implements hook_permission().
 */
function block_permission() {
  return array(
    'administer blocks' => array(
      'title' => t('Administer blocks'),
    ),
  );
}

/**
 * Implements hook_menu().
 */
function block_menu() {
  $default_theme = variable_get('theme_default', 'bartik');
  $items['admin/structure/block'] = array(
    'title' => 'Blocks',
    'description' => 'Configure what block content appears in your site\'s sidebars and other regions.',
    'page callback' => 'block_admin_display',
    'page arguments' => array($default_theme),
    'access arguments' => array('administer blocks'),
    'file' => 'block.admin.inc',
  );
  $items['admin/structure/block/manage/%/%'] = array(
    'title' => 'Configure block',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('block_admin_configure', 4, 5),
    'access arguments' => array('administer blocks'),
    'file' => 'block.admin.inc',
  );
  $items['admin/structure/block/manage/%/%/configure'] = array(
    'title' => 'Configure block',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'context' => MENU_CONTEXT_INLINE,
  );
  $items['admin/structure/block/manage/%/%/delete'] = array(
    'title' => 'Delete block',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('block_custom_block_delete', 4, 5),
    'access arguments' => array('administer blocks'),
    'type' => MENU_LOCAL_TASK,
    'context' => MENU_CONTEXT_NONE,
    'file' => 'block.admin.inc',
  );
  $items['admin/structure/block/add'] = array(
    'title' => 'Add block',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('block_add_block_form'),
    'access arguments' => array('administer blocks'),
    'type' => MENU_LOCAL_ACTION,
    'file' => 'block.admin.inc',
  );
  foreach (list_themes() as $key => $theme) {
    $items['admin/structure/block/list/' . $key] = array(
      'title' => $theme->info['name'],
      'page arguments' => array($key),
      'type' => $key == $default_theme ? MENU_DEFAULT_LOCAL_TASK : MENU_LOCAL_TASK,
      'weight' => $key == $default_theme ? -10 : 0,
      'access callback' => '_block_themes_access',
      'access arguments' => array($theme),
      'file' => 'block.admin.inc',
    );
    if ($key != $default_theme) {
      $items['admin/structure/block/list/' . $key . '/add'] = array(
        'title' => 'Add block',
        'page callback' => 'drupal_get_form',
        'page arguments' => array('block_add_block_form'),
        'access arguments' => array('administer blocks'),
        'type' => MENU_LOCAL_ACTION,
        'file' => 'block.admin.inc',
      );
    }
    $items['admin/structure/block/demo/' . $key] = array(
      'title' => $theme->info['name'],
      'page callback' => 'block_admin_demo',
      'page arguments' => array($key),
      'type' => MENU_CALLBACK,
      'access callback' => '_block_themes_access',
      'access arguments' => array($theme),
      'theme callback' => '_block_custom_theme',
      'theme arguments' => array($key),
      'file' => 'block.admin.inc',
    );
  }
  return $items;
}

/**
 * Menu item access callback - only admin or enabled themes can be accessed.
 */
function _block_themes_access($theme) {
  return user_access('administer blocks') && drupal_theme_access($theme);
}

/**
 * Theme callback for the block configuration pages.
 *
 * @param $theme
 *   The theme whose blocks are being configured. If not set, the default theme
 *   is assumed.
 *
 * @return
 *   The theme that should be used for the block configuration page, or NULL
 *   to indicate that the default theme should be used.
 */
function _block_custom_theme($theme = NULL) {
  // We return exactly what was passed in, to guarantee that the page will
  // always be displayed using the theme whose blocks are being configured.
  return $theme;
}

/**
 * Implements hook_block_info().
 */
function block_block_info() {
  $blocks = array();

  $result = db_query('SELECT bid, info FROM {block_custom} ORDER BY info');
  foreach ($result as $block) {
    $blocks[$block->bid]['info'] = $block->info;
    // Not worth caching.
    $blocks[$block->bid]['cache'] = DRUPAL_NO_CACHE;
  }
  return $blocks;
}

/**
 * Implements hook_block_configure().
 */
function block_block_configure($delta = 0) {
  if ($delta) {
    $custom_block = block_custom_block_get($delta);
  }
  else {
    $custom_block = array();
  }
  return block_custom_block_form($custom_block);
}

/**
 * Implements hook_block_save().
 */
function block_block_save($delta = 0, $edit = array()) {
  block_custom_block_save($edit, $delta);
}

/**
 * Implements hook_block_view().
 *
 * Generates the administrator-defined blocks for display.
 */
function block_block_view($delta = '') {
  $block = db_query('SELECT body, format FROM {block_custom} WHERE bid = :bid', array(':bid' => $delta))->fetchObject();
  $data['subject'] = NULL;
  $data['content'] = check_markup($block->body, $block->format, '', TRUE);
  return $data;
}

/**
 * Implements hook_page_build().
 *
 * Renders blocks into their regions.
 */
function block_page_build(&$page) {
  global $theme;

  // The theme system might not yet be initialized. We need $theme.
  drupal_theme_initialize();

  // Fetch a list of regions for the current theme.
  $all_regions = system_region_list($theme);

  $item = menu_get_item();
  if ($item === FALSE || $item['path'] != 'admin/structure/block/demo/' . $theme) {
    // Load all region content assigned via blocks.
    foreach (array_keys($all_regions) as $region) {
      // Assign blocks to region.
      if ($blocks = block_get_blocks_by_region($region)) {
        $page[$region] = $blocks;
      }
    }
    // Once we've finished attaching all blocks to the page, clear the static
    // cache to allow modules to alter the block list differently in different
    // contexts. For example, any code that triggers hook_page_build() more
    // than once in the same page request may need to alter the block list
    // differently each time, so that only certain parts of the page are
    // actually built. We do not clear the cache any earlier than this, though,
    // because it is used each time block_get_blocks_by_region() gets called
    // above.
    drupal_static_reset('block_list');
  }
  else {
    // Append region description if we are rendering the regions demo page.
    if ($item['path'] == 'admin/structure/block/demo/' . $theme) {
      foreach (system_region_list($theme, REGIONS_VISIBLE, FALSE) as $region) {
        $description = '<div class="block-region">' . $all_regions[$region] . '</div>';
        $page[$region]['block_description'] = array(
          '#markup' => $description,
          '#weight' => 15,
        );
      }
      $page['page_top']['backlink'] = array(
        '#type' => 'link',
        '#title' => t('Exit block region demonstration'),
        '#href' => 'admin/structure/block' . (variable_get('theme_default', 'bartik') == $theme ? '' : '/list/' . $theme),
        // Add the "overlay-restore" class to indicate this link should restore
        // the context in which the region demonstration page was opened.
        '#options' => array('attributes' => array('class' => array('block-demo-backlink', 'overlay-restore'))),
        '#weight' => -10,
      );
    }
  }
}

/**
 * Gets a renderable array of a region containing all enabled blocks.
 *
 * @param $region
 *   The requested region.
 *
 * @return
 *   A renderable array of a region containing all enabled blocks.
 */
function block_get_blocks_by_region($region) {
  $build = array();
  if ($list = block_list($region)) {
    $build = _block_get_renderable_array($list);
  }
  return $build;
}

/**
 * Gets an array of blocks suitable for drupal_render().
 *
 * @param $list
 *   A list of blocks such as that returned by block_list().
 *
 * @return
 *   A renderable array.
 */
function _block_get_renderable_array($list = array()) {
  $weight = 0;
  $build = array();
  foreach ($list as $key => $block) {
    $build[$key] = $block->content;
    unset($block->content);

    // Add contextual links for this block; skip the main content block, since
    // contextual links are basically output as tabs/local tasks already. Also
    // skip the help block, since we assume that most users do not need or want
    // to perform contextual actions on the help block, and the links needlessly
    // draw attention on it.
    if ($key != 'system_main' && $key != 'system_help') {
      $build[$key]['#contextual_links']['block'] = array(
        'admin/structure/block/manage',
        array($block->module, $block->delta),
      );
    }

    $build[$key] += array(
      '#block' => $block,
      '#weight' => ++$weight,
    );
    $build[$key]['#theme_wrappers'][] = 'block';
  }
  $build['#sorted'] = TRUE;
  return $build;
}

/**
 * Updates the 'block' DB table with the blocks currently exported by modules.
 *
 * @param $theme
 *   The theme to rehash blocks for. If not provided, defaults to the currently
 *   used theme.
 *
 * @return
 *   Blocks currently exported by modules.
 */
function _block_rehash($theme = NULL) {
  global $theme_key;

  drupal_theme_initialize();
  if (!isset($theme)) {
    // If theme is not specifically set, rehash for the current theme.
    $theme = $theme_key;
  }
  $regions = system_region_list($theme);

  // These are the blocks the function will return.
  $blocks = array();
  // These are the blocks defined by code and modified by the database.
  $current_blocks = array();
  // These are {block}.bid values to be kept.
  $bids = array();
  $or = db_or();
  // Gather the blocks defined by modules.
  foreach (module_implements('block_info') as $module) {
    $module_blocks = module_invoke($module, 'block_info');
    $delta_list = array();
    foreach ($module_blocks as $delta => $block) {
      // Compile a condition to retrieve this block from the database.
      // Add identifiers.
      $delta_list[] = $delta;
      $block['module'] = $module;
      $block['delta'] = $delta;
      $block['theme'] = $theme;
      $current_blocks[$module][$delta] = $block;
    }
    if (!empty($delta_list)) {
      $condition = db_and()->condition('module', $module)->condition('delta', $delta_list);
      $or->condition($condition);
    }
  }
  // Save the blocks defined in code for alter context.
  $code_blocks = $current_blocks;
  $database_blocks = db_select('block', 'b', array('fetch' => PDO::FETCH_ASSOC))
    ->fields('b')
    ->condition($or)
    ->condition('theme', $theme)
    ->execute();
  $original_database_blocks = array();
  foreach ($database_blocks as $block) {
    $module = $block['module'];
    $delta = $block['delta'];
    $original_database_blocks[$module][$delta] = $block;
    // The cache mode can only by set from hook_block_info(), so that has
    // precedence over the database's value.
    if (isset($current_blocks[$module][$delta]['cache'])) {
      $block['cache'] = $current_blocks[$module][$delta]['cache'];
    }
    // Preserve info which is not in the database.
    $block['info'] = $current_blocks[$module][$delta]['info'];
    // Blocks stored in the database override the blocks defined in code.
    $current_blocks[$module][$delta] = $block;
    // Preserve this block.
    $bids[$block['bid']] = $block['bid'];
  }
  drupal_alter('block_info', $current_blocks, $theme, $code_blocks);
  foreach ($current_blocks as $module => $module_blocks) {
    foreach ($module_blocks as $delta => $block) {
      // Make sure certain attributes are set.
      $block += array(
        'pages' => '',
        'weight' => 0,
        'status' => 0,
      );
      // Check for active blocks in regions that are not available.
      if (!empty($block['region']) && $block['region'] != BLOCK_REGION_NONE && !isset($regions[$block['region']]) && $block['status'] == 1) {
        drupal_set_message(t('The block %info was assigned to the invalid region %region and has been disabled.', array('%info' => $block['info'], '%region' => $block['region'])), 'warning');
        // Disabled modules are moved into the BLOCK_REGION_NONE later so no
        // need to move the block to another region.
        $block['status'] = 0;
      }
      // Set region to none if not enabled.
      if (empty($block['status'])) {
        $block['status'] = 0;
        $block['region'] = BLOCK_REGION_NONE;
      }
      // There is no point saving disabled blocks. Still, we need to save them
      // because the 'title' attribute is saved to the {blocks} table.
      if (isset($block['bid'])) {
        // If the block has a bid property, it comes from the database and
        // the record needs to be updated, so set the primary key to 'bid'
        // before passing to drupal_write_record().
        $primary_keys = array('bid');
        // Remove a block from the list of blocks to keep if it became disabled.
        unset($bids[$block['bid']]);
      }
      else {
        $primary_keys = array();
      }
      // If the block is new or differs from the original database block, save
      // it. To determine whether there was a change it is enough to examine
      // the values for the keys in the original database record as that
      // contained every database field.
      if (!$primary_keys || array_diff_assoc($original_database_blocks[$module][$delta], $block)) {
        drupal_write_record('block', $block, $primary_keys);
        // Make it possible to test this.
        $block['saved'] = TRUE;
      }
      // Add to the list of blocks we return.
      $blocks[] = $block;
    }
  }
  if ($bids) {
    // Remove disabled that are no longer defined by the code from the
    // database.
    db_delete('block')
      ->condition('bid', $bids, 'NOT IN')
      ->condition('theme', $theme)
      ->execute();
  }
  return $blocks;
}

/**
 * Returns information from database about a user-created (custom) block.
 *
 * @param $bid
 *   ID of the block to get information for.
 *
 * @return
 *   Associative array of information stored in the database for this block.
 *   Array keys:
 *   - bid: Block ID.
 *   - info: Block description.
 *   - body: Block contents.
 *   - format: Filter ID of the filter format for the body.
 */
function block_custom_block_get($bid) {
  return db_query("SELECT * FROM {block_custom} WHERE bid = :bid", array(':bid' => $bid))->fetchAssoc();
}

/**
 * Form constructor for the custom block form.
 *
 * @param $edit
 *   (optional) An associative array of information retrieved by
 *   block_custom_get_block() if an existing block is being edited, or an empty
 *   array otherwise. Defaults to array().
 *
 * @ingroup forms
 */
function block_custom_block_form($edit = array()) {
  $edit += array(
    'info' => '',
    'body' => '',
  );
  $form['info'] = array(
    '#type' => 'textfield',
    '#title' => t('Block description'),
    '#default_value' => $edit['info'],
    '#maxlength' => 64,
    '#description' => t('A brief description of your block. Used on the <a href="@overview">Blocks administration page</a>.', array('@overview' => url('admin/structure/block'))),
    '#required' => TRUE,
    '#weight' => -18,
  );
  $form['body_field']['#weight'] = -17;
  $form['body_field']['body'] = array(
    '#type' => 'text_format',
    '#title' => t('Block body'),
    '#default_value' => $edit['body'],
    '#format' => isset($edit['format']) ? $edit['format'] : NULL,
    '#rows' => 15,
    '#description' => t('The content of the block as shown to the user.'),
    '#required' => TRUE,
    '#weight' => -17,
  );

  return $form;
}

/**
 * Saves a user-created block in the database.
 *
 * @param $edit
 *   Associative array of fields to save. Array keys:
 *   - info: Block description.
 *   - body: Associative array of body value and format.  Array keys:
 *     - value: Block contents.
 *     - format: Filter ID of the filter format for the body.
 * @param $delta
 *   Block ID of the block to save.
 *
 * @return
 *   Always returns TRUE.
 */
function block_custom_block_save($edit, $delta) {
  db_update('block_custom')
    ->fields(array(
      'body' => $edit['body']['value'],
      'info' => $edit['info'],
      'format' => $edit['body']['format'],
    ))
    ->condition('bid', $delta)
    ->execute();
  return TRUE;
}

/**
 * Implements hook_form_FORM_ID_alter() for user_profile_form().
 */
function block_form_user_profile_form_alter(&$form, &$form_state) {
  if ($form['#user_category'] == 'account') {
    $account = $form['#user'];
    $rids = array_keys($account->roles);
    $result = db_query("SELECT DISTINCT b.* FROM {block} b LEFT JOIN {block_role} r ON b.module = r.module AND b.delta = r.delta WHERE b.status = 1 AND b.custom <> 0 AND (r.rid IN (:rids) OR r.rid IS NULL) ORDER BY b.weight, b.module", array(':rids' => $rids));

    $blocks = array();
    foreach ($result as $block) {
      $data = module_invoke($block->module, 'block_info');
      if ($data[$block->delta]['info']) {
        $blocks[$block->module][$block->delta] = array(
          '#type' => 'checkbox',
          '#title' => check_plain($data[$block->delta]['info']),
          '#default_value' => isset($account->data['block'][$block->module][$block->delta]) ? $account->data['block'][$block->module][$block->delta] : ($block->custom == 1),
        );
      }
    }
    // Only display the fieldset if there are any personalizable blocks.
    if ($blocks) {
      $form['block'] = array(
        '#type' => 'fieldset',
        '#title' => t('Personalize blocks'),
        '#description' => t('Blocks consist of content or information that complements the main content of the page. Enable or disable optional blocks using the checkboxes below.'),
        '#weight' => 3,
        '#collapsible' => TRUE,
        '#tree' => TRUE,
      );
      $form['block'] += $blocks;
    }
  }
}

/**
 * Implements hook_user_presave().
 */
function block_user_presave(&$edit, $account, $category) {
  if (isset($edit['block'])) {
    $edit['data']['block'] = $edit['block'];
  }
}

/**
 * Initializes blocks for enabled themes.
 *
 * @param $theme_list
 *   An array of theme names.
 */
function block_themes_enabled($theme_list) {
  foreach ($theme_list as $theme) {
    block_theme_initialize($theme);
  }
}

/**
 * Assigns an initial, default set of blocks for a theme.
 *
 * This function is called the first time a new theme is enabled. The new theme
 * gets a copy of the default theme's blocks, with the difference that if a
 * particular region isn't available in the new theme, the block is assigned
 * to the new theme's default region.
 *
 * @param $theme
 *   The name of a theme.
 */
function block_theme_initialize($theme) {
  // Initialize theme's blocks if none already registered.
  $has_blocks = (bool) db_query_range('SELECT 1 FROM {block} WHERE theme = :theme', 0, 1, array(':theme' => $theme))->fetchField();
  if (!$has_blocks) {
    $default_theme = variable_get('theme_default', 'bartik');
    // Apply only to new theme's visible regions.
    $regions = system_region_list($theme, REGIONS_VISIBLE);
    $result = db_query("SELECT * FROM {block} WHERE theme = :theme", array(':theme' => $default_theme), array('fetch' => PDO::FETCH_ASSOC));
    foreach ($result as $block) {
      // If the region isn't supported by the theme, assign the block to the
      // theme's default region.
      if ($block['status'] && !isset($regions[$block['region']])) {
        $block['region'] = system_default_region($theme);
      }
      $block['theme'] = $theme;
      unset($block['bid']);
      drupal_write_record('block', $block);
    }
  }
}

/**
 * Returns all blocks in the specified region for the current user.
 *
 * @param $region
 *   The name of a region.
 *
 * @return
 *   An array of block objects, indexed with the module name and block delta
 *   concatenated with an underscore, thus: MODULE_DELTA. If you are displaying
 *   your blocks in one or two sidebars, you may check whether this array is
 *   empty to see how many columns are going to be displayed.
 *
 * @todo
 *   Now that the block table has a primary key, we should use that as the
 *   array key instead of MODULE_DELTA.
 */
function block_list($region) {
  $blocks = &drupal_static(__FUNCTION__);

  if (!isset($blocks)) {
    $blocks = _block_load_blocks();
  }

  // Create an empty array if there are no entries.
  if (!isset($blocks[$region])) {
    $blocks[$region] = array();
  }
  else {
    $blocks[$region] = _block_render_blocks($blocks[$region]);
  }

  return $blocks[$region];
}

/**
 * Loads a block object from the database.
 *
 * This function returns the first block matching the module and delta
 * parameters, so it should not be used for theme-specific functionality.
 *
 * @param $module
 *   Name of the module that implements the block to load.
 * @param $delta
 *   Unique ID of the block within the context of $module. Pass NULL to return
 *   an empty block object for $module.
 *
 * @return
 *   A block object.
 */
function block_load($module, $delta) {
  if (isset($delta)) {
    $block = db_query('SELECT * FROM {block} WHERE module = :module AND delta = :delta', array(':module' => $module, ':delta' => $delta))->fetchObject();
  }

  // If the block does not exist in the database yet return a stub block
  // object.
  if (empty($block)) {
    $block = new stdClass();
    $block->module = $module;
    $block->delta = $delta;
  }

  return $block;
}

/**
 * Loads blocks' information from the database.
 *
 * @return
 *   An array of blocks grouped by region.
 */
function _block_load_blocks() {
  global $theme_key;

  $query = db_select('block', 'b');
  $result = $query
    ->fields('b')
    ->condition('b.theme', $theme_key)
    ->condition('b.status', 1)
    ->orderBy('b.region')
    ->orderBy('b.weight')
    ->orderBy('b.module')
    ->addTag('block_load')
    ->addTag('translatable')
    ->execute();

  $block_info = $result->fetchAllAssoc('bid');
  // Allow modules to modify the block list.
  drupal_alter('block_list', $block_info);

  $blocks = array();
  foreach ($block_info as $block) {
    $blocks[$block->region]["{$block->module}_{$block->delta}"] = $block;
  }
  return $blocks;
}

/**
 * Implements hook_block_list_alter().
 *
 * Checks the page, user role, and user-specific visibility settings.
 * Removes the block if the visibility conditions are not met.
 */
function block_block_list_alter(&$blocks) {
  global $user, $theme_key;

  // Build an array of roles for each block.
  $block_roles = array();
  $result = db_query('SELECT module, delta, rid FROM {block_role}');
  foreach ($result as $record) {
    $block_roles[$record->module][$record->delta][] = $record->rid;
  }

  foreach ($blocks as $key => $block) {
    if (!isset($block->theme) || !isset($block->status) || $block->theme != $theme_key || $block->status != 1) {
      // This block was added by a contrib module, leave it in the list.
      continue;
    }

    // If a block has no roles associated, it is displayed for every role.
    // For blocks with roles associated, if none of the user's roles matches
    // the settings from this block, remove it from the block list.
    if (isset($block_roles[$block->module][$block->delta]) && !array_intersect($block_roles[$block->module][$block->delta], array_keys($user->roles))) {
      // No match.
      unset($blocks[$key]);
      continue;
    }

    // Use the user's block visibility setting, if necessary.
    if ($block->custom != BLOCK_CUSTOM_FIXED) {
      if ($user->uid && isset($user->data['block'][$block->module][$block->delta])) {
        $enabled = $user->data['block'][$block->module][$block->delta];
      }
      else {
        $enabled = ($block->custom == BLOCK_CUSTOM_ENABLED);
      }
    }
    else {
      $enabled = TRUE;
    }

    // Limited visibility blocks must list at least one page.
    if ($block->visibility == BLOCK_VISIBILITY_LISTED && empty($block->pages)) {
      $enabled = FALSE;
    }

    if (!$enabled) {
      unset($blocks[$key]);
      continue;
    }

    // Match path if necessary.
    if ($block->pages) {
      // Convert path to lowercase. This allows comparison of the same path
      // with different case. Ex: /Page, /page, /PAGE.
      $pages = drupal_strtolower($block->pages);
      if ($block->visibility < BLOCK_VISIBILITY_PHP) {
        // Convert the Drupal path to lowercase.
        $path = drupal_strtolower(drupal_get_path_alias($_GET['q']));
        // Compare the lowercase internal and lowercase path alias (if any).
        $page_match = drupal_match_path($path, $pages);
        if ($path != $_GET['q']) {
          $page_match = $page_match || drupal_match_path($_GET['q'], $pages);
        }
        // When $block->visibility has a value of 0
        // (BLOCK_VISIBILITY_NOTLISTED), the block is displayed on all pages
        // except those listed in $block->pages. When set to 1
        // (BLOCK_VISIBILITY_LISTED), it is displayed only on those pages
        // listed in $block->pages.
        $page_match = !($block->visibility xor $page_match);
      }
      elseif (module_exists('php')) {
        $page_match = php_eval($block->pages);
      }
      else {
        $page_match = FALSE;
      }
    }
    else {
      $page_match = TRUE;
    }
    if (!$page_match) {
      unset($blocks[$key]);
    }
  }
}

/**
 * Render the content and subject for a set of blocks.
 *
 * @param $region_blocks
 *   An array of block objects such as returned for one region by
 *   _block_load_blocks().
 *
 * @return
 *   An array of visible blocks as expected by drupal_render().
 */
function _block_render_blocks($region_blocks) {
  $cacheable = TRUE;

  // We preserve the submission of forms in blocks, by fetching from cache only
  // if the request method is 'GET' (or 'HEAD').
  if ($_SERVER['REQUEST_METHOD'] != 'GET' && $_SERVER['REQUEST_METHOD'] != 'HEAD') {
    $cacheable = FALSE;
  }
  // Block caching is not usually compatible with node access modules, so by
  // default it is disabled when node access modules exist. However, it can be
  // allowed by using the variable 'block_cache_bypass_node_grants'.
  elseif (!variable_get('block_cache_bypass_node_grants', FALSE) && count(module_implements('node_grants'))) {
    $cacheable = FALSE;
  }

  // Proceed to loop over all blocks in order to compute their respective cache
  // identifiers; this allows us to do one single cache_get_multiple() call
  // instead of doing one cache_get() call per block.
  $cached_blocks = array();
  $cids = array();

  if ($cacheable) {
    foreach ($region_blocks as $key => $block) {
      if (!isset($block->content)) {
        if (($cid = _block_get_cache_id($block))) {
          $cids[$key] = $cid;
        }
      }
    }

    if ($cids) {
      // We cannot pass $cids in directly because cache_get_multiple() will
      // modify it, and we need to use it later on in this function.
      $cid_values = array_values($cids);
      $cached_blocks = cache_get_multiple($cid_values, 'cache_block');
    }
  }

  foreach ($region_blocks as $key => $block) {
    // Render the block content if it has not been created already.
    if (!isset($block->content)) {
      // Erase the block from the static array - we'll put it back if it has
      // content.
      unset($region_blocks[$key]);

      $cid = empty($cids[$key]) ? NULL : $cids[$key];

      // Try fetching the block from the previously loaded cache entries.
      if (isset($cached_blocks[$cid])) {
        $array = $cached_blocks[$cid]->data;
      }
      else {
        $array = module_invoke($block->module, 'block_view', $block->delta);

        // Valid PHP function names cannot contain hyphens.
        $delta = str_replace('-', '_', $block->delta);
        // Allow modules to modify the block before it is viewed, via either
        // hook_block_view_alter() or hook_block_view_MODULE_DELTA_alter().
        drupal_alter(array('block_view', "block_view_{$block->module}_{$delta}"), $array, $block);

        if (isset($cid)) {
          cache_set($cid, $array, 'cache_block', CACHE_TEMPORARY);
        }
      }

      if (isset($array) && is_array($array)) {
        foreach ($array as $k => $v) {
          $block->$k = $v;
        }
      }
      if (isset($block->content) && $block->content) {
        // Normalize to the drupal_render() structure.
        if (is_string($block->content)) {
          $block->content = array('#markup' => $block->content);
        }
        // Override default block title if a custom display title is present.
        if ($block->title) {
          // Check plain here to allow module generated titles to keep any
          // markup.
          $block->subject = $block->title == '<none>' ? '' : check_plain($block->title);
        }
        if (!isset($block->subject)) {
          $block->subject = '';
        }
        $region_blocks["{$block->module}_{$block->delta}"] = $block;
      }
    }
  }
  return $region_blocks;
}

/**
 * Assemble the cache_id to use for a given block.
 *
 * The cache_id string reflects the viewing context for the current block
 * instance, obtained by concatenating the relevant context information
 * (user, page, ...) according to the block's cache settings (BLOCK_CACHE_*
 * constants). Two block instances can use the same cached content when
 * they share the same cache_id.
 *
 * Theme and language contexts are automatically differentiated.
 *
 * @param $block
 *   The block to get the cache_id from.
 *
 * @return
 *   The string used as cache_id for the block.
 */
function _block_get_cache_id($block) {
  global $user;

  // User 1 being out of the regular 'roles define permissions' schema,
  // it brings too many chances of having unwanted output get in the cache
  // and later be served to other users. We therefore exclude user 1 from
  // block caching.
  if (variable_get('block_cache', FALSE) && !in_array($block->cache, array(DRUPAL_NO_CACHE, DRUPAL_CACHE_CUSTOM)) && $user->uid != 1) {
    // Start with common sub-patterns: block identification, theme, language.
    $cid_parts[] = $block->module;
    $cid_parts[] = $block->delta;
    drupal_alter('block_cid_parts', $cid_parts, $block);
    $cid_parts = array_merge($cid_parts, drupal_render_cid_parts($block->cache));

    return implode(':', $cid_parts);
  }
}

/**
 * Implements hook_flush_caches().
 */
function block_flush_caches() {
  // Rehash blocks for active themes. We don't use list_themes() here,
  // because if MAINTENANCE_MODE is defined it skips reading the database,
  // and we can't tell which themes are active.
  $themes = db_query("SELECT name FROM {system} WHERE type = 'theme' AND status = 1");
  foreach ($themes as $theme) {
    _block_rehash($theme->name);
  }

  return array('cache_block');
}

/**
 * Processes variables for block.tpl.php.
 *
 * Prepares the values passed to the theme_block function to be passed
 * into a pluggable template engine. Uses block properties to generate a
 * series of template file suggestions. If none are found, the default
 * block.tpl.php is used.
 *
 * Most themes utilize their own copy of block.tpl.php. The default is located
 * inside "modules/block/block.tpl.php". Look in there for the full list of
 * variables.
 *
 * The $variables array contains the following arguments:
 * - $block
 *
 * @see block.tpl.php
 */
function template_preprocess_block(&$variables) {
  $block_counter = &drupal_static(__FUNCTION__, array());
  $variables['block'] = $variables['elements']['#block'];
  // All blocks get an independent counter for each region.
  if (!isset($block_counter[$variables['block']->region])) {
    $block_counter[$variables['block']->region] = 1;
  }
  // Same with zebra striping.
  $variables['block_zebra'] = ($block_counter[$variables['block']->region] % 2) ? 'odd' : 'even';
  $variables['block_id'] = $block_counter[$variables['block']->region]++;

  // Create the $content variable that templates expect.
  $variables['content'] = $variables['elements']['#children'];

  $variables['classes_array'][] = drupal_html_class('block-' . $variables['block']->module);

  $variables['theme_hook_suggestions'][] = 'block__' . $variables['block']->region;
  $variables['theme_hook_suggestions'][] = 'block__' . $variables['block']->module;
  // Hyphens (-) and underscores (_) play a special role in theme suggestions.
  // Theme suggestions should only contain underscores, because within
  // drupal_find_theme_templates(), underscores are converted to hyphens to
  // match template file names, and then converted back to underscores to match
  // pre-processing and other function names. So if your theme suggestion
  // contains a hyphen, it will end up as an underscore after this conversion,
  // and your function names won't be recognized. So, we need to convert
  // hyphens to underscores in block deltas for the theme suggestions.
  $variables['theme_hook_suggestions'][] = 'block__' . $variables['block']->module . '__' . strtr($variables['block']->delta, '-', '_');

  // Create a valid HTML ID and make sure it is unique.
  $variables['block_html_id'] = drupal_html_id('block-' . $variables['block']->module . '-' . $variables['block']->delta);
}

/**
 * Implements hook_user_role_delete().
 *
 * Removes deleted role from blocks that use it.
 */
function block_user_role_delete($role) {
  db_delete('block_role')
    ->condition('rid', $role->rid)
    ->execute();
}

/**
 * Implements hook_menu_delete().
 */
function block_menu_delete($menu) {
  db_delete('block')
    ->condition('module', 'menu')
    ->condition('delta', $menu['menu_name'])
    ->execute();
  db_delete('block_role')
    ->condition('module', 'menu')
    ->condition('delta', $menu['menu_name'])
    ->execute();
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function block_form_system_performance_settings_alter(&$form, &$form_state) {
  $disabled = (!variable_get('block_cache_bypass_node_grants', FALSE) && count(module_implements('node_grants')));
  $form['caching']['block_cache'] = array(
    '#type' => 'checkbox',
    '#title' => t('Cache blocks'),
    '#default_value' => variable_get('block_cache', FALSE),
    '#disabled' => $disabled,
    '#description' => $disabled ? t('Block caching is inactive because you have enabled modules defining content access restrictions.') : NULL,
    '#weight' => -1,
  );
}

/**
 * Implements hook_admin_paths().
 */
function block_admin_paths() {
  $paths = array(
    // Exclude the block demonstration page from admin (overlay) treatment.
    // This allows us to present this page in its true form, full page.
    'admin/structure/block/demo/*' => FALSE,
  );
  return $paths;
}

/**
 * Implements hook_modules_uninstalled().
 *
 * Cleans up {block} and {block_role} tables from modules' blocks.
 */
function block_modules_uninstalled($modules) {
  db_delete('block')
    ->condition('module', $modules, 'IN')
    ->execute();
  db_delete('block_role')
    ->condition('module', $modules, 'IN')
    ->execute();
}
