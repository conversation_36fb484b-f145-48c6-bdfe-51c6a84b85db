<?php

/**
 * @file
 * Adds contextual links to perform actions related to elements on a page.
 */

/**
 * Implements hook_help().
 */
function contextual_help($path, $arg) {
  switch ($path) {
    case 'admin/help#contextual':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Contextual links module displays links related to regions of pages on your site to users with <em>access contextual links</em> permission. For more information, see the online handbook entry for <a href="@contextual">Contextual links module</a>.', array('@contextual' => 'http://drupal.org/documentation/modules/contextual')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Displaying contextual links') . '</dt>';
      $output .= '<dd>' . t('Contextual links are supplied by modules, to give you quick access to tasks associated with regions of pages on your site. For instance, if you have a custom menu block displayed in a sidebar of your site, the Blocks and Menus modules will supply links to configure the block and edit the menu. The Contextual links module collects these links into a list for display by your theme, and also adds JavaScript code to the page to hide the links initially, and display them when your mouse hovers over the block.') . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_permission().
 */
function contextual_permission() {
  return array(
    'access contextual links' => array(
      'title' => t('Use contextual links'),
      'description' => t('Use contextual links to perform actions related to elements on a page.'),
    ),
  );
}

/**
 * Implements hook_library().
 */
function contextual_library() {
  $path = drupal_get_path('module', 'contextual');
  $libraries['contextual-links'] = array(
    'title' => 'Contextual links',
    'website' => 'http://drupal.org/node/473268',
    'version' => '1.0',
    'js' => array(
      $path . '/contextual.js' => array(),
    ),
    'css' => array(
      $path . '/contextual.css' => array(),
    ),
  );
  return $libraries;
}

/**
 * Implements hook_element_info().
 */
function contextual_element_info() {
  $types['contextual_links'] = array(
    '#pre_render' => array('contextual_pre_render_links'),
    '#theme' => 'links__contextual',
    '#links' => array(),
    '#prefix' => '<div class="contextual-links-wrapper">',
    '#suffix' => '</div>',
    '#attributes' => array(
      'class' => array('contextual-links'),
    ),
    '#attached' => array(
      'library' => array(
        array('contextual', 'contextual-links'),
      ),
    ),
  );
  return $types;
}

/**
 * Implements hook_preprocess().
 *
 * @see contextual_pre_render_links()
 */
function contextual_preprocess(&$variables, $hook) {
  // Nothing to do here if the user is not permitted to access contextual links.
  if (!user_access('access contextual links')) {
    return;
  }

  $hooks = theme_get_registry(FALSE);

  // Determine the primary theme function argument.
  if (!empty($hooks[$hook]['variables'])) {
    $keys = array_keys($hooks[$hook]['variables']);
    $key = $keys[0];
  }
  elseif (!empty($hooks[$hook]['render element'])) {
    $key = $hooks[$hook]['render element'];
  }
  if (!empty($key) && isset($variables[$key])) {
    $element = $variables[$key];
  }

  if (isset($element) && is_array($element) && !empty($element['#contextual_links'])) {
    // Initialize the template variable as a renderable array.
    $variables['title_suffix']['contextual_links'] = array(
      '#type' => 'contextual_links',
      '#contextual_links' => $element['#contextual_links'],
      '#element' => $element,
    );
    // Mark this element as potentially having contextual links attached to it.
    $variables['classes_array'][] = 'contextual-links-region';
  }
}

/**
 * Build a renderable array for contextual links.
 *
 * @param $element
 *   A renderable array containing a #contextual_links property, which is a
 *   keyed array. Each key is the name of the implementing module, and each
 *   value is an array that forms the function arguments for
 *   menu_contextual_links(). For example:
 *   @code
 *     array('#contextual_links' => array(
 *       'block' => array('admin/structure/block/manage', array('system', 'navigation')),
 *       'menu' => array('admin/structure/menu/manage', array('navigation')),
 *     ))
 *   @endcode
 *
 * @return
 *   A renderable array representing contextual links.
 *
 * @see menu_contextual_links()
 * @see contextual_element_info()
 */
function contextual_pre_render_links($element) {
  // Retrieve contextual menu links.
  $items = array();
  foreach ($element['#contextual_links'] as $module => $args) {
    $items += menu_contextual_links($module, $args[0], $args[1]);
  }

  // Transform contextual links into parameters suitable for theme_link().
  $links = array();
  foreach ($items as $class => $item) {
    $class = drupal_html_class($class);
    $links[$class] = array(
      'title' => $item['title'],
      'href' => $item['href'],
    );
    // @todo theme_links() should *really* use the same parameters as l().
    $item['localized_options'] += array('query' => array());
    $item['localized_options']['query'] += drupal_get_destination();
    $links[$class] += $item['localized_options'];
  }
  $element['#links'] = $links;

  // Allow modules to alter the renderable contextual links element.
  drupal_alter('contextual_links_view', $element, $items);

  // If there are no links, tell drupal_render() to abort rendering.
  if (empty($element['#links'])) {
    $element['#printed'] = TRUE;
  }

  return $element;
}

