<?php

/**
 * @file
 * Enables site-wide keyword searching.
 */

/**
 * Matches all 'N' Unicode character classes (numbers)
 */
define('PREG_CLASS_NUMBERS',
  '\x{30}-\x{39}\x{b2}\x{b3}\x{b9}\x{bc}-\x{be}\x{660}-\x{669}\x{6f0}-\x{6f9}' .
  '\x{966}-\x{96f}\x{9e6}-\x{9ef}\x{9f4}-\x{9f9}\x{a66}-\x{a6f}\x{ae6}-\x{aef}' .
  '\x{b66}-\x{b6f}\x{be7}-\x{bf2}\x{c66}-\x{c6f}\x{ce6}-\x{cef}\x{d66}-\x{d6f}' .
  '\x{e50}-\x{e59}\x{ed0}-\x{ed9}\x{f20}-\x{f33}\x{1040}-\x{1049}\x{1369}-' .
  '\x{137c}\x{16ee}-\x{16f0}\x{17e0}-\x{17e9}\x{17f0}-\x{17f9}\x{1810}-\x{1819}' .
  '\x{1946}-\x{194f}\x{2070}\x{2074}-\x{2079}\x{2080}-\x{2089}\x{2153}-\x{2183}' .
  '\x{2460}-\x{249b}\x{24ea}-\x{24ff}\x{2776}-\x{2793}\x{3007}\x{3021}-\x{3029}' .
  '\x{3038}-\x{303a}\x{3192}-\x{3195}\x{3220}-\x{3229}\x{3251}-\x{325f}\x{3280}-' .
  '\x{3289}\x{32b1}-\x{32bf}\x{ff10}-\x{ff19}');

/**
 * Matches all 'P' Unicode character classes (punctuation)
 */
define('PREG_CLASS_PUNCTUATION',
  '\x{21}-\x{23}\x{25}-\x{2a}\x{2c}-\x{2f}\x{3a}\x{3b}\x{3f}\x{40}\x{5b}-\x{5d}' .
  '\x{5f}\x{7b}\x{7d}\x{a1}\x{ab}\x{b7}\x{bb}\x{bf}\x{37e}\x{387}\x{55a}-\x{55f}' .
  '\x{589}\x{58a}\x{5be}\x{5c0}\x{5c3}\x{5f3}\x{5f4}\x{60c}\x{60d}\x{61b}\x{61f}' .
  '\x{66a}-\x{66d}\x{6d4}\x{700}-\x{70d}\x{964}\x{965}\x{970}\x{df4}\x{e4f}' .
  '\x{e5a}\x{e5b}\x{f04}-\x{f12}\x{f3a}-\x{f3d}\x{f85}\x{104a}-\x{104f}\x{10fb}' .
  '\x{1361}-\x{1368}\x{166d}\x{166e}\x{169b}\x{169c}\x{16eb}-\x{16ed}\x{1735}' .
  '\x{1736}\x{17d4}-\x{17d6}\x{17d8}-\x{17da}\x{1800}-\x{180a}\x{1944}\x{1945}' .
  '\x{2010}-\x{2027}\x{2030}-\x{2043}\x{2045}-\x{2051}\x{2053}\x{2054}\x{2057}' .
  '\x{207d}\x{207e}\x{208d}\x{208e}\x{2329}\x{232a}\x{23b4}-\x{23b6}\x{2768}-' .
  '\x{2775}\x{27e6}-\x{27eb}\x{2983}-\x{2998}\x{29d8}-\x{29db}\x{29fc}\x{29fd}' .
  '\x{3001}-\x{3003}\x{3008}-\x{3011}\x{3014}-\x{301f}\x{3030}\x{303d}\x{30a0}' .
  '\x{30fb}\x{fd3e}\x{fd3f}\x{fe30}-\x{fe52}\x{fe54}-\x{fe61}\x{fe63}\x{fe68}' .
  '\x{fe6a}\x{fe6b}\x{ff01}-\x{ff03}\x{ff05}-\x{ff0a}\x{ff0c}-\x{ff0f}\x{ff1a}' .
  '\x{ff1b}\x{ff1f}\x{ff20}\x{ff3b}-\x{ff3d}\x{ff3f}\x{ff5b}\x{ff5d}\x{ff5f}-' .
  '\x{ff65}');

/**
 * Matches CJK (Chinese, Japanese, Korean) letter-like characters.
 *
 * This list is derived from the "East Asian Scripts" section of
 * http://www.unicode.org/charts/index.html, as well as a comment on
 * http://unicode.org/reports/tr11/tr11-11.html listing some character
 * ranges that are reserved for additional CJK ideographs.
 *
 * The character ranges do not include numbers, punctuation, or symbols, since
 * these are handled separately in search. Note that radicals and strokes are
 * considered symbols. (See
 * http://www.unicode.org/Public/UNIDATA/extracted/DerivedGeneralCategory.txt)
 *
 * @see search_expand_cjk()
 */
define('PREG_CLASS_CJK', '\x{1100}-\x{11FF}\x{3040}-\x{309F}\x{30A1}-\x{318E}' .
  '\x{31A0}-\x{31B7}\x{31F0}-\x{31FF}\x{3400}-\x{4DBF}\x{4E00}-\x{9FCF}' .
  '\x{A000}-\x{A48F}\x{A4D0}-\x{A4FD}\x{A960}-\x{A97F}\x{AC00}-\x{D7FF}' .
  '\x{F900}-\x{FAFF}\x{FF21}-\x{FF3A}\x{FF41}-\x{FF5A}\x{FF66}-\x{FFDC}' .
  '\x{20000}-\x{2FFFD}\x{30000}-\x{3FFFD}');

/**
 * Implements hook_help().
 */
function search_help($path, $arg) {
  switch ($path) {
    case 'admin/help#search':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Search module provides the ability to index and search for content by exact keywords, and for users by username or e-mail. For more information, see the online handbook entry for <a href="@search-module">Search module</a>.', array('@search-module' => 'http://drupal.org/documentation/modules/search/', '@search' => url('search'))) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Searching content and users') . '</dt>';
      $output .= '<dd>' . t('Users with <em>Use search</em> permission can use the search block and <a href="@search">Search page</a>. Users with the <em>View published content</em> permission can search for content containing exact keywords. Users with the <em>View user profiles</em> permission can search for users containing the keyword anywhere in the user name, and users with the <em>Administer users</em> permission can search for users by email address. Additionally, users with <em>Use advanced search</em> permission can find content using more complex search methods and filtering by choosing the <em>Advanced search</em> option on the <a href="@search">Search page</a>.', array('@search' => url('search'))) . '</dd>';
      $output .= '<dt>' . t('Indexing content with cron') . '</dt>';
      $output .= '<dd>' . t('To provide keyword searching, the search engine maintains an index of words found in the content and its fields, along with text added to your content by other modules (such as comments from the core Comment module, and taxonomy terms from the core Taxonomy module). To build and maintain this index, a correctly configured <a href="@cron">cron maintenance task</a> is required. Users with <em>Administer search</em> permission can further configure the cron settings on the <a href="@searchsettings">Search settings page</a>.', array('@cron' => 'http://drupal.org/cron', '@searchsettings' => url('admin/config/search/settings'))) . '</dd>';
      $output .= '<dt>' . t('Content reindexing') . '</dt>';
      $output .= '<dd>' . t('Content-related actions on your site (creating, editing, or deleting content and comments) automatically cause affected content items to be marked for indexing or reindexing at the next cron run. When content is marked for reindexing, the previous content remains in the index until cron runs, at which time it is replaced by the new content. Unlike content-related actions, actions related to the structure of your site do not cause affected content to be marked for reindexing. Examples of structure-related actions that affect content include deleting or editing taxonomy terms, enabling or disabling modules that add text to content (such as Taxonomy, Comment, and field-providing modules), and modifying the fields or display parameters of your content types. If you take one of these actions and you want to ensure that the search index is updated to reflect your changed site structure, you can mark all content for reindexing by clicking the "Re-index site" button on the <a href="@searchsettings">Search settings page</a>. If you have a lot of content on your site, it may take several cron runs for the content to be reindexed.', array('@searchsettings' => url('admin/config/search/settings'))) . '</dd>';
      $output .= '<dt>' . t('Configuring search settings') . '</dt>';
      $output .= '<dd>' . t('Indexing behavior can be adjusted using the <a href="@searchsettings">Search settings page</a>. Users with <em>Administer search</em> permission can control settings such as the <em>Number of items to index per cron run</em>, <em>Indexing settings</em> (word length), <em>Active search modules</em>, and <em>Content ranking</em>, which lets you adjust the priority in which indexed content is returned in results.', array('@searchsettings' => url('admin/config/search/settings'))) . '</dd>';
      $output .= '<dt>' . t('Search block') . '</dt>';
      $output .= '<dd>' . t('The Search module includes a default <em>Search form</em> block, which can be enabled and configured on the <a href="@blocks">Blocks administration page</a>. The block is available to users with the <em>Search content</em> permission.', array('@blocks' => url('admin/structure/block'))) . '</dd>';
      $output .= '<dt>' . t('Extending Search module') . '</dt>';
      $output .= '<dd>' . t('By default, the Search module only supports exact keyword matching in content searches. You can modify this behavior by installing a language-specific stemming module for your language (such as <a href="http://drupal.org/project/porterstemmer">Porter Stemmer</a> for American English), which allows words such as walk, walking, and walked to be matched in the Search module. Another approach is to use a third-party search technology with stemming or partial word matching features built in, such as <a href="http://drupal.org/project/apachesolr">Apache Solr</a> or <a href="http://drupal.org/project/sphinx">Sphinx</a>. These and other <a href="@contrib-search">search-related contributed modules</a> can be downloaded by visiting Drupal.org.', array('@contrib-search' => 'http://drupal.org/project/modules?filters=tid%3A105')) . '</dd>';
      $output .= '</dl>';
      return $output;
    case 'admin/config/search/settings':
      return '<p>' . t('The search engine maintains an index of words found in your site\'s content. To build and maintain this index, a correctly configured <a href="@cron">cron maintenance task</a> is required. Indexing behavior can be adjusted using the settings below.', array('@cron' => url('admin/reports/status'))) . '</p>';
    case 'search#noresults':
      return t('<ul>
<li>Check if your spelling is correct.</li>
<li>Remove quotes around phrases to search for each word individually. <em>bike shed</em> will often show more results than <em>&quot;bike shed&quot;</em>.</li>
<li>Consider loosening your query with <em>OR</em>. <em>bike OR shed</em> will often show more results than <em>bike shed</em>.</li>
</ul>');
  }
}

/**
 * Implements hook_theme().
 */
function search_theme() {
  return array(
    'search_block_form' => array(
      'render element' => 'form',
      'template' => 'search-block-form',
    ),
    'search_result' => array(
      'variables' => array('result' => NULL, 'module' => NULL),
      'file' => 'search.pages.inc',
      'template' => 'search-result',
    ),
    'search_results' => array(
      'variables' => array('results' => NULL, 'module' => NULL),
      'file' => 'search.pages.inc',
      'template' => 'search-results',
    ),
  );
}

/**
 * Implements hook_permission().
 */
function search_permission() {
  return array(
    'administer search' => array(
      'title' => t('Administer search'),
    ),
    'search content' => array(
      'title' => t('Use search'),
    ),
    'use advanced search' => array(
      'title' => t('Use advanced search'),
    ),
  );
}

/**
 * Implements hook_block_info().
 */
function search_block_info() {
  $blocks['form']['info'] = t('Search form');
  // Not worth caching.
  $blocks['form']['cache'] = DRUPAL_NO_CACHE;
  $blocks['form']['properties']['administrative'] = TRUE;
  return $blocks;
}

/**
 * Implements hook_block_view().
 */
function search_block_view($delta = '') {
  if (user_access('search content')) {
    $block['content'] = drupal_get_form('search_block_form');
    return $block;
  }
}

/**
 * Implements hook_menu().
 */
function search_menu() {
  $items['search'] = array(
    'title' => 'Search',
    'page callback' => 'search_view',
    'access callback' => 'search_is_active',
    'type' => MENU_SUGGESTED_ITEM,
    'file' => 'search.pages.inc',
  );
  $items['admin/config/search/settings'] = array(
    'title' => 'Search settings',
    'description' => 'Configure relevance settings for search and other indexing options.',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('search_admin_settings'),
    'access arguments' => array('administer search'),
    'weight' => -10,
    'file' => 'search.admin.inc',
  );
  $items['admin/config/search/settings/reindex'] = array(
    'title' => 'Clear index',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('search_reindex_confirm'),
    'access arguments' => array('administer search'),
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
    'file' => 'search.admin.inc',
  );

  // Add paths for searching. We add each module search path twice: once without
  // and once with %menu_tail appended. The reason for this is that we want to
  // preserve keywords when switching tabs, and also to have search tabs
  // highlighted properly. The only way to do that within the Drupal menu
  // system appears to be having two sets of tabs. See discussion on issue
  // http://drupal.org/node/245103 for details.

  drupal_static_reset('search_get_info');
  $default_info = search_get_default_module_info();
  if ($default_info) {
    foreach (search_get_info() as $module => $search_info) {
      $path = 'search/' . $search_info['path'];
      $items[$path] = array(
        'title' => $search_info['title'],
        'page callback' => 'search_view',
        'page arguments' => array($module, ''),
        'access callback' => '_search_menu_access',
        'access arguments' => array($module),
        'type' => MENU_LOCAL_TASK,
        'file' => 'search.pages.inc',
        'weight' => $module == $default_info['module'] ? -10 : 0,
      );
      $items["$path/%menu_tail"] = array(
        'title' => $search_info['title'],
        'load arguments' => array('%map', '%index'),
        'page callback' => 'search_view',
        'page arguments' => array($module, 2),
        'access callback' => '_search_menu_access',
        'access arguments' => array($module),
        // The default local task points to its parent, but this item points to
        // where it should so it should not be changed.
        'type' => MENU_LOCAL_TASK,
        'file' => 'search.pages.inc',
        'weight' => 0,
        // These tabs are not subtabs.
        'tab_root' => 'search/' . $default_info['path'] . '/%',
        // These tabs need to display at the same level.
        'tab_parent' => 'search/' . $default_info['path'],
      );
    }
  }
  return $items;
}

/**
 * Determines access for the ?q=search path.
 */
function search_is_active() {
  // This path cannot be accessed if there are no active modules.
  return user_access('search content') && search_get_info();
}

/**
 * Returns information about available search modules.
 *
 * @param $all
 *   If TRUE, information about all enabled modules implementing
 *   hook_search_info() will be returned. If FALSE (default), only modules that
 *   have been set to active on the search settings page will be returned.
 *
 * @return
 *   Array of hook_search_info() return values, keyed by module name. The
 *   'title' and 'path' array elements will be set to defaults for each module
 *   if not supplied by hook_search_info(), and an additional array element of
 *   'module' will be added (set to the module name).
 */
function search_get_info($all = FALSE) {
  $search_hooks = &drupal_static(__FUNCTION__);

  if (!isset($search_hooks)) {
    foreach (module_implements('search_info') as $module) {
      $search_hooks[$module] = call_user_func($module . '_search_info');
      // Use module name as the default value.
      $search_hooks[$module] += array('title' => $module, 'path' => $module);
      // Include the module name itself in the array.
      $search_hooks[$module]['module'] = $module;
    }
  }

  if ($all) {
    return $search_hooks;
  }

  $active = variable_get('search_active_modules', array('node', 'user'));
  return array_intersect_key($search_hooks, array_flip($active));
}

/**
 * Returns information about the default search module.
 *
 * @return
 *    The search_get_info() array element for the default search module, if any.
 */
function search_get_default_module_info() {
  $info = search_get_info();
  $default = variable_get('search_default_module', 'node');
  if (isset($info[$default])) {
    return $info[$default];
  }
  // The variable setting does not match any active module, so just return
  // the info for the first active module (if any).
  return reset($info);
}

/**
 * Access callback for search tabs.
 */
function _search_menu_access($name) {
  return user_access('search content') && (!function_exists($name . '_search_access') || module_invoke($name, 'search_access'));
}

/**
 * Clears a part of or the entire search index.
 *
 * @param $sid
 *   (optional) The ID of the item to remove from the search index. If
 *   specified, $module must also be given. Omit both $sid and $module to clear
 *   the entire search index.
 * @param $module
 *   (optional) The machine-readable name of the module for the item to remove
 *   from the search index.
 */
function search_reindex($sid = NULL, $module = NULL, $reindex = FALSE) {
  if ($module == NULL && $sid == NULL) {
    module_invoke_all('search_reset');
  }
  else {
    db_delete('search_dataset')
      ->condition('sid', $sid)
      ->condition('type', $module)
      ->execute();
    db_delete('search_index')
      ->condition('sid', $sid)
      ->condition('type', $module)
      ->execute();
    // Don't remove links if re-indexing.
    if (!$reindex) {
      db_delete('search_node_links')
        ->condition('sid', $sid)
        ->condition('type', $module)
        ->execute();
    }
  }
}

/**
 * Marks a word as "dirty" (changed), or retrieves the list of dirty words.
 *
 * This is used during indexing (cron). Words that are dirty have outdated
 * total counts in the search_total table, and need to be recounted.
 */
function search_dirty($word = NULL) {
  $dirty = &drupal_static(__FUNCTION__, array());
  if ($word !== NULL) {
    $dirty[$word] = TRUE;
  }
  else {
    return $dirty;
  }
}

/**
 * Implements hook_cron().
 *
 * Fires hook_update_index() in all modules and cleans up dirty words.
 *
 * @see search_dirty()
 */
function search_cron() {
  // We register a shutdown function to ensure that search_total is always up
  // to date.
  drupal_register_shutdown_function('search_update_totals');

  foreach (variable_get('search_active_modules', array('node', 'user')) as $module) {
    // Update word index
    module_invoke($module, 'update_index');
  }
}

/**
 * Updates the {search_total} database table.
 *
 * This function is called on shutdown to ensure that {search_total} is always
 * up to date (even if cron times out or otherwise fails).
 */
function search_update_totals() {
  // Update word IDF (Inverse Document Frequency) counts for new/changed words.
  foreach (search_dirty() as $word => $dummy) {
    // Get total count
    $total = db_query("SELECT SUM(score) FROM {search_index} WHERE word = :word", array(':word' => $word), array('target' => 'slave'))->fetchField();
    // Apply Zipf's law to equalize the probability distribution.
    $total = log10(1 + 1/(max(1, $total)));
    db_merge('search_total')
      ->key(array('word' => $word))
      ->fields(array('count' => $total))
      ->execute();
  }
  // Find words that were deleted from search_index, but are still in
  // search_total. We use a LEFT JOIN between the two tables and keep only the
  // rows which fail to join.
  $result = db_query("SELECT t.word AS realword, i.word FROM {search_total} t LEFT JOIN {search_index} i ON t.word = i.word WHERE i.word IS NULL", array(), array('target' => 'slave'));
  $or = db_or();
  foreach ($result as $word) {
    $or->condition('word', $word->realword);
  }
  if (count($or) > 0) {
    db_delete('search_total')
      ->condition($or)
      ->execute();
  }
}

/**
 * Simplifies a string according to indexing rules.
 *
 * @param $text
 *   Text to simplify.
 *
 * @return
 *   Simplified text.
 *
 * @see hook_search_preprocess()
 */
function search_simplify($text) {
  // Decode entities to UTF-8
  $text = decode_entities($text);

  // Lowercase
  $text = drupal_strtolower($text);

  // Call an external processor for word handling.
  search_invoke_preprocess($text);

  // Simple CJK handling
  if (variable_get('overlap_cjk', TRUE)) {
    $text = preg_replace_callback('/[' . PREG_CLASS_CJK . ']+/u', 'search_expand_cjk', $text);
  }

  // To improve searching for numerical data such as dates, IP addresses
  // or version numbers, we consider a group of numerical characters
  // separated only by punctuation characters to be one piece.
  // This also means that searching for e.g. '20/03/1984' also returns
  // results with '20-03-1984' in them.
  // Readable regexp: ([number]+)[punctuation]+(?=[number])
  $text = preg_replace('/([' . PREG_CLASS_NUMBERS . ']+)[' . PREG_CLASS_PUNCTUATION . ']+(?=[' . PREG_CLASS_NUMBERS . '])/u', '\1', $text);

  // Multiple dot and dash groups are word boundaries and replaced with space.
  // No need to use the unicode modifer here because 0-127 ASCII characters
  // can't match higher UTF-8 characters as the leftmost bit of those are 1.
  $text = preg_replace('/[.-]{2,}/', ' ', $text);

  // The dot, underscore and dash are simply removed. This allows meaningful
  // search behavior with acronyms and URLs. See unicode note directly above.
  $text = preg_replace('/[._-]+/', '', $text);

  // With the exception of the rules above, we consider all punctuation,
  // marks, spacers, etc, to be a word boundary.
  $text = preg_replace('/[' . PREG_CLASS_UNICODE_WORD_BOUNDARY . ']+/u', ' ', $text);

  // Truncate everything to 50 characters.
  $words = explode(' ', $text);
  array_walk($words, '_search_index_truncate');
  $text = implode(' ', $words);

  return $text;
}

/**
 * Splits CJK (Chinese, Japanese, Korean) text into tokens.
 *
 * The Search module matches exact words, where a word is defined to be a
 * sequence of characters delimited by spaces or punctuation. CJK languages are
 * written in long strings of characters, though, not split up into words. So
 * in order to allow search matching, we split up CJK text into tokens
 * consisting of consecutive, overlapping sequences of characters whose length
 * is equal to the 'minimum_word_size' variable. This tokenizing is only done if
 * the 'overlap_cjk' variable is TRUE.
 *
 * @param $matches
 *   This function is a callback for preg_replace_callback(), which is called
 *   from search_simplify(). So, $matches is an array of regular expression
 *   matches, which means that $matches[0] contains the matched text -- a string
 *   of CJK characters to tokenize.
 *
 * @return
 *   Tokenized text, starting and ending with a space character.
 */
function search_expand_cjk($matches) {
  $min = variable_get('minimum_word_size', 3);
  $str = $matches[0];
  $length = drupal_strlen($str);
  // If the text is shorter than the minimum word size, don't tokenize it.
  if ($length <= $min) {
    return ' ' . $str . ' ';
  }
  $tokens = ' ';
  // Build a FIFO queue of characters.
  $chars = array();
  for ($i = 0; $i < $length; $i++) {
    // Add the next character off the beginning of the string to the queue.
    $current = drupal_substr($str, 0, 1);
    $str = substr($str, strlen($current));
    $chars[] = $current;
    if ($i >= $min - 1) {
      // Make a token of $min characters, and add it to the token string.
      $tokens .= implode('', $chars) . ' ';
      // Shift out the first character in the queue.
      array_shift($chars);
    }
  }
  return $tokens;
}

/**
 * Simplifies and splits a string into tokens for indexing.
 */
function search_index_split($text) {
  $last = &drupal_static(__FUNCTION__);
  $lastsplit = &drupal_static(__FUNCTION__ . ':lastsplit');

  if ($last == $text) {
    return $lastsplit;
  }
  // Process words
  $text = search_simplify($text);
  $words = explode(' ', $text);

  // Save last keyword result
  $last = $text;
  $lastsplit = $words;

  return $words;
}

/**
 * Helper function for array_walk in search_index_split.
 */
function _search_index_truncate(&$text) {
  if (is_numeric($text)) {
    $text = ltrim($text, '0');
  }
  $text = truncate_utf8($text, 50);
}

/**
 * Invokes hook_search_preprocess() in modules.
 */
function search_invoke_preprocess(&$text) {
  foreach (module_implements('search_preprocess') as $module) {
    $text = module_invoke($module, 'search_preprocess', $text);
  }
}

/**
 * Update the full-text search index for a particular item.
 *
 * @param $sid
 *   An ID number identifying this particular item (e.g., node ID).
 * @param $module
 *   The machine-readable name of the module that this item comes from (a module
 *   that implements hook_search_info()).
 * @param $text
 *   The content of this item. Must be a piece of HTML or plain text.
 *
 * @ingroup search
 */
function search_index($sid, $module, $text) {
  $minimum_word_size = variable_get('minimum_word_size', 3);

  // Link matching
  global $base_url;
  $node_regexp = '@href=[\'"]?(?:' . preg_quote($base_url, '@') . '/|' . preg_quote(base_path(), '@') . ')(?:\?q=)?/?((?![a-z]+:)[^\'">]+)[\'">]@i';

  // Multipliers for scores of words inside certain HTML tags. The weights are stored
  // in a variable so that modules can overwrite the default weights.
  // Note: 'a' must be included for link ranking to work.
  $tags = variable_get('search_tag_weights', array(
    'h1' => 25,
    'h2' => 18,
    'h3' => 15,
    'h4' => 12,
    'h5' => 9,
    'h6' => 6,
    'u' => 3,
    'b' => 3,
    'i' => 3,
    'strong' => 3,
    'em' => 3,
    'a' => 10));

  // Strip off all ignored tags to speed up processing, but insert space before/after
  // them to keep word boundaries.
  $text = str_replace(array('<', '>'), array(' <', '> '), $text);
  $text = strip_tags($text, '<' . implode('><', array_keys($tags)) . '>');

  // Split HTML tags from plain text.
  $split = preg_split('/\s*<([^>]+?)>\s*/', $text, -1, PREG_SPLIT_DELIM_CAPTURE);
  // Note: PHP ensures the array consists of alternating delimiters and literals
  // and begins and ends with a literal (inserting $null as required).

  $tag = FALSE; // Odd/even counter. Tag or no tag.
  $link = FALSE; // State variable for link analyzer
  $score = 1; // Starting score per word
  $accum = ' '; // Accumulator for cleaned up data
  $tagstack = array(); // Stack with open tags
  $tagwords = 0; // Counter for consecutive words
  $focus = 1; // Focus state

  $results = array(0 => array()); // Accumulator for words for index

  foreach ($split as $value) {
    if ($tag) {
      // Increase or decrease score per word based on tag
      list($tagname) = explode(' ', $value, 2);
      $tagname = drupal_strtolower($tagname);
      // Closing or opening tag?
      if ($tagname[0] == '/') {
        $tagname = substr($tagname, 1);
        // If we encounter unexpected tags, reset score to avoid incorrect boosting.
        if (!count($tagstack) || $tagstack[0] != $tagname) {
          $tagstack = array();
          $score = 1;
        }
        else {
          // Remove from tag stack and decrement score
          $score = max(1, $score - $tags[array_shift($tagstack)]);
        }
        if ($tagname == 'a') {
          $link = FALSE;
        }
      }
      else {
        if (isset($tagstack[0]) && $tagstack[0] == $tagname) {
          // None of the tags we look for make sense when nested identically.
          // If they are, it's probably broken HTML.
          $tagstack = array();
          $score = 1;
        }
        else {
          // Add to open tag stack and increment score
          array_unshift($tagstack, $tagname);
          $score += $tags[$tagname];
        }
        if ($tagname == 'a') {
          // Check if link points to a node on this site
          if (preg_match($node_regexp, $value, $match)) {
            $path = drupal_get_normal_path($match[1]);
            if (preg_match('!(?:node|book)/(?:view/)?([0-9]+)!i', $path, $match)) {
              $linknid = $match[1];
              if ($linknid > 0) {
                $node = db_query('SELECT title, nid, vid FROM {node} WHERE nid = :nid', array(':nid' => $linknid), array('target' => 'slave'))->fetchObject();
                $link = TRUE;
                $linktitle = $node->title;
              }
            }
          }
        }
      }
      // A tag change occurred, reset counter.
      $tagwords = 0;
    }
    else {
      // Note: use of PREG_SPLIT_DELIM_CAPTURE above will introduce empty values
      if ($value != '') {
        if ($link) {
          // Check to see if the node link text is its URL. If so, we use the target node title instead.
          if (preg_match('!^https?://!i', $value)) {
            $value = $linktitle;
          }
        }
        $words = search_index_split($value);
        foreach ($words as $word) {
          // Add word to accumulator
          $accum .= $word . ' ';
          // Check wordlength
          if (is_numeric($word) || drupal_strlen($word) >= $minimum_word_size) {
            // Links score mainly for the target.
            if ($link) {
              if (!isset($results[$linknid])) {
                $results[$linknid] = array();
              }
              $results[$linknid][] = $word;
              // Reduce score of the link caption in the source.
              $focus *= 0.2;
            }
            // Fall-through
            if (!isset($results[0][$word])) {
              $results[0][$word] = 0;
            }
            $results[0][$word] += $score * $focus;

            // Focus is a decaying value in terms of the amount of unique words up to this point.
            // From 100 words and more, it decays, to e.g. 0.5 at 500 words and 0.3 at 1000 words.
            $focus = min(1, .01 + 3.5 / (2 + count($results[0]) * .015));
          }
          $tagwords++;
          // Too many words inside a single tag probably mean a tag was accidentally left open.
          if (count($tagstack) && $tagwords >= 15) {
            $tagstack = array();
            $score = 1;
          }
        }
      }
    }
    $tag = !$tag;
  }

  search_reindex($sid, $module, TRUE);

  // Insert cleaned up data into dataset
  db_insert('search_dataset')
    ->fields(array(
      'sid' => $sid,
      'type' => $module,
      'data' => $accum,
      'reindex' => 0,
    ))
    ->execute();

  // Insert results into search index
  foreach ($results[0] as $word => $score) {
    // If a word already exists in the database, its score gets increased
    // appropriately. If not, we create a new record with the appropriate
    // starting score.
    db_merge('search_index')
      ->key(array(
        'word' => $word,
        'sid' => $sid,
        'type' => $module,
      ))
      ->fields(array('score' => $score))
      ->expression('score', 'score + :score', array(':score' => $score))
      ->execute();
    search_dirty($word);
  }
  unset($results[0]);

  // Get all previous links from this item.
  $result = db_query("SELECT nid, caption FROM {search_node_links} WHERE sid = :sid AND type = :type", array(
    ':sid' => $sid,
    ':type' => $module
  ), array('target' => 'slave'));
  $links = array();
  foreach ($result as $link) {
    $links[$link->nid] = $link->caption;
  }

  // Now store links to nodes.
  foreach ($results as $nid => $words) {
    $caption = implode(' ', $words);
    if (isset($links[$nid])) {
      if ($links[$nid] != $caption) {
        // Update the existing link and mark the node for reindexing.
        db_update('search_node_links')
          ->fields(array('caption' => $caption))
          ->condition('sid', $sid)
          ->condition('type', $module)
          ->condition('nid', $nid)
          ->execute();
        search_touch_node($nid);
      }
      // Unset the link to mark it as processed.
      unset($links[$nid]);
    }
    elseif ($sid != $nid || $module != 'node') {
      // Insert the existing link and mark the node for reindexing, but don't
      // reindex if this is a link in a node pointing to itself.
      db_insert('search_node_links')
        ->fields(array(
          'caption' => $caption,
          'sid' => $sid,
          'type' => $module,
          'nid' => $nid,
        ))
        ->execute();
      search_touch_node($nid);
    }
  }
  // Any left-over links in $links no longer exist. Delete them and mark the nodes for reindexing.
  foreach ($links as $nid => $caption) {
    db_delete('search_node_links')
      ->condition('sid', $sid)
      ->condition('type', $module)
      ->condition('nid', $nid)
      ->execute();
    search_touch_node($nid);
  }
}

/**
 * Changes a node's changed timestamp to 'now' to force reindexing.
 *
 * @param $nid
 *   The node ID of the node that needs reindexing.
 */
function search_touch_node($nid) {
  db_update('search_dataset')
    ->fields(array('reindex' => REQUEST_TIME))
    ->condition('type', 'node')
    ->condition('sid', $nid)
    ->execute();
}

/**
 * Implements hook_node_update_index().
 */
function search_node_update_index($node) {
  // Transplant links to a node into the target node.
  $result = db_query("SELECT caption FROM {search_node_links} WHERE nid = :nid", array(':nid' => $node->nid), array('target' => 'slave'));
  $output = array();
  foreach ($result as $link) {
    $output[] = $link->caption;
  }
  if (count($output)) {
    return '<a>(' . implode(', ', $output) . ')</a>';
  }
}

/**
 * Implements hook_node_update().
 */
function search_node_update($node) {
  // Reindex the node when it is updated. The node is automatically indexed
  // when it is added, simply by being added to the node table.
  search_touch_node($node->nid);
}

/**
 * Implements hook_comment_insert().
 */
function search_comment_insert($comment) {
  // Reindex the node when comments are added.
  search_touch_node($comment->nid);
}

/**
 * Implements hook_comment_update().
 */
function search_comment_update($comment) {
  // Reindex the node when comments are changed.
  search_touch_node($comment->nid);
}

/**
 * Implements hook_comment_delete().
 */
function search_comment_delete($comment) {
  // Reindex the node when comments are deleted.
  search_touch_node($comment->nid);
}

/**
 * Implements hook_comment_publish().
 */
function search_comment_publish($comment) {
  // Reindex the node when comments are published.
  search_touch_node($comment->nid);
}

/**
 * Implements hook_comment_unpublish().
 */
function search_comment_unpublish($comment) {
  // Reindex the node when comments are unpublished.
  search_touch_node($comment->nid);
}

/**
 * Extracts a module-specific search option from a search expression.
 *
 * Search options are added using search_expression_insert(), and retrieved
 * using search_expression_extract(). They take the form option:value, and
 * are added to the ordinary keywords in the search expression.
 *
 * @param $expression
 *   The search expression to extract from.
 * @param $option
 *   The name of the option to retrieve from the search expression.
 *
 * @return
 *   The value previously stored in the search expression for option $option,
 *   if any. Trailing spaces in values will not be included.
 */
function search_expression_extract($expression, $option) {
  if (preg_match('/(^| )' . $option . ':([^ ]*)( |$)/i', $expression, $matches)) {
    return $matches[2];
  }
}

/**
 * Adds a module-specific search option to a search expression.
 *
 * Search options are added using search_expression_insert(), and retrieved
 * using search_expression_extract(). They take the form option:value, and
 * are added to the ordinary keywords in the search expression.
 *
 * @param $expression
 *   The search expression to add to.
 * @param $option
 *   The name of the option to add to the search expression.
 * @param $value
 *   The value to add for the option. If present, it will replace any previous
 *   value added for the option. Cannot contain any spaces or | characters, as
 *   these are used as delimiters. If you want to add a blank value $option: to
 *   the search expression, pass in an empty string or a string that is composed
 *   of only spaces. To clear a previously-stored option without adding a
 *   replacement, pass in NULL for $value or omit.
 *
 * @return
 *   $expression, with any previous value for this option removed, and a new
 *   $option:$value pair added if $value was provided.
 */
function search_expression_insert($expression, $option, $value = NULL) {
  // Remove any previous values stored with $option.
  $expression = trim(preg_replace('/(^| )' . $option . ':[^ ]*/i', '', $expression));

  // Set new value, if provided.
  if (isset($value)) {
    $expression .= ' ' . $option . ':' . trim($value);
  }
  return $expression;
}

/**
 * @defgroup search Search interface
 * @{
 * The Drupal search interface manages a global search mechanism.
 *
 * Modules may plug into this system to provide searches of different types of
 * data. Most of the system is handled by search.module, so this must be enabled
 * for all of the search features to work.
 *
 * There are three ways to interact with the search system:
 * - Specifically for searching nodes, you can implement
 *   hook_node_update_index() and hook_node_search_result(). However, note that
 *   the search system already indexes all visible output of a node; i.e.,
 *   everything displayed normally by hook_view() and hook_node_view(). This is
 *   usually sufficient. You should only use this mechanism if you want
 *   additional, non-visible data to be indexed.
 * - Implement hook_search_info(). This will create a search tab for your module
 *   on the /search page with a simple keyword search form. You will also need
 *   to implement hook_search_execute() to perform the search.
 * - Implement hook_update_index(). This allows your module to use Drupal's
 *   HTML indexing mechanism for searching full text efficiently.
 *
 * If your module needs to provide a more complicated search form, then you need
 * to implement it yourself without hook_search_info(). In that case, you should
 * define it as a local task (tab) under the /search page (e.g. /search/mymodule)
 * so that users can easily find it.
 */

/**
 * Builds a search form.
 *
 * @param $action
 *   Form action. Defaults to "search/$path", where $path is the search path
 *   associated with the module in its hook_search_info(). This will be
 *   run through url().
 * @param $keys
 *   The search string entered by the user, containing keywords for the search.
 * @param $module
 *   The search module to render the form for: a module that implements
 *   hook_search_info(). If not supplied, the default search module is used.
 * @param $prompt
 *   Label for the keywords field. Defaults to t('Enter your keywords') if NULL.
 *   Supply '' to omit.
 *
 * @return
 *   A Form API array for the search form.
 */
function search_form($form, &$form_state, $action = '', $keys = '', $module = NULL, $prompt = NULL) {
  $module_info = FALSE;
  if (!$module) {
    $module_info = search_get_default_module_info();
  }
  else {
    $info = search_get_info();
    $module_info = isset($info[$module]) ? $info[$module] : FALSE;
  }

  // Sanity check.
  if (!$module_info) {
    form_set_error(NULL, t('Search is currently disabled.'), 'error');
    return $form;
  }

  if (!$action) {
    $action = 'search/' . $module_info['path'];
  }
  if (!isset($prompt)) {
    $prompt = t('Enter your keywords');
  }

  $form['#action'] = url($action);
  // Record the $action for later use in redirecting.
  $form_state['action'] = $action;
  $form['#attributes']['class'][] = 'search-form';
  $form['module'] = array('#type' => 'value', '#value' => $module);
  $form['basic'] = array('#type' => 'container', '#attributes' => array('class' => array('container-inline')));
  $form['basic']['keys'] = array(
    '#type' => 'textfield',
    '#title' => $prompt,
    '#default_value' => $keys,
    '#size' => $prompt ? 40 : 20,
    '#maxlength' => 255,
  );
  // processed_keys is used to coordinate keyword passing between other forms
  // that hook into the basic search form.
  $form['basic']['processed_keys'] = array('#type' => 'value', '#value' => '');
  $form['basic']['submit'] = array('#type' => 'submit', '#value' => t('Search'));

  return $form;
}

/**
 * Form builder; Output a search form for the search block's search box.
 *
 * @ingroup forms
 * @see search_box_form_submit()
 * @see search-block-form.tpl.php
 */
function search_box($form, &$form_state, $form_id) {
  $form[$form_id] = array(
    '#type' => 'textfield',
    '#title' => t('Search'),
    '#title_display' => 'invisible',
    '#size' => 15,
    '#default_value' => '',
    '#attributes' => array('title' => t('Enter the terms you wish to search for.')),
  );
  $form['actions'] = array('#type' => 'actions');
  $form['actions']['submit'] = array('#type' => 'submit', '#value' => t('Search'));
  $form['#submit'][] = 'search_box_form_submit';

  return $form;
}

/**
 * Process a block search form submission.
 */
function search_box_form_submit($form, &$form_state) {
  // The search form relies on control of the redirect destination for its
  // functionality, so we override any static destination set in the request,
  // for example by drupal_access_denied() or drupal_not_found()
  // (see http://drupal.org/node/292565).
  if (isset($_GET['destination'])) {
    unset($_GET['destination']);
  }

  // Check to see if the form was submitted empty.
  // If it is empty, display an error message.
  // (This method is used instead of setting #required to TRUE for this field
  // because that results in a confusing error message.  It would say a plain
  // "field is required" because the search keywords field has no title.
  // The error message would also complain about a missing #title field.)
  if ($form_state['values']['search_block_form'] == '') {
    form_set_error('keys', t('Please enter some keywords.'));
  }

  $form_id = $form['form_id']['#value'];
  $info = search_get_default_module_info();
  if ($info) {
    $form_state['redirect'] = 'search/' . $info['path'] . '/' . trim($form_state['values'][$form_id]);
  }
  else {
    form_set_error(NULL, t('Search is currently disabled.'), 'error');
  }
}

/**
 * Process variables for search-block-form.tpl.php.
 *
 * The $variables array contains the following arguments:
 * - $form
 *
 * @see search-block-form.tpl.php
 */
function template_preprocess_search_block_form(&$variables) {
  $variables['search'] = array();
  $hidden = array();
  // Provide variables named after form keys so themers can print each element independently.
  foreach (element_children($variables['form']) as $key) {
    $type = isset($variables['form'][$key]['#type']) ? $variables['form'][$key]['#type'] : '';
    if ($type == 'hidden' || $type == 'token') {
      $hidden[] = drupal_render($variables['form'][$key]);
    }
    else {
      $variables['search'][$key] = drupal_render($variables['form'][$key]);
    }
  }
  // Hidden form elements have no value to themers. No need for separation.
  $variables['search']['hidden'] = implode($hidden);
  // Collect all form elements to make it easier to print the whole form.
  $variables['search_form'] = implode($variables['search']);
}

/**
 * Performs a search by calling hook_search_execute().
 *
 * @param $keys
 *   Keyword query to search on.
 * @param $module
 *   Search module to search.
 * @param $conditions
 *   Optional array of additional search conditions.
 *
 * @return
 *   Renderable array of search results. No return value if $keys are not
 *   supplied or if the given search module is not active.
 */
function search_data($keys, $module, $conditions = NULL) {
  if (module_hook($module, 'search_execute')) {
    $results = module_invoke($module, 'search_execute', $keys, $conditions);
    if (module_hook($module, 'search_page')) {
      return module_invoke($module, 'search_page', $results);
    }
    else {
      return array(
        '#theme' => 'search_results',
        '#results' => $results,
        '#module' => $module,
      );
    }
  }
}

/**
 * Returns snippets from a piece of text, with certain keywords highlighted.
 * Used for formatting search results.
 *
 * @param $keys
 *   A string containing a search query.
 *
 * @param $text
 *   The text to extract fragments from.
 *
 * @return
 *   A string containing HTML for the excerpt.
 */
function search_excerpt($keys, $text) {
  // We highlight around non-indexable or CJK characters.
  $boundary = '(?:(?<=[' . PREG_CLASS_UNICODE_WORD_BOUNDARY . PREG_CLASS_CJK . '])|(?=[' . PREG_CLASS_UNICODE_WORD_BOUNDARY . PREG_CLASS_CJK . ']))';

  // Extract positive keywords and phrases
  preg_match_all('/ ("([^"]+)"|(?!OR)([^" ]+))/', ' ' . $keys, $matches);
  $keys = array_merge($matches[2], $matches[3]);

  // Prepare text by stripping HTML tags and decoding HTML entities.
  $text = strip_tags(str_replace(array('<', '>'), array(' <', '> '), $text));
  $text = decode_entities($text);

  // Slash-escape quotes in the search keyword string.
  array_walk($keys, '_search_excerpt_replace');
  $workkeys = $keys;

  // Extract fragments around keywords.
  // First we collect ranges of text around each keyword, starting/ending
  // at spaces, trying to get to 256 characters.
  // If the sum of all fragments is too short, we look for second occurrences.
  $ranges = array();
  $included = array();
  $foundkeys = array();
  $length = 0;
  while ($length < 256 && count($workkeys)) {
    foreach ($workkeys as $k => $key) {
      if (strlen($key) == 0) {
        unset($workkeys[$k]);
        unset($keys[$k]);
        continue;
      }
      if ($length >= 256) {
        break;
      }
      // Remember occurrence of key so we can skip over it if more occurrences
      // are desired.
      if (!isset($included[$key])) {
        $included[$key] = 0;
      }
      // Locate a keyword (position $p, always >0 because $text starts with a
      // space). First try bare keyword, but if that doesn't work, try to find a
      // derived form from search_simplify().
      $p = 0;
      if (preg_match('/' . $boundary . $key . $boundary . '/iu', $text, $match, PREG_OFFSET_CAPTURE, $included[$key])) {
        $p = $match[0][1];
      }
      else {
        $info = search_simplify_excerpt_match($key, $text, $included[$key], $boundary);
        if (isset($info['where'])) {
          $p = $info['where'];
          if ($info['keyword']) {
            $foundkeys[] = $info['keyword'];
          }
        }
      }
      // Now locate a space in front (position $q) and behind it (position $s),
      // leaving about 60 characters extra before and after for context.
      // Note that a space was added to the front and end of $text above.
      if ($p) {
        if (($q = strpos(' ' . $text, ' ', max(0, $p - 61))) !== FALSE) {
          $end = substr($text . ' ', $p, 80);
          if (($s = strrpos($end, ' ')) !== FALSE) {
            // Account for the added spaces.
            $q = max($q - 1, 0);
            $s = min($s, strlen($end) - 1);
            $ranges[$q] = $p + $s;
            $length += $p + $s - $q;
            $included[$key] = $p + 1;
          }
          else {
            unset($workkeys[$k]);
          }
        }
        else {
          unset($workkeys[$k]);
        }
      }
      else {
        unset($workkeys[$k]);
      }
    }
  }

  if (count($ranges) == 0) {
    // We didn't find any keyword matches, so just return the first part of the
    // text. We also need to re-encode any HTML special characters that we
    // entity-decoded above.
    return check_plain(truncate_utf8($text, 256, TRUE, TRUE));
  }

  // Sort the text ranges by starting position.
  ksort($ranges);

  // Now we collapse overlapping text ranges into one. The sorting makes it O(n).
  $newranges = array();
  foreach ($ranges as $from2 => $to2) {
    if (!isset($from1)) {
      $from1 = $from2;
      $to1 = $to2;
      continue;
    }
    if ($from2 <= $to1) {
      $to1 = max($to1, $to2);
    }
    else {
      $newranges[$from1] = $to1;
      $from1 = $from2;
      $to1 = $to2;
    }
  }
  $newranges[$from1] = $to1;

  // Fetch text
  $out = array();
  foreach ($newranges as $from => $to) {
    $out[] = substr($text, $from, $to - $from);
  }

  // Let translators have the ... separator text as one chunk.
  $dots = explode('!excerpt', t('... !excerpt ... !excerpt ...'));

  $text = (isset($newranges[0]) ? '' : $dots[0]) . implode($dots[1], $out) . $dots[2];
  $text = check_plain($text);

  // Slash-escape quotes in keys found in a derived form and merge with original keys.
  array_walk($foundkeys, '_search_excerpt_replace');
  $keys = array_merge($keys, $foundkeys);

  // Highlight keywords. Must be done at once to prevent conflicts ('strong' and '<strong>').
  $text = preg_replace('/' . $boundary . '(' . implode('|', $keys) . ')' . $boundary . '/iu', '<strong>\0</strong>', $text);
  return $text;
}

/**
 * @} End of "defgroup search".
 */

/**
 * Helper function for array_walk() in search_excerpt().
 */
function _search_excerpt_replace(&$text) {
  $text = preg_quote($text, '/');
}

/**
 * Find words in the original text that matched via search_simplify().
 *
 * This is called in search_excerpt() if an exact match is not found in the
 * text, so that we can find the derived form that matches.
 *
 * @param $key
 *   The keyword to find.
 * @param $text
 *   The text to search for the keyword.
 * @param $offset
 *   Offset position in $text to start searching at.
 * @param $boundary
 *   Text to include in a regular expression that will match a word boundary.
 *
 * @return
 *   FALSE if no match is found. If a match is found, return an associative
 *   array with element 'where' giving the position of the match, and element
 *   'keyword' giving the actual word found in the text at that position.
 */
function search_simplify_excerpt_match($key, $text, $offset, $boundary) {
  $pos = NULL;
  $simplified_key = search_simplify($key);
  $simplified_text = search_simplify($text);

  // Return immediately if simplified key or text are empty.
  if (!$simplified_key || !$simplified_text) {
    return FALSE;
  }

  // Check if we have a match after simplification in the text.
  if (!preg_match('/' . $boundary . $simplified_key . $boundary . '/iu', $simplified_text, $match, PREG_OFFSET_CAPTURE, $offset)) {
    return FALSE;
  }

  // If we get here, we have a match. Now find the exact location of the match
  // and the original text that matched. Start by splitting up the text by all
  // potential starting points of the matching text and iterating through them.
  $split = array_filter(preg_split('/' . $boundary . '/iu', $text, -1, PREG_SPLIT_OFFSET_CAPTURE), '_search_excerpt_match_filter');
  foreach ($split as $value) {
    // Skip starting points before the offset.
    if ($value[1] < $offset) {
      continue;
    }

    // Check a window of 80 characters after the starting point for a match,
    // based on the size of the excerpt window.
    $window = substr($text, $value[1], 80);
    $simplified_window = search_simplify($window);
    if (strpos($simplified_window, $simplified_key) === 0) {
      // We have a match in this window. Store the position of the match.
      $pos = $value[1];
      // Iterate through the text in the window until we find the full original
      // matching text.
      $length = strlen($window);
      for ($i = 1; $i <= $length; $i++) {
        $keyfound = substr($text, $value[1], $i);
        if ($simplified_key == search_simplify($keyfound)) {
          break;
        }
      }
      break;
    }
  }

  return $pos ? array('where' => $pos, 'keyword' => $keyfound) : FALSE;
}

/**
 * Helper function for array_filter() in search_search_excerpt_match().
 */
function _search_excerpt_match_filter($var) {
  return strlen(trim($var[0]));
}

/**
 * Implements hook_forms().
 */
function search_forms() {
  $forms['search_block_form']= array(
    'callback' => 'search_box',
    'callback arguments' => array('search_block_form'),
  );
  return $forms;
}

