<?php

/**
 * @file
 * Install, update and uninstall functions for the search module.
 */

/**
 * Implements hook_uninstall().
 */
function search_uninstall() {
  variable_del('minimum_word_size');
  variable_del('overlap_cjk');
  variable_del('search_cron_limit');
  variable_del('search_logging');
}

/**
 * Implements hook_schema().
 */
function search_schema() {
  $schema['search_dataset'] = array(
    'description' => 'Stores items that will be searched.',
    'fields' => array(
      'sid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Search item ID, e.g. node ID for nodes.',
      ),
      'type' => array(
        'type' => 'varchar',
        'length' => 16,
        'not null' => TRUE,
        'description' => 'Type of item, e.g. node.',
      ),
      'data' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'List of space-separated words from the item.',
      ),
      'reindex' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Set to force node reindexing.',
      ),
    ),
    'primary key' => array('sid', 'type'),
  );

  $schema['search_index'] = array(
    'description' => 'Stores the search index, associating words, items and scores.',
    'fields' => array(
      'word' => array(
        'type' => 'varchar',
        'length' => 50,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The {search_total}.word that is associated with the search item.',
      ),
      'sid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {search_dataset}.sid of the searchable item to which the word belongs.',
      ),
      'type' => array(
        'type' => 'varchar',
        'length' => 16,
        'not null' => TRUE,
        'description' => 'The {search_dataset}.type of the searchable item to which the word belongs.',
      ),
      'score' => array(
        'type' => 'float',
        'not null' => FALSE,
        'description' => 'The numeric score of the word, higher being more important.',
      ),
    ),
    'indexes' => array(
      'sid_type' => array('sid', 'type'),
    ),
    'foreign keys' => array(
      'search_dataset' => array(
        'table' => 'search_dataset',
        'columns' => array(
          'sid' => 'sid',
          'type' => 'type',
        ),
      ),
    ),
    'primary key' => array('word', 'sid', 'type'),
  );

  $schema['search_total'] = array(
    'description' => 'Stores search totals for words.',
    'fields' => array(
      'word' => array(
        'description' => 'Primary Key: Unique word in the search index.',
        'type' => 'varchar',
        'length' => 50,
        'not null' => TRUE,
        'default' => '',
      ),
      'count' => array(
        'description' => "The count of the word in the index using Zipf's law to equalize the probability distribution.",
        'type' => 'float',
        'not null' => FALSE,
      ),
    ),
    'primary key' => array('word'),
  );

  $schema['search_node_links'] = array(
    'description' => 'Stores items (like nodes) that link to other nodes, used to improve search scores for nodes that are frequently linked to.',
    'fields' => array(
      'sid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {search_dataset}.sid of the searchable item containing the link to the node.',
      ),
      'type' => array(
        'type' => 'varchar',
        'length' => 16,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The {search_dataset}.type of the searchable item containing the link to the node.',
      ),
      'nid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {node}.nid that this item links to.',
      ),
      'caption' => array(
        'type' => 'text',
        'size' => 'big',
        'not null' => FALSE,
        'description' => 'The text used to link to the {node}.nid.',
      ),
    ),
    'primary key' => array('sid', 'type', 'nid'),
    'indexes' => array(
      'nid' => array('nid'),
    ),
  );

  return $schema;
}

/**
 * Replace unique keys in 'search_dataset' and 'search_index' by primary keys.
 */
function search_update_7000() {
  db_drop_unique_key('search_dataset', 'sid_type');
  $dataset_type_spec = array(
    'type' => 'varchar',
    'length' => 16,
    'not null' => TRUE,
    'description' => 'Type of item, e.g. node.',
  );
  db_change_field('search_dataset', 'type', 'type', $dataset_type_spec);
  db_add_primary_key('search_dataset', array('sid', 'type'));

  db_drop_index('search_index', 'word');
  db_drop_unique_key('search_index', 'word_sid_type');
  $index_type_spec = array(
    'type' => 'varchar',
    'length' => 16,
    'not null' => TRUE,
    'description' => 'The {search_dataset}.type of the searchable item to which the word belongs.',
  );
  db_change_field('search_index', 'type', 'type', $index_type_spec);
  db_add_primary_key('search_index', array('word', 'sid', 'type'));
}

