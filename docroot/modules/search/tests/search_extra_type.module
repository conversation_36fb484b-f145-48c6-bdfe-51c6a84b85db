<?php

/**
 * @file
 * Dummy module implementing a search type for search module testing.
 */

/**
 * Implements hook_search_info().
 */
function search_extra_type_search_info() {
  return array(
    'title' => 'Dummy search type',
    'path' => 'dummy_path',
    'conditions_callback' => 'search_extra_type_conditions',
  );
}

/**
 * Test conditions callback for hook_search_info().
 */
function search_extra_type_conditions() {
  $conditions = array();

  if (!empty($_REQUEST['search_conditions'])) {
    $conditions['search_conditions'] = $_REQUEST['search_conditions'];
  }
  return $conditions;
}

/**
 * Implements hook_search_execute().
 *
 * This is a dummy search, so when search "executes", we just return a dummy
 * result containing the keywords and a list of conditions.
 */
function search_extra_type_search_execute($keys = NULL, $conditions = NULL) {
  if (!$keys) {
    $keys = '';
  }
  return array(
    array(
      'link' => url('node'),
      'type' => 'Dummy result type',
      'title' => 'Dummy title',
      'snippet' => "Dummy search snippet to display. Keywords: {$keys}\n\nConditions: " . print_r($conditions, TRUE),
    ),
  );
}

/**
 * Implements hook_search_page().
 *
 * Adds some text to the search page so we can verify that it runs.
 */
function search_extra_type_search_page($results) {
  $output['prefix']['#markup'] = '<h2>Test page text is here</h2> <ol class="search-results">';

  foreach ($results as $entry) {
    $output[] = array(
      '#theme' => 'search_result',
      '#result' => $entry,
      '#module' => 'search_extra_type',
    );
  }
  $output['suffix']['#markup'] = '</ol>' . theme('pager');

  return $output;
}
