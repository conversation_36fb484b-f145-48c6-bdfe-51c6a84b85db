<?php

/**
 * @file
 * Install, update and uninstall functions for the openid module.
 */

/**
 * Implements hook_schema().
 */
function openid_schema() {
  $schema['openid_association'] = array(
    'description' => 'Stores temporary shared key association information for OpenID authentication.',
    'fields' => array(
      'idp_endpoint_uri' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'description' => 'Primary Key: URI of the OpenID Provider endpoint.',
      ),
      'assoc_handle' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'description' => 'Used to refer to this association in subsequent messages.',
      ),
      'assoc_type' => array(
        'type' => 'varchar',
        'length' => 32,
        'description' => 'The signature algorithm used: one of HMAC-SHA1 or HMAC-SHA256.',
      ),
      'session_type' => array(
        'type' => 'varchar',
        'length' => 32,
        'description' => 'Valid association session types: "no-encryption", "DH-SHA1", and "DH-SHA256".',
      ),
      'mac_key' => array(
        'type' => 'varchar',
        'length' => 255,
        'description' => 'The MAC key (shared secret) for this association.',
      ),
      'created' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'UNIX timestamp for when the association was created.',
      ),
      'expires_in' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The lifetime, in seconds, of this association.',
      ),
    ),
    'primary key' => array('idp_endpoint_uri'),
    'unique keys' => array(
      'assoc_handle' => array('assoc_handle'),
    ),
  );

  $schema['openid_nonce'] = array(
    'description' => 'Stores received openid.response_nonce per OpenID endpoint URL to prevent replay attacks.',
    'fields' => array(
      'idp_endpoint_uri' => array(
        'type' => 'varchar',
        'length' => 255,
        'description' => 'URI of the OpenID Provider endpoint.',
      ),
      'nonce' => array(
        'type' => 'varchar',
        'length' => 255,
        'description' => 'The value of openid.response_nonce.',
      ),
      'expires' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'A Unix timestamp indicating when the entry should expire.',
      ),
    ),
    'indexes' => array(
      'nonce' => array('nonce'),
      'expires' => array('expires'),
    ),
  );

  return $schema;
}

/**
 * Implements hook_requirements().
 */
function openid_requirements($phase) {
  $requirements = array();

  if ($phase == 'runtime') {
    // Check for the PHP BC Math library.
    if (!function_exists('bcadd') && !function_exists('gmp_add')) {
      $requirements['openid_math'] = array(
        'value' => t('Not installed'),
        'severity' => REQUIREMENT_ERROR,
        'description' => t('OpenID suggests the use of either the <a href="@gmp">GMP Math</a> (recommended for performance) or <a href="@bc">BC Math</a> libraries to enable OpenID associations.', array('@gmp' => 'http://php.net/manual/en/book.gmp.php', '@bc' => 'http://www.php.net/manual/en/book.bc.php')),
      );
    }
    elseif (!function_exists('gmp_add')) {
      $requirements['openid_math'] = array(
        'value' => t('Not optimized'),
        'severity' => REQUIREMENT_WARNING,
        'description' => t('OpenID suggests the use of the GMP Math library for PHP for optimal performance. Check the <a href="@url">GMP Math Library documentation</a> for installation instructions.', array('@url' => 'http://www.php.net/manual/en/book.gmp.php')),
      );
    }
    else {
      $requirements['openid_math'] = array(
        'value' => t('Installed'),
        'severity' => REQUIREMENT_OK,
      );
    }
    $requirements['openid_math']['title'] = t('OpenID Math library');
  }

  return $requirements;
}

/**
 * @addtogroup updates-6.x-to-7.x
 * @{
 */

/**
 * Add a table to store nonces.
 */
function openid_update_6000() {
  $schema['openid_nonce'] = array(
    'description' => 'Stores received openid.response_nonce per OpenID endpoint URL to prevent replay attacks.',
    'fields' => array(
      'idp_endpoint_uri' => array(
        'type' => 'varchar',
        'length' => 255,
        'description' => 'URI of the OpenID Provider endpoint.',
      ),
      'nonce' => array(
        'type' => 'varchar',
        'length' => 255,
        'description' => 'The value of openid.response_nonce'
        ),
      'expires' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'A Unix timestamp indicating when the entry should expire.',
        ),
      ),
    'indexes' => array(
      'nonce' => array('nonce'),
      'expires' => array('expires'),
    ),
  );

  db_create_table('openid_nonce', $schema['openid_nonce']);
}

/**
 * @} End of "addtogroup updates-6.x-to-7.x".
 */

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Bind associations to their providers.
 */
function openid_update_7000() {
  db_drop_table('openid_association');

  $schema = array(
    'description' => 'Stores temporary shared key association information for OpenID authentication.',
    'fields' => array(
      'idp_endpoint_uri' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'description' => 'Primary Key: URI of the OpenID Provider endpoint.',
      ),
      'assoc_handle' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'description' => 'Used to refer to this association in subsequent messages.',
      ),
      'assoc_type' => array(
        'type' => 'varchar',
        'length' => 32,
        'description' => 'The signature algorithm used: one of HMAC-SHA1 or HMAC-SHA256.',
      ),
      'session_type' => array(
        'type' => 'varchar',
        'length' => 32,
        'description' => 'Valid association session types: "no-encryption", "DH-SHA1", and "DH-SHA256".',
      ),
      'mac_key' => array(
        'type' => 'varchar',
        'length' => 255,
        'description' => 'The MAC key (shared secret) for this association.',
      ),
      'created' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'UNIX timestamp for when the association was created.',
      ),
      'expires_in' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The lifetime, in seconds, of this association.',
      ),
    ),
    'primary key' => array('idp_endpoint_uri'),
    'unique keys' => array(
      'assoc_handle' => array('assoc_handle'),
    ),
  );
  db_create_table('openid_association', $schema);
}

/**
 * @} End of "addtogroup updates-7.x-extra".
 */
