<?php

/**
 * @file
 * Tests for openid.module.
 */

/**
 * Base class for OpenID tests.
 */
abstract class OpenIDWebTestCase extends DrupalWebTestCase {

  /**
   * Initiates the login procedure using the specified User-supplied Identity.
   */
  function submitLoginForm($identity) {
    // Fill out and submit the login form.
    $edit = array('openid_identifier' => $identity);
    $this->drupalPost('', $edit, t('Log in'));

    // Check we are on the OpenID redirect form.
    $this->assertTitle(t('OpenID redirect'), 'OpenID redirect page was displayed.');

    // Submit form to the OpenID Provider Endpoint.
    $this->drupalPost(NULL, array(), t('Send'));
  }

  /**
   * Parses the last sent e-mail and returns the one-time login link URL.
   */
  function getPasswordResetURLFromMail() {
    $mails = $this->drupalGetMails();
    $mail = end($mails);
    preg_match('@.+user/reset/.+@', $mail['body'], $matches);
    return $matches[0];
  }
}

/**
 * Test discovery and login using OpenID
 */
class OpenIDFunctionalTestCase extends OpenIDWebTestCase {
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'OpenID discovery and login',
      'description' => "Adds an identity to a user's profile and uses it to log in.",
      'group' => 'OpenID'
    );
  }

  function setUp() {
    parent::setUp('openid', 'openid_test');

    // User doesn't need special permissions; only the ability to log in.
    $this->web_user = $this->drupalCreateUser(array());
  }

  /**
   * Test discovery of OpenID Provider Endpoint via Yadis and HTML.
   */
  function testDiscovery() {
    $this->drupalLogin($this->web_user);

    // The User-supplied Identifier entered by the user may indicate the URL of
    // the OpenID Provider Endpoint in various ways, as described in OpenID
    // Authentication 2.0 and Yadis Specification 1.0.
    // Note that all of the tested identifiers refer to the same endpoint, so
    // only the first will trigger an associate request in openid_association()
    // (association is only done the first time Drupal encounters a given
    // endpoint).


    // Yadis discovery (see Yadis Specification 1.0, section 6.2.5):
    // If the User-supplied Identifier is a URL, it may be a direct or indirect
    // reference to an XRDS document (a Yadis Resource Descriptor) that contains
    // the URL of the OpenID Provider Endpoint.

    // Identifier is the URL of an XRDS document.
    // On HTTP test environments, the URL scheme is stripped in order to test
    // that the supplied identifier is normalized in openid_begin().
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->addIdentity(preg_replace('@^http://@', '', $identity), 2, 'http://example.com/xrds', $identity);

    $identity = url('openid-test/yadis/xrds/delegate', array('absolute' => TRUE));
    $this->addIdentity(preg_replace('@^http://@', '', $identity), 2, 'http://example.com/xrds-delegate', $identity);

    // Identifier is the URL of an XRDS document containing an OP Identifier
    // Element. The Relying Party sends the special value
    // "http://specs.openid.net/auth/2.0/identifier_select" as Claimed
    // Identifier. The OpenID Provider responds with the actual identifier
    // including the fragment.
    $identity = url('openid-test/yadis/xrds/dummy-user', array('absolute' => TRUE, 'fragment' => $this->randomName()));
    // Tell openid_test.module to respond with this identifier. If the fragment
    // part is present in the identifier, it should be retained.
    variable_set('openid_test_response', array('openid.claimed_id' => $identity, 'openid.identity' => openid_normalize($identity)));
    $this->addIdentity(url('openid-test/yadis/xrds/server', array('absolute' => TRUE)), 2, 'http://specs.openid.net/auth/2.0/identifier_select', $identity);
    variable_set('openid_test_response', array());

    // Identifier is the URL of an HTML page that is sent with an HTTP header
    // that contains the URL of an XRDS document.
    $this->addIdentity(url('openid-test/yadis/x-xrds-location', array('absolute' => TRUE)), 2);

    // Identifier is the URL of an HTML page containing a <meta http-equiv=...>
    // element that contains the URL of an XRDS document.
    $this->addIdentity(url('openid-test/yadis/http-equiv', array('absolute' => TRUE)), 2);

    // Identifier is an XRI. Resolve using our own dummy proxy resolver.
    variable_set('xri_proxy_resolver', url('openid-test/yadis/xrds/xri', array('absolute' => TRUE)) . '/');
    $this->addIdentity('@example*résumé;%25', 2, 'http://example.com/xrds', 'http://example.com/user');

    // Make sure that unverified CanonicalID are not trusted.
    variable_set('openid_test_canonical_id_status', 'bad value');
    $this->addIdentity('@example*résumé;%25', 2, FALSE, FALSE);

    // HTML-based discovery:
    // If the User-supplied Identifier is a URL of an HTML page, the page may
    // contain a <link rel=...> element containing the URL of the OpenID
    // Provider Endpoint. OpenID 1 and 2 describe slightly different formats.

    // OpenID Authentication 1.1, section 3.1:
    $this->addIdentity(url('openid-test/html/openid1', array('absolute' => TRUE)), 1, 'http://example.com/html-openid1');

    // OpenID Authentication 2.0, section 7.3.3:
    $this->addIdentity(url('openid-test/html/openid2', array('absolute' => TRUE)), 2, 'http://example.com/html-openid2');

    // OpenID Authentication 2.0, section 7.2.4:
    // URL Identifiers MUST then be further normalized by both (1) following
    // redirects when retrieving their content and finally (2) applying the
    // rules in Section 6 of RFC3986 to the final destination URL. This final
    // URL MUST be noted by the Relying Party as the Claimed Identifier and be
    // used when requesting authentication.

    // Single redirect.
    $identity = $expected_claimed_id = url('openid-test/redirected/yadis/xrds/1', array('absolute' => TRUE));
    $this->addRedirectedIdentity($identity, 2, 'http://example.com/xrds', $expected_claimed_id, 0);

    // Exact 3 redirects (default value for the 'max_redirects' option in
    // drupal_http_request()).
    $identity = $expected_claimed_id = url('openid-test/redirected/yadis/xrds/2', array('absolute' => TRUE));
    $this->addRedirectedIdentity($identity, 2, 'http://example.com/xrds', $expected_claimed_id, 2);

    // Fails because there are more than 3 redirects (default value for the
    // 'max_redirects' option in drupal_http_request()).
    $identity = url('openid-test/redirected/yadis/xrds/3', array('absolute' => TRUE));
    $expected_claimed_id = FALSE;
    $this->addRedirectedIdentity($identity, 2, 'http://example.com/xrds', $expected_claimed_id, 3);
  }

  /**
   * Test login using OpenID.
   */
  function testLogin() {
    $this->drupalLogin($this->web_user);

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->addIdentity($identity);

    $this->drupalLogout();

    // Test logging in via the login block on the front page.
    $this->submitLoginForm($identity);
    $this->assertLink(t('Log out'), 0, 'User was logged in.');

    $this->drupalLogout();

    // Test logging in via the user/login page.
    $edit = array('openid_identifier' => $identity);
    $this->drupalPost('user/login', $edit, t('Log in'));

    // Check we are on the OpenID redirect form.
    $this->assertTitle(t('OpenID redirect'), 'OpenID redirect page was displayed.');

    // Submit form to the OpenID Provider Endpoint.
    $this->drupalPost(NULL, array(), t('Send'));

    $this->assertLink(t('Log out'), 0, 'User was logged in.');

    // Verify user was redirected away from user/login to an accessible page.
    $this->assertResponse(200);

    $this->drupalLogout();
    // Use a User-supplied Identity that is the URL of an XRDS document.
    // Tell the test module to add a doctype. This should fail.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE, 'query' => array('doctype' => 1)));
    // Test logging in via the login block on the front page.
    $edit = array('openid_identifier' => $identity);
    $this->drupalPost('', $edit, t('Log in'));
    $this->assertRaw(t('Sorry, that is not a valid OpenID. Ensure you have spelled your ID correctly.'), 'XML with DOCTYPE was rejected.');
  }

  /**
   * Test login using OpenID during maintenance mode.
   */
  function testLoginMaintenanceMode() {
    $this->web_user = $this->drupalCreateUser(array('access site in maintenance mode'));
    $this->drupalLogin($this->web_user);

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->addIdentity($identity);
    $this->drupalLogout();

    // Enable maintenance mode.
    variable_set('maintenance_mode', 1);

    // Test logging in via the user/login page while the site is offline.
    $edit = array('openid_identifier' => $identity);
    $this->drupalPost('user/login', $edit, t('Log in'));

    // Check we are on the OpenID redirect form.
    $this->assertTitle(t('OpenID redirect'), 'OpenID redirect page was displayed.');

    // Submit form to the OpenID Provider Endpoint.
    $this->drupalPost(NULL, array(), t('Send'));

    $this->assertLink(t('Log out'), 0, 'User was logged in.');

    // Verify user was redirected away from user/login to an accessible page.
    $this->assertText(t('Operating in maintenance mode.'));
    $this->assertResponse(200);
  }

  /**
   * Test deleting an OpenID identity from a user's profile.
   */
  function testDelete() {
    $this->drupalLogin($this->web_user);

    // Add identity to user's profile.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->addIdentity($identity);
    $this->assertText($identity, 'Identity appears in list.');

    // Delete the newly added identity.
    $this->clickLink(t('Delete'));
    $this->drupalPost(NULL, array(), t('Confirm'));

    $this->assertText(t('OpenID deleted.'), 'Identity deleted');
    $this->assertNoText($identity, 'Identity no longer appears in list.');
  }

  /**
   * Test that a blocked user cannot log in.
   */
  function testBlockedUserLogin() {
    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));

    // Log in and add an OpenID Identity to the account.
    $this->drupalLogin($this->web_user);
    $this->addIdentity($identity);
    $this->drupalLogout();

    // Log in as an admin user and block the account.
    $admin_user = $this->drupalCreateUser(array('administer users'));
    $this->drupalLogin($admin_user);
    $this->drupalGet('admin/people');
    $edit = array(
      'operation' => 'block',
      'accounts[' . $this->web_user->uid . ']' => TRUE,
    );
    $this->drupalPost('admin/people', $edit, t('Update'));
    $this->assertRaw('The update has been performed.', 'Account was blocked.');
    $this->drupalLogout();

    $this->submitLoginForm($identity);
    $this->assertRaw(t('The username %name has not been activated or is blocked.', array('%name' => $this->web_user->name)), 'User login was blocked.');
  }

  /**
   * Add OpenID identity to user's profile.
   *
   * @param $identity
   *   The User-supplied Identifier.
   * @param $version
   *   The protocol version used by the service.
   * @param $local_id
   *   The expected OP-Local Identifier found during discovery.
   * @param $claimed_id
   *   The expected Claimed Identifier returned by the OpenID Provider, or FALSE
   *   if the discovery is expected to fail.
   */
  function addIdentity($identity, $version = 2, $local_id = 'http://example.com/xrds', $claimed_id = NULL) {
    // Tell openid_test.module to only accept this OP-Local Identifier.
    variable_set('openid_test_identity', $local_id);

    $edit = array('openid_identifier' => $identity);
    $this->drupalPost('user/' . $this->web_user->uid . '/openid', $edit, t('Add an OpenID'));

    if ($claimed_id === FALSE) {
      $this->assertRaw(t('Sorry, that is not a valid OpenID. Ensure you have spelled your ID correctly.'), 'Invalid identity was rejected.');
      return;
    }

    // OpenID 1 used a HTTP redirect, OpenID 2 uses a HTML form that is submitted automatically using JavaScript.
    if ($version == 2) {
      // Check we are on the OpenID redirect form.
      $this->assertTitle(t('OpenID redirect'), 'OpenID redirect page was displayed.');

      // Submit form to the OpenID Provider Endpoint.
      $this->drupalPost(NULL, array(), t('Send'));
    }

    if (!isset($claimed_id)) {
      $claimed_id = $identity;
    }
    $this->assertRaw(t('Successfully added %identity', array('%identity' => $claimed_id)), format_string('Identity %identity was added.', array('%identity' => $identity)));
  }

  /**
   * Add OpenID identity, changed by the following redirects, to user's profile.
   *
   * According to OpenID Authentication 2.0, section 7.2.4, URL Identifiers MUST
   * be further normalized by following redirects when retrieving their content
   * and this final URL MUST be noted by the Relying Party as the Claimed
   * Identifier and be used when requesting authentication.
   *
   * @param $identity
   *   The User-supplied Identifier.
   * @param $version
   *   The protocol version used by the service.
   * @param $local_id
   *   The expected OP-Local Identifier found during discovery.
   * @param $claimed_id
   *   The expected Claimed Identifier returned by the OpenID Provider, or FALSE
   *   if the discovery is expected to fail.
   * @param $redirects
   *   The number of redirects.
   */
  function addRedirectedIdentity($identity, $version = 2, $local_id = 'http://example.com/xrds', $claimed_id = NULL, $redirects = 0) {
    // Set the final destination URL which is the same as the Claimed
    // Identifier, we insert the same identifier also to the provider response,
    // but provider could further change the Claimed ID actually (e.g. it could
    // add unique fragment).
    variable_set('openid_test_redirect_url', $identity);
    variable_set('openid_test_response', array('openid.claimed_id' => $identity));

    $this->addIdentity(url('openid-test/redirect/' . $redirects, array('absolute' => TRUE)), $version, $local_id, $claimed_id);

    // Clean up.
    variable_del('openid_test_redirect_url');
    variable_del('openid_test_response');
  }

  /**
   * Tests that openid.signed is verified.
   */
  function testSignatureValidation() {
    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));

    // Respond with an invalid signature.
    variable_set('openid_test_response', array('openid.sig' => 'this-is-an-invalid-signature'));
    $this->submitLoginForm($identity);
    $this->assertRaw('OpenID login failed.');

    // Do not sign the mandatory field openid.assoc_handle.
    variable_set('openid_test_response', array('openid.signed' => 'op_endpoint,claimed_id,identity,return_to,response_nonce'));
    $this->submitLoginForm($identity);
    $this->assertRaw('OpenID login failed.');

    // Sign all mandatory fields and a custom field.
    $keys_to_sign = array('op_endpoint', 'claimed_id', 'identity', 'return_to', 'response_nonce', 'assoc_handle', 'foo');
    $association = new stdClass();
    $association->mac_key = variable_get('mac_key');
    $response = array(
      'openid.op_endpoint' => url('openid-test/endpoint', array('absolute' => TRUE)),
      'openid.claimed_id' => $identity,
      'openid.identity' => $identity,
      'openid.return_to' => url('openid/authenticate', array('absolute' => TRUE)),
      'openid.response_nonce' => _openid_nonce(),
      'openid.assoc_handle' => 'openid-test',
      'openid.foo' => 123,
      'openid.signed' => implode(',', $keys_to_sign),
    );
    $response['openid.sig'] = _openid_signature($association, $response, $keys_to_sign);
    variable_set('openid_test_response', $response);
    $this->submitLoginForm($identity);
    $this->assertNoRaw('OpenID login failed.');
    $this->assertFieldByName('name', '', 'No username was supplied by provider.');
    $this->assertFieldByName('mail', '', 'No e-mail address was supplied by provider.');

    // Check that unsigned SREG fields are ignored.
    $response = array(
      'openid.signed' => 'op_endpoint,claimed_id,identity,return_to,response_nonce,assoc_handle,sreg.nickname',
      'openid.sreg.nickname' => 'john',
      'openid.sreg.email' => '<EMAIL>',
    );
    variable_set('openid_test_response', $response);
    $this->submitLoginForm($identity);
    $this->assertNoRaw('OpenID login failed.');
    $this->assertFieldByName('name', 'john', 'Username was supplied by provider.');
    $this->assertFieldByName('mail', '', 'E-mail address supplied by provider was ignored.');
  }
}

/**
 * Test account registration using Simple Registration and Attribute Exchange.
 */
class OpenIDRegistrationTestCase extends OpenIDWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'OpenID account registration',
      'description' => 'Creates a user account using auto-registration.',
      'group' => 'OpenID'
    );
  }

  function setUp() {
    parent::setUp('openid', 'openid_test');
    variable_set('user_register', USER_REGISTER_VISITORS);
  }

  /**
   * Test OpenID auto-registration with e-mail verification enabled.
   */
  function testRegisterUserWithEmailVerification() {
    variable_set('user_email_verification', TRUE);

    // Tell openid_test.module to respond with these SREG fields.
    variable_set('openid_test_response', array('openid.sreg.nickname' => 'john', 'openid.sreg.email' => '<EMAIL>'));

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->submitLoginForm($identity);
    $this->assertRaw(t('Once you have verified your e-mail address, you may log in via OpenID.'), 'User was asked to verify e-mail address.');
    $this->assertRaw(t('A welcome message with further instructions has been sent to your e-mail address.'), 'A welcome message was sent to the user.');
    $reset_url = $this->getPasswordResetURLFromMail();

    $user = user_load_by_name('john');
    $this->assertTrue($user, 'User was registered with right username.');
    $this->assertEqual($user->mail, '<EMAIL>', 'User was registered with right email address.');
    $this->assertFalse($user->data, 'No additional user info was saved.');

    $this->submitLoginForm($identity);
    $this->assertRaw(t('You must validate your email address for this account before logging in via OpenID.'));

    // Follow the one-time login that was sent in the welcome e-mail.
    $this->drupalGet($reset_url);
    $this->drupalPost(NULL, array(), t('Log in'));

    $this->drupalLogout();

    // Verify that the account was activated.
    $this->submitLoginForm($identity);
    $this->assertLink(t('Log out'), 0, 'User was logged in.');
  }

  /**
   * Test OpenID auto-registration with e-mail verification disabled.
   */
  function testRegisterUserWithoutEmailVerification() {
    variable_set('user_email_verification', FALSE);

    // Tell openid_test.module to respond with these SREG fields.
    variable_set('openid_test_response', array('openid.sreg.nickname' => 'john', 'openid.sreg.email' => '<EMAIL>'));

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->submitLoginForm($identity);
    $this->assertLink(t('Log out'), 0, 'User was logged in.');

    $user = user_load_by_name('john');
    $this->assertTrue($user, 'User was registered with right username.');
    $this->assertEqual($user->mail, '<EMAIL>', 'User was registered with right email address.');
    $this->assertFalse($user->data, 'No additional user info was saved.');

    $this->drupalLogout();

    $this->submitLoginForm($identity);
    $this->assertLink(t('Log out'), 0, 'User was logged in.');
  }

  /**
   * Test OpenID auto-registration with a provider that supplies invalid SREG
   * information (a username that is already taken, and no e-mail address).
   */
  function testRegisterUserWithInvalidSreg() {
    // Tell openid_test.module to respond with these SREG fields.
    $web_user = $this->drupalCreateUser(array());
    variable_set('openid_test_response', array('openid.sreg.nickname' => $web_user->name, 'openid.sreg.email' => 'mail@invalid#'));

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->submitLoginForm($identity);

    $this->assertRaw(t('Account registration using the information provided by your OpenID provider failed due to the reasons listed below. Complete the registration by filling out the form below. If you already have an account, you can <a href="@login">log in</a> now and add your OpenID under "My account".', array('@login' => url('user/login'))), 'User was asked to complete the registration process manually.');
    $this->assertRaw(t('The name %name is already taken.', array('%name' => $web_user->name)), 'Form validation error for username was displayed.');
    $this->assertRaw(t('The e-mail address %mail is not valid.', array('%mail' => 'mail@invalid#')), 'Form validation error for e-mail address was displayed.');

    // Enter username and e-mail address manually.
    $edit = array('name' => 'john', 'mail' => '<EMAIL>');
    $this->drupalPost(NULL, $edit, t('Create new account'));
    $this->assertRaw(t('Once you have verified your e-mail address, you may log in via OpenID.'), 'User was asked to verify e-mail address.');
    $reset_url = $this->getPasswordResetURLFromMail();

    $user = user_load_by_name('john');
    $this->assertTrue($user, 'User was registered with right username.');
    $this->assertFalse($user->data, 'No additional user info was saved.');

    // Follow the one-time login that was sent in the welcome e-mail.
    $this->drupalGet($reset_url);
    $this->drupalPost(NULL, array(), t('Log in'));

    // The user is taken to user/%uid/edit.
    $this->assertFieldByName('mail', '<EMAIL>', 'User was registered with right e-mail address.');

    $this->clickLink(t('OpenID identities'));
    $this->assertRaw($identity, 'OpenID identity was registered.');
  }

  /**
   * Test OpenID auto-registration with a provider that does not supply SREG
   * information (i.e. no username or e-mail address).
   */
  function testRegisterUserWithoutSreg() {
    // Load the front page to get the user login block.
    $this->drupalGet('');

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->submitLoginForm($identity);
    $this->assertRaw(t('Complete the registration by filling out the form below. If you already have an account, you can <a href="@login">log in</a> now and add your OpenID under "My account".', array('@login' => url('user/login'))), 'User was asked to complete the registration process manually.');
    $this->assertNoRaw(t('You must enter a username.'), 'Form validation error for username was not displayed.');
    $this->assertNoRaw(t('You must enter an e-mail address.'), 'Form validation error for e-mail address was not displayed.');

    // Enter username and e-mail address manually.
    $edit = array('name' => 'john', 'mail' => '<EMAIL>');
    $this->drupalPost(NULL, $edit, t('Create new account'));
    $this->assertRaw(t('Once you have verified your e-mail address, you may log in via OpenID.'), 'User was asked to verify e-mail address.');
    $reset_url = $this->getPasswordResetURLFromMail();

    $user = user_load_by_name('john');
    $this->assertTrue($user, 'User was registered with right username.');
    $this->assertFalse($user->data, 'No additional user info was saved.');

    // Follow the one-time login that was sent in the welcome e-mail.
    $this->drupalGet($reset_url);
    $this->drupalPost(NULL, array(), t('Log in'));

    // The user is taken to user/%uid/edit.
    $this->assertFieldByName('mail', '<EMAIL>', 'User was registered with right e-mail address.');

    $this->clickLink(t('OpenID identities'));
    $this->assertRaw($identity, 'OpenID identity was registered.');
  }

  /**
   * Test OpenID auto-registration with a provider that supplies AX information,
   * but no SREG.
   */
  function testRegisterUserWithAXButNoSREG() {
    variable_set('user_email_verification', FALSE);

    // Tell openid_test.module to respond with these AX fields.
    variable_set('openid_test_response', array(
      'openid.ns.ext123' => 'http://openid.net/srv/ax/1.0',
      'openid.ext123.type.mail456' => 'http://axschema.org/contact/email',
      'openid.ext123.value.mail456' => '<EMAIL>',
      'openid.ext123.type.name789' => 'http://schema.openid.net/namePerson/friendly',
      'openid.ext123.count.name789' => '1',
      'openid.ext123.value.name789.1' => 'john',
    ));

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE));
    $this->submitLoginForm($identity);
    $this->assertLink(t('Log out'), 0, 'User was logged in.');

    $user = user_load_by_name('john');
    $this->assertTrue($user, 'User was registered with right username.');
    $this->assertEqual($user->mail, '<EMAIL>', 'User was registered with right email address.');
  }
}

/**
 * Test account registration using Simple Registration and Attribute Exchange.
 */
class OpenIDInvalidIdentifierTransitionTestCase extends OpenIDFunctionalTestCase  {

  public static function getInfo() {
    return array(
      'name' => 'OpenID account update',
      'description' => 'Tries to correct OpenID identifiers attached to accounts if their identifiers were stripped.',
      'group' => 'OpenID',
    );
  }

  function setUp() {
    parent::setUp('openid', 'openid_test');
    variable_set('user_register', USER_REGISTER_VISITORS);
    variable_set('openid_less_obtrusive_transition', TRUE);
  }

  /**
   * Test OpenID transition with e-mail mismatch.
   */
  function testStrippedFragmentAccountEmailMismatch() {
    $this->drupalLogin($this->web_user);

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE, 'fragment' => $this->randomName()));
    $identity_stripped = preg_replace('/#.*/', '', $identity);

    // Add invalid identifier to the authmap (identifier has stripped fragment).
    $this->addIdentity($identity_stripped);
    $this->drupalLogout();

    // Test logging in via the login form, provider will respond with full
    // identifier (including fragment) but with different email, so we can't
    // provide auto-update.
    variable_set('openid_test_response', array(
      'openid.claimed_id' => $identity,
      'openid.sreg.nickname' => $this->web_user->name,
      'openid.sreg.email' => 'invalid-' . $this->web_user->mail));

    $edit = array('openid_identifier' => $identity_stripped);
    $this->submitLoginForm($identity_stripped);

    // Verify user was redirected away from user login to an accessible page.
    $this->assertResponse(200);

    // Verify the message.
    $this->assertRaw(t('There is already an existing account associated with the OpenID identifier that you have provided.'), 'Message that OpenID identifier must be updated manually was displayed.');
  }

  /**
   * Test OpenID auto transition with e-mail.
   */
  function testStrippedFragmentAccountAutoUpdateSreg() {
    $this->drupalLogin($this->web_user);

    // Use a User-supplied Identity that is the URL of an XRDS document.
    $identity = url('openid-test/yadis/xrds', array('absolute' => TRUE, 'fragment' => $this->randomName()));
    $identity_stripped = preg_replace('/#.*/', '', $identity);

    // Add invalid identifier to the authmap (identifier has stripped fragment).
    $this->addIdentity($identity_stripped);
    $this->drupalLogout();

    // Test logging in via the login form, provider will respond with full
    // identifier (including fragment) but with different email, so we can't
    // provide auto-update.
    variable_set('openid_test_response', array(
      'openid.claimed_id' => $identity,
      'openid.sreg.nickname' => $this->web_user->name,
      'openid.sreg.email' => $this->web_user->mail));

    $this->submitLoginForm($identity_stripped);

    // Verify user was redirected away from user login to an accessible page.
    $this->assertResponse(200);

    // Verify the message.
    $this->assertRaw(t('New OpenID identifier %identity was added as a replacement for invalid identifier %invalid_identity.', array('%invalid_identity' => $identity_stripped, '%identity' => $identity)), 'Message that OpenID identifier was added automatically was displayed.');
  }
}

/**
 * Test internal helper functions.
 */
class OpenIDTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'OpenID helper functions',
      'description' => 'Test OpenID helper functions.',
      'group' => 'OpenID'
    );
  }

  function setUp() {
    parent::setUp('openid');
    module_load_include('inc', 'openid');
  }

  /**
   * Test _openid_dh_XXX_to_XXX() functions.
   */
  function testConversion() {
    $this->assertIdentical(_openid_dh_long_to_base64('12345678901234567890123456789012345678901234567890'), 'CHJ/Y2mq+DyhUCZ0evjH8ZbOPwrS', '_openid_dh_long_to_base64() returned expected result.');
    $this->assertIdentical(_openid_dh_base64_to_long('BsH/g8Nrpn2dtBSdu/sr1y8hxwyx'), '9876543210987654321098765432109876543210987654321', '_openid_dh_base64_to_long() returned expected result.');

    $this->assertIdentical(_openid_dh_long_to_binary('12345678901234567890123456789012345678901234567890'), "\x08r\x7fci\xaa\xf8<\xa1P&tz\xf8\xc7\xf1\x96\xce?\x0a\xd2", '_openid_dh_long_to_binary() returned expected result.');
    $this->assertIdentical(_openid_dh_binary_to_long("\x06\xc1\xff\x83\xc3k\xa6}\x9d\xb4\x14\x9d\xbb\xfb+\xd7/!\xc7\x0c\xb1"), '9876543210987654321098765432109876543210987654321', '_openid_dh_binary_to_long() returned expected result.');
  }

  /**
   * Test _openid_dh_xorsecret().
   */
  function testOpenidDhXorsecret() {
    $this->assertEqual(_openid_dh_xorsecret('123456790123456790123456790', "abc123ABC\x00\xFF"), "\xa4'\x06\xbe\xf1.\x00y\xff\xc2\xc1", '_openid_dh_xorsecret() returned expected result.');
  }

  /**
   * Test _openid_signature().
   */
  function testOpenidSignature() {
    // Test that signature is calculated according to OpenID Authentication 2.0,
    // section 6.1. In the following array, only the two first entries should be
    // included in the calculation, because the substring following the period
    // is mentioned in the third argument for _openid_signature(). The last
    // entry should not be included, because it does not start with "openid.".
    $response = array(
      'openid.foo' => 'abc1',
      'openid.bar' => 'abc2',
      'openid.baz' => 'abc3',
      'foobar.foo' => 'abc4',
    );
    $association = new stdClass();
    $association->mac_key = "1234567890abcdefghij\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\xF0\xF1\xF2\xF3\xF4\xF5\xF6\xF7\xF8\xF9";
    $this->assertEqual(_openid_signature($association, $response, array('foo', 'bar')), 'QnKZQzSFstT+GNiJDFOptdcZjrc=', 'Expected signature calculated.');
  }

  /**
   * Test _openid_is_xri().
   */
  function testOpenidXRITest() {
    // Test that the XRI test is according to OpenID Authentication 2.0,
    // section 7.2. If the user-supplied string starts with xri:// it should be
    // stripped and the resulting string should be treated as an XRI when it
    // starts with "=", "@", "+", "$", "!" or "(".
    $this->assertTrue(_openid_is_xri('xri://=foo'), '_openid_is_xri() returned expected result for an xri identifier with xri scheme.');
    $this->assertTrue(_openid_is_xri('xri://@foo'), '_openid_is_xri() returned expected result for an xri identifier with xri scheme.');
    $this->assertTrue(_openid_is_xri('xri://+foo'), '_openid_is_xri() returned expected result for an xri identifier with xri scheme.');
    $this->assertTrue(_openid_is_xri('xri://$foo'), '_openid_is_xri() returned expected result for an xri identifier with xri scheme.');
    $this->assertTrue(_openid_is_xri('xri://!foo'), '_openid_is_xri() returned expected result for an xri identifier with xri scheme..');
    $this->assertTrue(_openid_is_xri('xri://(foo'), '_openid_is_xri() returned expected result for an xri identifier with xri scheme..');

    $this->assertTrue(_openid_is_xri('=foo'), '_openid_is_xri() returned expected result for an xri identifier.');
    $this->assertTrue(_openid_is_xri('@foo'), '_openid_is_xri() returned expected result for an xri identifier.');
    $this->assertTrue(_openid_is_xri('+foo'), '_openid_is_xri() returned expected result for an xri identifier.');
    $this->assertTrue(_openid_is_xri('$foo'), '_openid_is_xri() returned expected result for an xri identifier.');
    $this->assertTrue(_openid_is_xri('!foo'), '_openid_is_xri() returned expected result for an xri identifier.');
    $this->assertTrue(_openid_is_xri('(foo'), '_openid_is_xri() returned expected result for an xri identifier.');

    $this->assertFalse(_openid_is_xri('foo'), '_openid_is_xri() returned expected result for an http URL.');
    $this->assertFalse(_openid_is_xri('xri://foo'), '_openid_is_xri() returned expected result for an http URL.');
    $this->assertFalse(_openid_is_xri('http://foo/'), '_openid_is_xri() returned expected result for an http URL.');
    $this->assertFalse(_openid_is_xri('http://example.com/'), '_openid_is_xri() returned expected result for an http URL.');
    $this->assertFalse(_openid_is_xri('<EMAIL>/'), '_openid_is_xri() returned expected result for an http URL.');
    $this->assertFalse(_openid_is_xri('http://<EMAIL>/'), '_openid_is_xri() returned expected result for an http URL.');
  }

  /**
   * Test openid_normalize().
   */
  function testOpenidNormalize() {
    // Test that the normalization is according to OpenID Authentication 2.0,
    // section 7.2 and 11.5.2.

    $this->assertEqual(openid_normalize('$foo'), '$foo', 'openid_normalize() correctly normalized an XRI.');
    $this->assertEqual(openid_normalize('xri://$foo'), '$foo', 'openid_normalize() correctly normalized an XRI with an xri:// scheme.');

    $this->assertEqual(openid_normalize('example.com/'), 'http://example.com/', 'openid_normalize() correctly normalized a URL with a missing scheme.');
    $this->assertEqual(openid_normalize('example.com'), 'http://example.com/', 'openid_normalize() correctly normalized a URL with a missing scheme and empty path.');
    $this->assertEqual(openid_normalize('http://example.com'), 'http://example.com/', 'openid_normalize() correctly normalized a URL with an empty path.');

    $this->assertEqual(openid_normalize('http://example.com/path'), 'http://example.com/path', 'openid_normalize() correctly normalized a URL with a path.');

    $this->assertEqual(openid_normalize('http://example.com/path#fragment'), 'http://example.com/path', 'openid_normalize() correctly normalized a URL with a fragment.');
  }

  /**
   * Test openid_extract_namespace().
   */
  function testOpenidExtractNamespace() {
    $response = array(
      'openid.sreg.nickname' => 'john',
      'openid.ns.ext1' => OPENID_NS_SREG,
      'openid.ext1.nickname' => 'george',
      'openid.ext1.email' => '<EMAIL>',
      'openid.ns.ext2' => 'http://example.com/ns/ext2',
      'openid.ext2.foo' => '123',
      'openid.ext2.bar' => '456',
      'openid.signed' => 'sreg.nickname,ns.ext1,ext1.email,ext2.foo',
    );

    $values = openid_extract_namespace($response, 'http://example.com/ns/dummy', NULL, FALSE);
    $this->assertEqual($values, array(), 'Nothing found for unused namespace.');

    $values = openid_extract_namespace($response, 'http://example.com/ns/dummy', 'sreg', FALSE);
    $this->assertEqual($values, array('nickname' => 'john'), 'Value found for fallback prefix.');

    $values = openid_extract_namespace($response, OPENID_NS_SREG, 'sreg', FALSE);
    $this->assertEqual($values, array('nickname' => 'george', 'email' => '<EMAIL>'), 'Namespace takes precedence over fallback prefix.');

    // ext1.email is signed, but ext1.nickname is not.
    $values = openid_extract_namespace($response, OPENID_NS_SREG, 'sreg', TRUE);
    $this->assertEqual($values, array('email' => '<EMAIL>'), 'Unsigned namespaced fields ignored.');

    $values = openid_extract_namespace($response, 'http://example.com/ns/ext2', 'sreg', FALSE);
    $this->assertEqual($values, array('foo' => '123', 'bar' => '456'), 'Unsigned fields found.');

    // ext2.foo and ext2.bar are ignored, because ns.ext2 is not signed. The
    // fallback prefix is not used, because the namespace is specified.
    $values = openid_extract_namespace($response, 'http://example.com/ns/ext2', 'sreg', TRUE);
    $this->assertEqual($values, array(), 'Unsigned fields ignored.');
  }
}
