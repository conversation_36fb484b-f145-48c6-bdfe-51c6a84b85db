
#edit-openid-identifier {
  background-image: url("login-bg.png");
  background-position: left 50%; /* LTR */
  background-repeat: no-repeat;
  padding-left: 20px; /* LTR */
}
div.form-item-openid-identifier {
  display: block;
}
html.js #user-login-form div.form-item-openid-identifier,
html.js #user-login div.form-item-openid-identifier {
  display: none;
}
#user-login-form ul {
  margin-top: 0;
}
#user-login ul {
  margin: 0 0 5px;
}
#user-login ul li {
  margin: 0;
}
#user-login-form .openid-links {
  padding-bottom: 0;
}
#user-login .openid-links {
  padding-left: 0; /* LTR */
}
#user-login-form .openid-links li,
#user-login .openid-links li {
  display: none;
  list-style: none;
}
html.js #user-login-form li.openid-link,
html.js #user-login li.openid-link {
  display: block;
  margin-left: 0; /* LTR */
}
#user-login-form li.openid-link a,
#user-login li.openid-link a {
  background-image: url("login-bg.png");
  background-position: left top; /* LTR */
  background-repeat: no-repeat;
  padding: 0 0 0 1.5em; /* LTR */
}
