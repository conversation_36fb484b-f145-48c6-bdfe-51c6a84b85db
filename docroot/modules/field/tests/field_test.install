<?php

/**
 * @file
 * Install, update and uninstall functions for the field_test module.
 */

/**
 * Implements hook_install().
 */
function field_test_install() {
  // hook_entity_info_alter() needs to be executed as last.
  db_update('system')
    ->fields(array('weight' => 1))
    ->condition('name', 'field_test')
    ->execute();
}

/**
 * Implements hook_schema().
 */
function field_test_schema() {
  $schema['test_entity'] = array(
    'description' => 'The base table for test_entities.',
    'fields' => array(
      'ftid' => array(
        'description' => 'The primary identifier for a test_entity.',
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
      ),
      'ftvid' => array(
        'description' => 'The current {test_entity_revision}.ftvid version identifier.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'fttype' => array(
        'description' => 'The type of this test_entity.',
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'default' => '',
      ),
      'ftlabel' => array(
        'description' => 'The label of this test_entity.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
      ),
    ),
    'unique keys' => array(
      'ftvid' => array('ftvid'),
    ),
    'primary key' => array('ftid'),
  );
  $schema['test_entity_bundle_key'] = array(
    'description' => 'The base table for test entities with a bundle key.',
    'fields' => array(
      'ftid' => array(
        'description' => 'The primary identifier for a test_entity_bundle_key.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'fttype' => array(
        'description' => 'The type of this test_entity.',
        'type' => 'varchar',
        'length' => 32,
        'not null' => FALSE,
        'default' => '',
      ),
    ),
  );
  $schema['test_entity_bundle'] = array(
    'description' => 'The base table for test entities with a bundle.',
    'fields' => array(
      'ftid' => array(
        'description' => 'The primary identifier for a test_entity_bundle.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
    ),
  );
  $schema['test_entity_revision'] = array(
    'description' => 'Stores information about each saved version of a {test_entity}.',
    'fields' => array(
      'ftid' => array(
        'description' => 'The {test_entity} this version belongs to.',
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ),
      'ftvid' => array(
        'description' => 'The primary identifier for this version.',
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
      ),
    ),
    'indexes' => array(
      'nid' => array('ftid'),
    ),
    'primary key' => array('ftvid'),
  );

  return $schema;
}

/**
 * Implements hook_field_schema().
 */
function field_test_field_schema($field) {
  if ($field['type'] == 'test_field') {
    return array(
      'columns' => array(
        'value' => array(
          'type' => 'int',
          'size' => 'medium',
          'not null' => FALSE,
        ),
      ),
      'indexes' => array(
        'value' => array('value'),
      ),
    );
  }
  else {
    $foreign_keys = array();
    // The 'foreign keys' key is not always used in tests.
    if (!empty($field['settings']['foreign_key_name'])) {
      $foreign_keys['foreign keys'] = array(
      // This is a dummy foreign key definition, references a table that
      // doesn't exist, but that's not a problem.
      $field['settings']['foreign_key_name'] => array(
        'table' => $field['settings']['foreign_key_name'],
        'columns' => array($field['settings']['foreign_key_name'] => 'id'),
        ),
      );
    }
    return array(
      'columns' => array(
        'shape' => array(
          'type' => 'varchar',
          'length' => 32,
          'not null' => FALSE,
        ),
        'color' => array(
          'type' => 'varchar',
          'length' => 32,
          'not null' => FALSE,
        ),
      ),
    ) + $foreign_keys;
  }
}
