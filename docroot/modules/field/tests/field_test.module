<?php

/**
 * @file
 * Helper module for the Field API tests.
 *
 * The module defines
 * - an entity type (field_test.entity.inc)
 * - a field type and its formatters and widgets (field_test.field.inc)
 * - a field storage backend (field_test.storage.inc)
 *
 * The main field_test.module file implements generic hooks and provides some
 * test helper functions
 */

require_once DRUPAL_ROOT . '/modules/field/tests/field_test.entity.inc';
require_once DRUPAL_ROOT . '/modules/field/tests/field_test.field.inc';
require_once DRUPAL_ROOT . '/modules/field/tests/field_test.storage.inc';

/**
 * Implements hook_permission().
 */
function field_test_permission() {
  $perms = array(
    'access field_test content' => array(
      'title' => t('Access field_test content'),
      'description' => t('View published field_test content.'),
    ),
    'administer field_test content' => array(
      'title' => t('Administer field_test content'),
      'description' => t('Manage field_test content'),
    ),
  );
  return $perms;
}

/**
 * Implements hook_menu().
 */
function field_test_menu() {
  $items = array();
  $bundles = field_info_bundles('test_entity');

  foreach ($bundles as $bundle_name => $bundle_info) {
    $bundle_url_str = str_replace('_', '-', $bundle_name);
    $items['test-entity/add/' . $bundle_url_str] = array(
      'title' => t('Add %bundle test_entity', array('%bundle' => $bundle_info['label'])),
      'page callback' => 'field_test_entity_add',
      'page arguments' => array(2),
      'access arguments' => array('administer field_test content'),
      'type' => MENU_NORMAL_ITEM,
    );
  }
  $items['test-entity/manage/%field_test_entity_test/edit'] = array(
    'title' => 'Edit test entity',
    'page callback' => 'field_test_entity_edit',
    'page arguments' => array(2),
    'access arguments' => array('administer field_test content'),
    'type' => MENU_NORMAL_ITEM,
  );

  $items['test-entity/nested/%field_test_entity_test/%field_test_entity_test'] = array(
    'title' => 'Nested entity form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('field_test_entity_nested_form', 2, 3),
    'access arguments' => array('administer field_test content'),
    'type' => MENU_NORMAL_ITEM,
  );

  return $items;
}

/**
 * Generic op to test _field_invoke behavior.
 *
 * This simulates a field operation callback to be invoked by _field_invoke().
 */
function field_test_field_test_op($entity_type, $entity, $field, $instance, $langcode, &$items) {
  return array($langcode => hash('sha256', serialize(array($entity_type, $entity, $field['field_name'], $langcode, $items))));
}

/**
 * Generic op to test _field_invoke_multiple behavior.
 *
 * This simulates a multiple field operation callback to be invoked by
 * _field_invoke_multiple().
 */
function field_test_field_test_op_multiple($entity_type, $entities, $field, $instances, $langcode, &$items) {
  $result = array();
  foreach ($entities as $id => $entity) {
    // Entities, instances and items are assumed to be consistently grouped by
    // language. To verify this we try to access all the passed data structures
    // by entity id. If they are grouped correctly, one entity, one instance and
    // one array of items should be available for each entity id.
    $field_name = $instances[$id]['field_name'];
    $result[$id] = array($langcode => hash('sha256', serialize(array($entity_type, $entity, $field_name, $langcode, $items[$id]))));
  }
  return $result;
}

/**
 * Implements hook_field_available_languages_alter().
 */
function field_test_field_available_languages_alter(&$languages, $context) {
  if (variable_get('field_test_field_available_languages_alter', FALSE)) {
    // Add an unavailable language.
    $languages[] = 'xx';
    // Remove an available language.
    $index = array_search('en', $languages);
    unset($languages[$index]);
  }
}

/**
 * Implements hook_field_language_alter().
 */
function field_test_field_language_alter(&$display_language, $context) {
  if (variable_get('field_test_language_fallback', TRUE)) {
    locale_field_language_fallback($display_language, $context['entity'], $context['language']);
  }
}

/**
 * Store and retrieve keyed data for later verification by unit tests.
 *
 * This function is a simple in-memory key-value store with the
 * distinction that it stores all values for a given key instead of
 * just the most recently set value. field_test module hooks call
 * this function to record their arguments, keyed by hook name. The
 * unit tests later call this function to verify that the correct
 * hooks were called and were passed the correct arguments.
 *
 * This function ignores all calls until the first time it is called
 * with $key of NULL. Each time it is called with $key of NULL, it
 * erases all previously stored data from its internal cache, but also
 * returns the previously stored data to the caller. A typical usage
 * scenario is:
 *
 * @code
 *   // calls to field_test_memorize() here are ignored
 *
 *   // turn on memorization
 *   field_test_memorize();
 *
 *   // call some Field API functions that invoke field_test hooks
 *   $field = field_create_field(...);
 *
 *   // retrieve and reset the memorized hook call data
 *   $mem = field_test_memorize();
 *
 *   // make sure hook_field_create_field() is invoked correctly
 *   assertEqual(count($mem['field_test_field_create_field']), 1);
 *   assertEqual($mem['field_test_field_create_field'][0], array($field));
 * @endcode
 *
 * @param $key
 *   The key under which to store to $value, or NULL as described above.
 * @param $value
 *   A value to store for $key.
 * @return
 *   An array mapping each $key to an array of each $value passed in
 *   for that key.
 */
function field_test_memorize($key = NULL, $value = NULL) {
  $memorize = &drupal_static(__FUNCTION__, NULL);

  if (!isset($key)) {
    $return = $memorize;
    $memorize = array();
    return $return;
  }
  if (is_array($memorize)) {
    $memorize[$key][] = $value;
  }
}

/**
 * Memorize calls to hook_field_create_field().
 */
function field_test_field_create_field($field) {
  $args = func_get_args();
  field_test_memorize(__FUNCTION__, $args);
}

/**
 * Implements hook_entity_query_alter().
 */
function field_test_entity_query_alter(&$query) {
  if (variable_get('field_test_entity_query_alter_execute_callback', FALSE)) {
    $query->executeCallback = 'field_test_dummy_field_storage_query';
  }
}

/**
 * Pseudo-implements hook_field_storage_query().
 */
function field_test_dummy_field_storage_query(EntityFieldQuery $query) {
  // Return dummy values that will be checked by the test.
  return array(
    'user' => array(
      1 => entity_create_stub_entity('user', array(1, NULL, NULL)),
    ),
  );
}

/**
 * Implements callback_entity_info_label().
 *
 * @return
 *   The label of the entity prefixed with "label callback".
 */
function field_test_entity_label_callback($entity) {
  return 'label callback ' . $entity->ftlabel;
}

/**
 * Implements hook_field_attach_view_alter().
 */
function field_test_field_attach_view_alter(&$output, $context) {
  if (!empty($context['display']['settings']['alter'])) {
    $output['test_field'][] = array('#markup' => 'field_test_field_attach_view_alter');
  }

  if (isset($output['test_field'])) {
    $output['test_field'][] = array('#markup' => 'field language is ' . $context['language']);
  }
}

/**
 * Implements hook_field_widget_properties_alter().
 */
function field_test_field_widget_properties_alter(&$widget, $context) {
  // Make the alter_test_text field 42 characters for nodes and comments.
  if (in_array($context['entity_type'], array('node', 'comment')) && ($context['field']['field_name'] == 'alter_test_text')) {
    $widget['settings']['size'] = 42;
  }
}

/**
 * Implements hook_field_widget_properties_ENTITY_TYPE_alter().
 */
function field_test_field_widget_properties_user_alter(&$widget, $context) {
  // Always use buttons for the alter_test_options field on user forms.
  if ($context['field']['field_name'] == 'alter_test_options') {
    $widget['type'] = 'options_buttons';
  }
}

/**
 * Implements hook_field_widget_form_alter().
 */
function field_test_field_widget_form_alter(&$element, &$form_state, $context) {
  switch ($context['field']['field_name']) {
    case 'alter_test_text':
      drupal_set_message('Field size: ' . $context['instance']['widget']['settings']['size']);
      break;

    case 'alter_test_options':
      drupal_set_message('Widget type: ' . $context['instance']['widget']['type']);
      break;
  }
}

/**
 * Implements hook_query_TAG_alter() for tag 'efq_table_prefixing_test'.
 *
 * @see EntityFieldQueryTestCase::testTablePrefixing()
 */
function field_test_query_efq_table_prefixing_test_alter(&$query) {
  // Add an additional join onto the entity base table. This will cause an
  // exception if the EFQ does not properly prefix the base table.
  $query->join('test_entity','te2','%alias.ftid = test_entity.ftid');
}

/**
 * Implements hook_query_TAG_alter() for tag 'store_global_test_query'.
 */
function field_test_query_store_global_test_query_alter($query) {
  // Save the query in a global variable so that it can be examined by tests.
  // This can be used by any test which needs to check a query, but see
  // FieldSqlStorageTestCase::testFieldSqlStorageMultipleConditionsSameColumn()
  // for an example.
  $GLOBALS['test_query'] = $query;
}
