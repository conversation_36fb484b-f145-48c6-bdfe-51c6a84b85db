<?php

/**
 * @file
 * Default implementation of the field storage API.
 */

/**
 * Implements hook_help().
 */
function field_sql_storage_help($path, $arg) {
  switch ($path) {
    case 'admin/help#field_sql_storage':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Field SQL storage module stores field data in the database. It is the default field storage module; other field storage mechanisms may be available as contributed modules. See the <a href="@field-help">Field module help page</a> for more information about fields.', array('@field-help' => url('admin/help/field'))) . '</p>';
      return $output;
  }
}

/**
 * Implements hook_field_storage_info().
 */
function field_sql_storage_field_storage_info() {
  return array(
    'field_sql_storage' => array(
      'label' => t('Default SQL storage'),
      'description' => t('Stores fields in the local SQL database, using per-field tables.'),
    ),
  );
}

/**
 * Generate a table name for a field data table.
 *
 * @param $field
 *   The field structure.
 * @return
 *   A string containing the generated name for the database table
 */
function _field_sql_storage_tablename($field) {
  if ($field['deleted']) {
    return "field_deleted_data_{$field['id']}";
  }
  else {
    return "field_data_{$field['field_name']}";
  }
}

/**
 * Generate a table name for a field revision archive table.
 *
 * @param $name
 *   The field structure.
 * @return
 *   A string containing the generated name for the database table
 */
function _field_sql_storage_revision_tablename($field) {
  if ($field['deleted']) {
    return "field_deleted_revision_{$field['id']}";
  }
  else {
    return "field_revision_{$field['field_name']}";
  }
}

/**
 * Generates a table alias for a field data table.
 *
 * The table alias is unique for each unique combination of field name
 * (represented by $tablename), delta_group and language_group.
 *
 * @param $tablename
 *   The name of the data table for this field.
 * @param $field_key
 *   The numeric key of this field in this query.
 * @param $query
 *   The EntityFieldQuery that is executed.
 *
 * @return
 *   A string containing the generated table alias.
 */
function _field_sql_storage_tablealias($tablename, $field_key, EntityFieldQuery $query) {
  // No conditions present: use a unique alias.
  if (empty($query->fieldConditions[$field_key])) {
    return $tablename . $field_key;
  }

  // Find the delta and language condition values and append them to the alias.
  $condition = $query->fieldConditions[$field_key];
  $alias = $tablename;
  $has_group_conditions = FALSE;

  foreach (array('delta', 'language') as $column) {
    if (isset($condition[$column . '_group'])) {
      $alias .= '_' . $column . '_' . $condition[$column . '_group'];
      $has_group_conditions = TRUE;
    }
  }

  // Return the alias when it has delta/language group conditions.
  if ($has_group_conditions) {
    return $alias;
  }

  // Return a unique alias in other cases.
  return $tablename . $field_key;
}

/**
 * Generate a column name for a field data table.
 *
 * @param $name
 *   The name of the field
 * @param $column
 *   The name of the column
 * @return
 *   A string containing a generated column name for a field data
 *   table that is unique among all other fields.
 */
function _field_sql_storage_columnname($name, $column) {
  return $name . '_' . $column;
}

/**
 * Generate an index name for a field data table.
 *
 * @param $name
 *   The name of the field
 * @param $column
 *   The name of the index
 * @return
 *   A string containing a generated index name for a field data
 *   table that is unique among all other fields.
 */
function _field_sql_storage_indexname($name, $index) {
  return $name . '_' . $index;
}

/**
 * Return the database schema for a field. This may contain one or
 * more tables. Each table will contain the columns relevant for the
 * specified field. Leave the $field's 'columns' and 'indexes' keys
 * empty to get only the base schema.
 *
 * @param $field
 *   The field structure for which to generate a database schema.
 * @return
 *   One or more tables representing the schema for the field.
 */
function _field_sql_storage_schema($field) {
  $deleted = $field['deleted'] ? 'deleted ' : '';
  $current = array(
    'description' => "Data storage for {$deleted}field {$field['id']} ({$field['field_name']})",
    'fields' => array(
      'entity_type' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The entity type this data is attached to',
      ),
      'bundle' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The field instance bundle to which this row belongs, used when deleting a field instance',
      ),
      'deleted' => array(
        'type' => 'int',
        'size' => 'tiny',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'A boolean indicating whether this data item has been deleted'
      ),
      'entity_id' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'The entity id this data is attached to',
      ),
      'revision_id' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => FALSE,
        'description' => 'The entity revision id this data is attached to, or NULL if the entity type is not versioned',
      ),
      // @todo Consider storing language as integer.
      'language' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'default' => '',
        'description' => 'The language for this data item.',
      ),
      'delta' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'The sequence number for this data item, used for multi-value fields',
      ),
    ),
    'primary key' => array('entity_type', 'entity_id', 'deleted', 'delta', 'language'),
    'indexes' => array(
      'entity_type' => array('entity_type'),
      'bundle' => array('bundle'),
      'deleted' => array('deleted'),
      'entity_id' => array('entity_id'),
      'revision_id' => array('revision_id'),
      'language' => array('language'),
    ),
  );

  // If the target entity type uses a string for its entity ID then update
  // the fields entity_id and revision_id columns from INT to VARCHAR.
  if (!empty($field['entity_id_type']) && $field['entity_id_type'] === 'string') {
    $current['fields']['entity_id']['type'] = 'varchar';
    $current['fields']['entity_id']['length'] = 128;
    unset($current['fields']['entity_id']['unsigned']);

    $current['fields']['revision_id']['type'] = 'varchar';
    $current['fields']['revision_id']['length'] = 128;
    unset($current['fields']['revision_id']['unsigned']);
  }

  $field += array('columns' => array(), 'indexes' => array(), 'foreign keys' => array());
  // Add field columns.
  foreach ($field['columns'] as $column_name => $attributes) {
    $real_name = _field_sql_storage_columnname($field['field_name'], $column_name);
    $current['fields'][$real_name] = $attributes;
  }

  // Add indexes.
  foreach ($field['indexes'] as $index_name => $columns) {
    $real_name = _field_sql_storage_indexname($field['field_name'], $index_name);
    foreach ($columns as $column_name) {
      // Indexes can be specified as either a column name or an array with
      // column name and length. Allow for either case.
      if (is_array($column_name)) {
        $current['indexes'][$real_name][] = array(
          _field_sql_storage_columnname($field['field_name'], $column_name[0]),
          $column_name[1],
        );
      }
      else {
        $current['indexes'][$real_name][] = _field_sql_storage_columnname($field['field_name'], $column_name);
      }
    }
  }

  // Add foreign keys.
  foreach ($field['foreign keys'] as $specifier => $specification) {
    $real_name = _field_sql_storage_indexname($field['field_name'], $specifier);
    $current['foreign keys'][$real_name]['table'] = $specification['table'];
    foreach ($specification['columns'] as $column_name => $referenced) {
      $sql_storage_column = _field_sql_storage_columnname($field['field_name'], $column_name);
      $current['foreign keys'][$real_name]['columns'][$sql_storage_column] = $referenced;
    }
  }

  // Construct the revision table.
  $revision = $current;
  $revision['description'] = "Revision archive storage for {$deleted}field {$field['id']} ({$field['field_name']})";
  $revision['primary key'] = array('entity_type', 'entity_id', 'revision_id', 'deleted', 'delta', 'language');
  $revision['fields']['revision_id']['not null'] = TRUE;
  $revision['fields']['revision_id']['description'] = 'The entity revision id this data is attached to';

  return array(
    _field_sql_storage_tablename($field) => $current,
    _field_sql_storage_revision_tablename($field) => $revision,
  );
}

/**
 * Implements hook_field_storage_create_field().
 */
function field_sql_storage_field_storage_create_field($field) {
  $schema = _field_sql_storage_schema($field);
  foreach ($schema as $name => $table) {
    db_create_table($name, $table);
  }
  drupal_get_schema(NULL, TRUE);
}

/**
 * Implements hook_field_update_forbid().
 *
 * Forbid any field update that changes column definitions if there is
 * any data.
 */
function field_sql_storage_field_update_forbid($field, $prior_field, $has_data) {
  if ($has_data && $field['columns'] != $prior_field['columns']) {
    throw new FieldUpdateForbiddenException("field_sql_storage cannot change the schema for an existing field with data.");
  }
}

/**
 * Implements hook_field_storage_update_field().
 */
function field_sql_storage_field_storage_update_field($field, $prior_field, $has_data) {
  if (! $has_data) {
    // There is no data. Re-create the tables completely.

    if (Database::getConnection()->supportsTransactionalDDL()) {
      // If the database supports transactional DDL, we can go ahead and rely
      // on it. If not, we will have to rollback manually if something fails.
      $transaction = db_transaction();
    }

    try {
      $prior_schema = _field_sql_storage_schema($prior_field);
      foreach ($prior_schema as $name => $table) {
        db_drop_table($name, $table);
      }
      $schema = _field_sql_storage_schema($field);
      foreach ($schema as $name => $table) {
        db_create_table($name, $table);
      }
    }
    catch (Exception $e) {
      if (Database::getConnection()->supportsTransactionalDDL()) {
        $transaction->rollback();
      }
      else {
        // Recreate tables.
        $prior_schema = _field_sql_storage_schema($prior_field);
        foreach ($prior_schema as $name => $table) {
          if (!db_table_exists($name)) {
            db_create_table($name, $table);
          }
        }
      }
      throw $e;
    }
  }
  else {
    // There is data, so there are no column changes. Drop all the
    // prior indexes and create all the new ones, except for all the
    // priors that exist unchanged.
    $table = _field_sql_storage_tablename($prior_field);
    $revision_table = _field_sql_storage_revision_tablename($prior_field);
    foreach ($prior_field['indexes'] as $name => $columns) {
      if (!isset($field['indexes'][$name]) || $columns != $field['indexes'][$name]) {
        $real_name = _field_sql_storage_indexname($field['field_name'], $name);
        db_drop_index($table, $real_name);
        db_drop_index($revision_table, $real_name);
      }
    }
    $table = _field_sql_storage_tablename($field);
    $revision_table = _field_sql_storage_revision_tablename($field);
    foreach ($field['indexes'] as $name => $columns) {
      if (!isset($prior_field['indexes'][$name]) || $columns != $prior_field['indexes'][$name]) {
        $real_name = _field_sql_storage_indexname($field['field_name'], $name);
        $real_columns = array();
        foreach ($columns as $column_name) {
          // Indexes can be specified as either a column name or an array with
          // column name and length. Allow for either case.
          if (is_array($column_name)) {
            $real_columns[] = array(
              _field_sql_storage_columnname($field['field_name'], $column_name[0]),
              $column_name[1],
            );
          }
          else {
            $real_columns[] = _field_sql_storage_columnname($field['field_name'], $column_name);
          }
        }
        db_add_index($table, $real_name, $real_columns);
        db_add_index($revision_table, $real_name, $real_columns);
      }
    }
  }
  drupal_get_schema(NULL, TRUE);
}

/**
 * Implements hook_field_storage_delete_field().
 */
function field_sql_storage_field_storage_delete_field($field) {
  // Mark all data associated with the field for deletion.
  $field['deleted'] = 0;
  $table = _field_sql_storage_tablename($field);
  $revision_table = _field_sql_storage_revision_tablename($field);
  db_update($table)
    ->fields(array('deleted' => 1))
    ->execute();

  // Move the table to a unique name while the table contents are being deleted.
  $field['deleted'] = 1;
  $new_table = _field_sql_storage_tablename($field);
  $revision_new_table = _field_sql_storage_revision_tablename($field);
  db_rename_table($table, $new_table);
  db_rename_table($revision_table, $revision_new_table);
  drupal_get_schema(NULL, TRUE);
}

/**
 * Implements hook_field_storage_load().
 */
function field_sql_storage_field_storage_load($entity_type, $entities, $age, $fields, $options) {
  $load_current = $age == FIELD_LOAD_CURRENT;

  foreach ($fields as $field_id => $ids) {
    // By the time this hook runs, the relevant field definitions have been
    // populated and cached in FieldInfo, so calling field_info_field_by_id()
    // on each field individually is more efficient than loading all fields in
    // memory upfront with field_info_field_by_ids().
    $field = field_info_field_by_id($field_id);
    $field_name = $field['field_name'];
    $table = $load_current ? _field_sql_storage_tablename($field) : _field_sql_storage_revision_tablename($field);

    $query = db_select($table, 't')
      ->fields('t')
      ->condition('entity_type', $entity_type)
      ->condition($load_current ? 'entity_id' : 'revision_id', $ids, 'IN')
      ->condition('language', field_available_languages($entity_type, $field), 'IN')
      ->orderBy('delta');

    if (empty($options['deleted'])) {
      $query->condition('deleted', 0);
    }

    $results = $query->execute();

    $delta_count = array();
    foreach ($results as $row) {
      if (!isset($delta_count[$row->entity_id][$row->language])) {
        $delta_count[$row->entity_id][$row->language] = 0;
      }

      if ($field['cardinality'] == FIELD_CARDINALITY_UNLIMITED || $delta_count[$row->entity_id][$row->language] < $field['cardinality']) {
        $item = array();
        // For each column declared by the field, populate the item
        // from the prefixed database column.
        foreach ($field['columns'] as $column => $attributes) {
          $column_name = _field_sql_storage_columnname($field_name, $column);
          $item[$column] = $row->$column_name;
        }

        // Add the item to the field values for the entity.
        $entities[$row->entity_id]->{$field_name}[$row->language][] = $item;
        $delta_count[$row->entity_id][$row->language]++;
      }
    }
  }
}

/**
 * Callback for array_filter().
 */
function _field_sql_storage_write_compare_filter_callback($value) {
  return NULL !== $value && '' !== $value;
}

/**
 * Cleanup field values for later values comparison.
 *
 * @param array $field
 *   Field info as returned by field_info_field_by_id().
 *
 * @param array $array
 *   Field values to cleanup.
 *
 * @return array
 *   Filtered values.
 */
function _field_sql_storage_write_compare_filter($field, $array) {
  foreach ($array as $language => $items) {
    if (empty($items)) {
      unset($array[$language]);
    }
    else {
      foreach ($items as $delta => $item) {
        // This should not happen but some modules provide invalid data to the
        // field API.
        if (!is_array($item)) {
          continue;
        }
        // Let's start by pruning empty values and non storable values.
        $array[$language][$delta] = array_filter(array_intersect_key($item, $field['columns']), '_field_sql_storage_write_compare_filter_callback');
        // Ordering is important because for widget elements and loaded columns
        // from database order might differ and give false positives on field
        // value change, especially with complex fields such as image fields.
        ksort($array[$language][$delta]);
      }
    }
  }
  return $array;
}

/**
 * Compare a single field value for both entities and tell us if it changed.
 *
 * @param array $field
 *   Loaded field structure.
 * @param object $entity1
 *   First entity to compare.
 * @param object $entity2
 *   Second entity to compare.
 *
 * @return bool
 *   True if field value changed, false otherwise.
 */
function _field_sql_storage_write_compare($field, $entity1, $entity2) {
  $field_name = $field['field_name'];
  if (empty($entity1->$field_name) && empty($entity2->$field_name)) {
    // Both are empty we can safely assume that it did not change.
    return FALSE;
  }
  if (!isset($entity1->$field_name) || !isset($entity2->$field_name)) {
    // One of them is missing but not the other the value changed.
    return TRUE;
  }
  // We need to proceed to deep array comparison, but we cannot do it naively:
  // in most cases the field values come from the edit form, and some Form API
  // widget values that are not field columns may be present. We need to clean
  // up both original and new field values before comparison.
  $items1 = _field_sql_storage_write_compare_filter($field, (array) $entity1->$field_name);
  $items2 = _field_sql_storage_write_compare_filter($field, (array) $entity2->$field_name);
  return $items1 != $items2;
}

/**
 * Implements hook_field_storage_write().
 */
function field_sql_storage_field_storage_write($entity_type, $entity, $op, $fields) {
  list($id, $vid, $bundle) = entity_extract_ids($entity_type, $entity);
  if (!isset($vid)) {
    $vid = $id;
  }

  // Check if the given entity is a new revision or not. In case of a new
  // revision creation, we cannot skip any field.
  if (!empty($vid) && !empty($entity->original)) {
    list(, $original_vid) = entity_extract_ids($entity_type, $entity->original);
    if (NULL === $original_vid) {
      $original_vid = $id;
    }
    $is_new_revision = $original_vid != $vid;
  }
  else {
    $is_new_revision = FALSE;
  }

  // Allow this optimization to be optional.
  $skip_unchanged_fields = variable_get('field_sql_storage_skip_writing_unchanged_fields', FALSE);

  foreach ($fields as $field_id) {
    $field = field_info_field_by_id($field_id);

    if ($skip_unchanged_fields && !$is_new_revision && !empty($entity->original) && !_field_sql_storage_write_compare($field, $entity, $entity->original)) {
      continue;
    }

    $field_name = $field['field_name'];
    $table_name = _field_sql_storage_tablename($field);
    $revision_name = _field_sql_storage_revision_tablename($field);

    $all_languages = field_available_languages($entity_type, $field);
    $field_languages = array_intersect($all_languages, array_keys((array) $entity->$field_name));

    // Delete and insert, rather than update, in case a value was added.
    if ($op == FIELD_STORAGE_UPDATE) {
      // Delete languages present in the incoming $entity->$field_name.
      // Delete all languages if $entity->$field_name is empty.
      $languages = !empty($entity->$field_name) ? $field_languages : $all_languages;
      if ($languages) {
        db_delete($table_name)
          ->condition('entity_type', $entity_type)
          ->condition('entity_id', $id)
          ->condition('language', $languages, 'IN')
          ->execute();
        db_delete($revision_name)
          ->condition('entity_type', $entity_type)
          ->condition('entity_id', $id)
          ->condition('revision_id', $vid)
          ->condition('language', $languages, 'IN')
          ->execute();
      }
    }

    // Prepare the multi-insert query.
    $do_insert = FALSE;
    $columns = array('entity_type', 'entity_id', 'revision_id', 'bundle', 'delta', 'language');
    foreach ($field['columns'] as $column => $attributes) {
      $columns[] = _field_sql_storage_columnname($field_name, $column);
    }
    $query = db_insert($table_name)->fields($columns);
    $revision_query = db_insert($revision_name)->fields($columns);

    foreach ($field_languages as $langcode) {
      $items = (array) $entity->{$field_name}[$langcode];
      $delta_count = 0;
      foreach ($items as $delta => $item) {
        // We now know we have something to insert.
        $do_insert = TRUE;
        $record = array(
          'entity_type' => $entity_type,
          'entity_id' => $id,
          'revision_id' => $vid,
          'bundle' => $bundle,
          'delta' => $delta,
          'language' => $langcode,
        );
        foreach ($field['columns'] as $column => $attributes) {
          $record[_field_sql_storage_columnname($field_name, $column)] = isset($item[$column]) ? $item[$column] : NULL;
        }
        $query->values($record);
        if (isset($vid)) {
          $revision_query->values($record);
        }

        if ($field['cardinality'] != FIELD_CARDINALITY_UNLIMITED && ++$delta_count == $field['cardinality']) {
          break;
        }
      }
    }

    // Execute the query if we have values to insert.
    if ($do_insert) {
      $query->execute();
      $revision_query->execute();
    }
  }
}

/**
 * Implements hook_field_storage_delete().
 *
 * This function deletes data for all fields for an entity from the database.
 */
function field_sql_storage_field_storage_delete($entity_type, $entity, $fields) {
  list($id, $vid, $bundle) = entity_extract_ids($entity_type, $entity);

  foreach (field_info_instances($entity_type, $bundle) as $instance) {
    if (isset($fields[$instance['field_id']])) {
      $field = field_info_field_by_id($instance['field_id']);
      field_sql_storage_field_storage_purge($entity_type, $entity, $field, $instance);
    }
  }
}

/**
 * Implements hook_field_storage_purge().
 *
 * This function deletes data from the database for a single field on
 * an entity.
 */
function field_sql_storage_field_storage_purge($entity_type, $entity, $field, $instance) {
  list($id, $vid, $bundle) = entity_extract_ids($entity_type, $entity);

  $table_name = _field_sql_storage_tablename($field);
  $revision_name = _field_sql_storage_revision_tablename($field);
  db_delete($table_name)
    ->condition('entity_type', $entity_type)
    ->condition('entity_id', $id)
    ->execute();
  db_delete($revision_name)
    ->condition('entity_type', $entity_type)
    ->condition('entity_id', $id)
    ->execute();
}

/**
 * Implements hook_field_storage_query().
 */
function field_sql_storage_field_storage_query(EntityFieldQuery $query) {
  if ($query->age == FIELD_LOAD_CURRENT) {
    $tablename_function = '_field_sql_storage_tablename';
    $id_key = 'entity_id';
  }
  else {
    $tablename_function = '_field_sql_storage_revision_tablename';
    $id_key = 'revision_id';
  }
  $table_aliases = array();
  $query_tables = NULL;
  // Add tables for the fields used.
  foreach ($query->fields as $key => $field) {
    $tablename = $tablename_function($field);
    $table_alias = _field_sql_storage_tablealias($tablename, $key, $query);
    $table_aliases[$key] = $table_alias;
    if ($key) {
      if (!isset($query_tables[$table_alias])) {
        $select_query->join($tablename, $table_alias, "$table_alias.entity_type = $field_base_table.entity_type AND $table_alias.$id_key = $field_base_table.$id_key");
      }
    }
    else {
      $select_query = db_select($tablename, $table_alias);
      // Store a reference to the list of joined tables.
      $query_tables =& $select_query->getTables();
      // Allow queries internal to the Field API to opt out of the access
      // check, for situations where the query's results should not depend on
      // the access grants for the current user.
      if (!isset($query->tags['DANGEROUS_ACCESS_CHECK_OPT_OUT'])) {
        $select_query->addTag('entity_field_access');
      }
      $select_query->addMetaData('base_table', $tablename);
      $select_query->fields($table_alias, array('entity_type', 'entity_id', 'revision_id', 'bundle'));
      $field_base_table = $table_alias;
    }
    if ($field['cardinality'] != 1 || $field['translatable']) {
      $select_query->distinct();
    }
  }

  // Add field conditions. We need a fresh grouping cache.
  drupal_static_reset('_field_sql_storage_query_field_conditions');
  _field_sql_storage_query_field_conditions($query, $select_query, $query->fieldConditions, $table_aliases, '_field_sql_storage_columnname');

  // Add field meta conditions.
  _field_sql_storage_query_field_conditions($query, $select_query, $query->fieldMetaConditions, $table_aliases, '_field_sql_storage_query_columnname');

  if (isset($query->deleted)) {
    $select_query->condition("$field_base_table.deleted", (int) $query->deleted);
  }

  // Is there a need to sort the query by property?
  $has_property_order = FALSE;
  foreach ($query->order as $order) {
    if ($order['type'] == 'property') {
      $has_property_order = TRUE;
    }
  }

  if ($query->propertyConditions || $has_property_order) {
    if (empty($query->entityConditions['entity_type']['value'])) {
      throw new EntityFieldQueryException('Property conditions and orders must have an entity type defined.');
    }
    $entity_type = $query->entityConditions['entity_type']['value'];
    $entity_base_table = _field_sql_storage_query_join_entity($select_query, $entity_type, $field_base_table);
    $query->entityConditions['entity_type']['operator'] = '=';
    foreach ($query->propertyConditions as $property_condition) {
      $query->addCondition($select_query, "$entity_base_table." . $property_condition['column'], $property_condition);
    }
  }
  foreach ($query->entityConditions as $key => $condition) {
    $query->addCondition($select_query, "$field_base_table.$key", $condition);
  }

  // Order the query.
  foreach ($query->order as $order) {
    if ($order['type'] == 'entity') {
      $key = $order['specifier'];
      $select_query->orderBy("$field_base_table.$key", $order['direction']);
    }
    elseif ($order['type'] == 'field') {
      $specifier = $order['specifier'];
      $field = $specifier['field'];
      $table_alias = $table_aliases[$specifier['index']];
      $sql_field = "$table_alias." . _field_sql_storage_columnname($field['field_name'], $specifier['column']);
      $select_query->orderBy($sql_field, $order['direction']);
    }
    elseif ($order['type'] == 'property') {
      $select_query->orderBy("$entity_base_table." . $order['specifier'], $order['direction']);
    }
  }

  return $query->finishQuery($select_query, $id_key);
}

/**
 * Adds the base entity table to a field query object.
 *
 * @param SelectQuery $select_query
 *   A SelectQuery containing at least one table as specified by
 *   _field_sql_storage_tablename().
 * @param $entity_type
 *   The entity type for which the base table should be joined.
 * @param $field_base_table
 *   Name of a table in $select_query. As only INNER JOINs are used, it does
 *   not matter which.
 *
 * @return
 *   The name of the entity base table joined in.
 */
function _field_sql_storage_query_join_entity(SelectQuery $select_query, $entity_type, $field_base_table) {
  $entity_info = entity_get_info($entity_type);
  $entity_base_table = $entity_info['base table'];
  $entity_field = $entity_info['entity keys']['id'];
  $select_query->join($entity_base_table, $entity_base_table, "$entity_base_table.$entity_field = $field_base_table.entity_id");
  return $entity_base_table;
}

/**
 * Adds field (meta) conditions to the given query objects respecting groupings.
 *
 * @param EntityFieldQuery $query
 *   The field query object to be processed.
 * @param SelectQuery $select_query
 *   The SelectQuery that should get grouping conditions.
 * @param condtions
 *   The conditions to be added.
 * @param $table_aliases
 *   An associative array of table aliases keyed by field index.
 * @param $column_callback
 *   A callback that should return the column name to be used for the field
 *   conditions. Accepts a field name and a field column name as parameters.
 */
function _field_sql_storage_query_field_conditions(EntityFieldQuery $query, SelectQuery $select_query, $conditions, $table_aliases, $column_callback) {
  $groups = &drupal_static(__FUNCTION__, array());
  foreach ($conditions as $key => $condition) {
    $table_alias = $table_aliases[$key];
    $field = $condition['field'];
    // Add the specified condition.
    $sql_field = "$table_alias." . $column_callback($field['field_name'], $condition['column']);
    $query->addCondition($select_query, $sql_field, $condition);
    // Add delta / language group conditions.
    foreach (array('delta', 'language') as $column) {
      if (isset($condition[$column . '_group'])) {
        $group_name = $condition[$column . '_group'];
        if (!isset($groups[$column][$group_name])) {
          $groups[$column][$group_name] = $table_alias;
        }
        else {
          $select_query->where("$table_alias.$column = " . $groups[$column][$group_name] . ".$column");
        }
      }
    }
  }
}

/**
 * Field meta condition column callback.
 */
function _field_sql_storage_query_columnname($field_name, $column) {
  return $column;
}

/**
 * Implements hook_field_storage_delete_revision().
 *
 * This function actually deletes the data from the database.
 */
function field_sql_storage_field_storage_delete_revision($entity_type, $entity, $fields) {
  list($id, $vid, $bundle) = entity_extract_ids($entity_type, $entity);

  if (isset($vid)) {
    foreach ($fields as $field_id) {
      $field = field_info_field_by_id($field_id);
      $revision_name = _field_sql_storage_revision_tablename($field);
      db_delete($revision_name)
        ->condition('entity_type', $entity_type)
        ->condition('entity_id', $id)
        ->condition('revision_id', $vid)
        ->execute();
    }
  }
}

/**
 * Implements hook_field_storage_delete_instance().
 *
 * This function simply marks for deletion all data associated with the field.
 */
function field_sql_storage_field_storage_delete_instance($instance) {
  $field = field_info_field($instance['field_name']);
  $table_name = _field_sql_storage_tablename($field);
  $revision_name = _field_sql_storage_revision_tablename($field);
  db_update($table_name)
    ->fields(array('deleted' => 1))
    ->condition('entity_type', $instance['entity_type'])
    ->condition('bundle', $instance['bundle'])
    ->execute();
  db_update($revision_name)
    ->fields(array('deleted' => 1))
    ->condition('entity_type', $instance['entity_type'])
    ->condition('bundle', $instance['bundle'])
    ->execute();
}

/**
 * Implements hook_field_attach_rename_bundle().
 */
function field_sql_storage_field_attach_rename_bundle($entity_type, $bundle_old, $bundle_new) {
  // We need to account for deleted or inactive fields and instances.
  $instances = field_read_instances(array('entity_type' => $entity_type, 'bundle' => $bundle_new), array('include_deleted' => TRUE, 'include_inactive' => TRUE));
  foreach ($instances as $instance) {
    $field = field_info_field_by_id($instance['field_id']);
    if ($field['storage']['type'] == 'field_sql_storage') {
      $table_name = _field_sql_storage_tablename($field);
      $revision_name = _field_sql_storage_revision_tablename($field);
      db_update($table_name)
        ->fields(array('bundle' => $bundle_new))
        ->condition('entity_type', $entity_type)
        ->condition('bundle', $bundle_old)
        ->execute();
      db_update($revision_name)
        ->fields(array('bundle' => $bundle_new))
        ->condition('entity_type', $entity_type)
        ->condition('bundle', $bundle_old)
        ->execute();
    }
  }
}

/**
 * Implements hook_field_storage_purge_field().
 *
 * All field data items and instances have already been purged, so all
 * that is left is to delete the table.
 */
function field_sql_storage_field_storage_purge_field($field) {
  $table_name = _field_sql_storage_tablename($field);
  $revision_name = _field_sql_storage_revision_tablename($field);
  db_drop_table($table_name);
  db_drop_table($revision_name);
}

/**
 * Implements hook_field_storage_details().
 */
function field_sql_storage_field_storage_details($field) {
  $details = array();
  if (!empty($field['columns'])) {
     // Add field columns.
    foreach ($field['columns'] as $column_name => $attributes) {
      $real_name = _field_sql_storage_columnname($field['field_name'], $column_name);
      $columns[$column_name] = $real_name;
    }
    return array(
      'sql' => array(
        FIELD_LOAD_CURRENT => array(
          _field_sql_storage_tablename($field) => $columns,
        ),
        FIELD_LOAD_REVISION => array(
          _field_sql_storage_revision_tablename($field) => $columns,
        ),
      ),
    );
  }
}
