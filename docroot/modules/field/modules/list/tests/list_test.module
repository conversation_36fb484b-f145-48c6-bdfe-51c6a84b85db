<?php

/**
 * @file
 * Helper module for the List module tests.
 */

/**
 * Allowed values callback.
 */
function list_test_allowed_values_callback($field) {
  $values = array(
    'Group 1' => array(
      0 => 'Zero',
    ),
    1 => 'One',
    'Group 2' => array(
      2 => 'Some <script>dangerous</script> & unescaped <strong>markup</strong>',
    ),
  );

  return $values;
}

/**
 * An entity-bound allowed values callback.
 */
function list_test_dynamic_values_callback($field, $instance, $entity_type, $entity, &$cacheable) {
  $cacheable = FALSE;
  // We need the values of the entity as keys.
  return drupal_map_assoc(array_merge(array($entity->ftlabel), entity_extract_ids($entity_type, $entity)));
}
