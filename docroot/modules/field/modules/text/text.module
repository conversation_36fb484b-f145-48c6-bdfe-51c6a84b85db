<?php

/**
 * @file
 * Defines simple text field types.
 */

/**
 * Implements hook_help().
 */
function text_help($path, $arg) {
  switch ($path) {
    case 'admin/help#text':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t("The Text module defines various text field types for the Field module. A text field may contain plain text only, or optionally, may use <PERSON><PERSON><PERSON>'s <a href='@filter-help'>text filters</a> to securely manage HTML output. Text input fields may be either a single line (text field), multiple lines (text area), or for greater input control, a select box, checkbox, or radio buttons. If desired, the field can be validated, so that it is limited to a set of allowed values. See the <a href='@field-help'>Field module help page</a> for more information about fields.", array('@field-help' => url('admin/help/field'), '@filter-help' => url('admin/help/filter'))) . '</p>';
      return $output;
  }
}

/**
 * Implements hook_field_info().
 *
 * Field settings:
 *   - max_length: the maximum length for a varchar field.
 * Instance settings:
 *   - text_processing: whether text input filters should be used.
 *   - display_summary: whether the summary field should be displayed.
 *     When empty and not displayed the summary will take its value from the
 *     trimmed value of the main text field.
 */
function text_field_info() {
  return array(
    'text' => array(
      'label' => t('Text'),
      'description' => t('This field stores varchar text in the database.'),
      'settings' => array('max_length' => 255),
      'instance_settings' => array('text_processing' => 0),
      'default_widget' => 'text_textfield',
      'default_formatter' => 'text_default',
    ),
    'text_long' => array(
      'label' => t('Long text'),
      'description' => t('This field stores long text in the database.'),
      'instance_settings' => array('text_processing' => 0),
      'default_widget' => 'text_textarea',
      'default_formatter' => 'text_default',
    ),
    'text_with_summary' => array(
      'label' => t('Long text and summary'),
      'description' => t('This field stores long text in the database along with optional summary text.'),
      'instance_settings' => array('text_processing' => 1, 'display_summary' => 0),
      'default_widget' => 'text_textarea_with_summary',
      'default_formatter' => 'text_default',
    ),
  );
}

/**
 * Implements hook_field_settings_form().
 */
function text_field_settings_form($field, $instance, $has_data) {
  $settings = $field['settings'];

  $form = array();

  if ($field['type'] == 'text') {
    $form['max_length'] = array(
      '#type' => 'textfield',
      '#title' => t('Maximum length'),
      '#default_value' => $settings['max_length'],
      '#required' => TRUE,
      '#description' => t('The maximum length of the field in characters.'),
      '#element_validate' => array('element_validate_integer_positive'),
      // @todo: If $has_data, add a validate handler that only allows
      // max_length to increase.
      '#disabled' => $has_data,
    );
  }

  return $form;
}

/**
 * Implements hook_field_instance_settings_form().
 */
function text_field_instance_settings_form($field, $instance) {
  $settings = $instance['settings'];

  $form['text_processing'] = array(
    '#type' => 'radios',
    '#title' => t('Text processing'),
    '#default_value' => $settings['text_processing'],
    '#options' => array(
      t('Plain text'),
      t('Filtered text (user selects text format)'),
    ),
  );
  if ($field['type'] == 'text_with_summary') {
    $form['display_summary'] = array(
      '#type' => 'checkbox',
      '#title' => t('Summary input'),
      '#default_value' => $settings['display_summary'],
      '#description' => t('This allows authors to input an explicit summary, to be displayed instead of the automatically trimmed text when using the "Summary or trimmed" display type.'),
    );
  }

  return $form;
}

/**
 * Implements hook_field_validate().
 *
 * Possible error codes:
 * - 'text_value_max_length': The value exceeds the maximum length.
 * - 'text_summary_max_length': The summary exceeds the maximum length.
 */
function text_field_validate($entity_type, $entity, $field, $instance, $langcode, $items, &$errors) {
  foreach ($items as $delta => $item) {
    // @todo Length is counted separately for summary and value, so the maximum
    //   length can be exceeded very easily.
    foreach (array('value', 'summary') as $column) {
      if (!empty($item[$column])) {
        if (!empty($field['settings']['max_length']) && drupal_strlen($item[$column]) > $field['settings']['max_length']) {
          switch ($column) {
            case 'value':
              $message = t('%name: the text may not be longer than %max characters.', array('%name' => $instance['label'], '%max' => $field['settings']['max_length']));
              break;

            case 'summary':
              $message = t('%name: the summary may not be longer than %max characters.', array('%name' => $instance['label'], '%max' => $field['settings']['max_length']));
              break;
          }
          $errors[$field['field_name']][$langcode][$delta][] = array(
            'error' => "text_{$column}_length",
            'message' => $message,
          );
        }
      }
    }
  }
}

/**
 * Implements hook_field_load().
 *
 * Where possible, generate the sanitized version of each field early so that
 * it is cached in the field cache. This avoids looking up from the filter cache
 * separately.
 *
 * @see text_field_formatter_view()
 */
function text_field_load($entity_type, $entities, $field, $instances, $langcode, &$items) {
  foreach ($entities as $id => $entity) {
    foreach ($items[$id] as $delta => $item) {
      // Only process items with a cacheable format, the rest will be handled
      // by formatters if needed.
      if (empty($instances[$id]['settings']['text_processing']) || filter_format_allowcache($item['format'])) {
        $items[$id][$delta]['safe_value'] = isset($item['value']) ? _text_sanitize($instances[$id], $langcode, $item, 'value') : '';
        if ($field['type'] == 'text_with_summary') {
          $items[$id][$delta]['safe_summary'] = isset($item['summary']) ? _text_sanitize($instances[$id], $langcode, $item, 'summary') : '';
        }
      }
    }
  }
}

/**
 * Implements hook_field_is_empty().
 */
function text_field_is_empty($item, $field) {
  if (!isset($item['value']) || $item['value'] === '') {
    return !isset($item['summary']) || $item['summary'] === '';
  }
  return FALSE;
}

/**
 * Implements hook_field_formatter_info().
 */
function text_field_formatter_info() {
  return array(
    'text_default' => array(
      'label' => t('Default'),
      'field types' => array('text', 'text_long', 'text_with_summary'),
    ),
    'text_plain' => array(
      'label' => t('Plain text'),
      'field types' => array('text', 'text_long', 'text_with_summary'),
    ),

    // The text_trimmed formatter displays the trimmed version of the
    // full element of the field. It is intended to be used with text
    // and text_long fields. It also works with text_with_summary
    // fields though the text_summary_or_trimmed formatter makes more
    // sense for that field type.
    'text_trimmed' => array(
      'label' => t('Trimmed'),
      'field types' => array('text', 'text_long', 'text_with_summary'),
      'settings' => array('trim_length' => 600),
    ),

    // The 'summary or trimmed' field formatter for text_with_summary
    // fields displays returns the summary element of the field or, if
    // the summary is empty, the trimmed version of the full element
    // of the field.
    'text_summary_or_trimmed' => array(
      'label' => t('Summary or trimmed'),
      'field types' => array('text_with_summary'),
      'settings' => array('trim_length' => 600),
    ),
  );
}

/**
 * Implements hook_field_formatter_settings_form().
 */
function text_field_formatter_settings_form($field, $instance, $view_mode, $form, &$form_state) {
  $display = $instance['display'][$view_mode];
  $settings = $display['settings'];

  $element = array();

  if (strpos($display['type'], '_trimmed') !== FALSE) {
    $element['trim_length'] = array(
      '#title' => t('Trimmed limit'),
      '#type' => 'textfield',
      '#field_suffix' => t('characters'),
      '#size' => 10,
      '#default_value' => $settings['trim_length'],
      '#element_validate' => array('element_validate_integer_positive'),
      '#description' => t('If the summary is not set, the trimmed %label field will be shorter than this character limit.', array('%label' => $instance['label'])),
      '#required' => TRUE,
    );
  }

  return $element;
}

/**
 * Implements hook_field_formatter_settings_summary().
 */
function text_field_formatter_settings_summary($field, $instance, $view_mode) {
  $display = $instance['display'][$view_mode];
  $settings = $display['settings'];

  $summary = '';

  if (strpos($display['type'], '_trimmed') !== FALSE) {
    $summary = t('Trimmed limit: @trim_length characters', array('@trim_length' => $settings['trim_length']));
  }

  return $summary;
}

/**
 * Implements hook_field_formatter_view().
 */
function text_field_formatter_view($entity_type, $entity, $field, $instance, $langcode, $items, $display) {
  $element = array();

  switch ($display['type']) {
    case 'text_default':
    case 'text_trimmed':
      foreach ($items as $delta => $item) {
        $output = _text_sanitize($instance, $langcode, $item, 'value');
        if ($display['type'] == 'text_trimmed') {
          $output = text_summary($output, $instance['settings']['text_processing'] ? $item['format'] : NULL, $display['settings']['trim_length']);
        }
        $element[$delta] = array('#markup' => $output);
      }
      break;

    case 'text_summary_or_trimmed':
      foreach ($items as $delta => $item) {
        if (!empty($item['summary'])) {
          $output = _text_sanitize($instance, $langcode, $item, 'summary');
        }
        else {
          $output = _text_sanitize($instance, $langcode, $item, 'value');
          $output = text_summary($output, $instance['settings']['text_processing'] ? $item['format'] : NULL, $display['settings']['trim_length']);
        }
        $element[$delta] = array('#markup' => $output);
      }
      break;

    case 'text_plain':
      foreach ($items as $delta => $item) {
        $element[$delta] = array('#markup' => strip_tags($item['value']));
      }
      break;
  }

  return $element;
}

/**
 * Sanitizes the 'value' or 'summary' data of a text value.
 *
 * Depending on whether the field instance uses text processing, data is run
 * through check_plain() or check_markup().
 *
 * @param $instance
 *   The instance definition.
 * @param $langcode
 *  The language associated to $item.
 * @param $item
 *   The field value to sanitize.
 * @param $column
 *   The column to sanitize (either 'value' or 'summary').
 *
 * @return
 *  The sanitized string.
 */
function _text_sanitize($instance, $langcode, $item, $column) {
  // If the value uses a cacheable text format, text_field_load() precomputes
  // the sanitized string.
  if (isset($item["safe_$column"])) {
    return $item["safe_$column"];
  }
  if ($item[$column] === '') {
    return '';
  }
  return $instance['settings']['text_processing'] ? check_markup($item[$column], $item['format'], $langcode) : check_plain($item[$column]);
}

/**
 * Generate a trimmed, formatted version of a text field value.
 *
 * If the end of the summary is not indicated using the <!--break--> delimiter
 * then we generate the summary automatically, trying to end it at a sensible
 * place such as the end of a paragraph, a line break, or the end of a
 * sentence (in that order of preference).
 *
 * @param $text
 *   The content for which a summary will be generated.
 * @param $format
 *   The format of the content.
 *   If the PHP filter is present and $text contains PHP code, we do not
 *   split it up to prevent parse errors.
 *   If the line break filter is present then we treat newlines embedded in
 *   $text as line breaks.
 *   If the htmlcorrector filter is present, it will be run on the generated
 *   summary (if different from the incoming $text).
 * @param $size
 *   The desired character length of the summary. If omitted, the default
 *   value will be used. Ignored if the special delimiter is present
 *   in $text.
 * @return
 *   The generated summary.
 */
function text_summary($text, $format = NULL, $size = NULL) {

  // If the input text is NULL, return unchanged.
  if (is_null($text)) {
    return NULL;
  }

  if (!isset($size)) {
    // What used to be called 'teaser' is now called 'summary', but
    // the variable 'teaser_length' is preserved for backwards compatibility.
    $size = variable_get('teaser_length', 600);
  }

  // Find where the delimiter is in the body
  $delimiter = strpos($text, '<!--break-->');

  // If the size is zero, and there is no delimiter, the entire body is the summary.
  if ($size == 0 && $delimiter === FALSE) {
    return $text;
  }

  // If a valid delimiter has been specified, use it to chop off the summary.
  if ($delimiter !== FALSE) {
    return substr($text, 0, $delimiter);
  }

  // We check for the presence of the PHP evaluator filter in the current
  // format. If the body contains PHP code, we do not split it up to prevent
  // parse errors.
  if (isset($format)) {
    $filters = filter_list_format($format);
    if (isset($filters['php_code']) && $filters['php_code']->status && strpos($text, '<?') !== FALSE) {
      return $text;
    }
  }

  // If we have a short body, the entire body is the summary.
  if (drupal_strlen($text) <= $size) {
    return $text;
  }

  // If the delimiter has not been specified, try to split at paragraph or
  // sentence boundaries.

  // The summary may not be longer than maximum length specified. Initial slice.
  $summary = truncate_utf8($text, $size);

  // Store the actual length of the UTF8 string -- which might not be the same
  // as $size.
  $max_rpos = strlen($summary);

  // How much to cut off the end of the summary so that it doesn't end in the
  // middle of a paragraph, sentence, or word.
  // Initialize it to maximum in order to find the minimum.
  $min_rpos = $max_rpos;

  // Store the reverse of the summary. We use strpos on the reversed needle and
  // haystack for speed and convenience.
  $reversed = strrev($summary);

  // Build an array of arrays of break points grouped by preference.
  $break_points = array();

  // A paragraph near the end of sliced summary is most preferable.
  $break_points[] = array('</p>' => 0);

  // If no complete paragraph then treat line breaks as paragraphs.
  $line_breaks = array('<br />' => 6, '<br>' => 4);
  // Newline only indicates a line break if line break converter
  // filter is present.
  if (isset($filters['filter_autop'])) {
    $line_breaks["\n"] = 1;
  }
  $break_points[] = $line_breaks;

  // If the first paragraph is too long, split at the end of a sentence.
  $break_points[] = array('. ' => 1, '! ' => 1, '? ' => 1, '。' => 0, '؟ ' => 1);

  // Iterate over the groups of break points until a break point is found.
  foreach ($break_points as $points) {
    // Look for each break point, starting at the end of the summary.
    foreach ($points as $point => $offset) {
      // The summary is already reversed, but the break point isn't.
      $rpos = strpos($reversed, strrev($point));
      if ($rpos !== FALSE) {
        $min_rpos = min($rpos + $offset, $min_rpos);
      }
    }

    // If a break point was found in this group, slice and stop searching.
    if ($min_rpos !== $max_rpos) {
      // Don't slice with length 0. Length must be <0 to slice from RHS.
      $summary = ($min_rpos === 0) ? $summary : substr($summary, 0, 0 - $min_rpos);
      break;
    }
  }

  // If the htmlcorrector filter is present, apply it to the generated summary.
  if (isset($filters['filter_htmlcorrector'])) {
    $summary = _filter_htmlcorrector($summary);
  }

  return $summary;
}

/**
 * Implements hook_field_widget_info().
 */
function text_field_widget_info() {
  return array(
    'text_textfield' => array(
      'label' => t('Text field'),
      'field types' => array('text'),
      'settings' => array('size' => 60),
    ),
    'text_textarea' => array(
      'label' => t('Text area (multiple rows)'),
      'field types' => array('text_long'),
      'settings' => array('rows' => 5),
    ),
    'text_textarea_with_summary' => array(
      'label' => t('Text area with a summary'),
      'field types' => array('text_with_summary'),
      'settings' => array('rows' => 20, 'summary_rows' => 5),
    ),
  );
}

/**
 * Implements hook_field_widget_settings_form().
 */
function text_field_widget_settings_form($field, $instance) {
  $widget = $instance['widget'];
  $settings = $widget['settings'];

  if ($widget['type'] == 'text_textfield') {
    $form['size'] = array(
      '#type' => 'textfield',
      '#title' => t('Size of textfield'),
      '#default_value' => $settings['size'],
      '#required' => TRUE,
      '#element_validate' => array('element_validate_integer_positive'),
    );
  }
  else {
    $form['rows'] = array(
      '#type' => 'textfield',
      '#title' => t('Rows'),
      '#default_value' => $settings['rows'],
      '#required' => TRUE,
      '#element_validate' => array('element_validate_integer_positive'),
    );
  }

  return $form;
}

/**
 * Implements hook_field_widget_form().
 */
function text_field_widget_form(&$form, &$form_state, $field, $instance, $langcode, $items, $delta, $element) {
  $summary_widget = array();
  $main_widget = array();

  switch ($instance['widget']['type']) {
    case 'text_textfield':
      $main_widget = $element + array(
        '#type' => 'textfield',
        '#default_value' => isset($items[$delta]['value']) ? $items[$delta]['value'] : NULL,
        '#size' => $instance['widget']['settings']['size'],
        '#maxlength' => $field['settings']['max_length'],
        '#attributes' => array('class' => array('text-full')),
      );
      break;

    case 'text_textarea_with_summary':
      $display = !empty($items[$delta]['summary']) || !empty($instance['settings']['display_summary']);
      $summary_widget = array(
        '#type' => $display ? 'textarea' : 'value',
        '#default_value' => isset($items[$delta]['summary']) ? $items[$delta]['summary'] : NULL,
        '#title' => t('Summary'),
        '#rows' => $instance['widget']['settings']['summary_rows'],
        '#description' => t('Leave blank to use trimmed value of full text as the summary.'),
        '#attached' => array(
          'js' => array(drupal_get_path('module', 'text') . '/text.js'),
        ),
        '#attributes' => array('class' => array('text-summary')),
        '#prefix' => '<div class="text-summary-wrapper">',
        '#suffix' => '</div>',
        '#weight' => -10,
      );
      // Fall through to the next case.

    case 'text_textarea':
      $main_widget = $element + array(
        '#type' => 'textarea',
        '#default_value' => isset($items[$delta]['value']) ? $items[$delta]['value'] : NULL,
        '#rows' => $instance['widget']['settings']['rows'],
        '#attributes' => array('class' => array('text-full')),
      );
      break;
  }

  if ($main_widget) {
    // Conditionally alter the form element's type if text processing is enabled.
    if ($instance['settings']['text_processing']) {
      $element = $main_widget;
      $element['#type'] = 'text_format';
      $element['#format'] = isset($items[$delta]['format']) ? $items[$delta]['format'] : NULL;
      $element['#base_type'] = $main_widget['#type'];
    }
    else {
      $element['value'] = $main_widget;
    }
  }
  if ($summary_widget) {
    $element['summary'] = $summary_widget;
  }

  return $element;
}

/**
 * Implements hook_field_widget_error().
 */
function text_field_widget_error($element, $error, $form, &$form_state) {
  switch ($error['error']) {
    case 'text_summary_max_length':
      $error_element = $element[$element['#columns'][1]];
      break;

    default:
      $error_element = $element[$element['#columns'][0]];
      break;
  }

  form_error($error_element, $error['message']);
}

/**
 * Implements hook_field_prepare_translation().
 */
function text_field_prepare_translation($entity_type, $entity, $field, $instance, $langcode, &$items, $source_entity, $source_langcode) {
  // If the translating user is not permitted to use the assigned text format,
  // we must not expose the source values.
  $field_name = $field['field_name'];
  if (!empty($source_entity->{$field_name}[$source_langcode])) {
    $formats = filter_formats();
    foreach ($source_entity->{$field_name}[$source_langcode] as $delta => $item) {
      $format_id = $item['format'];
      if (!empty($format_id) && !filter_access($formats[$format_id])) {
        unset($items[$delta]);
      }
    }
  }
}

/**
 * Implements hook_filter_format_update().
 */
function text_filter_format_update($format) {
  field_cache_clear();
}

/**
 * Implements hook_filter_format_disable().
 */
function text_filter_format_disable($format) {
  field_cache_clear();
}
