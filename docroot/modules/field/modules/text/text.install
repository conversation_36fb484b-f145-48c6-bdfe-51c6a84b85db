<?php

/**
 * @file
 * Install, update and uninstall functions for the text module.
 */

/**
 * Implements hook_field_schema().
 */
function text_field_schema($field) {
  switch ($field['type']) {
    case 'text':
      $columns = array(
        'value' => array(
          'type' => 'varchar',
          'length' => $field['settings']['max_length'],
          'not null' => FALSE,
        ),
      );
      break;

    case 'text_long':
      $columns = array(
        'value' => array(
          'type' => 'text',
          'size' => 'big',
          'not null' => FALSE,
        ),
      );
      break;

    case 'text_with_summary':
      $columns = array(
        'value' => array(
          'type' => 'text',
          'size' => 'big',
          'not null' => FALSE,
        ),
        'summary' => array(
          'type' => 'text',
          'size' => 'big',
          'not null' => FALSE,
        ),
      );
      break;
  }
  $columns += array(
    'format' => array(
      'type' => 'varchar',
      'length' => 255,
      'not null' => FALSE,
    ),
  );
  return array(
    'columns' => $columns,
    'indexes' => array(
      'format' => array('format'),
    ),
    'foreign keys' => array(
      'format' => array(
        'table' => 'filter_format',
        'columns' => array('format' => 'format'),
      ),
    ),
  );
}

/**
 * Change text field 'format' columns into varchar.
 */
function text_update_7000() {
  $spec = array(
    'type' => 'varchar',
    'length' => 255,
    'not null' => FALSE,
  );
  $fields = _update_7000_field_read_fields(array(
    'module' => 'text',
    'storage_type' => 'field_sql_storage',
  ));
  foreach ($fields as $field) {
    if ($field['deleted']) {
      $table = "field_deleted_data_{$field['id']}";
      $revision_table = "field_deleted_revision_{$field['id']}";
    }
    else {
      $table = "field_data_{$field['field_name']}";
      $revision_table = "field_revision_{$field['field_name']}";
    }
    $column = $field['field_name'] . '_' . 'format';
    db_change_field($table, $column, $column, $spec);
    db_change_field($revision_table, $column, $column, $spec);
  }
}
