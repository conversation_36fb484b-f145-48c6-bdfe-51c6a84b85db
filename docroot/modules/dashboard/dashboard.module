<?php
/**
 * @file
 * Provides a dashboard page in the administrative interface.
 */

/**
 * Implements hook_help().
 */
function dashboard_help($path, $arg) {
  switch ($path) {
    case 'admin/help#dashboard':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Dashboard module provides a <a href="@dashboard">Dashboard page</a> in the administrative interface for organizing administrative tasks and navigation, and tracking information within your site. The Dashboard page contains blocks, which you can add to and arrange using the drag-and-drop interface that appears when you click on the <em>Customize dashboard</em> link. Within this interface, blocks that are not primarily used for site administration do not appear by default, but can be added via the <em>Add other blocks</em> link. For more information, see the online handbook entry for <a href="@handbook">Dashboard module</a>.', array('@handbook' => 'http://drupal.org/documentation/modules/dashboard', '@dashboard' => url('admin/dashboard'))) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Tracking user activity') . '</dt>';
      $output .= '<dd>' . t("By enabling blocks such as <em>Who's online</em> and <em>Who's new</em>, site users can track who is logged in and new user signups at a centralized location.") . '</dd>';
      $output .= '<dt>' . t('Tracking content activity') . '</dt>';
      $output .= '<dd>' . t('By enabling blocks such as <em>Recent blog posts</em>, <em>New forum topics</em> and <em>Recent comments</em>, site users can view newly added site content at a glance.') . '</dd>';
      $output .= '</dl>';
      return $output;

    case 'admin/dashboard/configure':
      // @todo This assumes the current page is being displayed using the same
      //   theme that the dashboard is displayed in.
      $output = '<p>' . t('Rearrange blocks for display on the <a href="@dashboard-url">Dashboard page</a>. Blocks placed in the <em>Dashboard (inactive)</em> region are not displayed when viewing the Dashboard page, but are available within its <em>Customize dashboard</em> interface. Removing a block from active dashboard display makes it available on the main <a href="@blocks-url">blocks administration page</a>.', array('@dashboard-url' => url('admin/dashboard'), '@blocks-url' => url("admin/structure/block/list/{$GLOBALS['theme_key']}"))) . '</p>';
      return $output;
  }
}

/**
 * Implements hook_menu().
 */
function dashboard_menu() {
  $items['admin/dashboard'] = array(
    'title' => 'Dashboard',
    'description' => 'View and customize your dashboard.',
    'page callback' => 'dashboard_admin',
    'access arguments' => array('access dashboard'),
    // Make this appear first, so for example, in admin menus, it shows up on
    // the top corner of the window as a convenient "home link".
    'weight' => -15,
  );
  $items['admin/dashboard/configure'] = array(
    'title' => 'Configure available dashboard blocks',
    'description' => 'Configure which blocks can be shown on the dashboard.',
    'page callback' => 'dashboard_admin_blocks',
    'access arguments' => array('administer blocks'),
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
  );
  $items['admin/dashboard/customize'] = array(
    'title' => 'Customize dashboard',
    'description' => 'Customize your dashboard.',
    'page callback' => 'dashboard_admin',
    'page arguments' => array(TRUE),
    'access arguments' => array('access dashboard'),
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
  );
  $items['admin/dashboard/drawer'] = array(
    'page callback' => 'dashboard_show_disabled',
    'access arguments' => array('administer blocks'),
    'type' => MENU_CALLBACK,
  );
  $items['admin/dashboard/block-content/%/%'] = array(
    'page callback' => 'dashboard_show_block_content',
    'page arguments' => array(3, 4),
    'access arguments' => array('administer blocks'),
    'type' => MENU_CALLBACK,
  );
  $items['admin/dashboard/update'] = array(
    'page callback' => 'dashboard_update',
    'access arguments' => array('administer blocks'),
    'type' => MENU_CALLBACK,
  );

  return $items;
}

/**
 * Implements hook_permission().
 */
function dashboard_permission() {
  return array(
    'access dashboard' => array(
      'title' => t('View the administrative dashboard'),
      // Note: We translate the 'Administer blocks' permission string here with
      // a separate t() call, to make sure it gets the same translation as when
      // it's in block_permission().
      'description' => t('Customizing the dashboard requires the !permission-name permission.', array(
        '!permission-name' => l(t('Administer blocks'), 'admin/people/permissions', array('fragment' => 'module-block')),
      )),
    ),
  );
}

/**
 * Implements hook_block_info_alter().
 */
function dashboard_block_info_alter(&$blocks, $theme, $code_blocks) {
  $admin_theme = variable_get('admin_theme');
  if (($admin_theme && $theme == $admin_theme) || (!$admin_theme && $theme == variable_get('theme_default', 'bartik'))) {
    foreach ($blocks as $module => &$module_blocks) {
      foreach ($module_blocks as $delta => &$block) {
        // Make administrative blocks that are not already in use elsewhere
        // available for the dashboard.
        if (empty($block['status']) && (empty($block['region']) || $block['region'] == BLOCK_REGION_NONE) && !empty($code_blocks[$module][$delta]['properties']['administrative'])) {
          $block['status'] = 1;
          $block['region'] = 'dashboard_inactive';
        }
      }
    }
  }
}

/**
 * Implements hook_block_list_alter().
 *
 * Skip rendering dashboard blocks when not on the dashboard page itself. This
 * prevents expensive dashboard blocks from causing performance issues on pages
 * where they will never be displayed.
 */
function dashboard_block_list_alter(&$blocks) {
  if (!dashboard_is_visible()) {
    foreach ($blocks as $key => $block) {
      if (in_array($block->region, dashboard_regions())) {
        unset($blocks[$key]);
      }
    }
  }
}

/**
 * Implements hook_page_build().
 *
 * Display dashboard blocks in the main content region.
 */
function dashboard_page_build(&$page) {
  global $theme_key;

  if (dashboard_is_visible()) {
    $block_info = array();

    // Create a wrapper for the dashboard itself, then insert each dashboard
    // region into it.
    $page['content']['dashboard'] = array('#theme_wrappers' => array('dashboard'));
    foreach (dashboard_regions() as $region) {
      // Do not show dashboard blocks that are disabled.
      if ($region == 'dashboard_inactive') {
        continue;
      }
      // Insert regions even when they are empty, so that they will be
      // displayed when the dashboard is being configured.
      $page['content']['dashboard'][$region] = !empty($page[$region]) ? $page[$region] : array();
      $page['content']['dashboard'][$region]['#dashboard_region'] = $region;
      // Allow each dashboard region to be themed differently, or fall back on
      // the generic theme wrapper function for dashboard regions.
      $page['content']['dashboard'][$region]['#theme_wrappers'][] = array($region, 'dashboard_region');
      unset($page[$region]);
      $blocks_found = array();
      foreach ($page['content']['dashboard'][$region] as $item) {
        if (isset($item['#theme_wrappers']) && is_array($item['#theme_wrappers']) && in_array('block', $item['#theme_wrappers'])) {
          // If this item is a block, ensure it has a subject.
          if (empty($item['#block']->subject)) {
            // Locally cache info data for the object for all blocks, in case
            // we find a block similarly missing title from the same module.
            if (!isset($block_info[$item['#block']->module])) {
              $block_info[$item['#block']->module] = module_invoke($item['#block']->module, 'block_info');
            }
            $item['#block']->subject = $block_info[$item['#block']->module][$item['#block']->delta]['info'];
          }
          $blocks_found[$item['#block']->module . '_' . $item['#block']->delta] = TRUE;
        }
      }

      // Find blocks which were not yet displayed on the page (were empty), and
      // add placeholder items in their place for rendering.
      $block_list = db_select('block')
        ->condition('theme', $theme_key)
        ->condition('status', 1)
        ->condition('region', $region)
        ->fields('block')
        ->execute();
      foreach ($block_list as $block) {
        if (!isset($blocks_found[$block->module . '_' . $block->delta])) {
          $block->enabled = $block->page_match = TRUE;
          $block->content = array('#markup' => '<div class="dashboard-block-empty">(empty)</div>');
          if (!isset($block_info[$block->module])) {
            $block_info[$block->module] = module_invoke($block->module, 'block_info');
          }
          $block->subject = t('@title', array('@title' => $block_info[$block->module][$block->delta]['info']));
          $block_render = array($block->module . '_' . $block->delta => $block);
          $build = _block_get_renderable_array($block_render);
          $page['content']['dashboard'][$block->region][] = $build;
        }
      }
    }
  }
}

/**
 * Implements hook_system_info_alter().
 *
 * Add regions to each theme to store the dashboard blocks.
 */
function dashboard_system_info_alter(&$info, $file, $type) {
  if ($type == 'theme') {
    // Add the dashboard regions (the "inactive" region should always appear
    // last in the list, for usability reasons).
    $dashboard_regions = dashboard_region_descriptions();
    if (isset($dashboard_regions['dashboard_inactive'])) {
      $inactive_region = $dashboard_regions['dashboard_inactive'];
      unset($dashboard_regions['dashboard_inactive']);
      $dashboard_regions['dashboard_inactive'] = $inactive_region;
    }
    $info['regions'] += $dashboard_regions;
    // Indicate that these regions are intended to be displayed whenever the
    // dashboard is displayed in an overlay. This information is provided for
    // any module that might need to use it, not just the core Overlay module.
    $info['overlay_regions'] = !empty($info['overlay_regions']) ? array_merge($info['overlay_regions'], dashboard_regions()) : dashboard_regions();
  }
}

/**
 * Implements hook_theme().
 */
function dashboard_theme() {
  return array(
    'dashboard' => array(
      'render element' => 'element',
    ),
    'dashboard_admin' => array(
      'render element' => 'element',
    ),
    'dashboard_region' => array(
      'render element' => 'element',
    ),
    'dashboard_disabled_blocks' => array(
      'variables' => array('blocks' => NULL),
    ),
    'dashboard_disabled_block' => array(
      'variables' => array('block' => NULL),
    ),
    'dashboard_admin_display_form' => array(
      // When building the form for configuring dashboard blocks, reuse the
      // Block module's template for the main block configuration form.
      'template' => 'block-admin-display-form',
      'path' => drupal_get_path('module', 'block'),
      'file' => 'block.admin.inc',
      'render element' => 'form',
    ),
  );
}

/**
 * Implements hook_forms().
 */
function dashboard_forms() {
  // Reroute the dashboard configuration form to the main blocks administration
  // form. This allows us to distinguish them by form ID in hook_form_alter().
  $forms['dashboard_admin_display_form'] = array(
    'callback' => 'block_admin_display_form',
  );

  return $forms;
}

/**
 * Page callback: Displays the dashboard.
 *
 * @param $launch_customize
 *   Whether to launch in customization mode right away. TRUE or FALSE.
 */
function dashboard_admin($launch_customize = FALSE) {
  $js_settings = array(
    'dashboard' => array(
      'drawer' => url('admin/dashboard/drawer'),
      'blockContent' => url('admin/dashboard/block-content'),
      'updatePath' => url('admin/dashboard/update'),
      'formToken' => drupal_get_token('dashboard-update'),
      'launchCustomize' => $launch_customize,
      'dashboard' => url('admin/dashboard'),
      'emptyBlockText' => t('(empty)'),
      'emptyRegionTextInactive' => t('This dashboard region is empty. Click <em>Customize dashboard</em> to add blocks to it.'),
      'emptyRegionTextActive' => t('DRAG HERE'),
    ),
  );
  $build = array(
    '#theme' => 'dashboard_admin',
    '#message' => t('To customize the dashboard page, move blocks to the dashboard regions on the <a href="@dashboard">Dashboard administration page</a>, or enable JavaScript on this page to use the drag-and-drop interface.', array('@dashboard' => url('admin/dashboard/configure'))),
    '#access' => user_access('administer blocks'),
    '#attached' => array(
      'js' => array(
        drupal_get_path('module', 'dashboard') . '/dashboard.js',
        array('data' => $js_settings, 'type' => 'setting'),
      ),
      'library' => array(array('system', 'ui.sortable')),
    ),
  );
  return $build;
}

/**
 * Page callback: Builds the page for administering dashboard blocks.
 *
 * This page reuses the Block module's administration form but limits editing
 * to blocks that are available to appear on the dashboard.
 *
 * @see block_admin_display()
 * @see block_admin_display_form()
 * @see dashboard_form_dashboard_admin_display_form_alter()
 * @see template_preprocess_dashboard_admin_display_form()
 */
function dashboard_admin_blocks() {
  global $theme_key;
  drupal_theme_initialize();
  module_load_include('inc', 'block', 'block.admin');

  // Prepare the blocks for the current theme, and remove those that are
  // currently displayed in non-dashboard regions.
  // @todo This assumes the current page is being displayed using the same
  //   theme that the dashboard is displayed in.
  $blocks = block_admin_display_prepare_blocks($theme_key);
  $dashboard_regions = dashboard_region_descriptions();
  $regions_to_remove = array_diff_key(system_region_list($theme_key, REGIONS_VISIBLE), $dashboard_regions);
  foreach ($blocks as $id => $block) {
    if (isset($regions_to_remove[$block['region']])) {
      unset($blocks[$id]);
    }
  }

  // Pass in the above blocks and dashboard regions to the form, so that only
  // dashboard-related regions will be displayed.
  return drupal_get_form('dashboard_admin_display_form', $blocks, $theme_key, $dashboard_regions);
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function dashboard_form_block_admin_display_form_alter(&$form, &$form_state, $form_id) {
  // Hide dashboard regions (and any blocks placed within them) from the block
  // administration form and from the options list on that form. This
  // function is called for both the dashboard block configuration form and the
  // standard block configuration form so that both forms can share the same
  // constructor. As a result the form_id must be checked.
  if ($form_id != 'dashboard_admin_display_form') {
    $dashboard_regions = dashboard_region_descriptions();
    $form['block_regions']['#value'] = array_diff_key($form['block_regions']['#value'], $dashboard_regions);
    foreach (element_children($form['blocks']) as $i) {
      $block = &$form['blocks'][$i];
      if (isset($block['region']['#default_value']) && isset($dashboard_regions[$block['region']['#default_value']]) && $block['region']['#default_value'] != 'dashboard_inactive') {
        $block['#access'] = FALSE;
      }
      elseif (isset($block['region']['#options'])) {
        $block['region']['#options'] = array_diff_key($block['region']['#options'], $dashboard_regions);
      }
      // Show inactive dashboard blocks as disabled on the main block
      // administration form, so that they are available to place in other
      // regions of the theme. Note that when the form is submitted, any such
      // blocks which still remain disabled will immediately be put back in the
      // 'dashboard_inactive' region, because dashboard_block_info_alter() is
      // called when the blocks are rehashed. Fortunately, this is the exact
      // behavior we want.
      if ($block['region']['#default_value'] == 'dashboard_inactive') {
        // @todo These do not wind up in correct alphabetical order.
        $block['region']['#default_value'] = NULL;
      }
    }
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function dashboard_form_dashboard_admin_display_form_alter(&$form, &$form_state) {
  // Redirect the 'configure' and 'delete' links on each block back to the
  // dashboard blocks administration page.
  foreach ($form['blocks'] as &$block) {
    if (isset($block['configure']['#href'])) {
      $block['configure']['#options']['query']['destination'] = 'admin/dashboard/configure';
    }
    if (isset($block['delete']['#href'])) {
      $block['delete']['#options']['query']['destination'] = 'admin/dashboard/configure';
    }
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function dashboard_form_block_admin_configure_alter(&$form, &$form_state) {
  global $theme_key;
  drupal_theme_initialize();
  // Hide the dashboard regions from the region select list on the block
  // configuration form, for all themes except the current theme (since the
  // other themes do not display the dashboard).
  // @todo This assumes the current page is being displayed using the same
  //   theme that the dashboard is displayed in.
  $dashboard_regions = dashboard_region_descriptions();
  foreach (element_children($form['regions']) as $region_name) {
    $region = &$form['regions'][$region_name];
    if ($region_name != $theme_key && isset($region['#options'])) {
      $region['#options'] = array_diff_key($region['#options'], $dashboard_regions);
    }
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function dashboard_form_block_add_block_form_alter(&$form, &$form_state) {
  dashboard_form_block_admin_configure_alter($form, $form_state);
}

/**
 * Preprocesses variables for block-admin-display-form.tpl.php.
 */
function template_preprocess_dashboard_admin_display_form(&$variables) {
  template_preprocess_block_admin_display_form($variables);
  if (isset($variables['block_regions'][BLOCK_REGION_NONE])) {
    $variables['block_regions'][BLOCK_REGION_NONE] = t('Other blocks');
  }
}

/**
 * Determines if the dashboard should be displayed on the current page.
 *
 * This function checks if the user is currently viewing the dashboard and has
 * access to see it. It is used by other functions in the dashboard module to
 * decide whether or not the dashboard content should be displayed to the
 * current user.
 *
 * Although the menu system normally handles the above tasks, it only does so
 * for the main page content. However, the dashboard is not part of the main
 * page content, but rather is displayed in special regions of the page (so it
 * can interface with the Block module's method of managing page regions). We
 * therefore need to maintain this separate function to check the menu item for
 * us.
 *
 * @return
 *   TRUE if the dashboard should be visible on the current page, FALSE
 *   otherwise.
 *
 * @see dashboard_block_list_alter()
 * @see dashboard_page_build()
 */
function dashboard_is_visible() {
  static $is_visible;
  if (!isset($is_visible)) {
    // If the current menu item represents the page on which we want to display
    // the dashboard, and if the current user has access to see it, return
    // TRUE.
    $menu_item = menu_get_item();
    $is_visible = isset($menu_item['page_callback']) && $menu_item['page_callback'] == 'dashboard_admin' && !empty($menu_item['access']);
  }
  return $is_visible;
}

/**
 * Returns an array of dashboard region descriptions, keyed by region name.
 */
function dashboard_region_descriptions() {
  $regions = module_invoke_all('dashboard_regions');
  drupal_alter('dashboard_regions', $regions);
  return $regions;
}

/**
 * Returns an array of dashboard region names.
 */
function dashboard_regions() {
  $regions = &drupal_static(__FUNCTION__);
  if (!isset($regions)) {
    $regions = array_keys(dashboard_region_descriptions());
  }
  return $regions;
}

/**
 * Implements hook_dashboard_regions().
 */
function dashboard_dashboard_regions() {
  return array(
    'dashboard_main' => 'Dashboard (main)',
    'dashboard_sidebar' => 'Dashboard (sidebar)',
    'dashboard_inactive' => 'Dashboard (inactive)',
  );
}

/**
 * Ajax callback: Shows disabled blocks in the dashboard customization mode.
 */
function dashboard_show_disabled() {
  global $theme_key;

  // Blocks are not necessarily initialized at this point.
  $blocks = _block_rehash();

  // Limit the list to blocks that are marked as disabled for the dashboard.
  foreach ($blocks as $key => $block) {
    if ($block['theme'] != $theme_key || $block['region'] != 'dashboard_inactive') {
      unset($blocks[$key]);
    }
  }

  // Theme the output and end the page request.
  print theme('dashboard_disabled_blocks', array('blocks' => $blocks));
  drupal_exit();
}

/**
 * Ajax callback: Displays the rendered contents of a specific block.
 *
 * @param $module
 *   The block's module name.
 * @param $delta
 *   The block's delta.
 */
function dashboard_show_block_content($module, $delta) {
  drupal_theme_initialize();
  global $theme_key;

  $blocks = array();
  $block_object = db_query("SELECT * FROM {block} WHERE theme = :theme AND module = :module AND delta = :delta", array(
    ":theme" => $theme_key,
    ":module" => $module,
    ":delta" => $delta,
    ))
    ->fetchObject();
  $block_object->enabled = $block_object->page_match = TRUE;
  $blocks[$module . "_" . $delta] = $block_object;
  $block_content = _block_render_blocks($blocks);
  $build = _block_get_renderable_array($block_content);
  $rendered_block = drupal_render($build);
  print $rendered_block;
  drupal_exit();
}

/**
 * Sets the new weight of each region according to the drag-and-drop order.
 */
function dashboard_update() {
  drupal_theme_initialize();
  global $theme_key;
  // Check the form token to make sure we have a valid request.
  if (!empty($_REQUEST['form_token']) && drupal_valid_token($_REQUEST['form_token'], 'dashboard-update')) {
    parse_str($_REQUEST['regions'], $regions);
    foreach ($regions as $region_name => $blocks) {
      if ($region_name == 'disabled_blocks') {
        $region_name = 'dashboard_inactive';
      }
      foreach ($blocks as $weight => $block_string) {
        // Parse the query string to determine the block's module and delta.
        preg_match('/block-([^-]+)-(.+)/', $block_string, $matches);
        $block = new stdClass();
        $block->module = $matches[1];
        $block->delta = $matches[2];

        $block->region = $region_name;
        $block->weight = $weight;
        $block->status = 1;

        db_merge('block')
          ->key(array(
            'module' => $block->module,
            'delta' => $block->delta,
            'theme' => $theme_key,
          ))
          ->fields(array(
            'status' => $block->status,
            'weight' => $block->weight,
            'region' => $block->region,
            'pages' => '',
          ))
          ->execute();
      }
    }
    drupal_set_message(t('The configuration options have been saved.'), 'status', FALSE);
  }
  drupal_exit();
}

/**
 * Returns HTML for the entire dashboard.
 *
 * @param $variables
 *   An associative array containing:
 *   - element: A render element containing the properties of the dashboard
 *     region element, #dashboard_region and #children.
 *
 * @ingroup themeable
 */
function theme_dashboard($variables) {
  extract($variables);
  drupal_add_css(drupal_get_path('module', 'dashboard') . '/dashboard.css');
  return '<div id="dashboard" class="clearfix">' . $element['#children'] . '</div>';
}

/**
 * Returns HTML for the non-customizable part of the dashboard page.
 *
 * @param $variables
 *   An associative array containing:
 *   - element: A render element containing a #message.
 *
 * @ingroup themeable
 */
function theme_dashboard_admin($variables) {
  // We only return a simple help message, since the actual content of the page
  // will be populated via the dashboard regions in dashboard_page_build().
  return '<div class="customize-dashboard js-hide">' . $variables['element']['#message'] . '</div>';
}

/**
 * Returns HTML for a generic dashboard region.
 *
 * @param $variables
 *   An associative array containing:
 *   - element: A render element containing the properties of the dashboard
 *     region element, #dashboard_region and #children.
 *
 * @ingroup themeable
 */
function theme_dashboard_region($variables) {
  extract($variables);
  $output = '<div id="' . $element['#dashboard_region'] . '" class="dashboard-region">';
  $output .= '<div class="region clearfix">';
  $output .= $element['#children'];
  // Closing div.region
  $output .= '</div>';
  // Closing div.dashboard-region
  $output .= '</div>';
  return $output;
}

/**
 * Returns HTML for disabled blocks, for use in dashboard customization mode.
 *
 * @param $variables
 *   An associative array containing:
 *   - blocks: An array of block objects from _block_rehash().
 *
 * @ingroup themeable
 */
function theme_dashboard_disabled_blocks($variables) {
  extract($variables);
  $output = '<div class="canvas-content"><p>' . t('Drag and drop these blocks to the columns below. Changes are automatically saved. More options are available on the <a href="@dashboard-url">configuration page</a>.', array('@dashboard-url' => url('admin/dashboard/configure'))) . '</p>';
  $output .= '<div id="disabled-blocks"><div class="region disabled-blocks clearfix">';
  foreach ($blocks as $block) {
    $output .= theme('dashboard_disabled_block', array('block' => $block));
  }
  $output .= '<div class="clearfix"></div>';
  $output .= '<p class="dashboard-add-other-blocks">' . l(t('Add other blocks'), 'admin/dashboard/configure') . '</p>';
  $output .= '</div></div></div>';
  return $output;
}

/**
 * Returns HTML for disabled blocks, for use in dashboard customization mode.
 *
 * @param $variables
 *   An associative array containing:
 *   - block: A block object from _block_rehash().
 *
 * @ingroup themeable
 */
function theme_dashboard_disabled_block($variables) {
  extract($variables);
  $output = "";
  if (isset($block)) {
    $output .= '<div id="block-' . $block['module'] . '-' . $block['delta']
    . '" class="disabled-block block block-' . $block['module'] . '-' . $block['delta']
    . ' module-' . $block['module'] . ' delta-' . $block['delta'] . '">'
    . '<h2>' . (!empty($block['title']) && $block['title'] != '<none>' ? check_plain($block['title']) : check_plain($block['info'])) . '</h2>'
    . '<div class="content"></div>'
    . '</div>';
  }
  return $output;
}

