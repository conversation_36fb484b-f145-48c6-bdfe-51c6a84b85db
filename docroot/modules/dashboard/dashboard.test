<?php

/**
 * @file
 * Tests for dashboard.module.
 */

/**
 * Tests the Dashboard module blocks.
 */
class DashboardBlocksTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Dashboard blocks',
      'description' => 'Test blocks as used by the dashboard.',
      'group' => 'Dashboard',
    );
  }

  function setUp() {
    parent::setUp();

    // Create and log in an administrative user having access to the dashboard.
    $admin_user = $this->drupalCreateUser(array('access dashboard', 'administer blocks', 'access administration pages', 'administer modules'));
    $this->drupalLogin($admin_user);

    // Make sure that the dashboard is using the same theme as the rest of the
    // site (and in particular, the same theme used on 403 pages). This forces
    // the dashboard blocks to be the same for an administrator as for a
    // regular user, and therefore lets us test that the dashboard blocks
    // themselves are specifically removed for a user who does not have access
    // to the dashboard page.
    theme_enable(array('stark'));
    variable_set('theme_default', 'stark');
    variable_set('admin_theme', 'stark');
  }

  /**
   * Tests adding a block to the dashboard and checking access to it.
   */
  function testDashboardAccess() {
    // Add a new custom block to a dashboard region.
    $custom_block = array();
    $custom_block['info'] = $this->randomName(8);
    $custom_block['title'] = $this->randomName(8);
    $custom_block['body[value]'] = $this->randomName(32);
    $custom_block['regions[stark]'] = 'dashboard_main';
    $this->drupalPost('admin/structure/block/add', $custom_block, t('Save block'));

    // Ensure admin access.
    $this->drupalGet('admin/dashboard');
    $this->assertResponse(200, 'Admin has access to the dashboard.');
    $this->assertRaw($custom_block['title'], 'Admin has access to a dashboard block.');

    // Ensure non-admin access is denied.
    $normal_user = $this->drupalCreateUser();
    $this->drupalLogin($normal_user);
    $this->drupalGet('admin/dashboard');
    $this->assertResponse(403, 'Non-admin has no access to the dashboard.');
    $this->assertNoText($custom_block['title'], 'Non-admin has no access to a dashboard block.');
  }

  /**
   * Tests that dashboard regions are displayed or hidden properly.
   */
  function testDashboardRegions() {
    $dashboard_regions = dashboard_region_descriptions();

    // Ensure blocks can be placed in dashboard regions.
    $this->drupalGet('admin/dashboard/configure');
    foreach ($dashboard_regions as $region => $description) {
      $elements = $this->xpath('//option[@value=:region]', array(':region' => $region));
      $this->assertTrue(!empty($elements), format_string('%region is an available choice on the dashboard block configuration page.', array('%region' => $region)));
    }

    // Ensure blocks cannot be placed in dashboard regions on the standard
    // blocks configuration page.
    $this->drupalGet('admin/structure/block');
    foreach ($dashboard_regions as $region => $description) {
      $elements = $this->xpath('//option[@value=:region]', array(':region' => $region));
      $this->assertTrue(empty($elements), format_string('%region is not an available choice on the block configuration page.', array('%region' => $region)));
    }
  }

  /**
   * Tests that the dashboard module can be re-enabled, retaining its blocks.
   */
  function testDisableEnable() {
    // Add a new custom block to a dashboard region.
    $custom_block = array();
    $custom_block['info'] = $this->randomName(8);
    $custom_block['title'] = $this->randomName(8);
    $custom_block['body[value]'] = $this->randomName(32);
    $custom_block['regions[stark]'] = 'dashboard_main';
    $this->drupalPost('admin/structure/block/add', $custom_block, t('Save block'));
    $this->drupalGet('admin/dashboard');
    $this->assertRaw($custom_block['title'], 'Block appears on the dashboard.');

    $edit = array();
    $edit['modules[Core][dashboard][enable]'] = FALSE;
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    $this->assertNoRaw('assigned to the invalid region', 'Dashboard blocks gracefully disabled.');
    module_list(TRUE);
    $this->assertFalse(module_exists('dashboard'), 'Dashboard disabled.');

    $edit['modules[Core][dashboard][enable]'] = 'dashboard';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    module_list(TRUE);
    $this->assertTrue(module_exists('dashboard'), 'Dashboard enabled.');

    $this->drupalGet('admin/dashboard');
    $this->assertRaw($custom_block['title'], 'Block still appears on the dashboard.');
  }

  /**
   * Tests that administrative blocks are available for the dashboard.
   */
  function testBlockAvailability() {
    // Test "Recent comments", which should be available (defined as
    // "administrative") but not enabled.
    $this->drupalGet('admin/dashboard');
    $this->assertNoText(t('Recent comments'), '"Recent comments" not on dashboard.');
    $this->drupalGet('admin/dashboard/drawer');
    $this->assertText(t('Recent comments'), 'Drawer of disabled blocks includes a block defined as "administrative".');
    $this->assertNoText(t('Syndicate'), 'Drawer of disabled blocks excludes a block not defined as "administrative".');
    $this->drupalGet('admin/dashboard/configure');
    $elements = $this->xpath('//select[@id=:id]//option[@selected="selected"]', array(':id' => 'edit-blocks-comment-recent-region'));
    $this->assertTrue($elements[0]['value'] == 'dashboard_inactive', 'A block defined as "administrative" defaults to dashboard_inactive.');

    // Now enable the block on the dashboard.
    $values = array();
    $values['blocks[comment_recent][region]'] = 'dashboard_main';
    $this->drupalPost('admin/dashboard/configure', $values, t('Save blocks'));
    $this->drupalGet('admin/dashboard');
    $this->assertText(t('Recent comments'), '"Recent comments" was placed on dashboard.');
    $this->drupalGet('admin/dashboard/drawer');
    $this->assertNoText(t('Recent comments'), 'Drawer of disabled blocks excludes enabled blocks.');
  }
}
