<?php

/**
 * @file
 * Provides File module pages for testing purposes.
 */

/**
 * Implements hook_menu().
 */
function file_module_test_menu() {
  $items = array();

  $items['file/test'] = array(
    'title' => 'Managed file test',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('file_module_test_form'),
    'access arguments' => array('access content'),
  );

  return $items;
}

/**
 * Form constructor for testing a 'managed_file' element.
 *
 * @see file_module_test_form_submit()
 * @ingroup forms
 */
function file_module_test_form($form, &$form_state, $tree = TRUE, $extended = FALSE, $default_fid = NULL) {
  $form['#tree'] = (bool) $tree;

  $form['nested']['file'] = array(
    '#type' => 'managed_file',
    '#title' => t('Managed file'),
    '#upload_location' => 'public://test',
    '#progress_message' => t('Please wait...'),
    '#extended' => (bool) $extended,
    '#size' => 13,
  );
  if ($default_fid) {
    $form['nested']['file']['#default_value'] = $extended ? array('fid' => $default_fid) : $default_fid;
  }

  $form['textfield'] = array(
    '#type' => 'textfield',
    '#title' => t('Type a value and ensure it stays'),
  );

  $form['submit'] = array(
    '#type' => 'submit',
    '#value' => t('Save'),
  );

  return $form;
}

/**
 * Form submission handler for file_module_test_form().
 */
function file_module_test_form_submit($form, &$form_state) {
  if ($form['#tree']) {
    $fid = $form['nested']['file']['#extended'] ? $form_state['values']['nested']['file']['fid'] : $form_state['values']['nested']['file'];
  }
  else {
    $fid = $form['nested']['file']['#extended'] ? $form_state['values']['file']['fid'] : $form_state['values']['file'];
  }
  drupal_set_message(t('The file id is %fid.', array('%fid' => $fid)));
}

/**
 * Implements hook_file_download().
 */
function file_module_test_file_download($uri) {
  if (variable_get('file_module_test_grant_download_access')) {
    // Mimic what file_get_content_headers() would do if we had a full $file
    // object to pass to it.
    return array(
      'Content-Type' => mime_header_encode(file_get_mimetype($uri)),
      'Content-Length' => filesize($uri),
      'Cache-Control' => 'private',
    );
  }
}
