<?php

/**
 * @file
 * Tests for profile.module.
 */

/**
 * A class for common methods for testing profile fields.
 */
class ProfileTestCase extends DrupalWebTestCase {
  protected $admin_user;
  protected $normal_user;

  function setUp() {
    parent::setUp('profile');
    variable_set('user_register', USER_REGISTER_VISITORS);

    $this->admin_user = $this->drupalCreateUser(array('administer users', 'access user profiles', 'administer blocks'));

    // This is the user whose profile will be edited.
    $this->normal_user = $this->drupalCreateUser();
  }

  /**
   * Create a profile field.
   *
   * @param $type
   *   The field type to be created.
   * @param $category
   *   The category in which the field should be created.
   * @param $edit
   *   Additional parameters to be submitted.
   * @return
   *   The fid of the field that was just created.
   */
  function createProfileField($type = 'textfield', $category = 'simpletest', $edit = array()) {
    $edit['title'] = $title = $this->randomName(8);
    $edit['name'] = $form_name = 'profile_' . $title;
    $edit['category'] = $category;
    $edit['explanation'] = $this->randomName(50);

    $this->drupalPost('admin/config/people/profile/add/' . $type, $edit, t('Save field'));
    $fid = db_query("SELECT fid FROM {profile_field} WHERE title = :title", array(':title' => $title))->fetchField();
    $this->assertTrue($fid, 'New Profile field has been entered in the database');

    // Check that the new field is appearing on the user edit form.
    $this->drupalGet('user/' . $this->admin_user->uid . '/edit/' . $category);

    // Checking field.
    if ($type == 'date') {
      $this->assertField($form_name . '[month]', 'Found month selection field');
      $this->assertField($form_name . '[day]', 'Found day selection field');
      $this->assertField($form_name . '[year]', 'Found day selection field');
    }
    else {
      $this->assertField($form_name , format_string('Found form named @name', array('@name' => $form_name)));
    }

    // Checking name.
    $this->assertText($title, format_string('Checking title for field %title', array('%title' => $title)));
    // Checking explanation.
    $this->assertText($edit['explanation'], format_string('Checking explanation for field %title', array('%title' => $title)));

    return array(
      'fid' => $fid,
      'type' => $type,
      'form_name' => $form_name,
      'title' => $title,
      'category' => $category,
    );
  }

  /**
   * Update a profile field.
   *
   * @param $fid
   *   The fid of the field to be updated.
   * @param $type
   *   The type of field to be updated.
   * @param $edit
   *   Field parameters to be submitted.
   * @return
   *   Array representation of the updated field.
   */
  function updateProfileField($fid, $type = 'textfield', $edit = array()) {

    $form_name = $edit['name'];
    $title = $edit['title'];
    $category = $edit['category'];

    $this->drupalPost('admin/config/people/profile/edit/' . $fid, $edit, t('Save field'));

    // Check that the updated field is appearing on the user edit form.
    $this->drupalGet('user/' . $this->admin_user->uid . '/edit/' . $category);

    // Checking field.
    if ($type == 'date') {
      $this->assertField($form_name . '[month]', 'Found month selection field');
      $this->assertField($form_name . '[day]', 'Found day selection field');
      $this->assertField($form_name . '[year]', 'Found day selection field');
    }
    else {
      $this->assertField($form_name , format_string('Found form named @name', array('@name' => $form_name)));
    }

    // Checking name.
    $this->assertText($title, format_string('Checking title for field %title', array('%title' => $title)));
    // Checking explanation.
    $this->assertText($edit['explanation'], format_string('Checking explanation for field %title', array('%title' => $title)));

    return array(
      'fid' => $fid,
      'type' => $type,
      'form_name' => $form_name,
      'title' => $title,
      'category' => $category,
    );
  }

  /**
   * Set the profile field to a random value
   *
   * @param $field
   *   The field that should be set.
   * @param $value
   *   The value for the field, defaults to a random string.
   * @return
   *   The value that has been assigned to
   */
  function setProfileField($field, $value = NULL) {

    if (!isset($value)) {
      $value = $this->randomName();
    }

    $edit = array(
      $field['form_name'] => $value,
    );
    $this->drupalPost('user/' . $this->normal_user->uid . '/edit/' . $field['category'], $edit, t('Save'));

    // Check profile page.
    $content = $this->drupalGet('user/' . $this->normal_user->uid);
    $this->assertText($field['title'], format_string('Found profile field with title %title', array('%title' => $field['title'])));

    if ($field['type'] != 'checkbox') {
      // $value must be cast to a string in order to be found by assertText.
      $this->assertText("$value", format_string('Found profile field with value %value', array('%value' => $value)));
    }

    return $value;
  }

  /**
   * Delete a profile field.
   *
   * @param $field
   *   The field to be deleted.
   */
  function deleteProfileField($field) {
    $this->drupalPost('admin/config/people/profile/delete/' . $field['fid'], array(), t('Delete'));
    $this->drupalGet('admin/config/people/profile');
    $this->assertNoText($field['title'], format_string('Checking deleted field %title', array('%title' => $field['title'])));
  }
}

class ProfileTestFields extends ProfileTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Test single fields',
      'description' => 'Testing profile module with add/edit/delete textfield, textarea, list, checkbox, and url fields into profile page',
      'group' => 'Profile'
    );
  }

  /**
   * Test each of the field types. List selection and date fields are tested
   * separately because they need some special handling.
   */
  function testProfileFields() {
    $this->drupalLogin($this->admin_user);

    // Set test values for every field type.
    $field_types = array(
      'textfield' => $this->randomName(),
      'textarea' => $this->randomName(),
      'list' => $this->randomName(),
      'checkbox' => 1,
      // An underscore is an invalid character in a domain name. The method randomName can
      // return an underscore.
      'url' => 'http://www.' . str_replace('_', '', $this->randomName(10)) . '.org',
    );

    // For each field type, create a field, give it a value, update the field,
    // and delete the field.
    foreach ($field_types as $type => $value) {
      $field = $this->createProfileField($type);
      $this->setProfileField($field, $value);
      $edit = array(
        'name' => $field['form_name'],
        'title' => $this->randomName(),
        'category' => $field['category'],
        'explanation' => $this->randomName(),
      );
      $field = $this->updateProfileField($field['fid'], $field['type'], $edit);
      $this->deleteProfileField($field);
    }
  }
}

class ProfileTestSelect extends ProfileTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Test select field',
      'description' => 'Testing profile module with add/edit/delete a select field',
      'group' => 'Profile'
    );
  }

  /**
   * Create a list selection field, give it a value, update and delete the field.
   */
  function testProfileSelectionField() {
    $this->drupalLogin($this->admin_user);

    $edit = array(
      'options' => implode("\n", range(1, 10)),
    );
    $field = $this->createProfileField('selection', 'simpletest', $edit);

    $this->setProfileField($field, rand(1, 10));

    $edit = array(
      'name' => $field['form_name'],
      'title' => $this->randomName(),
      'category' => $field['category'],
      'explanation' => $this->randomName(),
    );
    $field = $this->updateProfileField($field['fid'], $field['type'], $edit);
    $this->deleteProfileField($field);
  }
}

class ProfileTestDate extends ProfileTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Test date field',
      'description' => 'Testing profile module with add/edit/delete a date field',
      'group' => 'Profile'
    );
  }

  /**
   * Create a date field, give it a value, update and delete the field.
   */
  function testProfileDateField() {
    $this->drupalLogin($this->admin_user);

    variable_set('date_format_short', 'm/d/Y - H:i');
    $field = $this->createProfileField('date');

    // Set date to January 09, 1983
    $edit = array(
      $field['form_name'] . '[month]' => 1,
      $field['form_name'] . '[day]' => 9,
      $field['form_name'] . '[year]' => 1983,
    );

    $this->drupalPost('user/' . $this->normal_user->uid . '/edit/' . $field['category'], $edit, t('Save'));

    // Check profile page.
    $this->drupalGet('user/' . $this->normal_user->uid);
    $this->assertText($field['title'], format_string('Found profile field with title %title', array('%title' => $field['title'])));

    $this->assertText('01/09/1983', 'Found date profile field.');

    $edit = array(
      'name' => $field['form_name'],
      'title' => $this->randomName(),
      'category' => $field['category'],
      'explanation' => $this->randomName(),
    );
    $field = $this->updateProfileField($field['fid'], $field['type'], $edit);
    $this->deleteProfileField($field);
  }
}

class ProfileTestWeights extends ProfileTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Test field weights',
      'description' => 'Testing profile modules weigting of fields',
      'group' => 'Profile'
    );
  }

  function testProfileFieldWeights() {
    $this->drupalLogin($this->admin_user);

    $category = $this->randomName();
    $field1 = $this->createProfileField('textfield', $category, array('weight' => 1));
    $field2 = $this->createProfileField('textfield', $category, array('weight' => -1));

    $this->setProfileField($field1, $this->randomName(8));
    $this->setProfileField($field2, $this->randomName(8));

    $profile_edit = $this->drupalGet('user/' . $this->normal_user->uid . '/edit/' . $category);
    $this->assertTrue(strpos($profile_edit, $field1['title']) > strpos($profile_edit, $field2['title']), 'Profile field weights are respected on the user edit form.');

    $profile_page = $this->drupalGet('user/' . $this->normal_user->uid);
    $this->assertTrue(strpos($profile_page, $field1['title']) > strpos($profile_page, $field2['title']), 'Profile field weights are respected on the user profile page.');
  }
}

/**
 * Test profile field autocompletion and access.
 */
class ProfileTestAutocomplete extends ProfileTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Autocompletion',
      'description' => 'Test profile fields with autocompletion.',
      'group' => 'Profile'
    );
  }

  /**
   * Tests profile field autocompletion and access.
   */
  function testAutocomplete() {
    $this->drupalLogin($this->admin_user);

    // Create a new profile field with autocompletion enabled.
    $category = $this->randomName();
    $field = $this->createProfileField('textfield', $category, array('weight' => 1, 'autocomplete' => 1));

    // Enter profile field value.
    $field['value'] = $this->randomName();
    $this->setProfileField($field, $field['value']);

    // Set some html for what we want to see in the page output later.
    // Autocomplete always uses non-clean URLs.
    $current_clean_url = isset($GLOBALS['conf']['clean_url']) ? $GLOBALS['conf']['clean_url'] : NULL;
    $GLOBALS['conf']['clean_url'] = 0;
    $autocomplete_url = url('profile/autocomplete/' . $field['fid'], array('absolute' => TRUE, 'script' => 'index.php'));
    $GLOBALS['conf']['clean_url'] = $current_clean_url;
    $autocomplete_id = drupal_html_id('edit-' . $field['form_name'] . '-autocomplete');
    $autocomplete_html = '<input type="hidden" id="' . $autocomplete_id . '" value="' . $autocomplete_url . '" disabled="disabled" class="autocomplete" />';

    // Check that autocompletion html is found on the user's profile edit page.
    $this->drupalGet('user/' . $this->admin_user->uid . '/edit/' . $category);
    $this->assertRaw($autocomplete_html, 'Autocomplete found.');
    $this->assertFieldByXPath(
      '//input[@type="text" and @name="' . $field['form_name'] . '" and contains(@class, "form-autocomplete")]',
      '',
      'Text input field found'
    );
    $this->assertRaw('misc/autocomplete.js', 'Autocomplete JavaScript found.');
    $this->assertRaw('class="form-text form-autocomplete"', 'Autocomplete form element class found.');

    // Check the autocompletion path using the first letter of our user's profile
    // field value to make sure access is allowed and a valid result if found.
    $this->drupalGet('profile/autocomplete/' . $field['fid'] . '/' . $field['value'][0]);
    $this->assertResponse(200, 'Autocomplete path allowed to user with permission.');
    $this->assertRaw($field['value'], 'Autocomplete value found.');

    // Logout and login with a user without the 'access user profiles' permission.
    $this->drupalLogout();
    $this->drupalLogin($this->normal_user);

    // Check that autocompletion html is not found on the user's profile edit page.
    $this->drupalGet('user/' . $this->normal_user->uid . '/edit/' . $category);
    $this->assertNoRaw($autocomplete_html, 'Autocomplete not found.');

    // User should be denied access to the profile autocomplete path.
    $this->drupalGet('profile/autocomplete/' . $field['fid'] . '/' . $field['value'][0]);
    $this->assertResponse(403, 'Autocomplete path denied to user without permission.');
  }
}

class ProfileBlockTestCase extends ProfileTestCase {
  protected $field1;
  protected $field2;
  protected $value1;
  protected $value2;
  protected $node;

  public static function getInfo() {
    return array(
      'name' => 'Block availability',
      'description' => 'Check if the Author Information block is available.',
      'group' => 'Profile',
    );
  }

  function setUp() {
    parent::setUp();

    // Login the admin user.
    $this->drupalLogin($this->admin_user);

    // Create two fields.
    $category = $this->randomName();
    $this->field1 = $this->createProfileField('textfield', $category, array('weight' => 0));
    $this->field2 = $this->createProfileField('textfield', $category, array('weight' => 1));

    // Assign values to those fields.
    $this->value1 = $this->setProfileField($this->field1);
    $this->value2 = $this->setProfileField($this->field2);

    // Create a node authored by the normal user.
    $this->node = $this->drupalCreateNode(array(
      'uid' => $this->normal_user->uid,
    ));
  }

  function testAuthorInformationBlock() {
    // Set the block to a region to confirm the block is available.
    $edit = array();
    $edit['blocks[profile_author-information][region]'] = 'footer';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertText(t('The block settings have been updated.'), 'Block successfully move to footer region.');

    // Enable field 1.
    $this->drupalPost('admin/structure/block/manage/profile/author-information/configure', array(
      'profile_block_author_fields[' . $this->field1['form_name'] . ']' => TRUE,
    ), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

    // Visit the node and confirm that the field is displayed.
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertRaw($this->value1, 'Field 1 is displayed');
    $this->assertNoRaw($this->value2, 'Field 2 is not displayed');

    // Enable only field 2.
    $this->drupalPost('admin/structure/block/manage/profile/author-information/configure', array(
      'profile_block_author_fields[' . $this->field1['form_name'] . ']' => FALSE,
      'profile_block_author_fields[' . $this->field2['form_name'] . ']' => TRUE,
    ), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

    // Visit the node and confirm that the field is displayed.
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertNoRaw($this->value1, 'Field 1 is not displayed');
    $this->assertRaw($this->value2, 'Field 2 is displayed');

    // Enable both fields.
    $this->drupalPost('admin/structure/block/manage/profile/author-information/configure', array(
      'profile_block_author_fields[' . $this->field1['form_name'] . ']' => TRUE,
      'profile_block_author_fields[' . $this->field2['form_name'] . ']' => TRUE,
    ), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

    // Visit the node and confirm that the field is displayed.
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertRaw($this->value1, 'Field 1 is displayed');
    $this->assertRaw($this->value2, 'Field 2 is displayed');

    // Enable the link to the user profile.
    $this->drupalPost('admin/structure/block/manage/profile/author-information/configure', array(
      'profile_block_author_fields[user_profile]' => TRUE,
    ), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block configuration set.');

    // Visit the node and confirm that the user profile link is displayed.
    $this->drupalGet('node/' . $this->node->nid);
    $this->clickLink(t('View full user profile'));
    $this->assertEqual($this->getUrl(), url('user/' . $this->normal_user->uid, array('absolute' => TRUE)));
  }
}

/**
 * Test profile browsing.
 */
class ProfileTestBrowsing extends ProfileTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Profile browsing',
      'description' => 'Test profile browsing.',
      'group' => 'Profile',
    );
  }

  /**
   * Test profile browsing.
   */
  function testProfileBrowsing() {
    $this->drupalLogin($this->admin_user);
    $field = $this->createProfileField('list', 'simpletest', array('page' => '%value'));

    // Set a random value for the profile field.
    $value = $this->setProfileField($field);

    // Check that user is found on the profile browse page.
    $this->drupalGet("profile/{$field['form_name']}/$value");
    $this->assertText($this->normal_user->name);
  }
}

/**
 * Test profile integration with user CRUD operations.
 */
class ProfileCrudTestCase extends ProfileTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Profile CRUD tests',
      'description' => 'Test profile integration with user create, read, update, delete.',
      'group' => 'Profile',
    );
  }

  /**
   * Test profile integration with user CRUD operations.
   */
  public function testUserCRUD() {
    // @todo Add profile fields in addition to base user properties.
    $edit = array(
      'name' => 'Test user',
      'mail' => '<EMAIL>',
    );

    // Create.
    // @todo Add assertions.
    $account = user_save(NULL, $edit);

    // Read.
    // @todo Add assertions.
    $account = user_load($account->uid);

    // Update.
    // @todo Add assertions.
    $account = user_save($account, $edit);

    // Delete.
    // @todo Add assertions.
    user_delete($account->uid);
  }
}

  /**
   * TODO:
   * - Test field visibility
   * - Test required fields
   * - Test fields on registration form
   * - Test updating fields
   */
