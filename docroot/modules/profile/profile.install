<?php

/**
 * @file
 * Install, update and uninstall functions for the profile module.
 */

/**
 * Implements hook_uninstall().
 */
function profile_uninstall() {
  variable_del('profile_block_author_fields');
}

/**
 * Implements hook_schema().
 */
function profile_schema() {
  $schema['profile_field'] = array(
    'description' => 'Stores profile field information.',
    'fields' => array(
      'fid' => array(
        'type' => 'serial',
        'not null' => TRUE,
        'description' => 'Primary Key: Unique profile field ID.',
      ),
      'title' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => FALSE,
        'description' => 'Title of the field shown to the end user.',
      ),
      'name' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Internal name of the field used in the form HTML and URLs.',
      ),
      'explanation' => array(
        'type' => 'text',
        'not null' => FALSE,
        'description' => 'Explanation of the field to end users.',
      ),
      'category' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => FALSE,
        'description' => 'Profile category that the field will be grouped under.',
      ),
      'page' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => FALSE,
        'description' => "Title of page used for browsing by the field's value",
      ),
      'type' => array(
        'type' => 'varchar',
        'length' => 128,
        'not null' => FALSE,
        'description' => 'Type of form field.',
      ),
      'weight' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Weight of field in relation to other profile fields.',
      ),
      'required' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Whether the user is required to enter a value. (0 = no, 1 = yes)',
      ),
      'register' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Whether the field is visible in the user registration form. (1 = yes, 0 = no)',
      ),
      'visibility' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'The level of visibility for the field. (0 = hidden, 1 = private, 2 = public on profile but not member list pages, 3 = public on profile and list pages)',
      ),
      'autocomplete' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Whether form auto-completion is enabled. (0 = disabled, 1 = enabled)',
      ),
      'options' => array(
        'type' => 'text',
        'not null' => FALSE,
        'description' => 'List of options to be used in a list selection field.',
      ),
    ),
    'indexes' => array(
      'category' => array('category'),
    ),
    'unique keys' => array(
      'name' => array('name'),
    ),
    'primary key' => array('fid'),
  );

  $schema['profile_value'] = array(
    'description' => 'Stores values for profile fields.',
    'fields' => array(
      'fid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {profile_field}.fid of the field.',
      ),
      'uid' => array(
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {users}.uid of the profile user.',
      ),
      'value' => array(
        'type' => 'text',
        'not null' => FALSE,
        'description' => 'The value for the field.',
      ),
    ),
    'primary key' => array('uid', 'fid'),
    'indexes' => array(
      'fid' => array('fid'),
    ),
    'foreign keys' => array(
      'profile_field' => array(
        'table' => 'profile_field',
        'columns' => array('fid' => 'fid'),
      ),
      'profile_user' => array(
        'table' => 'users',
        'columns' => array('uid' => 'uid'),
      ),
    ),
  );

  return $schema;
}

/**
 * Rename {profile_fields} table to {profile_field} and {profile_values} to {profile_value}.
 */
function profile_update_7001() {
  db_rename_table('profile_fields', 'profile_field');
  db_rename_table('profile_values', 'profile_value');
}

/**
 * Change the weight column to normal int.
 */
function profile_update_7002() {
  db_change_field('profile_field', 'weight', 'weight', array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
    'description' => 'Weight of field in relation to other profile fields.',
  ));
}
