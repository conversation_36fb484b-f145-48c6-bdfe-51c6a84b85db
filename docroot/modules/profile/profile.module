<?php

/**
 * @file
 * Support for configurable user profiles.
 */

/**
 * Private field, content only available to privileged users.
 */
define('PROFILE_PRIVATE', 1);

/**
 * Public field, content shown on profile page but not used on member list pages.
 */
define('PROFILE_PUBLIC', 2);

/**
 * Public field, content shown on profile page and on member list pages.
 */
define('PROFILE_PUBLIC_LISTINGS', 3);

/**
 * Hidden profile field, only accessible by administrators, modules and themes.
 */
define('PROFILE_HIDDEN', 4);

/**
 * Implements hook_help().
 */
function profile_help($path, $arg) {
  switch ($path) {
    case 'admin/help#profile':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Profile module allows site administrators to define custom fields (such as country, full name, or age) for user profiles, which are then displayed in the <a href="@user">My Account</a> section. This permits users of a site to share more information about themselves, and can help community-based sites organize users around specific information. For more information, see the online handbook entry for <a href="@profile">Profile module</a>.', array('@user' => url('user'), '@profile' => 'http://drupal.org/documentation/modules/profile/')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Adding fields to the default profile') . '</dt>';
      $output .= '<dd>' . t('To provide the ability for users to enter more information about themselves, the Profile module allows additional fields to be added to the default user profile. Examples of common additions would be <em>Location</em>, <em>Company</em>, <em>Age</em>, or <em>About me</em>.') . '</dd>';
      $output .= '<dt>' . t('User information pages') . '</dt>';
      $output .= '<dd>' . t('The Profile module enables links to see further information about site users. You can view both a main <a href="@profile">User list page</a>, and more specified pages by clicking on linked fields in any profile. For example, the <a href="@profile-country">People who live in Canada</a> listing on Drupal.org displays all users who have entered <em>Canada</em> in the <em>Country</em> field on their user profile.', array('@profile' => url('profile'), '@profile-country' => 'http://drupal.org/profile/country/Canada')) . '</dd>';
      $output .= '<dt>' . t('Author information block') . '</dt>';
      $output .= '<dd>' . t('The <em>Author information block</em> is a default block created by the Profile module that can be enabled on the <a href="@blocks">Blocks administration page</a>. It shows visitors of your website information about the author of the page they are reading.', array('@blocks' => url('admin/structure/block'))) . '</dd>';
      $output .= '</dl>';
      return $output;
    case 'admin/config/people/profile':
      return '<p>' . t("This page displays a list of the existing custom profile fields to be displayed on a user's <em>My account</em> page. To provide structure, similar or related fields may be placed inside a category. To add a new category (or edit an existing one), edit a profile field and provide a new category name.") . '</p>';
  }
}

/**
 * Implements hook_theme().
 */
function profile_theme() {
  return array(
    'profile_block' => array(
      'variables' => array('account' => NULL, 'fields' => array()),
      'template' => 'profile-block',
    ),
    'profile_listing' => array(
      'variables' => array('account' => NULL, 'fields' => array()),
      'template' => 'profile-listing',
    ),
    'profile_wrapper' => array(
      'variables' => array('content' => NULL),
      'template' => 'profile-wrapper',
    ),
    'profile_admin_overview' => array(
      'render element' => 'form',
      'file' => 'profile.admin.inc',
    )
  );
}

/**
 * Implements hook_menu().
 */
function profile_menu() {
  $items['profile'] = array(
    'title' => 'User list',
    'page callback' => 'profile_browse',
    'access arguments' => array('access user profiles'),
    'file' => 'profile.pages.inc',
    'type' => MENU_SUGGESTED_ITEM,
  );
  $items['admin/config/people/profile'] = array(
    'title' => 'Profiles',
    'description' => 'Create customizable fields for your users.',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('profile_admin_overview'),
    'access arguments' => array('administer users'),
    'file' => 'profile.admin.inc',
  );
  $items['admin/config/people/profile/add'] = array(
    'title' => 'Add field',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('profile_field_form'),
    'access arguments' => array('administer users'),
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
    'file' => 'profile.admin.inc',
  );
  $items['admin/config/people/profile/autocomplete'] = array(
    'title' => 'Profile category autocomplete',
    'page callback' => 'profile_admin_settings_autocomplete',
    'access arguments' => array('administer users'),
    'file' => 'profile.admin.inc',
    'type' => MENU_CALLBACK,
  );
  $items['admin/config/people/profile/edit'] = array(
    'title' => 'Edit field',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('profile_field_form'),
    'access arguments' => array('administer users'),
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
    'file' => 'profile.admin.inc',
  );
  $items['admin/config/people/profile/delete'] = array(
    'title' => 'Delete field',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('profile_field_delete'),
    'access arguments' => array('administer users'),
    'type' => MENU_VISIBLE_IN_BREADCRUMB,
    'file' => 'profile.admin.inc',
  );
  $items['profile/autocomplete'] = array(
    'title' => 'Profile autocomplete',
    'page callback' => 'profile_autocomplete',
    'access arguments' => array('access user profiles'),
    'file' => 'profile.pages.inc',
    'type' => MENU_CALLBACK,
  );
  return $items;
}

/**
 * Implements hook_block_info().
 */
 function profile_block_info() {
  $blocks['author-information']['info'] = t('Author information');
  $blocks['author-information']['cache'] = DRUPAL_CACHE_PER_PAGE | DRUPAL_CACHE_PER_ROLE;
  return $blocks;
}

/**
 * Implements hook_block_configure().
 */
function profile_block_configure($delta = '') {
  // Compile a list of fields to show
  $fields = array();
  $result = db_query('SELECT name, title, weight, visibility FROM {profile_field} WHERE visibility IN (:visibility) ORDER BY weight', array(':visibility' => array(PROFILE_PUBLIC, PROFILE_PUBLIC_LISTINGS)));
  foreach ($result as $record) {
    $fields[$record->name] = check_plain($record->title);
  }
  $fields['user_profile'] = t('Link to full user profile');
  $form['profile_block_author_fields'] = array(
    '#type' => 'checkboxes',
    '#title' => t('Profile fields to display'),
    '#default_value' => variable_get('profile_block_author_fields', array()),
    '#options' => $fields,
    '#description' => t('Select which profile fields you wish to display in the block. Only fields designated as public in the <a href="@profile-admin">profile field configuration</a> are available.', array('@profile-admin' => url('admin/config/people/profile'))),
  );
  return $form;
}

/**
 * Implements hook_block_save().
 */
function profile_block_save($delta = '', $edit = array()) {
  variable_set('profile_block_author_fields', $edit['profile_block_author_fields']);
}

/**
 * Implements hook_block_view().
 */
function profile_block_view($delta = '') {
  if (user_access('access user profiles')) {
    $output = '';
    if (arg(0) == 'node' && is_numeric(arg(1)) && arg(2) == NULL) {
      $node = node_load(arg(1));
      $account = user_load($node->uid);

      if ($use_fields = variable_get('profile_block_author_fields', array())) {
        // Compile a list of fields to show.
        $fields = array();
        $result = db_query('SELECT * FROM {profile_field} WHERE visibility IN (:visibility) ORDER BY weight', array(':visibility' => array(PROFILE_PUBLIC, PROFILE_PUBLIC_LISTINGS)));
        foreach ($result as $record) {
          // Ensure that field is displayed only if it is among the defined block fields and, if it is private, the user has appropriate permissions.
          if (isset($use_fields[$record->name]) && $use_fields[$record->name]) {
            $fields[] = $record;
          }
        }
      }

      if (!empty($fields)) {
        $profile = _profile_update_user_fields($fields, $account);
        $output .= theme('profile_block', array('account' => $account, 'fields' => $profile));
      }

      if (isset($use_fields['user_profile']) && $use_fields['user_profile']) {
        $output .= '<div>' . l(t('View full user profile'), 'user/' . $account->uid) . '</div>';
      }
    }

    if ($output) {
      $block['subject'] = t('About %name', array('%name' => format_username($account)));
      $block['content'] = $output;
      return $block;
    }
  }
}

/**
 * Implements hook_user_presave().
 */
function profile_user_presave(&$edit, $account, $category) {
  if (!empty($account->uid)) {
    profile_save_profile($edit, $account, $category);
  }
}

/**
 * Implements hook_user_insert().
 */
function profile_user_insert(&$edit, $account, $category) {
  profile_save_profile($edit, $account, $category, TRUE);
}

/**
 * Implements hook_user_cancel().
 */
function profile_user_cancel($edit, $account, $method) {
  switch ($method) {
    case 'user_cancel_reassign':
      db_delete('profile_value')
        ->condition('uid', $account->uid)
        ->execute();
  }
}

/**
 * Implements hook_user_delete().
 */
function profile_user_delete($account) {
  db_delete('profile_value')
    ->condition('uid', $account->uid)
    ->execute();
}

/**
 * Implements hook_user_load().
 */
function profile_user_load($users) {
  $result = db_query('SELECT f.name, f.type, v.uid, v.value FROM {profile_field} f INNER JOIN {profile_value} v ON f.fid = v.fid WHERE uid IN (:uids)', array(':uids' => array_keys($users)));
  foreach ($result as $record) {
    if (empty($users[$record->uid]->{$record->name})) {
      $users[$record->uid]->{$record->name} = _profile_field_serialize($record->type) ? unserialize($record->value) : $record->value;
    }
  }
}

function profile_save_profile(&$edit, $account, $category, $register = FALSE) {
  $result = _profile_get_fields($category, $register);
  foreach ($result as $field) {
    if (_profile_field_serialize($field->type)) {
      $edit[$field->name] = serialize($edit[$field->name]);
    }
    db_merge('profile_value')
      ->key(array(
        'fid' => $field->fid,
        'uid' => $account->uid,
      ))
      ->fields(array('value' => $edit[$field->name]))
      ->execute();
  }
}

function profile_view_field($account, $field) {
  // Only allow browsing of private fields for admins, if browsing is enabled,
  // and if a user has permission to view profiles. Note that this check is
  // necessary because a user may always see their own profile.
  $browse = user_access('access user profiles')
        && (user_access('administer users') || $field->visibility != PROFILE_PRIVATE)
        && !empty($field->page);

  if (isset($account->{$field->name}) && $value = $account->{$field->name}) {
    switch ($field->type) {
      case 'textarea':
        return check_markup($value, filter_default_format($account), '', TRUE);
      case 'textfield':
      case 'selection':
        return $browse ? l($value, 'profile/' . $field->name . '/' . $value) : check_plain($value);
      case 'checkbox':
        return $browse ? l($field->title, 'profile/' . $field->name) : check_plain($field->title);
      case 'url':
        return '<a href="' . check_url($value) . '">' . check_plain($value) . '</a>';
      case 'date':
        $format = substr(variable_get('date_format_short', 'm/d/Y - H:i'), 0, 5);
        // Note: Avoid PHP's date() because it does not handle dates before
        // 1970 on Windows. This would make the date field useless for e.g.
        // birthdays.
        $replace = array(
          'd' => sprintf('%02d', $value['day']),
          'j' => $value['day'],
          'm' => sprintf('%02d', $value['month']),
          'M' => map_month($value['month']),
          'Y' => $value['year'],
          'H:i' => NULL,
          'g:ia' => NULL,
        );
        return strtr($format, $replace);
      case 'list':
        $values = preg_split("/[,\n\r]/", $value);
        $fields = array();
        foreach ($values as $value) {
          if ($value = trim($value)) {
            $fields[] = $browse ? l($value, 'profile/' . $field->name . '/' . $value) : check_plain($value);
          }
        }
        return implode(', ', $fields);
    }
  }
}

/**
 * Implements hook_user_view().
 */
function profile_user_view($account) {
  // Show private fields to administrators and people viewing their own account.
  if (user_access('administer users') || $GLOBALS['user']->uid == $account->uid) {
    $result = db_query('SELECT * FROM {profile_field} WHERE visibility <> :hidden ORDER BY category, weight', array(':hidden' => PROFILE_HIDDEN));
  }
  else {
    $result = db_query('SELECT * FROM {profile_field} WHERE visibility <> :private AND visibility <> :hidden ORDER BY category, weight', array(':private' => PROFILE_PRIVATE, ':hidden' => PROFILE_HIDDEN));
  }

  $fields = array();
  foreach ($result as $field) {
    if ($value = profile_view_field($account, $field)) {
      $title = ($field->type != 'checkbox') ? check_plain($field->title) : NULL;

      // Create a single fieldset for each category.
      if (!isset($account->content[$field->category])) {
        $account->content[$field->category] = array(
          '#type' => 'user_profile_category',
          '#title' => $field->category,
        );
      }

      $account->content[$field->category][$field->name] = array(
        '#type' => 'user_profile_item',
        '#title' => $title,
        '#markup' => $value,
        '#weight' => $field->weight,
        '#attributes' => array('class' => array('profile-' . $field->name)),
      );
    }
  }
}

function _profile_form_explanation($field) {
  $output = filter_xss_admin($field->explanation);

  if ($field->type == 'list') {
    $output .= ' ' . t('Put each item on a separate line or separate them by commas. No HTML allowed.');
  }

  if ($field->visibility == PROFILE_PRIVATE) {
    $output .= ' ' . t('The content of this field is kept private and will not be shown publicly.');
  }

  return $output;
}

/**
 * Implements hook_form_alter().
 */
function profile_form_alter(&$form, &$form_state, $form_id) {
  if (!($form_id == 'user_register_form' || $form_id == 'user_profile_form')) {
    return;
  }
  $form['#validate'][] = 'profile_user_form_validate';
  $account = $form['#user'];
  $result = _profile_get_fields($form['#user_category'], $form['#user_category'] == 'register');
  $weight = 1;
  foreach ($result as $field) {
    $category = $field->category;
    if (!isset($form[$category])) {
      $form[$category] = array('#type' => 'fieldset', '#title' => check_plain($category), '#weight' => $weight++);
    }
    switch ($field->type) {
      case 'textfield':
      case 'url':
        $form[$category][$field->name] = array(
          '#type' => 'textfield',
          '#title' => check_plain($field->title),
          '#default_value' => isset($account->{$field->name}) ? $account->{$field->name} : '',
          '#maxlength' => 255,
          '#description' => _profile_form_explanation($field),
          '#required' => $field->required,
        );
        if ($field->autocomplete) {
          $form[$category][$field->name]['#autocomplete_path'] = "profile/autocomplete/" . $field->fid;
        }
        break;

      case 'textarea':
        $form[$category][$field->name] = array(
          '#type' => 'textarea',
          '#title' => check_plain($field->title),
          '#default_value' => isset($account->{$field->name}) ? $account->{$field->name} : '',
          '#description' => _profile_form_explanation($field),
          '#required' => $field->required,
        );
        break;

      case 'list':
        $form[$category][$field->name] = array(
          '#type' => 'textarea',
          '#title' => check_plain($field->title),
          '#default_value' => isset($account->{$field->name}) ? $account->{$field->name} : '',
          '#description' => _profile_form_explanation($field),
          '#required' => $field->required,
        );
        break;

      case 'checkbox':
        $form[$category][$field->name] = array(
          '#type' => 'checkbox',
          '#title' => check_plain($field->title),
          '#default_value' => isset($account->{$field->name}) ? $account->{$field->name} : '',
          '#description' => _profile_form_explanation($field),
          '#required' => $field->required,
        );
        break;

      case 'selection':
        $options = array();
        $lines = preg_split("/[\n\r]/", $field->options);
        foreach ($lines as $line) {
          if ($line = trim($line)) {
            $options[$line] = $line;
          }
        }
        $form[$category][$field->name] = array(
          '#type' => 'select',
          '#title' => check_plain($field->title),
          '#default_value' => isset($account->{$field->name}) ? $account->{$field->name} : '',
          '#options' => $options,
          '#description' => _profile_form_explanation($field),
          '#required' => $field->required,
          '#empty_value' => 0,
        );
        break;

      case 'date':
        $form[$category][$field->name] = array(
          '#type' => 'date',
          '#title' => check_plain($field->title),
          '#default_value' => isset($account->{$field->name}) ? $account->{$field->name} : '',
          '#description' => _profile_form_explanation($field),
          '#required' => $field->required,
        );
        break;
    }
  }
}

/**
 * Helper function: update an array of user fields by calling profile_view_field
 */
function _profile_update_user_fields($fields, $account) {
  foreach ($fields as $key => $field) {
    $fields[$key]->value = profile_view_field($account, $field);
  }
  return $fields;
}

/**
 * Form validation handler for the user register/profile form.
 *
 * @see profile_form_alter()
 */
function profile_user_form_validate($form, &$form_state) {
  $result = _profile_get_fields($form['#user_category'], $form['#user_category'] == 'register');
  foreach ($result as $field) {
    if (!empty($form_state['values'][$field->name])) {
      if ($field->type == 'url' && !valid_url($form_state['values'][$field->name], TRUE)) {
        form_set_error($field->name, t('The value provided for %field is not a valid URL.', array('%field' => $field->title)));
      }
    }
    elseif ($field->required && !user_access('administer users')) {
      form_set_error($field->name, t('The field %field is required.', array('%field' => $field->title)));
    }
  }
}

/**
 * Implements hook_user_categories().
 */
function profile_user_categories() {
  $result = db_query("SELECT DISTINCT(category) FROM {profile_field}");
  $data = array();
  foreach ($result as $category) {
    $data[] = array(
      'name' => $category->category,
      'title' => $category->category,
      'weight' => 3,
      'access callback' => 'profile_category_access',
      'access arguments' => array(1, $category->category)
    );
  }
  return $data;
}

/**
 * Menu item access callback - check if a user has access to a profile category.
 */
function profile_category_access($account, $category) {
  if (user_access('administer users') && $account->uid > 0) {
    return TRUE;
  }
  else {
    $category_visible = (bool) db_query_range('SELECT 1 FROM {profile_field} WHERE category = :category AND visibility <> :visibility', 0, 1, array(
      ':category' => $category,
      ':visibility' => PROFILE_HIDDEN
    ))->fetchField();
    return user_edit_access($account) && $category_visible;
  }
}

/**
 * Process variables for profile-block.tpl.php.
 *
 * The $variables array contains the following arguments:
 * - $account
 * - $fields
 *
 * @see profile-block.tpl.php
 */
function template_preprocess_profile_block(&$variables) {

  $variables['user_picture'] = theme('user_picture', array('account' => $variables['account']));
  $variables['profile'] = array();
  // Supply filtered version of $fields that have values.
  foreach ($variables['fields'] as $field) {
    if ($field->value) {
      $variables['profile'][$field->name] = new stdClass();
      $variables['profile'][$field->name]->title = check_plain($field->title);
      $variables['profile'][$field->name]->value = $field->value;
      $variables['profile'][$field->name]->type = $field->type;
    }
  }

}

/**
 * Process variables for profile-listing.tpl.php.
 *
 * The $variables array contains the following arguments:
 * - $account
 * - $fields
 *
 * @see profile-listing.tpl.php
 */
function template_preprocess_profile_listing(&$variables) {

  $variables['user_picture'] = theme('user_picture', array('account' => $variables['account']));
  $variables['name'] = theme('username', array('account' => $variables['account']));
  $variables['profile'] = array();
  // Supply filtered version of $fields that have values.
  foreach ($variables['fields'] as $field) {
    if ($field->value) {
      $variables['profile'][$field->name] = new stdClass();
      $variables['profile'][$field->name]->title = $field->title;
      $variables['profile'][$field->name]->value = $field->value;
      $variables['profile'][$field->name]->type = $field->type;
    }
  }

}

/**
 * Process variables for profile-wrapper.tpl.php.
 *
 * The $variables array contains the following arguments:
 * - $content
 *
 * @see profile-wrapper.tpl.php
 */
function template_preprocess_profile_wrapper(&$variables) {
  $variables['current_field'] = '';
  if ($field = arg(1)) {
    $variables['current_field'] = $field;
    $variables['theme_hook_suggestions'][] = 'profile_wrapper__' . $field;
  }
}

function _profile_field_types($type = NULL) {
  $types = array('textfield' => t('single-line textfield'),
                 'textarea' => t('multi-line textfield'),
                 'checkbox' => t('checkbox'),
                 'selection' => t('list selection'),
                 'list' => t('freeform list'),
                 'url' => t('URL'),
                 'date' => t('date'));
  return isset($type) ? $types[$type] : $types;
}

function _profile_field_serialize($type = NULL) {
  return $type == 'date';
}

function _profile_get_fields($category, $register = FALSE) {
  $query = db_select('profile_field');
  if ($register) {
    $query->condition('register', 1);
  }
  else {
    $query->condition('category', db_like($category), 'LIKE');
  }
  if (!user_access('administer users')) {
    $query->condition('visibility', PROFILE_HIDDEN, '<>');
  }
  return $query
    ->fields('profile_field')
    ->orderBy('category', 'ASC')
    ->orderBy('weight', 'ASC')
    ->execute();
}
