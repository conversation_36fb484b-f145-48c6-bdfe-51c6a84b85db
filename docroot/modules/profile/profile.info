name = Profile
description = Supports configurable user profiles.
package = Core
version = VERSION
core = 7.x
files[] = profile.test
configure = admin/config/people/profile
; The Profile module is deprecated, and included in Drupal 7 for legacy
; purposes only. By default, the module will be hidden from the UI unless you
; are upgrading a site that uses the Profile module to extend user profiles.
; See user_system_info_alter().
hidden = TRUE

; Information added by Drupal.org packaging script on 2023-06-07
version = "7.98"
project = "drupal"
datestamp = "1686154435"
