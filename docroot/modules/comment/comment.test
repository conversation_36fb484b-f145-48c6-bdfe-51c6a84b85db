<?php

/**
 * @file
 * Tests for comment.module.
 */

class CommentHelperCase extends DrupalWebTestCase {
  protected $super_user;
  protected $admin_user;
  protected $web_user;
  protected $node;

  function setUp() {
    $modules = func_get_args();
    if (isset($modules[0]) && is_array($modules[0])) {
      $modules = $modules[0];
    }
    $modules[] = 'comment';
    parent::setUp($modules);

    // Create users and test node.
    $this->super_user = $this->drupalCreateUser(array('access administration pages', 'administer modules'));
    $this->admin_user = $this->drupalCreateUser(array('administer content types', 'administer comments', 'administer blocks', 'administer actions', 'administer fields'));
    $this->web_user = $this->drupalCreateUser(array('access comments', 'post comments', 'create article content', 'edit own comments'));
    $this->node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1, 'uid' => $this->web_user->uid));
  }

  /**
   * Post comment.
   *
   * @param $node
   *   Node to post comment on.
   * @param $comment
   *   Comment body.
   * @param $subject
   *   Comment subject.
   * @param $contact
   *   Set to NULL for no contact info, TRUE to ignore success checking, and
   *   array of values to set contact info.
   */
  function postComment($node, $comment, $subject = '', $contact = NULL) {
    $langcode = LANGUAGE_NONE;
    $edit = array();
    $edit['comment_body[' . $langcode . '][0][value]'] = $comment;

    $preview_mode = variable_get('comment_preview_article', DRUPAL_OPTIONAL);
    $subject_mode = variable_get('comment_subject_field_article', 1);

    // Must get the page before we test for fields.
    if ($node !== NULL) {
      $this->drupalGet('comment/reply/' . $node->nid);
    }

    if ($subject_mode == TRUE) {
      $edit['subject'] = $subject;
    }
    else {
      $this->assertNoFieldByName('subject', '', 'Subject field not found.');
    }

    if ($contact !== NULL && is_array($contact)) {
      $edit += $contact;
    }
    switch ($preview_mode) {
      case DRUPAL_REQUIRED:
        // Preview required so no save button should be found.
        $this->assertNoFieldByName('op', t('Save'), 'Save button not found.');
        $this->drupalPost(NULL, $edit, t('Preview'));
        // Don't break here so that we can test post-preview field presence and
        // function below.
      case DRUPAL_OPTIONAL:
        $this->assertFieldByName('op', t('Preview'), 'Preview button found.');
        $this->assertFieldByName('op', t('Save'), 'Save button found.');
        $this->drupalPost(NULL, $edit, t('Save'));
        break;

      case DRUPAL_DISABLED:
        $this->assertNoFieldByName('op', t('Preview'), 'Preview button not found.');
        $this->assertFieldByName('op', t('Save'), 'Save button found.');
        $this->drupalPost(NULL, $edit, t('Save'));
        break;
    }
    $match = array();
    // Get comment ID
    preg_match('/#comment-([0-9]+)/', $this->getURL(), $match);

    // Get comment.
    if ($contact !== TRUE) { // If true then attempting to find error message.
      if ($subject) {
        $this->assertText($subject, 'Comment subject posted.');
      }
      $this->assertText($comment, 'Comment body posted.');
      $this->assertTrue((!empty($match) && !empty($match[1])), 'Comment id found.');
    }

    if (isset($match[1])) {
      return (object) array('id' => $match[1], 'subject' => $subject, 'comment' => $comment);
    }
  }

  /**
   * Checks current page for specified comment.
   *
   * @param object $comment Comment object.
   * @param boolean $reply The comment is a reply to another comment.
   * @return boolean Comment found.
   */
  function commentExists($comment, $reply = FALSE) {
    if ($comment && is_object($comment)) {
      $regex = '/' . ($reply ? '<div class="indented">(.*?)' : '');
      $regex .= '<a id="comment-' . $comment->id . '"(.*?)'; // Comment anchor.
      $regex .= '<div(.*?)'; // Begin in comment div.
      $regex .= $comment->subject . '(.*?)'; // Match subject.
      $regex .= $comment->comment . '(.*?)'; // Match comment.
      $regex .= '/s';

      return (boolean)preg_match($regex, $this->drupalGetContent());
    }
    else {
      return FALSE;
    }
  }

  /**
   * Delete comment.
   *
   * @param object $comment
   *   Comment to delete.
   */
  function deleteComment($comment) {
    $this->drupalPost('comment/' . $comment->id . '/delete', array(), t('Delete'));
    $this->assertText(t('The comment and all its replies have been deleted.'), 'Comment deleted.');
  }

  /**
   * Set comment subject setting.
   *
   * @param boolean $enabled
   *   Subject value.
   */
  function setCommentSubject($enabled) {
    $this->setCommentSettings('comment_subject_field', ($enabled ? '1' : '0'), 'Comment subject ' . ($enabled ? 'enabled' : 'disabled') . '.');
  }

  /**
   * Set comment preview setting.
   *
   * @param int $mode
   *   Preview value.
   */
  function setCommentPreview($mode) {
    switch ($mode) {
      case DRUPAL_DISABLED:
        $mode_text = 'disabled';
        break;

      case DRUPAL_OPTIONAL:
        $mode_text = 'optional';
        break;

      case DRUPAL_REQUIRED:
        $mode_text = 'required';
        break;
    }
    $this->setCommentSettings('comment_preview', $mode, format_string('Comment preview @mode_text.', array('@mode_text' => $mode_text)));
  }

  /**
   * Set comment form location setting.
   *
   * @param boolean $enabled
   *   Form value.
   */
  function setCommentForm($enabled) {
    $this->setCommentSettings('comment_form_location', ($enabled ? COMMENT_FORM_BELOW : COMMENT_FORM_SEPARATE_PAGE), 'Comment controls ' . ($enabled ? 'enabled' : 'disabled') . '.');
  }

  /**
   * Set comment anonymous level setting.
   *
   * @param integer $level
   *   Anonymous level.
   */
  function setCommentAnonymous($level) {
    $this->setCommentSettings('comment_anonymous', $level, format_string('Anonymous commenting set to level @level.', array('@level' => $level)));
  }

  /**
   * Set the default number of comments per page.
   *
   * @param integer $comments
   *   Comments per page value.
   */
  function setCommentsPerPage($number) {
    $this->setCommentSettings('comment_default_per_page', $number, format_string('Number of comments per page set to @number.', array('@number' => $number)));
  }

  /**
   * Set comment setting for article content type.
   *
   * @param string $name
   *   Name of variable.
   * @param string $value
   *   Value of variable.
   * @param string $message
   *   Status message to display.
   */
  function setCommentSettings($name, $value, $message) {
    variable_set($name . '_article', $value);
    // Display status message.
    $this->pass($message);
  }

  /**
   * Check for contact info.
   *
   * @return boolean Contact info is available.
   */
  function commentContactInfoAvailable() {
    return preg_match('/(input).*?(name="name").*?(input).*?(name="mail").*?(input).*?(name="homepage")/s', $this->drupalGetContent());
  }

  /**
   * Perform the specified operation on the specified comment.
   *
   * @param object $comment
   *   Comment to perform operation on.
   * @param string $operation
   *   Operation to perform.
   * @param boolean $aproval
   *   Operation is found on approval page.
   */
  function performCommentOperation($comment, $operation, $approval = FALSE) {
    $edit = array();
    $edit['operation'] = $operation;
    $edit['comments[' . $comment->id . ']'] = TRUE;
    $this->drupalPost('admin/content/comment' . ($approval ? '/approval' : ''), $edit, t('Update'));

    if ($operation == 'delete') {
      $this->drupalPost(NULL, array(), t('Delete comments'));
      $this->assertRaw(format_plural(1, 'Deleted 1 comment.', 'Deleted @count comments.'), format_string('Operation @operation was performed on comment.', array('@operation' => $operation)));
    }
    else {
      $this->assertText(t('The update has been performed.'), format_string('Operation @operation was performed on comment.', array('@operation' => $operation)));
    }
  }

  /**
   * Get the comment ID for an unapproved comment.
   *
   * @param string $subject
   *   Comment subject to find.
   * @return integer
   *   Comment id.
   */
  function getUnapprovedComment($subject) {
    $this->drupalGet('admin/content/comment/approval');
    preg_match('/href="(.*?)#comment-([^"]+)"(.*?)>(' . $subject . ')/', $this->drupalGetContent(), $match);

    return $match[2];
  }
}

class CommentInterfaceTest extends CommentHelperCase {
  protected $web_user2;
  protected $comment;

  public static function getInfo() {
    return array(
      'name' => 'Comment interface',
      'description' => 'Test comment user interfaces.',
      'group' => 'Comment',
    );
  }

  /**
   * Test comment interface.
   */
  function testCommentInterface() {
    $langcode = LANGUAGE_NONE;
    // Set comments to have subject and preview disabled.
    $this->drupalLogin($this->admin_user);
    $this->setCommentPreview(DRUPAL_DISABLED);
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(FALSE);
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Comment paging changed.');
    $this->drupalLogout();

    // Post comment #1 without subject or preview.
    $this->drupalLogin($this->web_user);
    $comment_text = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text);
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue($this->commentExists($comment), 'Comment found.');

    // Set comments to have subject and preview to required.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);
    $this->setCommentSubject(TRUE);
    $this->setCommentPreview(DRUPAL_REQUIRED);
    $this->drupalLogout();

    // Create comment #2 that allows subject and requires preview.
    $this->drupalLogin($this->web_user);
    $subject_text = $this->randomName();
    $comment_text = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text, $subject_text, TRUE);
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue($this->commentExists($comment), 'Comment found.');

    // Check comment display.
    $this->drupalGet('node/' . $this->node->nid . '/' . $comment->id);
    $this->assertText($subject_text, 'Individual comment subject found.');
    $this->assertText($comment_text, 'Individual comment body found.');

    // Set comments to have subject and preview to optional.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);
    $this->setCommentSubject(TRUE);
    $this->setCommentPreview(DRUPAL_OPTIONAL);

    // Test changing the comment author to "Anonymous".
    $this->drupalGet('comment/' . $comment->id . '/edit');
    $comment = $this->postComment(NULL, $comment->comment, $comment->subject, array('name' => ''));
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue(empty($comment_loaded->name) && $comment_loaded->uid == 0, 'Comment author successfully changed to anonymous.');

    // Test changing the comment author to an unverified user.
    $random_name = $this->randomName();
    $this->drupalGet('comment/' . $comment->id . '/edit');
    $comment = $this->postComment(NULL, $comment->comment, $comment->subject, array('name' => $random_name));
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertText($random_name . ' (' . t('not verified') . ')', 'Comment author successfully changed to an unverified user.');

    // Test changing the comment author to a verified user.
    $this->drupalGet('comment/' . $comment->id . '/edit');
    $comment = $this->postComment(NULL, $comment->comment, $comment->subject, array('name' => $this->web_user->name));
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue($comment_loaded->name == $this->web_user->name && $comment_loaded->uid == $this->web_user->uid, 'Comment author successfully changed to a registered user.');

    $this->drupalLogout();

    // Reply to comment #2 creating comment #3 with optional preview and no
    // subject though field enabled.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $comment->id);
    $this->assertText($subject_text, 'Individual comment-reply subject found.');
    $this->assertText($comment_text, 'Individual comment-reply body found.');
    $reply = $this->postComment(NULL, $this->randomName(), '', TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Reply found.');
    $this->assertEqual($comment->id, $reply_loaded->pid, 'Pid of a reply to a comment is set correctly.');
    $this->assertEqual(rtrim($comment_loaded->thread, '/') . '.00/', $reply_loaded->thread, 'Thread of reply grows correctly.');

    // Second reply to comment #3 creating comment #4.
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $comment->id);
    $this->assertText($subject_text, 'Individual comment-reply subject found.');
    $this->assertText($comment_text, 'Individual comment-reply body found.');
    $reply = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Second reply found.');
    $this->assertEqual(rtrim($comment_loaded->thread, '/') . '.01/', $reply_loaded->thread, 'Thread of second reply grows correctly.');

    // Edit reply.
    $this->drupalGet('comment/' . $reply->id . '/edit');
    $reply = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Modified reply found.');

    // Correct link count
    $this->drupalGet('node');
    $this->assertRaw('4 comments', 'Link to the 4 comments exist.');

    // Confirm a new comment is posted to the correct page.
    $this->setCommentsPerPage(2);
    $comment_new_page = $this->postComment($this->node, $this->randomName(), $this->randomName(), TRUE);
    $this->assertTrue($this->commentExists($comment_new_page), 'Page one exists. %s');
    $this->drupalGet('node/' . $this->node->nid, array('query' => array('page' => 1)));
    $this->assertTrue($this->commentExists($reply, TRUE), 'Page two exists. %s');
    $this->setCommentsPerPage(50);

    // Attempt to post to node with comments disabled.
    $this->node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1, 'comment' => COMMENT_NODE_HIDDEN));
    $this->assertTrue($this->node, 'Article node created.');
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $this->assertText('This discussion is closed', 'Posting to node with comments disabled');
    $this->assertNoField('edit-comment', 'Comment body field found.');

    // Attempt to post to node with read-only comments.
    $this->node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1, 'comment' => COMMENT_NODE_CLOSED));
    $this->assertTrue($this->node, 'Article node created.');
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $this->assertText('This discussion is closed', 'Posting to node with comments read-only');
    $this->assertNoField('edit-comment', 'Comment body field found.');

    // Attempt to post to node with comments enabled (check field names etc).
    $this->node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1, 'comment' => COMMENT_NODE_OPEN));
    $this->assertTrue($this->node, 'Article node created.');
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $this->assertNoText('This discussion is closed', 'Posting to node with comments enabled');
    $this->assertField('edit-comment-body-' . $langcode . '-0-value', 'Comment body field found.');

    // Delete comment and make sure that reply is also removed.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);
    $this->deleteComment($comment);
    $this->deleteComment($comment_new_page);

    $this->drupalGet('node/' . $this->node->nid);
    $this->assertFalse($this->commentExists($comment), 'Comment not found.');
    $this->assertFalse($this->commentExists($reply, TRUE), 'Reply not found.');

    // Enabled comment form on node page.
    $this->drupalLogin($this->admin_user);
    $this->setCommentForm(TRUE);
    $this->drupalLogout();

    // Submit comment through node form.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node/' . $this->node->nid);
    $form_comment = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);
    $this->assertTrue($this->commentExists($form_comment), 'Form comment found.');

    // Disable comment form on node page.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);
    $this->setCommentForm(FALSE);
  }

  /**
   * Tests new comment marker.
   */
  public function testCommentNewCommentsIndicator() {
    // Test if the right links are displayed when no comment is present for the
    // node.
    $this->drupalLogin($this->admin_user);
    $this->node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1, 'comment' => COMMENT_NODE_OPEN));
    $this->drupalGet('node');
    $this->assertNoLink(t('@count comments', array('@count' => 0)));
    $this->assertNoLink(t('@count new comments', array('@count' => 0)));
    $this->assertLink(t('Read more'));
    $count = $this->xpath('//div[@id=:id]/div[@class=:class]/ul/li', array(':id' => 'node-' . $this->node->nid, ':class' => 'link-wrapper'));
    $this->assertTrue(count($count) == 1, 'One child found');

    // Create a new comment. This helper function may be run with different
    // comment settings so use comment_save() to avoid complex setup.
    $comment = (object) array(
      'cid' => NULL,
      'nid' => $this->node->nid,
      'node_type' => $this->node->type,
      'pid' => 0,
      'uid' => $this->loggedInUser->uid,
      'status' => COMMENT_PUBLISHED,
      'subject' => $this->randomName(),
      'hostname' => ip_address(),
      'language' => LANGUAGE_NONE,
      'comment_body' => array(LANGUAGE_NONE => array($this->randomName())),
    );
    comment_save($comment);
    $this->drupalLogout();

    // Log in with 'web user' and check comment links.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('node');
    $this->assertLink(t('1 new comment'));
    $this->clickLink(t('1 new comment'));
    $this->assertRaw('<a id="new"></a>', 'Found "new" marker.');
    $this->assertTrue($this->xpath('//a[@id=:new]/following-sibling::a[1][@id=:comment_id]', array(':new' => 'new', ':comment_id' => 'comment-1')), 'The "new" anchor is positioned at the right comment.');

    // Test if "new comment" link is correctly removed.
    $this->drupalGet('node');
    $this->assertLink(t('1 comment'));
    $this->assertLink(t('Read more'));
    $this->assertNoLink(t('1 new comment'));
    $this->assertNoLink(t('@count new comments', array('@count' => 0)));
    $count = $this->xpath('//div[@id=:id]/div[@class=:class]/ul/li', array(':id' => 'node-' . $this->node->nid, ':class' => 'link-wrapper'));
    $this->assertTrue(count($count) == 2, print_r($count, TRUE));
  }

  /**
   * Tests CSS classes on comments.
   */
  function testCommentClasses() {
    // Create all permutations for comments, users, and nodes.
    $parameters = array(
      'node_uid' => array(0, $this->web_user->uid),
      'comment_uid' => array(0, $this->web_user->uid, $this->admin_user->uid),
      'comment_status' => array(COMMENT_PUBLISHED, COMMENT_NOT_PUBLISHED),
      'user' => array('anonymous', 'authenticated', 'admin'),
    );
    $permutations = $this->generatePermutations($parameters);

    foreach ($permutations as $case) {
      // Create a new node.
      $node = $this->drupalCreateNode(array('type' => 'article', 'uid' => $case['node_uid']));

      // Add a comment.
      $comment = (object) array(
        'cid' => NULL,
        'nid' => $node->nid,
        'pid' => 0,
        'uid' => $case['comment_uid'],
        'status' => $case['comment_status'],
        'subject' => $this->randomName(),
        'language' => LANGUAGE_NONE,
        'comment_body' => array(LANGUAGE_NONE => array($this->randomName())),
      );
      comment_save($comment);

      // Adjust the current/viewing user.
      switch ($case['user']) {
        case 'anonymous':
          $this->drupalLogout();
          $case['user_uid'] = 0;
          break;

        case 'authenticated':
          $this->drupalLogin($this->web_user);
          $case['user_uid'] = $this->web_user->uid;
          break;

        case 'admin':
          $this->drupalLogin($this->admin_user);
          $case['user_uid'] = $this->admin_user->uid;
          break;
      }
      // Request the node with the comment.
      $this->drupalGet('node/' . $node->nid);

      // Verify classes if the comment is visible for the current user.
      if ($case['comment_status'] == COMMENT_PUBLISHED || $case['user'] == 'admin') {
        // Verify the comment-by-anonymous class.
        $comments = $this->xpath('//*[contains(@class, "comment-by-anonymous")]');
        if ($case['comment_uid'] == 0) {
          $this->assertTrue(count($comments) == 1, 'comment-by-anonymous class found.');
        }
        else {
          $this->assertFalse(count($comments), 'comment-by-anonymous class not found.');
        }

        // Verify the comment-by-node-author class.
        $comments = $this->xpath('//*[contains(@class, "comment-by-node-author")]');
        if ($case['comment_uid'] > 0 && $case['comment_uid'] == $case['node_uid']) {
          $this->assertTrue(count($comments) == 1, 'comment-by-node-author class found.');
        }
        else {
          $this->assertFalse(count($comments), 'comment-by-node-author class not found.');
        }

        // Verify the comment-by-viewer class.
        $comments = $this->xpath('//*[contains(@class, "comment-by-viewer")]');
        if ($case['comment_uid'] > 0 && $case['comment_uid'] == $case['user_uid']) {
          $this->assertTrue(count($comments) == 1, 'comment-by-viewer class found.');
        }
        else {
          $this->assertFalse(count($comments), 'comment-by-viewer class not found.');
        }
      }

      // Verify the comment-unpublished class.
      $comments = $this->xpath('//*[contains(@class, "comment-unpublished")]');
      if ($case['comment_status'] == COMMENT_NOT_PUBLISHED && $case['user'] == 'admin') {
        $this->assertTrue(count($comments) == 1, 'comment-unpublished class found.');
      }
      else {
        $this->assertFalse(count($comments), 'comment-unpublished class not found.');
      }

      // Verify the comment-new class.
      if ($case['comment_status'] == COMMENT_PUBLISHED || $case['user'] == 'admin') {
        $comments = $this->xpath('//*[contains(@class, "comment-new")]');
        if ($case['user'] != 'anonymous') {
          $this->assertTrue(count($comments) == 1, 'comment-new class found.');

          // Request the node again. The comment-new class should disappear.
          $this->drupalGet('node/' . $node->nid);
          $comments = $this->xpath('//*[contains(@class, "comment-new")]');
          $this->assertFalse(count($comments), 'comment-new class not found.');
        }
        else {
          $this->assertFalse(count($comments), 'comment-new class not found.');
        }
      }
    }
  }

  /**
   * Tests the node comment statistics.
   */
  function testCommentNodeCommentStatistics() {
    $langcode = LANGUAGE_NONE;
    // Set comments to have subject and preview disabled.
    $this->drupalLogin($this->admin_user);
    $this->setCommentPreview(DRUPAL_DISABLED);
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(FALSE);
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Comment paging changed.');
    $this->drupalLogout();

    // Creates a second user to post comments.
    $this->web_user2 = $this->drupalCreateUser(array('access comments', 'post comments', 'create article content', 'edit own comments'));

    // Checks the initial values of node comment statistics with no comment.
    $node = node_load($this->node->nid);
    $this->assertEqual($node->last_comment_timestamp, $this->node->created, 'The initial value of node last_comment_timestamp is the node created date.');
    $this->assertEqual($node->last_comment_name, NULL, 'The initial value of node last_comment_name is NULL.');
    $this->assertEqual($node->last_comment_uid, $this->web_user->uid, 'The initial value of node last_comment_uid is the node uid.');
    $this->assertEqual($node->comment_count, 0, 'The initial value of node comment_count is zero.');

    // Post comment #1 as web_user2.
    $this->drupalLogin($this->web_user2);
    $comment_text = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text);
    $comment_loaded = comment_load($comment->id);

    // Checks the new values of node comment statistics with comment #1.
    // The node needs to be reloaded with a node_load_multiple cache reset.
    $node = node_load($this->node->nid, NULL, TRUE);
    $this->assertEqual($node->last_comment_name, NULL, 'The value of node last_comment_name is NULL.');
    $this->assertEqual($node->last_comment_uid, $this->web_user2->uid, 'The value of node last_comment_uid is the comment #1 uid.');
    $this->assertEqual($node->comment_count, 1, 'The value of node comment_count is 1.');

    // Prepare for anonymous comment submission (comment approval enabled).
    variable_set('user_register', USER_REGISTER_VISITORS);
    $this->drupalLogin($this->admin_user);
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => TRUE,
      'post comments' => TRUE,
      'skip comment approval' => FALSE,
    ));
    // Ensure that the poster can leave some contact info.
    $this->setCommentAnonymous('1');
    $this->drupalLogout();

    // Post comment #2 as anonymous (comment approval enabled).
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $anonymous_comment = $this->postComment($this->node, $this->randomName(), '', TRUE);
    $comment_unpublished_loaded = comment_load($anonymous_comment->id);

    // Checks the new values of node comment statistics with comment #2 and
    // ensure they haven't changed since the comment has not been moderated.
    // The node needs to be reloaded with a node_load_multiple cache reset.
    $node = node_load($this->node->nid, NULL, TRUE);
    $this->assertEqual($node->last_comment_name, NULL, 'The value of node last_comment_name is still NULL.');
    $this->assertEqual($node->last_comment_uid, $this->web_user2->uid, 'The value of node last_comment_uid is still the comment #1 uid.');
    $this->assertEqual($node->comment_count, 1, 'The value of node comment_count is still 1.');

    // Prepare for anonymous comment submission (no approval required).
    $this->drupalLogin($this->admin_user);
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => TRUE,
      'post comments' => TRUE,
      'skip comment approval' => TRUE,
    ));
    $this->drupalLogout();

    // Post comment #3 as anonymous.
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $anonymous_comment = $this->postComment($this->node, $this->randomName(), '', array('name' => $this->randomName()));
    $comment_loaded = comment_load($anonymous_comment->id);

    // Checks the new values of node comment statistics with comment #3.
    // The node needs to be reloaded with a node_load_multiple cache reset.
    $node = node_load($this->node->nid, NULL, TRUE);
    $this->assertEqual($node->last_comment_name, $comment_loaded->name, 'The value of node last_comment_name is the name of the anonymous user.');
    $this->assertEqual($node->last_comment_uid, 0, 'The value of node last_comment_uid is zero.');
    $this->assertEqual($node->comment_count, 2, 'The value of node comment_count is 2.');
  }

  /**
   * Tests comment links.
   *
   * The output of comment links depends on various environment conditions:
   * - Various Comment module configuration settings, user registration
   *   settings, and user access permissions.
   * - Whether the user is authenticated or not, and whether any comments exist.
   *
   * To account for all possible cases, this test creates permutations of all
   * possible conditions and tests the expected appearance of comment links in
   * each environment.
   */
  function testCommentLinks() {
    // Bartik theme alters comment links, so use a different theme.
    theme_enable(array('garland'));
    variable_set('theme_default', 'garland');

    // Remove additional user permissions from $this->web_user added by setUp(),
    // since this test is limited to anonymous and authenticated roles only.
    user_role_delete(key($this->web_user->roles));

    // Matrix of possible environmental conditions and configuration settings.
    // See setEnvironment() for details.
    $conditions = array(
      'authenticated'   => array(FALSE, TRUE),
      'comment count'   => array(FALSE, TRUE),
      'access comments' => array(0, 1),
      'post comments'   => array(0, 1),
      'form'            => array(COMMENT_FORM_BELOW, COMMENT_FORM_SEPARATE_PAGE),
      // USER_REGISTER_VISITORS_ADMINISTRATIVE_APPROVAL is irrelevant for this
      // test; there is only a difference between open and closed registration.
      'user_register'   => array(USER_REGISTER_VISITORS, USER_REGISTER_ADMINISTRATORS_ONLY),
      // @todo Complete test coverage for:
      //'comments'        => array(COMMENT_NODE_OPEN, COMMENT_NODE_CLOSED, COMMENT_NODE_HIDDEN),
      //// COMMENT_ANONYMOUS_MUST_CONTACT is irrelevant for this test.
      //'contact '        => array(COMMENT_ANONYMOUS_MAY_CONTACT, COMMENT_ANONYMOUS_MAYNOT_CONTACT),
    );

    $environments = $this->generatePermutations($conditions);
    foreach ($environments as $info) {
      $this->assertCommentLinks($info);
    }
  }

  /**
   * Re-configures the environment, module settings, and user permissions.
   *
   * @param $info
   *   An associative array describing the environment to setup:
   *   - Environment conditions:
   *     - authenticated: Boolean whether to test with $this->web_user or
   *       anonymous.
   *     - comment count: Boolean whether to test with a new/unread comment on
   *       $this->node or no comments.
   *   - Configuration settings:
   *     - form: COMMENT_FORM_BELOW or COMMENT_FORM_SEPARATE_PAGE.
   *     - user_register: USER_REGISTER_ADMINISTRATORS_ONLY or
   *       USER_REGISTER_VISITORS.
   *     - contact: COMMENT_ANONYMOUS_MAY_CONTACT or
   *       COMMENT_ANONYMOUS_MAYNOT_CONTACT.
   *     - comments: COMMENT_NODE_OPEN, COMMENT_NODE_CLOSED, or
   *       COMMENT_NODE_HIDDEN.
   *   - User permissions:
   *     These are granted or revoked for the user, according to the
   *     'authenticated' flag above. Pass 0 or 1 as parameter values. See
   *     user_role_change_permissions().
   *     - access comments
   *     - post comments
   *     - skip comment approval
   *     - edit own comments
   */
  function setEnvironment(array $info) {
    static $current;

    // Apply defaults to initial environment.
    if (!isset($current)) {
      $current = array(
        'authenticated' => FALSE,
        'comment count' => FALSE,
        'form' => COMMENT_FORM_BELOW,
        'user_register' => USER_REGISTER_VISITORS,
        'contact' => COMMENT_ANONYMOUS_MAY_CONTACT,
        'comments' => COMMENT_NODE_OPEN,
        'access comments' => 0,
        'post comments' => 0,
        // Enabled by default, because it's irrelevant for this test.
        'skip comment approval' => 1,
        'edit own comments' => 0,
      );
    }
    // Complete new environment with current environment.
    $info = array_merge($current, $info);

    // Change environment conditions.
    if ($current['authenticated'] != $info['authenticated']) {
      if ($this->loggedInUser) {
        $this->drupalLogout();
      }
      else {
        $this->drupalLogin($this->web_user);
      }
    }
    if ($current['comment count'] != $info['comment count']) {
      if ($info['comment count']) {
        // Create a comment via CRUD API functionality, since
        // $this->postComment() relies on actual user permissions.
        $comment = (object) array(
          'cid' => NULL,
          'nid' => $this->node->nid,
          'node_type' => $this->node->type,
          'pid' => 0,
          'uid' => 0,
          'status' => COMMENT_PUBLISHED,
          'subject' => $this->randomName(),
          'hostname' => ip_address(),
          'language' => LANGUAGE_NONE,
          'comment_body' => array(LANGUAGE_NONE => array($this->randomName())),
        );
        comment_save($comment);
        $this->comment = $comment;

        // comment_num_new() relies on node_last_viewed(), so ensure that no one
        // has seen the node of this comment.
        db_delete('history')->condition('nid', $this->node->nid)->execute();
      }
      else {
        $cids = db_query("SELECT cid FROM {comment}")->fetchCol();
        comment_delete_multiple($cids);
        unset($this->comment);
      }
    }

    // Change comment settings.
    variable_set('comment_form_location_' . $this->node->type, $info['form']);
    variable_set('comment_anonymous_' . $this->node->type, $info['contact']);
    if ($this->node->comment != $info['comments']) {
      $this->node->comment = $info['comments'];
      node_save($this->node);
    }

    // Change user settings.
    variable_set('user_register', $info['user_register']);

    // Change user permissions.
    $rid = ($this->loggedInUser ? DRUPAL_AUTHENTICATED_RID : DRUPAL_ANONYMOUS_RID);
    $perms = array_intersect_key($info, array('access comments' => 1, 'post comments' => 1, 'skip comment approval' => 1, 'edit own comments' => 1));
    user_role_change_permissions($rid, $perms);

    // Output verbose debugging information.
    // @see DrupalTestCase::error()
    $t_form = array(
      COMMENT_FORM_BELOW => 'below',
      COMMENT_FORM_SEPARATE_PAGE => 'separate page',
    );
    $t_contact = array(
      COMMENT_ANONYMOUS_MAY_CONTACT => 'optional',
      COMMENT_ANONYMOUS_MAYNOT_CONTACT => 'disabled',
      COMMENT_ANONYMOUS_MUST_CONTACT => 'required',
    );
    $t_comments = array(
      COMMENT_NODE_OPEN => 'open',
      COMMENT_NODE_CLOSED => 'closed',
      COMMENT_NODE_HIDDEN => 'hidden',
    );
    $verbose = $info;
    $verbose['form'] = $t_form[$info['form']];
    $verbose['contact'] = $t_contact[$info['contact']];
    $verbose['comments'] = $t_comments[$info['comments']];
    $message = t('Changed environment:<pre>@verbose</pre>', array(
      '@verbose' => var_export($verbose, TRUE),
    ));
    $this->assert('debug', $message, 'Debug');

    // Update current environment.
    $current = $info;

    return $info;
  }

  /**
   * Asserts that comment links appear according to the passed environment setup.
   *
   * @param $info
   *   An associative array describing the environment to pass to
   *   setEnvironment().
   */
  function assertCommentLinks(array $info) {
    $info = $this->setEnvironment($info);

    $nid = $this->node->nid;

    foreach (array('', "node/$nid") as $path) {
      $this->drupalGet($path);

      // User is allowed to view comments.
      if ($info['access comments']) {
        if ($path == '') {
          // In teaser view, a link containing the comment count is always
          // expected.
          if ($info['comment count']) {
            $this->assertLink(t('1 comment'));

            // For logged in users, a link containing the amount of new/unread
            // comments is expected.
            // See important note about comment_num_new() below.
            if ($this->loggedInUser && isset($this->comment) && !isset($this->comment->seen)) {
              $this->assertLink(t('1 new comment'));
              $this->comment->seen = TRUE;
            }
          }
        }
      }
      else {
        $this->assertNoLink(t('1 comment'));
        $this->assertNoLink(t('1 new comment'));
      }
      // comment_num_new() is based on node views, so comments are marked as
      // read when a node is viewed, regardless of whether we have access to
      // comments.
      if ($path == "node/$nid" && $this->loggedInUser && isset($this->comment)) {
        $this->comment->seen = TRUE;
      }

      // User is not allowed to post comments.
      if (!$info['post comments']) {
        $this->assertNoLink('Add new comment');

        // Anonymous users should see a note to log in or register in case
        // authenticated users are allowed to post comments.
        // @see theme_comment_post_forbidden()
        if (!$this->loggedInUser) {
          if (user_access('post comments', $this->web_user)) {
            // The note depends on whether users are actually able to register.
            if ($info['user_register']) {
              $this->assertText('Log in or register to post comments');
            }
            else {
              $this->assertText('Log in to post comments');
            }
          }
          else {
            $this->assertNoText('Log in or register to post comments');
            $this->assertNoText('Log in to post comments');
          }
        }
      }
      // User is allowed to post comments.
      else {
        $this->assertNoText('Log in or register to post comments');

        // "Add new comment" is always expected, except when there are no
        // comments or if the user cannot see them.
        if ($path == "node/$nid" && $info['form'] == COMMENT_FORM_BELOW && (!$info['comment count'] || !$info['access comments'])) {
          $this->assertNoLink('Add new comment');
        }
        else {
          $this->assertLink('Add new comment');
        }

        // Also verify that the comment form appears according to the configured
        // location.
        if ($path == "node/$nid") {
          $elements = $this->xpath('//form[@id=:id]', array(':id' => 'comment-form'));
          if ($info['form'] == COMMENT_FORM_BELOW) {
            $this->assertTrue(count($elements), 'Comment form found below.');
          }
          else {
            $this->assertFalse(count($elements), 'Comment form not found below.');
          }
        }
      }
    }
  }
}

/**
 * Test previewing comments.
 */
class CommentPreviewTest extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment preview',
      'description' => 'Test comment preview.',
      'group' => 'Comment',
    );
  }

  /**
   * Test comment preview.
   */
  function testCommentPreview() {
    $langcode = LANGUAGE_NONE;

    // As admin user, configure comment settings.
    $this->drupalLogin($this->admin_user);
    $this->setCommentPreview(DRUPAL_OPTIONAL);
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Comment paging changed.');
    $this->drupalLogout();

    // Login as web user and add a signature and a user picture.
    $this->drupalLogin($this->web_user);
    variable_set('user_signatures', 1);
    variable_set('user_pictures', 1);
    $test_signature = $this->randomName();
    $edit['signature[value]'] = '<a href="http://example.com/">' . $test_signature. '</a>';
    $edit['signature[format]'] = 'filtered_html';
    $image = current($this->drupalGetTestFiles('image'));
    $edit['files[picture_upload]'] = drupal_realpath($image->uri);
    $this->drupalPost('user/' . $this->web_user->uid . '/edit', $edit, t('Save'));

    // As the web user, fill in the comment form and preview the comment.
    $edit = array();
    $edit['subject'] = $this->randomName(8);
    $edit['comment_body[' . $langcode . '][0][value]'] = $this->randomName(16);
    $this->drupalPost('node/' . $this->node->nid, $edit, t('Preview'));

    // Check that the preview is displaying the title and body.
    $this->assertTitle(t('Preview comment | Drupal'), 'Page title is "Preview comment".');
    $this->assertText($edit['subject'], 'Subject displayed.');
    $this->assertText($edit['comment_body[' . $langcode . '][0][value]'], 'Comment displayed.');

    // Check that the title and body fields are displayed with the correct values.
    $this->assertFieldByName('subject', $edit['subject'], 'Subject field displayed.');
    $this->assertFieldByName('comment_body[' . $langcode . '][0][value]', $edit['comment_body[' . $langcode . '][0][value]'], 'Comment field displayed.');

    // Check that the signature is displaying with the correct text format.
    $this->assertLink($test_signature);

    // Check that the user picture is displayed.
    $this->assertFieldByXPath("//div[contains(@class, 'comment-preview')]//div[contains(@class, 'user-picture')]//img", NULL, 'User picture displayed.');
  }

  /**
   * Test comment edit, preview, and save.
   */
  function testCommentEditPreviewSave() {
    $langcode = LANGUAGE_NONE;
    $web_user = $this->drupalCreateUser(array('access comments', 'post comments', 'skip comment approval', 'edit own comments'));
    $this->drupalLogin($this->admin_user);
    $this->setCommentPreview(DRUPAL_OPTIONAL);
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Comment paging changed.');

    $edit = array();
    $edit['subject'] = $this->randomName(8);
    $edit['comment_body[' . $langcode . '][0][value]'] = $this->randomName(16);
    $edit['name'] = $web_user->name;
    $edit['date'] = '2008-03-02 17:23 +0300';
    $raw_date = strtotime($edit['date']);
    $expected_text_date = format_date($raw_date);
    $expected_form_date = format_date($raw_date, 'custom', 'Y-m-d H:i:s O');
    $comment = $this->postComment($this->node, $edit['subject'], $edit['comment_body[' . $langcode . '][0][value]'], TRUE);
    $this->drupalPost('comment/' . $comment->id . '/edit', $edit, t('Preview'));

    // Check that the preview is displaying the subject, comment, author and date correctly.
    $this->assertTitle(t('Preview comment | Drupal'), 'Page title is "Preview comment".');
    $this->assertText($edit['subject'], 'Subject displayed.');
    $this->assertText($edit['comment_body[' . $langcode . '][0][value]'], 'Comment displayed.');
    $this->assertText($edit['name'], 'Author displayed.');
    $this->assertText($expected_text_date, 'Date displayed.');

    // Check that the subject, comment, author and date fields are displayed with the correct values.
    $this->assertFieldByName('subject', $edit['subject'], 'Subject field displayed.');
    $this->assertFieldByName('comment_body[' . $langcode . '][0][value]', $edit['comment_body[' . $langcode . '][0][value]'], 'Comment field displayed.');
    $this->assertFieldByName('name', $edit['name'], 'Author field displayed.');
    $this->assertFieldByName('date', $edit['date'], 'Date field displayed.');

    // Check that saving a comment produces a success message.
    $this->drupalPost('comment/' . $comment->id . '/edit', $edit, t('Save'));
    $this->assertText(t('Your comment has been posted.'), 'Comment posted.');

    // Check that the comment fields are correct after loading the saved comment.
    $this->drupalGet('comment/' . $comment->id . '/edit');
    $this->assertFieldByName('subject', $edit['subject'], 'Subject field displayed.');
    $this->assertFieldByName('comment_body[' . $langcode . '][0][value]', $edit['comment_body[' . $langcode . '][0][value]'], 'Comment field displayed.');
    $this->assertFieldByName('name', $edit['name'], 'Author field displayed.');
    $this->assertFieldByName('date', $expected_form_date, 'Date field displayed.');

    // Submit the form using the displayed values.
    $displayed = array();
    $displayed['subject'] = (string) current($this->xpath("//input[@id='edit-subject']/@value"));
    $displayed['comment_body[' . $langcode . '][0][value]'] = (string) current($this->xpath("//textarea[@id='edit-comment-body-" . $langcode . "-0-value']"));
    $displayed['name'] = (string) current($this->xpath("//input[@id='edit-name']/@value"));
    $displayed['date'] = (string) current($this->xpath("//input[@id='edit-date']/@value"));
    $this->drupalPost('comment/' . $comment->id . '/edit', $displayed, t('Save'));

    // Check that the saved comment is still correct.
    $comment_loaded = comment_load($comment->id);
    $this->assertEqual($comment_loaded->subject, $edit['subject'], 'Subject loaded.');
    $this->assertEqual($comment_loaded->comment_body[$langcode][0]['value'], $edit['comment_body[' . $langcode . '][0][value]'], 'Comment body loaded.');
    $this->assertEqual($comment_loaded->name, $edit['name'], 'Name loaded.');
    $this->assertEqual($comment_loaded->created, $raw_date, 'Date loaded.');
    $this->drupalLogout();

    // Check that the date and time of the comment are correct when edited by
    // non-admin users.
    $user_edit = array();
    $expected_created_time = $comment_loaded->created;
    $this->drupalLogin($web_user);
    $this->drupalPost('comment/' . $comment->id . '/edit', $user_edit, t('Save'));
    $comment_loaded = comment_load($comment->id, TRUE);
    $this->assertEqual($comment_loaded->created, $expected_created_time, 'Expected date and time for comment edited.');
  }

}

class CommentAnonymous extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Anonymous comments',
      'description' => 'Test anonymous comments.',
      'group' => 'Comment',
    );
  }

  function setUp() {
    parent::setUp();
    variable_set('user_register', USER_REGISTER_VISITORS);
  }

  /**
   * Test anonymous comment functionality.
   */
  function testAnonymous() {
    $this->drupalLogin($this->admin_user);
    // Enabled anonymous user comments.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => TRUE,
      'post comments' => TRUE,
      'skip comment approval' => TRUE,
    ));
    $this->setCommentAnonymous('0'); // Ensure that doesn't require contact info.
    $this->drupalLogout();

    // Post anonymous comment without contact info.
    $anonymous_comment1 = $this->postComment($this->node, $this->randomName(), $this->randomName());
    $this->assertTrue($this->commentExists($anonymous_comment1), 'Anonymous comment without contact info found.');

    // Allow contact info.
    $this->drupalLogin($this->admin_user);
    $this->setCommentAnonymous('1');

    // Attempt to edit anonymous comment.
    $this->drupalGet('comment/' . $anonymous_comment1->id . '/edit');
    $edited_comment = $this->postComment(NULL, $this->randomName(), $this->randomName());
    $this->assertTrue($this->commentExists($edited_comment, FALSE), 'Modified reply found.');
    $this->drupalLogout();

    // Post anonymous comment with contact info (optional).
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $this->assertTrue($this->commentContactInfoAvailable(), 'Contact information available.');

    $anonymous_comment2 = $this->postComment($this->node, $this->randomName(), $this->randomName());
    $this->assertTrue($this->commentExists($anonymous_comment2), 'Anonymous comment with contact info (optional) found.');

    // Ensure anonymous users cannot post in the name of registered users.
    $langcode = LANGUAGE_NONE;
    $edit = array(
      'name' => $this->admin_user->name,
      'mail' => $this->randomName() . '@example.com',
      'subject' => $this->randomName(),
      "comment_body[$langcode][0][value]" => $this->randomName(),
    );
    $this->drupalPost('comment/reply/' . $this->node->nid, $edit, t('Save'));
    $this->assertText(t('The name you used belongs to a registered user.'));

    // Require contact info.
    $this->drupalLogin($this->admin_user);
    $this->setCommentAnonymous('2');
    $this->drupalLogout();

    // Try to post comment with contact info (required).
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $this->assertTrue($this->commentContactInfoAvailable(), 'Contact information available.');

    $anonymous_comment3 = $this->postComment($this->node, $this->randomName(), $this->randomName(), TRUE);
    // Name should have 'Anonymous' for value by default.
    $this->assertText(t('E-mail field is required.'), 'E-mail required.');
    $this->assertFalse($this->commentExists($anonymous_comment3), 'Anonymous comment with contact info (required) not found.');

    // Post comment with contact info (required).
    $author_name = $this->randomName();
    $author_mail = $this->randomName() . '@example.com';
    $anonymous_comment3 = $this->postComment($this->node, $this->randomName(), $this->randomName(), array('name' => $author_name, 'mail' => $author_mail));
    $this->assertTrue($this->commentExists($anonymous_comment3), 'Anonymous comment with contact info (required) found.');

    // Make sure the user data appears correctly when editing the comment.
    $this->drupalLogin($this->admin_user);
    $this->drupalGet('comment/' . $anonymous_comment3->id . '/edit');
    $this->assertRaw($author_name, "The anonymous user's name is correct when editing the comment.");
    $this->assertRaw($author_mail, "The anonymous user's e-mail address is correct when editing the comment.");

    // Unpublish comment.
    $this->performCommentOperation($anonymous_comment3, 'unpublish');

    $this->drupalGet('admin/content/comment/approval');
    $this->assertRaw('comments[' . $anonymous_comment3->id . ']', 'Comment was unpublished.');

    // Publish comment.
    $this->performCommentOperation($anonymous_comment3, 'publish', TRUE);

    $this->drupalGet('admin/content/comment');
    $this->assertRaw('comments[' . $anonymous_comment3->id . ']', 'Comment was published.');

    // Delete comment.
    $this->performCommentOperation($anonymous_comment3, 'delete');

    $this->drupalGet('admin/content/comment');
    $this->assertNoRaw('comments[' . $anonymous_comment3->id . ']', 'Comment was deleted.');
    $this->drupalLogout();

    // Reset.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => FALSE,
      'post comments' => FALSE,
      'skip comment approval' => FALSE,
    ));

    // Attempt to view comments while disallowed.
    // NOTE: if authenticated user has permission to post comments, then a
    // "Login or register to post comments" type link may be shown.
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertNoPattern('@<h2[^>]*>Comments</h2>@', 'Comments were not displayed.');
    $this->assertNoLink('Add new comment', 'Link to add comment was found.');

    // Attempt to view node-comment form while disallowed.
    $this->drupalGet('comment/reply/' . $this->node->nid);
    $this->assertText('You are not authorized to post comments', 'Error attempting to post comment.');
    $this->assertNoFieldByName('subject', '', 'Subject field not found.');
    $this->assertNoFieldByName("comment_body[$langcode][0][value]", '', 'Comment field not found.');

    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => TRUE,
      'post comments' => FALSE,
      'skip comment approval' => FALSE,
    ));
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertPattern('@<h2[^>]*>Comments</h2>@', 'Comments were displayed.');
    $this->assertLink('Log in', 1, 'Link to log in was found.');
    $this->assertLink('register', 1, 'Link to register was found.');

    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => FALSE,
      'post comments' => TRUE,
      'skip comment approval' => TRUE,
    ));
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertNoPattern('@<h2[^>]*>Comments</h2>@', 'Comments were not displayed.');
    $this->assertFieldByName('subject', '', 'Subject field found.');
    $this->assertFieldByName("comment_body[$langcode][0][value]", '', 'Comment field found.');

    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $anonymous_comment3->id);
    $this->assertText('You are not authorized to view comments', 'Error attempting to post reply.');
    $this->assertNoText($author_name, 'Comment not displayed.');
  }
}

/**
 * Verify pagination of comments.
 */
class CommentPagerTest extends CommentHelperCase {

  public static function getInfo() {
    return array(
      'name' => 'Comment paging settings',
      'description' => 'Test paging of comments and their settings.',
      'group' => 'Comment',
    );
  }

  /**
   * Confirm comment paging works correctly with flat and threaded comments.
   */
  function testCommentPaging() {
    $this->drupalLogin($this->admin_user);

    // Set comment variables.
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentPreview(DRUPAL_DISABLED);

    // Create a node and three comments.
    $node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $comments = array();
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);

    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_FLAT, 'Comment paging changed.');

    // Set comments to one per page so that we are able to test paging without
    // needing to insert large numbers of comments.
    $this->setCommentsPerPage(1);

    // Check the first page of the node, and confirm the correct comments are
    // shown.
    $this->drupalGet('node/' . $node->nid);
    $this->assertRaw(t('next'), 'Paging links found.');
    $this->assertTrue($this->commentExists($comments[0]), 'Comment 1 appears on page 1.');
    $this->assertFalse($this->commentExists($comments[1]), 'Comment 2 does not appear on page 1.');
    $this->assertFalse($this->commentExists($comments[2]), 'Comment 3 does not appear on page 1.');

    // Check the second page.
    $this->drupalGet('node/' . $node->nid, array('query' => array('page' => 1)));
    $this->assertTrue($this->commentExists($comments[1]), 'Comment 2 appears on page 2.');
    $this->assertFalse($this->commentExists($comments[0]), 'Comment 1 does not appear on page 2.');
    $this->assertFalse($this->commentExists($comments[2]), 'Comment 3 does not appear on page 2.');

    // Check the third page.
    $this->drupalGet('node/' . $node->nid, array('query' => array('page' => 2)));
    $this->assertTrue($this->commentExists($comments[2]), 'Comment 3 appears on page 3.');
    $this->assertFalse($this->commentExists($comments[0]), 'Comment 1 does not appear on page 3.');
    $this->assertFalse($this->commentExists($comments[1]), 'Comment 2 does not appear on page 3.');

    // Post a reply to the oldest comment and test again.
    $replies = array();
    $oldest_comment = reset($comments);
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $oldest_comment->id);
    $reply = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    $this->setCommentsPerPage(2);
    // We are still in flat view - the replies should not be on the first page,
    // even though they are replies to the oldest comment.
    $this->drupalGet('node/' . $node->nid, array('query' => array('page' => 0)));
    $this->assertFalse($this->commentExists($reply, TRUE), 'In flat mode, reply does not appear on page 1.');

    // If we switch to threaded mode, the replies on the oldest comment
    // should be bumped to the first page and comment 6 should be bumped
    // to the second page.
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Switched to threaded mode.');
    $this->drupalGet('node/' . $node->nid, array('query' => array('page' => 0)));
    $this->assertTrue($this->commentExists($reply, TRUE), 'In threaded mode, reply appears on page 1.');
    $this->assertFalse($this->commentExists($comments[1]), 'In threaded mode, comment 2 has been bumped off of page 1.');

    // If (# replies > # comments per page) in threaded expanded view,
    // the overage should be bumped.
    $reply2 = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);
    $this->drupalGet('node/' . $node->nid, array('query' => array('page' => 0)));
    $this->assertFalse($this->commentExists($reply2, TRUE), 'In threaded mode where # replies > # comments per page, the newest reply does not appear on page 1.');

    $this->drupalLogout();
  }

  /**
   * Test comment ordering and threading.
   */
  function testCommentOrderingThreading() {
    $this->drupalLogin($this->admin_user);

    // Set comment variables.
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentPreview(DRUPAL_DISABLED);

    // Display all the comments on the same page.
    $this->setCommentsPerPage(1000);

    // Create a node and three comments.
    $node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $comments = array();
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the second comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $comments[1]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the first comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $comments[0]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the last comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $comments[2]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the second comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $comments[3]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // At this point, the comment tree is:
    // - 0
    //   - 4
    // - 1
    //   - 3
    //     - 6
    // - 2
    //   - 5

    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_FLAT, 'Comment paging changed.');

    $expected_order = array(
      0,
      1,
      2,
      3,
      4,
      5,
      6,
    );
    $this->drupalGet('node/' . $node->nid);
    $this->assertCommentOrder($comments, $expected_order);

    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Switched to threaded mode.');

    $expected_order = array(
      0,
      4,
      1,
      3,
      6,
      2,
      5,
    );
    $this->drupalGet('node/' . $node->nid);
    $this->assertCommentOrder($comments, $expected_order);
  }

  /**
   * Helper function: assert that the comments are displayed in the correct order.
   *
   * @param $comments
   *   And array of comments.
   * @param $expected_order
   *   An array of keys from $comments describing the expected order.
   */
  function assertCommentOrder(array $comments, array $expected_order) {
    $expected_cids = array();

    // First, rekey the expected order by cid.
    foreach ($expected_order as $key) {
      $expected_cids[] = $comments[$key]->id;
    }

    $comment_anchors = $this->xpath('//a[starts-with(@id,"comment-")]');
    $result_order = array();
    foreach ($comment_anchors as $anchor) {
      $result_order[] = substr($anchor['id'], 8);
    }

    return $this->assertIdentical($expected_cids, $result_order, format_string('Comment order: expected @expected, returned @returned.', array('@expected' => implode(',', $expected_cids), '@returned' => implode(',', $result_order))));
  }

  /**
   * Test comment_new_page_count().
   */
  function testCommentNewPageIndicator() {
    $this->drupalLogin($this->admin_user);

    // Set comment variables.
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentPreview(DRUPAL_DISABLED);

    // Set comments to one per page so that we are able to test paging without
    // needing to insert large numbers of comments.
    $this->setCommentsPerPage(1);

    // Create a node and three comments.
    $node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1));
    $comments = array();
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);
    $comments[] = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the second comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $comments[1]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the first comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $comments[0]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the last comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $comments[2]->id);
    $comments[] = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);

    // At this point, the comment tree is:
    // - 0
    //   - 4
    // - 1
    //   - 3
    // - 2
    //   - 5

    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_FLAT, 'Comment paging changed.');

    $expected_pages = array(
      1 => 5, // Page of comment 5
      2 => 4, // Page of comment 4
      3 => 3, // Page of comment 3
      4 => 2, // Page of comment 2
      5 => 1, // Page of comment 1
      6 => 0, // Page of comment 0
    );

    $node = node_load($node->nid);
    foreach ($expected_pages as $new_replies => $expected_page) {
      $returned = comment_new_page_count($node->comment_count, $new_replies, $node);
      $returned_page = is_array($returned) ? $returned['page'] : 0;
      $this->assertIdentical($expected_page, $returned_page, format_string('Flat mode, @new replies: expected page @expected, returned page @returned.', array('@new' => $new_replies, '@expected' => $expected_page, '@returned' => $returned_page)));
    }

    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Switched to threaded mode.');

    $expected_pages = array(
      1 => 5, // Page of comment 5
      2 => 1, // Page of comment 4
      3 => 1, // Page of comment 4
      4 => 1, // Page of comment 4
      5 => 1, // Page of comment 4
      6 => 0, // Page of comment 0
    );

    $node = node_load($node->nid);
    foreach ($expected_pages as $new_replies => $expected_page) {
      $returned = comment_new_page_count($node->comment_count, $new_replies, $node);
      $returned_page = is_array($returned) ? $returned['page'] : 0;
      $this->assertEqual($expected_page, $returned_page, format_string('Threaded mode, @new replies: expected page @expected, returned page @returned.', array('@new' => $new_replies, '@expected' => $expected_page, '@returned' => $returned_page)));
    }
  }
}

/**
 * Tests comments with node access.
 *
 * See http://drupal.org/node/886752 -- verify there is no PostgreSQL error when
 * viewing a node with threaded comments (a comment and a reply), if a node
 * access module is in use.
 */
class CommentNodeAccessTest extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment node access',
      'description' => 'Test comment viewing with node access.',
      'group' => 'Comment',
    );
  }

  function setUp() {
    parent::setUp('search', 'node_access_test');
    node_access_rebuild();

    // Create users and test node.
    $this->admin_user = $this->drupalCreateUser(array('administer content types', 'administer comments', 'administer blocks'));
    $this->web_user = $this->drupalCreateUser(array('access comments', 'post comments', 'create article content', 'edit own comments', 'node test view'));
    $this->node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1, 'uid' => $this->web_user->uid));
  }

  /**
   * Test that threaded comments can be viewed.
   */
  function testThreadedCommentView() {
    $langcode = LANGUAGE_NONE;
    // Set comments to have subject required and preview disabled.
    $this->drupalLogin($this->admin_user);
    $this->setCommentPreview(DRUPAL_DISABLED);
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Comment paging changed.');
    $this->drupalLogout();

    // Post comment.
    $this->drupalLogin($this->web_user);
    $comment_text = $this->randomName();
    $comment_subject = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text, $comment_subject);
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue($this->commentExists($comment), 'Comment found.');

    // Check comment display.
    $this->drupalGet('node/' . $this->node->nid . '/' . $comment->id);
    $this->assertText($comment_subject, 'Individual comment subject found.');
    $this->assertText($comment_text, 'Individual comment body found.');

    // Reply to comment, creating second comment.
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $comment->id);
    $reply_text = $this->randomName();
    $reply_subject = $this->randomName();
    $reply = $this->postComment(NULL, $reply_text, $reply_subject, TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Reply found.');

    // Go to the node page and verify comment and reply are visible.
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertText($comment_text);
    $this->assertText($comment_subject);
    $this->assertText($reply_text);
    $this->assertText($reply_subject);
  }
}

class CommentApprovalTest extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment approval',
      'description' => 'Test comment approval functionality.',
      'group' => 'Comment',
    );
  }

  /**
   * Test comment approval functionality through admin/content/comment.
   */
  function testApprovalAdminInterface() {
    // Set anonymous comments to require approval.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => TRUE,
      'post comments' => TRUE,
      'skip comment approval' => FALSE,
    ));
    $this->drupalLogin($this->admin_user);
    $this->setCommentAnonymous('0'); // Ensure that doesn't require contact info.

    // Test that the comments page loads correctly when there are no comments
    $this->drupalGet('admin/content/comment');
    $this->assertText(t('No comments available.'));

    $this->drupalLogout();

    // Post anonymous comment without contact info.
    $subject = $this->randomName();
    $body = $this->randomName();
    $this->postComment($this->node, $body, $subject, TRUE); // Set $contact to true so that it won't check for id and message.
    $this->assertText(t('Your comment has been queued for review by site administrators and will be published after approval.'), 'Comment requires approval.');

    // Get unapproved comment id.
    $this->drupalLogin($this->admin_user);
    $anonymous_comment4 = $this->getUnapprovedComment($subject);
    $anonymous_comment4 = (object) array('id' => $anonymous_comment4, 'subject' => $subject, 'comment' => $body);
    $this->drupalLogout();

    $this->assertFalse($this->commentExists($anonymous_comment4), 'Anonymous comment was not published.');

    // Approve comment.
    $this->drupalLogin($this->admin_user);
    $this->performCommentOperation($anonymous_comment4, 'publish', TRUE);
    $this->drupalLogout();

    $this->drupalGet('node/' . $this->node->nid);
    $this->assertTrue($this->commentExists($anonymous_comment4), 'Anonymous comment visible.');

    // Post 2 anonymous comments without contact info.
    $comments[] = $this->postComment($this->node, $this->randomName(), $this->randomName(), TRUE);
    $comments[] = $this->postComment($this->node, $this->randomName(), $this->randomName(), TRUE);

    // Publish multiple comments in one operation.
    $this->drupalLogin($this->admin_user);
    $this->drupalGet('admin/content/comment/approval');
    $this->assertText(t('Unapproved comments (@count)', array('@count' => 2)), 'Two unapproved comments waiting for approval.');
    $edit = array(
      "comments[{$comments[0]->id}]" => 1,
      "comments[{$comments[1]->id}]" => 1,
    );
    $this->drupalPost(NULL, $edit, t('Update'));
    $this->assertText(t('Unapproved comments (@count)', array('@count' => 0)), 'All comments were approved.');

    // Delete multiple comments in one operation.
    $edit = array(
      'operation' => 'delete',
      "comments[{$comments[0]->id}]" => 1,
      "comments[{$comments[1]->id}]" => 1,
      "comments[{$anonymous_comment4->id}]" => 1,
    );
    $this->drupalPost(NULL, $edit, t('Update'));
    $this->assertText(t('Are you sure you want to delete these comments and all their children?'), 'Confirmation required.');
    $this->drupalPost(NULL, $edit, t('Delete comments'));
    $this->assertText(t('No comments available.'), 'All comments were deleted.');
  }

  /**
   * Test comment approval functionality through node interface.
   */
  function testApprovalNodeInterface() {
    // Set anonymous comments to require approval.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array(
      'access comments' => TRUE,
      'post comments' => TRUE,
      'skip comment approval' => FALSE,
    ));
    $this->drupalLogin($this->admin_user);
    $this->setCommentAnonymous('0'); // Ensure that doesn't require contact info.
    $this->drupalLogout();

    // Post anonymous comment without contact info.
    $subject = $this->randomName();
    $body = $this->randomName();
    $this->postComment($this->node, $body, $subject, TRUE); // Set $contact to true so that it won't check for id and message.
    $this->assertText(t('Your comment has been queued for review by site administrators and will be published after approval.'), 'Comment requires approval.');

    // Get unapproved comment id.
    $this->drupalLogin($this->admin_user);
    $anonymous_comment4 = $this->getUnapprovedComment($subject);
    $anonymous_comment4 = (object) array('id' => $anonymous_comment4, 'subject' => $subject, 'comment' => $body);
    $this->drupalLogout();

    $this->assertFalse($this->commentExists($anonymous_comment4), 'Anonymous comment was not published.');

    // Approve comment.
    $this->drupalLogin($this->admin_user);
    $this->drupalGet('comment/1/approve');
    $this->assertResponse(403, 'Forged comment approval was denied.');
    $this->drupalGet('comment/1/approve', array('query' => array('token' => 'forged')));
    $this->assertResponse(403, 'Forged comment approval was denied.');
    $this->drupalGet('node/' . $this->node->nid);
    $this->clickLink(t('approve'));
    $this->drupalLogout();

    $this->drupalGet('node/' . $this->node->nid);
    $this->assertTrue($this->commentExists($anonymous_comment4), 'Anonymous comment visible.');
  }
}

/**
 * Functional tests for the comment module blocks.
 */
class CommentBlockFunctionalTest extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment blocks',
      'description' => 'Test comment block functionality.',
      'group' => 'Comment',
    );
  }

  /**
   * Test the recent comments block.
   */
  function testRecentCommentBlock() {
    $this->drupalLogin($this->admin_user);

    // Set the block to a region to confirm block is available.
    $edit = array(
      'blocks[comment_recent][region]' => 'sidebar_first',
    );
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertText(t('The block settings have been updated.'), 'Block saved to first sidebar region.');

    // Set block title and variables.
    $block = array(
      'title' => $this->randomName(),
      'comment_block_count' => 2,
    );
    $this->drupalPost('admin/structure/block/manage/comment/recent/configure', $block, t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block saved.');

    // Add some test comments, one without a subject.
    $comment1 = $this->postComment($this->node, $this->randomName(), $this->randomName());
    $comment2 = $this->postComment($this->node, $this->randomName(), $this->randomName());
    $comment3 = $this->postComment($this->node, $this->randomName());

    // Test that a user without the 'access comments' permission cannot see the
    // block.
    $this->drupalLogout();
    user_role_revoke_permissions(DRUPAL_ANONYMOUS_RID, array('access comments'));
    $this->drupalGet('');
    $this->assertNoText($block['title'], 'Block was not found.');
    user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array('access comments'));

    // Test that a user with the 'access comments' permission can see the
    // block.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('');
    $this->assertText($block['title'], 'Block was found.');

    // Test the only the 2 latest comments are shown and in the proper order.
    $this->assertNoText($comment1->subject, 'Comment not found in block.');
    $this->assertText($comment2->subject, 'Comment found in block.');
    $this->assertText($comment3->comment, 'Comment found in block.');
    $this->assertTrue(strpos($this->drupalGetContent(), $comment3->comment) < strpos($this->drupalGetContent(), $comment2->subject), 'Comments were ordered correctly in block.');

    // Set the number of recent comments to show to 10.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);
    $block = array(
      'comment_block_count' => 10,
    );
    $this->drupalPost('admin/structure/block/manage/comment/recent/configure', $block, t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), 'Block saved.');

    // Post an additional comment.
    $comment4 = $this->postComment($this->node, $this->randomName(), $this->randomName());

    // Test that all four comments are shown.
    $this->assertText($comment1->subject, 'Comment found in block.');
    $this->assertText($comment2->subject, 'Comment found in block.');
    $this->assertText($comment3->comment, 'Comment found in block.');
    $this->assertText($comment4->subject, 'Comment found in block.');

    // Test that links to comments work when comments are across pages.
    $this->setCommentsPerPage(1);
    $this->drupalGet('');
    $this->clickLink($comment1->subject);
    $this->assertText($comment1->subject, 'Comment link goes to correct page.');
    $this->drupalGet('');
    $this->clickLink($comment2->subject);
    $this->assertText($comment2->subject, 'Comment link goes to correct page.');
    $this->clickLink($comment4->subject);
    $this->assertText($comment4->subject, 'Comment link goes to correct page.');
    // Check that when viewing a comment page from a link to the comment, that
    // rel="canonical" is added to the head of the document.
    $this->assertRaw('<link rel="canonical"', 'Canonical URL was found in the HTML head');
  }
}

/**
 * Unit tests for comment module integration with RSS feeds.
 */
class CommentRSSUnitTest extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment RSS',
      'description' => 'Test comments as part of an RSS feed.',
      'group' => 'Comment',
    );
  }

  /**
   * Test comments as part of an RSS feed.
   */
  function testCommentRSS() {
    // Find comment in RSS feed.
    $this->drupalLogin($this->web_user);
    $comment = $this->postComment($this->node, $this->randomName(), $this->randomName());
    $this->drupalGet('rss.xml');
    $raw = '<comments>' . url('node/' . $this->node->nid, array('fragment' => 'comments', 'absolute' => TRUE)) . '</comments>';
    $this->assertRaw($raw, 'Comments as part of RSS feed.');

    // Hide comments from RSS feed and check presence.
    $this->node->comment = COMMENT_NODE_HIDDEN;
    node_save($this->node);
    $this->drupalGet('rss.xml');
    $this->assertNoRaw($raw, 'Hidden comments is not a part of RSS feed.');
  }
}


/**
 * Test to make sure comment content is rebuilt.
 */
class CommentContentRebuild extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment Rebuild',
      'description' => 'Test to make sure the comment content is rebuilt.',
      'group' => 'Comment',
    );
  }

  /**
   * Test to ensure that the comment's content array is rebuilt for every
   * call to comment_view().
   */
  function testCommentRebuild() {
    // Update the comment settings so preview isn't required.
    $this->drupalLogin($this->admin_user);
    $this->setCommentSubject(TRUE);
    $this->setCommentPreview(DRUPAL_OPTIONAL);
    $this->drupalLogout();

    // Log in as the web user and add the comment.
    $this->drupalLogin($this->web_user);
    $subject_text = $this->randomName();
    $comment_text = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text, $subject_text, TRUE);
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue($this->commentExists($comment), 'Comment found.');

    // Add the property to the content array and then see if it still exists on build.
    $comment_loaded->content['test_property'] = array('#value' => $this->randomString());
    $built_content = comment_view($comment_loaded, $this->node);

    // This means that the content was rebuilt as the added test property no longer exists.
    $this->assertFalse(isset($built_content['test_property']), 'Comment content was emptied before being built.');
  }
}

/**
 * Test comment token replacement in strings.
 */
class CommentTokenReplaceTestCase extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment token replacement',
      'description' => 'Generates text using placeholders for dummy content to check comment token replacement.',
      'group' => 'Comment',
    );
  }

  /**
   * Creates a comment, then tests the tokens generated from it.
   */
  function testCommentTokenReplacement() {
    global $language;
    $url_options = array(
      'absolute' => TRUE,
      'language' => $language,
    );

    $this->drupalLogin($this->admin_user);

    // Set comment variables.
    $this->setCommentSubject(TRUE);

    // Create a node and a comment.
    $node = $this->drupalCreateNode(array('type' => 'article'));
    $parent_comment = $this->postComment($node, $this->randomName(), $this->randomName(), TRUE);

    // Post a reply to the comment.
    $this->drupalGet('comment/reply/' . $node->nid . '/' . $parent_comment->id);
    $child_comment = $this->postComment(NULL, $this->randomName(), $this->randomName());
    $comment = comment_load($child_comment->id);
    $comment->homepage = 'http://example.org/';

    // Add HTML to ensure that sanitation of some fields tested directly.
    $comment->subject = '<blink>Blinking Comment</blink>';
    $instance = field_info_instance('comment', 'body', 'comment_body');

    // Generate and test sanitized tokens.
    $tests = array();
    $tests['[comment:cid]'] = $comment->cid;
    $tests['[comment:hostname]'] = check_plain($comment->hostname);
    $tests['[comment:name]'] = filter_xss($comment->name);
    $tests['[comment:mail]'] = check_plain($this->admin_user->mail);
    $tests['[comment:homepage]'] = check_url($comment->homepage);
    $tests['[comment:title]'] = filter_xss($comment->subject);
    $tests['[comment:body]'] = _text_sanitize($instance, LANGUAGE_NONE, $comment->comment_body[LANGUAGE_NONE][0], 'value');
    $tests['[comment:url]'] = url('comment/' . $comment->cid, $url_options + array('fragment' => 'comment-' . $comment->cid));
    $tests['[comment:edit-url]'] = url('comment/' . $comment->cid . '/edit', $url_options);
    $tests['[comment:created:since]'] = format_interval(REQUEST_TIME - $comment->created, 2, $language->language);
    $tests['[comment:changed:since]'] = format_interval(REQUEST_TIME - $comment->changed, 2, $language->language);
    $tests['[comment:parent:cid]'] = $comment->pid;
    $tests['[comment:parent:title]'] = check_plain($parent_comment->subject);
    $tests['[comment:node:nid]'] = $comment->nid;
    $tests['[comment:node:title]'] = check_plain($node->title);
    $tests['[comment:author:uid]'] = $comment->uid;
    $tests['[comment:author:name]'] = check_plain($this->admin_user->name);

    // Test to make sure that we generated something for each token.
    $this->assertFalse(in_array(0, array_map('strlen', $tests)), 'No empty tokens generated.');

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('comment' => $comment), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Sanitized comment token %token replaced.', array('%token' => $input)));
    }

    // Generate and test unsanitized tokens.
    $tests['[comment:hostname]'] = $comment->hostname;
    $tests['[comment:name]'] = $comment->name;
    $tests['[comment:mail]'] = $this->admin_user->mail;
    $tests['[comment:homepage]'] = $comment->homepage;
    $tests['[comment:title]'] = $comment->subject;
    $tests['[comment:body]'] = $comment->comment_body[LANGUAGE_NONE][0]['value'];
    $tests['[comment:parent:title]'] = $parent_comment->subject;
    $tests['[comment:node:title]'] = $node->title;
    $tests['[comment:author:name]'] = $this->admin_user->name;

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('comment' => $comment), array('language' => $language, 'sanitize' => FALSE));
      $this->assertEqual($output, $expected, format_string('Unsanitized comment token %token replaced.', array('%token' => $input)));
    }

    // Load node so comment_count gets computed.
    $node = node_load($node->nid);

    // Generate comment tokens for the node (it has 2 comments, both new).
    $tests = array();
    $tests['[node:comment-count]'] = 2;
    $tests['[node:comment-count-new]'] = 2;

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('node' => $node), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Node comment token %token replaced.', array('%token' => $input)));
    }
  }
}

/**
 * Test actions provided by the comment module.
 */
class CommentActionsTestCase extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment actions',
      'description' => 'Test actions provided by the comment module.',
      'group' => 'Comment',
    );
  }

  /**
   * Test comment publish and unpublish actions.
   */
  function testCommentPublishUnpublishActions() {
    $this->drupalLogin($this->web_user);
    $comment_text = $this->randomName();
    $subject = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text, $subject);
    $comment = comment_load($comment->id);

    // Unpublish a comment (direct form: doesn't actually save the comment).
    comment_unpublish_action($comment);
    $this->assertEqual($comment->status, COMMENT_NOT_PUBLISHED, 'Comment was unpublished');
    $this->assertWatchdogMessage('Unpublished comment %subject.', array('%subject' => $subject), 'Found watchdog message');
    $this->clearWatchdog();

    // Unpublish a comment (indirect form: modify the comment in the database).
    comment_unpublish_action(NULL, array('cid' => $comment->cid));
    $this->assertEqual(comment_load($comment->cid)->status, COMMENT_NOT_PUBLISHED, 'Comment was unpublished');
    $this->assertWatchdogMessage('Unpublished comment %subject.', array('%subject' => $subject), 'Found watchdog message');

    // Publish a comment (direct form: doesn't actually save the comment).
    comment_publish_action($comment);
    $this->assertEqual($comment->status, COMMENT_PUBLISHED, 'Comment was published');
    $this->assertWatchdogMessage('Published comment %subject.', array('%subject' => $subject), 'Found watchdog message');
    $this->clearWatchdog();

    // Publish a comment (indirect form: modify the comment in the database).
    comment_publish_action(NULL, array('cid' => $comment->cid));
    $this->assertEqual(comment_load($comment->cid)->status, COMMENT_PUBLISHED, 'Comment was published');
    $this->assertWatchdogMessage('Published comment %subject.', array('%subject' => $subject), 'Found watchdog message');
    $this->clearWatchdog();
  }

  /**
   * Tests the unpublish comment by keyword action.
   */
  public function testCommentUnpublishByKeyword() {
    $this->drupalLogin($this->admin_user);
    $callback = 'comment_unpublish_by_keyword_action';
    $hash = drupal_hash_base64($callback);
    $comment_text = $keywords = $this->randomName();
    $edit = array(
      'actions_label' => $callback,
      'keywords' => $keywords,
    );

    $this->drupalPost("admin/config/system/actions/configure/$hash", $edit, t('Save'));

    $action = db_query("SELECT aid, type, callback, parameters, label FROM {actions} WHERE callback = :callback", array(':callback' => $callback))->fetchObject();

    $this->assertTrue($action, 'The action could be loaded.');

    $comment = $this->postComment($this->node, $comment_text, $this->randomName());

    // Load the full comment so that status is available.
    $comment = comment_load($comment->id);

    $this->assertTrue($comment->status == COMMENT_PUBLISHED, 'The comment status was set to published.');

    comment_unpublish_by_keyword_action($comment, array('keywords' => array($keywords)));

    // We need to make sure that the comment has been saved with status
    // unpublished.
    $this->assertEqual(comment_load($comment->cid)->status, COMMENT_NOT_PUBLISHED, 'Comment was unpublished.');
    $this->assertWatchdogMessage('Unpublished comment %subject.', array('%subject' => $comment->subject), 'Found watchdog message.');
    $this->clearWatchdog();
  }

  /**
   * Test comment publish and unpublish hooks.
   */
  function testCommentPublishUnpublishHooks() {
    $this->drupalLogin($this->web_user);
    module_enable(array('comment_hook_test'));
    $_SESSION['comment_hook_test'] = array();

    // Publishing a comment should trigger hook_comment_publish().
    $comment = (object) array(
      'cid' => NULL,
      'nid' => $this->node->nid,
      'node_type' => $this->node->type,
      'pid' => 0,
      'uid' => $this->loggedInUser->uid,
      'status' => COMMENT_PUBLISHED,
      'subject' => $this->randomName(),
      'hostname' => ip_address(),
      'language' => LANGUAGE_NONE,
      'comment_body' => array(LANGUAGE_NONE => array($this->randomName())),
    );
    comment_save($comment);
    $this->assertHookMessage('comment_hook_test_comment_publish called');

    // Unpublishing a comment should trigger hook_comment_unpublish().
    $comment = comment_load($comment->cid);
    $comment->status = COMMENT_NOT_PUBLISHED;
    comment_save($comment);
    $this->assertHookMessage('comment_hook_test_comment_unpublish called');
  }

  /**
   * Verify that a watchdog message has been entered.
   *
   * @param $watchdog_message
   *   The watchdog message.
   * @param $variables
   *   The array of variables passed to watchdog().
   * @param $message
   *   The assertion message.
   */
  function assertWatchdogMessage($watchdog_message, $variables, $message) {
    $status = (bool) db_query_range("SELECT 1 FROM {watchdog} WHERE message = :message AND variables = :variables", 0, 1, array(':message' => $watchdog_message, ':variables' => serialize($variables)))->fetchField();
    return $this->assert($status, format_string('@message', array('@message' => $message)));
  }

  /**
   * Helper function: clear the watchdog.
   */
  function clearWatchdog() {
    db_truncate('watchdog')->execute();
  }

  /**
   * Pass if the message $text was set by one of the comment hooks in
   * comment_hook_test.module, i.e., if the $text is an element of
   * $_SESSION['comment_hook_test'].
   *
   * @param $text
   *   Plain text to look for.
   * @param $message
   *   Message to display.
   * @param $group
   *   The group this message belongs to, defaults to 'Other'.
   * @return
   *   TRUE on pass, FALSE on fail.
   */
  protected function assertHookMessage($text, $message = NULL, $group = 'Other') {
    if (!isset($message)) {
      $message = $text;
    }
    return $this->assertTrue(array_search($text, $_SESSION['comment_hook_test']) !== FALSE, $message, $group);
  }
}

/**
 * Test fields on comments.
 */
class CommentFieldsTest extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment fields',
      'description' => 'Tests fields on comments.',
      'group' => 'Comment',
    );
  }

  /**
   * Tests that the default 'comment_body' field is correctly added.
   */
  function testCommentDefaultFields() {
    // Do not make assumptions on default node types created by the test
    // installation profile, and create our own.
    $this->drupalCreateContentType(array('type' => 'test_node_type'));

    // Check that the 'comment_body' field is present on all comment bundles.
    $instances = field_info_instances('comment');
    foreach (node_type_get_types() as $type_name => $info) {
      $this->assertTrue(isset($instances['comment_node_' . $type_name]['comment_body']), format_string('The comment_body field is present for comments on type @type', array('@type' => $type_name)));

      // Delete the instance along the way.
      field_delete_instance($instances['comment_node_' . $type_name]['comment_body']);
    }

    // Check that the 'comment_body' field is deleted.
    $field = field_info_field('comment_body');
    $this->assertTrue(empty($field), 'The comment_body field was deleted');

    // Create a new content type.
    $type_name = 'test_node_type_2';
    $this->drupalCreateContentType(array('type' => $type_name));

    // Check that the 'comment_body' field exists and has an instance on the
    // new comment bundle.
    $field = field_info_field('comment_body');
    $this->assertTrue($field, 'The comment_body field exists');
    $instances = field_info_instances('comment');
    $this->assertTrue(isset($instances['comment_node_' . $type_name]['comment_body']), format_string('The comment_body field is present for comments on type @type', array('@type' => $type_name)));
  }

  /**
   * Test that comment module works when enabled after a content module.
   */
  function testCommentEnable() {
    // Create a user to do module administration.
    $this->admin_user = $this->drupalCreateUser(array('access administration pages', 'administer modules'));
    $this->drupalLogin($this->admin_user);

    // Disable the comment module.
    $edit = array();
    $edit['modules[Core][comment][enable]'] = FALSE;
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->resetAll();
    $this->assertFalse(module_exists('comment'), 'Comment module disabled.');

    // Enable core content type modules (blog, book, and poll).
    $edit = array();
    $edit['modules[Core][blog][enable]'] = 'blog';
    $edit['modules[Core][book][enable]'] = 'book';
    $edit['modules[Core][poll][enable]'] = 'poll';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->resetAll();

    // Now enable the comment module.
    $edit = array();
    $edit['modules[Core][comment][enable]'] = 'comment';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->resetAll();
    $this->assertTrue(module_exists('comment'), 'Comment module enabled.');

    // Create nodes of each type.
    $blog_node = $this->drupalCreateNode(array('type' => 'blog'));
    $book_node = $this->drupalCreateNode(array('type' => 'book'));
    $poll_node = $this->drupalCreateNode(array('type' => 'poll', 'active' => 1, 'runtime' => 0, 'choice' => array(array('chtext' => ''))));

    $this->drupalLogout();

    // Try to post a comment on each node. A failure will be triggered if the
    // comment body is missing on one of these forms, due to postComment()
    // asserting that the body is actually posted correctly.
    $this->web_user = $this->drupalCreateUser(array('access content', 'access comments', 'post comments', 'skip comment approval'));
    $this->drupalLogin($this->web_user);
    $this->postComment($blog_node, $this->randomName(), $this->randomName());
    $this->postComment($book_node, $this->randomName(), $this->randomName());
    $this->postComment($poll_node, $this->randomName(), $this->randomName());
  }

  /**
   * Test that comment module works correctly with plain text format.
   */
  function testCommentFormat() {
    // Disable text processing for comments.
    $this->drupalLogin($this->admin_user);
    $edit = array('instance[settings][text_processing]' => 0);
    $this->drupalPost('admin/structure/types/manage/article/comment/fields/comment_body', $edit, t('Save settings'));

    // Post a comment without an explicit subject.
    $this->drupalLogin($this->web_user);
    $edit = array('comment_body[und][0][value]' => $this->randomName(8));
    $this->drupalPost('node/' . $this->node->nid, $edit, t('Save'));
  }
}

/**
 * Tests comment threading.
 */
class CommentThreadingTestCase extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment Threading',
      'description' => 'Test to make sure the comment number increments properly.',
      'group' => 'Comment',
    );
  }

  /**
   * Tests the comment threading.
   */
  function testCommentThreading() {
    $langcode = LANGUAGE_NONE;
    // Set comments to have a subject with preview disabled.
    $this->drupalLogin($this->admin_user);
    $this->setCommentPreview(DRUPAL_DISABLED);
    $this->setCommentForm(TRUE);
    $this->setCommentSubject(TRUE);
    $this->setCommentSettings('comment_default_mode', COMMENT_MODE_THREADED, 'Comment paging changed.');
    $this->drupalLogout();

    // Create a node.
    $this->drupalLogin($this->web_user);
    $this->node = $this->drupalCreateNode(array('type' => 'article', 'promote' => 1, 'uid' => $this->web_user->uid));

    // Post comment #1.
    $this->drupalLogin($this->web_user);
    $subject_text = $this->randomName();
    $comment_text = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text, $subject_text, TRUE);
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue($this->commentExists($comment), 'Comment #1. Comment found.');
    $this->assertEqual($comment_loaded->thread, '01/');

    // Reply to comment #1 creating comment #2.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $comment->id);
    $reply = $this->postComment(NULL, $this->randomName(), '', TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Comment #2. Reply found.');
    $this->assertEqual($reply_loaded->thread, '01.00/');

    // Reply to comment #2 creating comment #3.
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $reply->id);
    $reply = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Comment #3. Second reply found.');
    $this->assertEqual($reply_loaded->thread, '01.00.00/');

    // Reply to comment #1 creating comment #4.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $comment->id);
    $reply = $this->postComment(NULL, $this->randomName(), '', TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($comment), 'Comment #4. Third reply found.');
    $this->assertEqual($reply_loaded->thread, '01.01/');

    // Post comment #2 overall comment #5.
    $this->drupalLogin($this->web_user);
    $subject_text = $this->randomName();
    $comment_text = $this->randomName();
    $comment = $this->postComment($this->node, $comment_text, $subject_text, TRUE);
    $comment_loaded = comment_load($comment->id);
    $this->assertTrue($this->commentExists($comment), 'Comment #5. Second comment found.');
    $this->assertEqual($comment_loaded->thread, '02/');

    // Reply to comment #5 creating comment #6.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $comment->id);
    $reply = $this->postComment(NULL, $this->randomName(), '', TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Comment #6. Reply found.');
    $this->assertEqual($reply_loaded->thread, '02.00/');

    // Reply to comment #6 creating comment #7.
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $reply->id);
    $reply = $this->postComment(NULL, $this->randomName(), $this->randomName(), TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($reply, TRUE), 'Comment #7. Second reply found.');
    $this->assertEqual($reply_loaded->thread, '02.00.00/');

    // Reply to comment #5 creating comment #8.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('comment/reply/' . $this->node->nid . '/' . $comment->id);
    $reply = $this->postComment(NULL, $this->randomName(), '', TRUE);
    $reply_loaded = comment_load($reply->id);
    $this->assertTrue($this->commentExists($comment), 'Comment #8. Third reply found.');
    $this->assertEqual($reply_loaded->thread, '02.01/');
  }
}

/**
 * Tests that comments behave correctly when the node is changed.
 */
class CommentNodeChangesTestCase extends CommentHelperCase {

  public static function getInfo() {
    return array(
      'name' => 'Comment deletion on node changes',
      'description' => 'Tests that comments behave correctly when the node is changed.',
      'group' => 'Comment',
    );
  }

  /**
   * Tests that comments are deleted with the node.
   */
  function testNodeDeletion() {
    $this->drupalLogin($this->web_user);
    $comment = $this->postComment($this->node, $this->randomName(), $this->randomName());
    $this->assertTrue(comment_load($comment->id), 'The comment could be loaded.');
    node_delete($this->node->nid);
    $this->assertFalse(comment_load($comment->id), 'The comment could not be loaded after the node was deleted.');
  }
}

/**
 * Tests the behavior of comments when the comment author is deleted.
 */
class CommentAuthorDeletionTestCase extends CommentHelperCase {
  public static function getInfo() {
    return array(
      'name' => 'Comment author deletion',
      'description' => 'Test the behavior of comments when the comment author is deleted.',
      'group' => 'Comment',
    );
  }

  /**
   * Tests that comments are correctly deleted when their author is deleted.
   */
  function testAuthorDeletion() {
    // Create a comment as the admin user.
    $this->drupalLogin($this->admin_user);
    $comment = $this->postComment($this->node, $this->randomName());
    $this->assertTrue($this->commentExists($comment), t('Comment is displayed initially.'));
    $this->drupalLogout();

    // Delete the admin user, and check that the node which displays the
    // comment can still be viewed, but the comment itself does not appear
    // there.
    user_delete($this->admin_user->uid);
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertResponse(200, t('Node page is accessible after the comment author is deleted.'));
    $this->assertFalse($this->commentExists($comment), t('Comment is not displayed after the comment author is deleted.'));
  }

  /**
   * Test comment author deletion while the comment module is disabled.
   */
  function testAuthorDeletionCommentModuleDisabled() {
    // Create a comment as the admin user.
    $this->drupalLogin($this->admin_user);
    $comment = $this->postComment($this->node, $this->randomName());
    $this->assertTrue($this->commentExists($comment), t('Comment is displayed initially.'));
    $this->drupalLogout();

    // Delete the admin user while the comment module is disabled, and ensure
    // that the missing comment author is still handled correctly (the node
    // itself should be displayed, but the comment should not be displayed on
    // it).
    module_disable(array('comment'));
    user_delete($this->admin_user->uid);
    module_enable(array('comment'));
    $this->drupalGet('node/' . $this->node->nid);
    $this->assertResponse(200, t('Node page is accessible after the comment author is deleted.'));
    $this->assertFalse($this->commentExists($comment), t('Comment is not displayed after the comment author is deleted.'));
  }
}

/**
 * Tests uninstalling the comment module.
 */
class CommentUninstallTestCase extends CommentHelperCase {

  public static function getInfo() {
    return array(
      'name' => 'Comment module uninstallation',
      'description' => 'Tests that the comments module can be properly uninstalled.',
      'group' => 'Comment',
    );
  }

  function testCommentUninstall() {
    $this->drupalLogin($this->super_user);

    // Disable comment module.
    $edit['modules[Core][comment][enable]'] = FALSE;
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Comment module was disabled.');

    // Uninstall comment module.
    $edit = array('uninstall[comment]' => 'comment');
    $this->drupalPost('admin/modules/uninstall', $edit, t('Uninstall'));
    $this->drupalPost(NULL, NULL, t('Uninstall'));
    $this->assertText(t('The selected modules have been uninstalled.'), 'Comment module was uninstalled.');

    // Run cron and clear field cache so that comment fields and instances
    // marked for deletion are actually removed.
    $this->cronRun();
    field_cache_clear();

    // Verify that comment fields have been removed.
    $all_fields = array_keys(field_info_field_map());
    $this->assertFalse(in_array('comment_body', $all_fields), 'Comment fields were removed by uninstall.');

    // Verify that comment field instances have been removed (or at least marked
    // for deletion).
    // N.B. field_read_instances does an INNER JOIN on field_config so if the
    // comment_body row has been removed successfully from there no instances
    // will be returned, but that does not guarantee that no rows are left over
    // in the field_config_instance table.
    $count = db_select('field_config_instance', 'fci')
      ->condition('entity_type', 'comment')
      ->condition('field_name', 'comment_body')
      ->condition('deleted', 0)
      ->countQuery()
      ->execute()
      ->fetchField();
    $this->assertTrue($count == 0, 'Comment field instances were removed by uninstall.');
  }
}
