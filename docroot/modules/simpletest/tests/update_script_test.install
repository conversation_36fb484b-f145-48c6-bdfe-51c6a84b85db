<?php

/**
 * @file
 * Install, update and uninstall functions for the update_script_test module.
 */

/**
 * Implements hook_requirements().
 */
function update_script_test_requirements($phase) {
  $requirements = array();

  if ($phase == 'update') {
    // Set a requirements warning or error when the test requests it.
    $requirement_type = variable_get('update_script_test_requirement_type');
    switch ($requirement_type) {
      case REQUIREMENT_WARNING:
        $requirements['update_script_test'] = array(
          'title' => 'Update script test',
          'value' => 'Warning',
          'description' => 'This is a requirements warning provided by the update_script_test module.',
          'severity' => REQUIREMENT_WARNING,
        );
        break;
      case REQUIREMENT_ERROR:
        $requirements['update_script_test'] = array(
          'title' => 'Update script test',
          'value' => 'Error',
          'description' => 'This is a requirements error provided by the update_script_test module.',
          'severity' => REQUIREMENT_ERROR,
        );
        break;
      case REQUIREMENT_INFO:
        $requirements['update_script_test_stop'] = array(
          'title' => 'Update script test stop',
          'value' => 'Error',
          'description' => 'This is a requirements error provided by the update_script_test module to stop the page redirect for the info.',
          'severity' => REQUIREMENT_ERROR,
        );
        $requirements['update_script_test'] = array(
          'title' => 'Update script test',
          'description' => 'This is a requirements info provided by the update_script_test module.',
          'severity' => REQUIREMENT_INFO,
        );
        break;
    }
  }

  return $requirements;
}

/**
 * Dummy update function to run during the tests.
 */
function update_script_test_update_7000() {
  return t('The update_script_test_update_7000() update was executed successfully.');
}
