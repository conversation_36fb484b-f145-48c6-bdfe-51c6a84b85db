<?php

/**
 * @file
 * Module to help test hook_url_inbound_alter() and hook_url_outbound_alter().
 */

/**
 * Implements hook_menu().
 */
function url_alter_test_menu() {
  $items['url-alter-test/foo'] = array(
    'title' => 'Foo',
    'page callback' => 'url_alter_test_foo',
    'access arguments' => array('access content'),
    'type' => MENU_CALLBACK,
  );
  return $items;
}

/**
 * Menu callback.
 */
function url_alter_test_foo() {
  print 'current_path=' . current_path() . ' request_path=' . request_path();
  exit;
}

/**
 * Implements hook_url_inbound_alter().
 */
function url_alter_test_url_inbound_alter(&$path, $original_path, $path_language) {
  if (!request_path() && !empty($_GET['q'])) {
    drupal_set_message("\$_GET['q'] is non-empty with an empty request path.");
  }

  // Rewrite user/username to user/uid.
  if (preg_match('!^user/([^/]+)(/.*)?!', $path, $matches)) {
    if ($account = user_load_by_name($matches[1])) {
      $matches += array(2 => '');
      $path = 'user/' . $account->uid . $matches[2];
    }
  }

  // Rewrite community/ to forum/.
  if ($path == 'community' || strpos($path, 'community/') === 0) {
    $path = 'forum' . substr($path, 9);
  }

  if ($path == 'url-alter-test/bar') {
    $path = 'url-alter-test/foo';
  }
}

/**
 * Implements hook_url_outbound_alter().
 */
function url_alter_test_url_outbound_alter(&$path, &$options, $original_path) {
  // Rewrite user/uid to user/username.
  if (preg_match('!^user/([0-9]+)(/.*)?!', (string) $path, $matches)) {
    if ($account = user_load($matches[1])) {
      $matches += array(2 => '');
      $path = 'user/' . $account->name . $matches[2];
    }
  }

  // Rewrite forum/ to community/.
  if ($path == 'forum' || strpos((string) $path, 'forum/') === 0) {
    $path = 'community' . substr($path, 5);
  }
}
