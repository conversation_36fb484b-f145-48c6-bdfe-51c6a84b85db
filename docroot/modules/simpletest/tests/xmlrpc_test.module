<?php

function xmlrpc_test_arrayOfStructsTest($array) {
  $sum = 0;
  foreach ($array as $struct) {
    if (isset($struct['curly'])) {
      $sum += $struct['curly'];
    }
  }
  return $sum;
}

function xmlrpc_test_countTheEntities($string) {
  return array(
    'ctLeftAngleBrackets' => substr_count($string, '<'),
    'ctRightAngleBrackets' => substr_count($string, '>'),
    'ctAmpersands' => substr_count($string, '&'),
    'ctApostrophes' => substr_count($string, "'"),
    'ctQuotes' => substr_count($string, '"'),
  );
}

function xmlrpc_test_easyStructTest($array) {
  return $array["curly"] + $array["moe"] + $array["larry"];
}

function xmlrpc_test_echoStructTest($array) {
  return $array;
}

function xmlrpc_test_manyTypesTest($number, $boolean, $string, $double, $dateTime, $base64) {
  $timestamp = gmmktime($dateTime->hour, $dateTime->minute, $dateTime->second, $dateTime->month, $dateTime->day, $dateTime->year);
  return array($number, $boolean, $string, $double, xmlrpc_date($timestamp), xmlrpc_Base64($base64));
}

function xmlrpc_test_moderateSizeArrayCheck($array) {
  return array_shift($array) . array_pop($array);
}

function xmlrpc_test_nestedStructTest($array) {
  return $array["2000"]["04"]["01"]["larry"] + $array["2000"]["04"]["01"]["moe"] + $array["2000"]["04"]["01"]["curly"];
}

function xmlrpc_test_simpleStructReturnTest($number) {
  return array("times10" => ($number*10), "times100" => ($number*100), "times1000" => ($number*1000));
}

/**
 * Implements hook_xmlrpc().
 */
function xmlrpc_test_xmlrpc() {
  return array(
    'validator1.arrayOfStructsTest' => 'xmlrpc_test_arrayOfStructsTest',
    'validator1.countTheEntities' => 'xmlrpc_test_countTheEntities',
    'validator1.easyStructTest' => 'xmlrpc_test_easyStructTest',
    'validator1.echoStructTest' => 'xmlrpc_test_echoStructTest',
    'validator1.manyTypesTest' => 'xmlrpc_test_manyTypesTest',
    'validator1.moderateSizeArrayCheck' => 'xmlrpc_test_moderateSizeArrayCheck',
    'validator1.nestedStructTest' => 'xmlrpc_test_nestedStructTest',
    'validator1.simpleStructReturnTest' => 'xmlrpc_test_simpleStructReturnTest',
    'messages.messageSizedInKB' => 'xmlrpc_test_message_sized_in_kb',
  );
}

/**
 * Implements hook_xmlrpc_alter().
 *
 * Hide (or not) the system.methodSignature() service depending on a variable.
 */
function xmlrpc_test_xmlrpc_alter(&$services) {
  if (variable_get('xmlrpc_test_xmlrpc_alter', FALSE)) {
    $remove = NULL;
    foreach ($services as $key => $value) {
      if (!is_array($value)) {
        continue;
      }
      if ($value[0] == 'system.methodSignature') {
        $remove = $key;
        break;
      }
    }
    if (isset($remove)) {
      unset($services[$remove]);
    }
  }
}

/**
 * Created a message of the desired size in KB.
 *
 * @param $size
 *   Message size in KB.
 * @return array
 *   Generated message structure.
 */
function xmlrpc_test_message_sized_in_kb($size) {
  $message = array();

  $word = 'abcdefg';

  // Create a ~1KB sized struct.
  for ($i = 0 ; $i < 128; $i++) {
    $line['word_' . $i] = $word;
  }

  for ($i = 0; $i < $size; $i++) {
    $message['line_' . $i] = $line;
  }

  return $message;
}
