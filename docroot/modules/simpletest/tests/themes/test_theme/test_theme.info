name = Test theme
description = Theme for testing the theme system
core = 7.x
hidden = TRUE

; Normally, themes may list CSS files like this, and if they exist in the theme
; folder, then they get added to the page. If they have the same file name as a
; module CSS file, then the theme's version overrides the module's version, so
; that the module's version is not added to the page. Additionally, a theme may
; have an entry like this one, without having the corresponding CSS file in the
; theme's folder, and in this case, it just stops the module's version from
; being loaded, and does not replace it with an alternate version. We have this
; here in order for a test to ensure that this correctly prevents the module
; version from being loaded, and that errors aren't caused by the lack of this
; file within the theme folder.
stylesheets[all][] = system.base.css

settings[theme_test_setting] = default value

; Information added by Drupal.org packaging script on 2023-06-07
version = "7.98"
project = "drupal"
datestamp = "1686154435"
