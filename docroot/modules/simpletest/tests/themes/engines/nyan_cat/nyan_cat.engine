<?php

/**
 * @file
 * Handles integration of Nyan cat templates because we love kittens.
 */

/**
 * Includes .theme file from themes.
 */
function nyan_cat_init($template) {
  $file = dirname($template->filename) . '/template.theme';
  if (file_exists($file)) {
    include_once DRUPAL_ROOT . '/' . $file;
  }
}

/**
 * Implements hook_theme().
 */
function nyan_cat_theme($existing, $type, $theme, $path) {
  $templates = drupal_find_theme_functions($existing, array($theme));
  $templates += drupal_find_theme_templates($existing, '.nyan-cat.html', $path);
  return $templates;
}

/**
 * Implements hook_extension().
 */
function nyan_cat_extension() {
  return '.nyan-cat.html';
}

/**
 * Implements hook_render_template().
 *
 * @param string $template_file
 *   The filename of the template to render.
 * @param mixed[] $variables
 *   A keyed array of variables that will appear in the output.
 *
 * @return string
 *   The output generated by the template.
 */
function nyan_cat_render_template($template_file, $variables) {
  $output = str_replace('div', 'nyancat', file_get_contents(DRUPAL_ROOT . '/' . $template_file));
  foreach ($variables as $key => $variable) {
    if (strpos($output, '9' . $key) !== FALSE) {
      $output = str_replace('9' . $key, $variable, $output);
    }
  }
  return $output;
}
