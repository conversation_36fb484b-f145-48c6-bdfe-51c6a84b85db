<?php

/**
 * @file
 * Install, update and uninstall functions for the update_test_1 module.
 */

/**
 * Implements hook_update_dependencies().
 *
 * @see update_test_2_update_dependencies()
 */
function update_test_1_update_dependencies() {
  // These dependencies are used in combination with those declared in
  // update_test_2_update_dependencies() for the sole purpose of testing that
  // the results of hook_update_dependencies() are collected correctly and have
  // the correct array structure. Therefore, we use updates from System module
  // (which have already run), so that they will not get in the way of other
  // tests.
  $dependencies['system'][7000] = array(
    // Compare to update_test_2_update_dependencies(), where the same System
    // module update function is forced to depend on an update function from a
    // different module. This allows us to test that both dependencies are
    // correctly recorded.
    'update_test_1' => 7000,
  );
  $dependencies['system'][7001] = array(
    // Compare to update_test_2_update_dependencies(), where the same System
    // module update function is forced to depend on a different update
    // function within the same module. This allows us to test that only the
    // dependency on the higher-numbered update function is recorded.
    'update_test_1' => 7002,
  );
  return $dependencies;
}

/**
 * Dummy update_test_1 update 7000.
 */
function update_test_1_update_7000() {
}

/**
 * Dummy update_test_1 update 7001.
 */
function update_test_1_update_7001() {
}

/**
 * Dummy update_test_1 update 7002.
 */
function update_test_1_update_7002() {
}
