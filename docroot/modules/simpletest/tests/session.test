<?php

/**
 * @file
 * Provides SimpleTests for core session handling functionality.
 */

class SessionTestCase extends DrupalWebTestCase {
  protected $_logged_in;

  public static function getInfo() {
    return array(
      'name' => 'Session tests',
      'description' => 'Drupal session handling tests.',
      'group' => 'Session'
    );
  }

  function setUp() {
    parent::setUp('session_test');
  }

  /**
   * Tests for drupal_save_session() and drupal_session_regenerate().
   */
  function testSessionSaveRegenerate() {
    $this->assertFalse(drupal_save_session(), 'drupal_save_session() correctly returns FALSE (inside of testing framework) when initially called with no arguments.', 'Session');
    $this->assertFalse(drupal_save_session(FALSE), 'drupal_save_session() correctly returns FALSE when called with FALSE.', 'Session');
    $this->assertFalse(drupal_save_session(), 'drupal_save_session() correctly returns FALSE when saving has been disabled.', 'Session');
    $this->assertTrue(drupal_save_session(TRUE), 'drupal_save_session() correctly returns TRUE when called with TRUE.', 'Session');
    $this->assertTrue(drupal_save_session(), 'drupal_save_session() correctly returns TRUE when saving has been enabled.', 'Session');

    // Test session hardening code from SA-2008-044.
    $user = $this->drupalCreateUser(array('access content'));

    // Enable sessions.
    $this->sessionReset($user->uid);

    // Make sure the session cookie is set as HttpOnly.
    $this->drupalLogin($user);
    $this->assertTrue(preg_match('/HttpOnly/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie is set as HttpOnly.');
    $this->drupalLogout();

    // Verify that the session is regenerated if a module calls exit
    // in hook_user_login().
    user_save($user, array('name' => 'session_test_user'));
    $user->name = 'session_test_user';
    $this->drupalGet('session-test/id');
    $matches = array();
    preg_match('/\s*session_id:(.*)\n/', $this->drupalGetContent(), $matches);
    $this->assertTrue(!empty($matches[1]) , 'Found session ID before logging in.');
    $original_session = $matches[1];

    // We cannot use $this->drupalLogin($user); because we exit in
    // session_test_user_login() which breaks a normal assertion.
    $edit = array(
      'name' => $user->name,
      'pass' => $user->pass_raw
    );
    $this->drupalPost('user', $edit, t('Log in'));
    $this->drupalGet('user');
    $pass = $this->assertText($user->name, format_string('Found name: %name', array('%name' => $user->name)), 'User login');
    $this->_logged_in = $pass;

    $this->drupalGet('session-test/id');
    $matches = array();
    preg_match('/\s*session_id:(.*)\n/', $this->drupalGetContent(), $matches);
    $this->assertTrue(!empty($matches[1]) , 'Found session ID after logging in.');
    $this->assertTrue($matches[1] != $original_session, 'Session ID changed after login.');
  }

  /**
   * Test data persistence via the session_test module callbacks.
   */
  function testDataPersistence() {
    $user = $this->drupalCreateUser(array('access content'));
    // Enable sessions.
    $this->sessionReset($user->uid);

    $this->drupalLogin($user);

    $value_1 = $this->randomName();
    $this->drupalGet('session-test/set/' . $value_1);
    $this->assertText($value_1, 'The session value was stored.', 'Session');
    $this->drupalGet('session-test/get');
    $this->assertText($value_1, 'Session correctly returned the stored data for an authenticated user.', 'Session');

    // Attempt to write over val_1. If drupal_save_session(FALSE) is working.
    // properly, val_1 will still be set.
    $value_2 = $this->randomName();
    $this->drupalGet('session-test/no-set/' . $value_2);
    $this->assertText($value_2, 'The session value was correctly passed to session-test/no-set.', 'Session');
    $this->drupalGet('session-test/get');
    $this->assertText($value_1, 'Session data is not saved for drupal_save_session(FALSE).', 'Session');

    // Switch browser cookie to anonymous user, then back to user 1.
    $this->sessionReset();
    $this->sessionReset($user->uid);
    $this->assertText($value_1, 'Session data persists through browser close.', 'Session');

    // Logout the user and make sure the stored value no longer persists.
    $this->drupalLogout();
    $this->sessionReset();
    $this->drupalGet('session-test/get');
    $this->assertNoText($value_1, "After logout, previous user's session data is not available.", 'Session');

    // Now try to store some data as an anonymous user.
    $value_3 = $this->randomName();
    $this->drupalGet('session-test/set/' . $value_3);
    $this->assertText($value_3, 'Session data stored for anonymous user.', 'Session');
    $this->drupalGet('session-test/get');
    $this->assertText($value_3, 'Session correctly returned the stored data for an anonymous user.', 'Session');

    // Try to store data when drupal_save_session(FALSE).
    $value_4 = $this->randomName();
    $this->drupalGet('session-test/no-set/' . $value_4);
    $this->assertText($value_4, 'The session value was correctly passed to session-test/no-set.', 'Session');
    $this->drupalGet('session-test/get');
    $this->assertText($value_3, 'Session data is not saved for drupal_save_session(FALSE).', 'Session');

    // Login, the data should persist.
    $this->drupalLogin($user);
    $this->sessionReset($user->uid);
    $this->drupalGet('session-test/get');
    $this->assertNoText($value_1, 'Session has persisted for an authenticated user after logging out and then back in.', 'Session');

    // Change session and create another user.
    $user2 = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user2->uid);
    $this->drupalLogin($user2);
  }

  /**
   * Test that empty anonymous sessions are destroyed.
   */
  function testEmptyAnonymousSession() {
    // Verify that no session is automatically created for anonymous user.
    $this->drupalGet('');
    $this->assertSessionCookie(FALSE);
    $this->assertSessionEmpty(TRUE);

    // The same behavior is expected when caching is enabled.
    variable_set('cache', 1);
    $this->drupalGet('');
    $this->assertSessionCookie(FALSE);
    $this->assertSessionEmpty(TRUE);
    $this->assertEqual($this->drupalGetHeader('X-Drupal-Cache'), 'MISS', 'Page was not cached.');

    // Start a new session by setting a message.
    $this->drupalGet('session-test/set-message');
    $this->assertSessionCookie(TRUE);
    $this->assertTrue($this->drupalGetHeader('Set-Cookie'), 'New session was started.');

    // Display the message, during the same request the session is destroyed
    // and the session cookie is unset.
    $this->drupalGet('');
    $this->assertSessionCookie(FALSE);
    $this->assertSessionEmpty(FALSE);
    $this->assertFalse($this->drupalGetHeader('X-Drupal-Cache'), 'Caching was bypassed.');
    $this->assertText(t('This is a dummy message.'), 'Message was displayed.');
    $this->assertTrue(preg_match('/SESS\w+=deleted/', $this->drupalGetHeader('Set-Cookie')), 'Session cookie was deleted.');

    // Verify that session was destroyed.
    $this->drupalGet('');
    $this->assertSessionCookie(FALSE);
    $this->assertSessionEmpty(TRUE);
    $this->assertNoText(t('This is a dummy message.'), 'Message was not cached.');
    $this->assertEqual($this->drupalGetHeader('X-Drupal-Cache'), 'HIT', 'Page was cached.');
    $this->assertFalse($this->drupalGetHeader('Set-Cookie'), 'New session was not started.');

    // Verify that no session is created if drupal_save_session(FALSE) is called.
    $this->drupalGet('session-test/set-message-but-dont-save');
    $this->assertSessionCookie(FALSE);
    $this->assertSessionEmpty(TRUE);

    // Verify that no message is displayed.
    $this->drupalGet('');
    $this->assertSessionCookie(FALSE);
    $this->assertSessionEmpty(TRUE);
    $this->assertNoText(t('This is a dummy message.'), 'The message was not saved.');
  }

  /**
   * Test that sessions are only saved when necessary.
   */
  function testSessionWrite() {
    $user = $this->drupalCreateUser(array('access content'));
    $this->drupalLogin($user);

    $sql = 'SELECT u.access, s.timestamp FROM {users} u INNER JOIN {sessions} s ON u.uid = s.uid WHERE u.uid = :uid';
    $times1 = db_query($sql, array(':uid' => $user->uid))->fetchObject();

    // Before every request we sleep one second to make sure that if the session
    // is saved, its timestamp will change.

    // Modify the session.
    sleep(1);
    $this->drupalGet('session-test/set/foo');
    $times2 = db_query($sql, array(':uid' => $user->uid))->fetchObject();
    $this->assertEqual($times2->access, $times1->access, 'Users table was not updated.');
    $this->assertNotEqual($times2->timestamp, $times1->timestamp, 'Sessions table was updated.');

    // Write the same value again, i.e. do not modify the session.
    sleep(1);
    $this->drupalGet('session-test/set/foo');
    $times3 = db_query($sql, array(':uid' => $user->uid))->fetchObject();
    $this->assertEqual($times3->access, $times1->access, 'Users table was not updated.');
    $this->assertEqual($times3->timestamp, $times2->timestamp, 'Sessions table was not updated.');

    // Do not change the session.
    sleep(1);
    $this->drupalGet('');
    $times4 = db_query($sql, array(':uid' => $user->uid))->fetchObject();
    $this->assertEqual($times4->access, $times3->access, 'Users table was not updated.');
    $this->assertEqual($times4->timestamp, $times3->timestamp, 'Sessions table was not updated.');

    // Force updating of users and sessions table once per second.
    variable_set('session_write_interval', 0);
    $this->drupalGet('');
    $times5 = db_query($sql, array(':uid' => $user->uid))->fetchObject();
    $this->assertNotEqual($times5->access, $times4->access, 'Users table was updated.');
    $this->assertNotEqual($times5->timestamp, $times4->timestamp, 'Sessions table was updated.');
  }

  /**
   * Test that empty session IDs are not allowed.
   */
  function testEmptySessionID() {
    $user = $this->drupalCreateUser(array('access content'));
    $this->drupalLogin($user);
    $this->drupalGet('session-test/is-logged-in');
    $this->assertResponse(200, 'User is logged in.');

    // Reset the sid in {sessions} to a blank string. This may exist in the
    // wild in some cases, although we normally prevent it from happening.
    db_query("UPDATE {sessions} SET sid = '' WHERE uid = :uid", array(':uid' => $user->uid));
    // Send a blank sid in the session cookie, and the session should no longer
    // be valid. Closing the curl handler will stop the previous session ID
    // from persisting.
    $this->curlClose();
    $this->additionalCurlOptions[CURLOPT_COOKIE] = rawurlencode($this->session_name) . '=;';
    $this->drupalGet('session-test/id-from-cookie');
    $this->assertRaw("session_id:\n", 'Session ID is blank as sent from cookie header.');
    // Assert that we have an anonymous session now.
    $this->drupalGet('session-test/is-logged-in');
    $this->assertResponse(403, 'An empty session ID is not allowed.');
  }

  /**
   * Test hashing of session ids in the database.
   */
  function testHashedSessionIds() {
    $user = $this->drupalCreateUser(array('access content'));
    $this->drupalLogin($user);
    $this->drupalGet('session-test/is-logged-in');
    $this->assertResponse(200, 'User is logged in.');

    $this->drupalGet('session-test/id');
    $matches = array();
    preg_match('/\s*session_id:(.*)\n/', $this->drupalGetContent(), $matches);
    $this->assertTrue(!empty($matches[1]) , 'Found session ID after logging in.');
    $session_id = $matches[1];

    $this->drupalGet('session-test/id-from-cookie');
    $matches = array();
    preg_match('/\s*session_id:(.*)\n/', $this->drupalGetContent(), $matches);
    $this->assertTrue(!empty($matches[1]) , 'Found session ID from cookie.');
    $cookie_session_id = $matches[1];

    $this->assertEqual($session_id, $cookie_session_id, 'Session id and cookie session id are the same.');

    $sql = 'SELECT s.sid FROM {users} u INNER JOIN {sessions} s ON u.uid = s.uid WHERE u.uid = :uid';
    $db_session = db_query($sql, array(':uid' => $user->uid))->fetchObject();

    $this->assertNotEqual($db_session->sid, $cookie_session_id, 'Session id in the database is not the same as in the session cookie.');
    $this->assertEqual($db_session->sid, drupal_hash_base64($cookie_session_id), 'Session id in the database is the cookie session id hashed.');
  }

  /**
   * Test opt-out of hashing of session ids in the database.
   */
  function testHashedSessionIdsOptOut() {
    variable_set('do_not_hash_session_ids', TRUE);

    $user = $this->drupalCreateUser(array('access content'));
    $this->drupalLogin($user);
    $this->drupalGet('session-test/is-logged-in');
    $this->assertResponse(200, 'User is logged in.');

    $this->drupalGet('session-test/id');
    $matches = array();
    preg_match('/\s*session_id:(.*)\n/', $this->drupalGetContent(), $matches);
    $this->assertTrue(!empty($matches[1]) , 'Found session ID after logging in.');
    $session_id = $matches[1];

    $this->drupalGet('session-test/id-from-cookie');
    $matches = array();
    preg_match('/\s*session_id:(.*)\n/', $this->drupalGetContent(), $matches);
    $this->assertTrue(!empty($matches[1]) , 'Found session ID from cookie.');
    $cookie_session_id = $matches[1];

    $this->assertEqual($session_id, $cookie_session_id, 'Session id and cookie session id are the same.');

    $sql = 'SELECT s.sid FROM {users} u INNER JOIN {sessions} s ON u.uid = s.uid WHERE u.uid = :uid';
    $db_session = db_query($sql, array(':uid' => $user->uid))->fetchObject();

    $this->assertEqual($db_session->sid, $cookie_session_id, 'Session id in the database is the same as in the session cookie.');
    $this->assertNotEqual($db_session->sid, drupal_hash_base64($cookie_session_id), 'Session id in the database is not the cookie session id hashed.');
  }

  /**
   * Test absence of SameSite attribute on session cookies by default.
   */
  function testNoSameSiteCookieAttributeDefault() {
    $user = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user->uid);
    if (\PHP_VERSION_ID < 70300) {
      $this->drupalLogin($user);
    }
    else {
      // PHP often defaults to an empty value for session.cookie_samesite but
      // that may vary, so we set an explicit empty value.
      // Send our own login POST so that we can pass a custom header to trigger
      // session_test.module to call ini_set('session.cookie_samesite', $value)
      $headers[] = 'X-Session-Cookie-Ini-Set: *EMPTY*';
      $edit = array(
        'name' => $user->name,
        'pass' => $user->pass_raw,
      );
      $this->drupalPost('user', $edit, t('Log in'), array(), $headers);
    }
    $this->assertFalse(preg_match('/SameSite=/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie has no SameSite attribute (default).');
  }

  /**
   * Test SameSite attribute = None by default on Secure session cookies.
   */
  function testSameSiteCookieAttributeNoneSecure() {
    $user = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user->uid);
    $headers = array();
    if (\PHP_VERSION_ID >= 70300) {
      // Send our own login POST so that we can pass a custom header to trigger
      // session_test.module to call ini_set('session.cookie_samesite', $value)
      $headers[] = 'X-Session-Cookie-Ini-Set: None';
    }
    // Test HTTPS session handling by altering the form action to submit the
    // login form through https.php, which creates a mock HTTPS request.
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $form[0]['action'] = $this->httpsUrl('user');
    $edit = array('name' => $user->name, 'pass' => $user->pass_raw);
    $this->drupalPost(NULL, $edit, t('Log in'), array(), $headers);
    $this->assertTrue(preg_match('/SameSite=None/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie is set as SameSite=None.');
  }

  /**
   * Test SameSite attribute = None on session cookies.
   */
  function testSameSiteCookieAttributeNone() {
    variable_set('samesite_cookie_value', 'None');
    $user = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user->uid);
    $this->drupalLogin($user);
    $this->assertTrue(preg_match('/SameSite=None/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie is set as SameSite=None.');
  }

  /**
   * Test SameSite attribute = Lax on session cookies.
   */
  function testSameSiteCookieAttributeLax() {
    variable_set('samesite_cookie_value', 'Lax');
    $user = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user->uid);
    $this->drupalLogin($user);
    $this->assertTrue(preg_match('/SameSite=Lax/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie is set as SameSite=Lax.');
  }

  /**
   * Test SameSite attribute = Strict on session cookies.
   */
  function testSameSiteCookieAttributeStrict() {
    variable_set('samesite_cookie_value', 'Strict');
    $user = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user->uid);
    $this->drupalLogin($user);
    $this->assertTrue(preg_match('/SameSite=Strict/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie is set as SameSite=Strict.');
  }

  /**
   * Test disabling the samesite attribute on session cookies via $conf
   */
  function testSameSiteCookieAttributeDisabledViaConf() {
    $user = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user->uid);
    variable_set('samesite_cookie_value', FALSE);
    if (\PHP_VERSION_ID < 70300) {
      // There is no session.cookie_samesite in earlier PHP versions.
      $this->drupalLogin($user);
    }
    else {
      // Send our own login POST so that we can pass a custom header to trigger
      // session_test.module to call ini_set('session.cookie_samesite', $value)
      $headers[] = 'X-Session-Cookie-Ini-Set: Lax';
      $edit = array(
        'name' => $user->name,
        'pass' => $user->pass_raw,
      );
      $this->drupalPost('user', $edit, t('Log in'), array(), $headers);
    }
    $this->assertFalse(preg_match('/SameSite=/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie has no SameSite attribute (conf).');
  }

  /**
   * Test disabling the samesite attribute on session cookies via php ini
   */
  function testSameSiteCookieAttributeDisabledViaPhpIni() {
    if (\PHP_VERSION_ID < 70300) {
      // There is no session.cookie_samesite in earlier PHP versions.
      $this->pass('This test is only for PHP 7.3 and later.');
      return;
    }
    $user = $this->drupalCreateUser(array('access content'));
    // Send our own login POST so that we can pass a custom header to trigger
    // session_test.module to call ini_set('session.cookie_samesite', $value)
    $headers[] = 'X-Session-Cookie-Ini-Set: *EMPTY*';
    $edit = array(
      'name' => $user->name,
      'pass' => $user->pass_raw,
    );
    $this->drupalPost('user', $edit, t('Log in'), array(), $headers);
    $this->assertFalse(preg_match('/SameSite=/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie has no SameSite attribute (ini).');
  }

  /**
   * Test that a PHP setting for session.cookie_samesite is not overridden by
   * the default value in Drupal, without a samesite_cookie_value variable.
   */
  function testSamesiteCookiePhpSettingLax() {
    if (\PHP_VERSION_ID < 70300) {
      // There is no session.cookie_samesite in earlier PHP versions.
      $this->pass('This test is only for PHP 7.3 and later.');
      return;
    }
    $user = $this->drupalCreateUser(array('access content'));
    // Send our own login POST so that we can pass a custom header to trigger
    // session_test.module to call ini_set('session.cookie_samesite', $value)
    $headers[] = 'X-Session-Cookie-Ini-Set: Lax';
    $edit = array(
      'name' => $user->name,
      'pass' => $user->pass_raw,
    );
    $this->drupalPost('user', $edit, t('Log in'), array(), $headers);
    $this->assertTrue(preg_match('/SameSite=Lax/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie is set as SameSite=Lax.');
  }

  /**
   * Test overriding the PHP setting for session.cookie_samesite with the
   * samesite_cookie_value variable.
   */
  function testSamesiteCookieOverrideLaxToStrict() {
    if (\PHP_VERSION_ID < 70300) {
      // There is no session.cookie_samesite in earlier PHP versions.
      $this->pass('This test is only for PHP 7.3 and later.');
      return;
    }
    variable_set('samesite_cookie_value', 'Strict');
    $user = $this->drupalCreateUser(array('access content'));
    // Send our own login POST so that we can pass a custom header to trigger
    // session_test.module to call ini_set('session.cookie_samesite', $value)
    $headers[] = 'X-Session-Cookie-Ini-Set: Lax';
    $edit = array(
      'name' => $user->name,
      'pass' => $user->pass_raw,
    );
    $this->drupalPost('user', $edit, t('Log in'), array(), $headers);
    $this->assertTrue(preg_match('/SameSite=Strict/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie is set as SameSite=Strict.');
  }

  /**
   * Test SameSite attribute = Lax on set-cookie header on logout.
   */
  function testSamesiteCookieLogoutLax() {
    variable_set('samesite_cookie_value', 'Lax');
    $user = $this->drupalCreateUser(array('access content'));
    $this->sessionReset($user->uid);
    $this->drupalLogin($user);
    $this->drupalGet('user/logout');
    $this->assertTrue(preg_match('/SameSite=Lax/i', $this->drupalGetHeader('Set-Cookie', TRUE)), 'Session cookie deletion includes SameSite=Lax.');
  }

  /**
   * Reset the cookie file so that it refers to the specified user.
   *
   * @param $uid User id to set as the active session.
   */
  function sessionReset($uid = 0) {
    // Close the internal browser.
    $this->curlClose();
    $this->loggedInUser = FALSE;

    // Change cookie file for user.
    $this->cookieFile = file_stream_wrapper_get_instance_by_scheme('temporary')->getDirectoryPath() . '/cookie.' . $uid . '.txt';
    $this->additionalCurlOptions[CURLOPT_COOKIEFILE] = $this->cookieFile;
    $this->additionalCurlOptions[CURLOPT_COOKIESESSION] = TRUE;
    $this->drupalGet('session-test/get');
    $this->assertResponse(200, 'Session test module is correctly enabled.', 'Session');
  }

  /**
   * Assert whether the SimpleTest browser sent a session cookie.
   */
  function assertSessionCookie($sent) {
    if ($sent) {
      $this->assertNotNull($this->session_id, 'Session cookie was sent.');
    }
    else {
      $this->assertNull($this->session_id, 'Session cookie was not sent.');
    }
  }

  /**
   * Assert whether $_SESSION is empty at the beginning of the request.
   */
  function assertSessionEmpty($empty) {
    if ($empty) {
      $this->assertIdentical($this->drupalGetHeader('X-Session-Empty'), '1', 'Session was empty.');
    }
    else {
      $this->assertIdentical($this->drupalGetHeader('X-Session-Empty'), '0', 'Session was not empty.');
    }
  }

  /**
   * Builds a URL for submitting a mock HTTPS request to HTTP test environments.
   *
   * @param $url
   *   A Drupal path such as 'user'.
   *
   * @return
   *   An absolute URL.
   */
  protected function httpsUrl($url) {
    global $base_url;
    return $base_url . '/modules/simpletest/tests/https.php?q=' . $url;
  }
}

/**
 * Ensure that when running under HTTPS two session cookies are generated.
 */
class SessionHttpsTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Session HTTPS handling',
      'description' => 'Ensure that when running under HTTPS two session cookies are generated.',
      'group' => 'Session'
    );
  }

  public function setUp() {
    parent::setUp('session_test');
  }

  protected function testHttpsSession() {
    global $is_https;

    if ($is_https) {
      $secure_session_name = session_name();
      $insecure_session_name = substr(session_name(), 1);
    }
    else {
      $secure_session_name = 'S' . session_name();
      $insecure_session_name = session_name();
    }

    $user = $this->drupalCreateUser(array('access administration pages'));

    // Test HTTPS session handling by altering the form action to submit the
    // login form through https.php, which creates a mock HTTPS request.
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $form[0]['action'] = $this->httpsUrl('user');
    $edit = array('name' => $user->name, 'pass' => $user->pass_raw);
    $this->drupalPost(NULL, $edit, t('Log in'));

    // Test a second concurrent session.
    $this->curlClose();
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $form[0]['action'] = $this->httpsUrl('user');
    $this->drupalPost(NULL, $edit, t('Log in'));

    // Check secure cookie on secure page.
    $this->assertTrue($this->cookies[$secure_session_name]['secure'], 'The secure cookie has the secure attribute');
    // Check insecure cookie is not set.
    $this->assertFalse(isset($this->cookies[$insecure_session_name]));
    $ssid = $this->cookies[$secure_session_name]['value'];
    $this->assertSessionIds($ssid, $ssid, 'Session has a non-empty SID and a correct secure SID.');
    $cookie = $secure_session_name . '=' . $ssid;

    // Verify that user is logged in on secure URL.
    $this->curlClose();
    $this->drupalGet($this->httpsUrl('admin/config'), array(), array('Cookie: ' . $cookie));
    $this->assertText(t('Configuration'));
    $this->assertResponse(200);

    // Verify that user is not logged in on non-secure URL.
    $this->curlClose();
    $this->drupalGet($this->httpUrl('admin/config'), array(), array('Cookie: ' . $cookie));
    $this->assertNoText(t('Configuration'));
    $this->assertResponse(403);

    // Verify that empty SID cannot be used on the non-secure site.
    $this->curlClose();
    $cookie = $insecure_session_name . '=';
    $this->drupalGet($this->httpUrl('admin/config'), array(), array('Cookie: ' . $cookie));
    $this->assertResponse(403);

    // Test HTTP session handling by altering the form action to submit the
    // login form through http.php, which creates a mock HTTP request on HTTPS
    // test environments.
    $this->curlClose();
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $form[0]['action'] = $this->httpUrl('user');
    $edit = array('name' => $user->name, 'pass' => $user->pass_raw);
    $this->drupalPost(NULL, $edit, t('Log in'));
    $this->drupalGet($this->httpUrl('admin/config'));
    $this->assertResponse(200);
    $sid = $this->cookies[$insecure_session_name]['value'];
    $this->assertSessionIds($sid, '', 'Session has the correct SID and an empty secure SID.');

    // Verify that empty secure SID cannot be used on the secure site.
    $this->curlClose();
    $cookie = $secure_session_name . '=';
    $this->drupalGet($this->httpsUrl('admin/config'), array(), array('Cookie: ' . $cookie));
    $this->assertResponse(403);

    // Clear browser cookie jar.
    $this->cookies = array();

    if ($is_https) {
      // The functionality does not make sense when running on HTTPS.
      return;
    }

    // Enable secure pages.
    variable_set('https', TRUE);

    $this->curlClose();
    // Start an anonymous session on the insecure site.
    $session_data = $this->randomName();
    $this->drupalGet('session-test/set/' . $session_data);
    // Check secure cookie on insecure page.
    $this->assertFalse(isset($this->cookies[$secure_session_name]), 'The secure cookie is not sent on insecure pages.');
    // Check insecure cookie on insecure page.
    $this->assertFalse($this->cookies[$insecure_session_name]['secure'], 'The insecure cookie does not have the secure attribute');

    // Store the anonymous cookie so we can validate that its session is killed
    // after login.
    $anonymous_cookie = $insecure_session_name . '=' . $this->cookies[$insecure_session_name]['value'];

    // Check that password request form action is not secure.
    $this->drupalGet('user/password');
    $form = $this->xpath('//form[@id="user-pass"]');
    $this->assertNotEqual(substr($form[0]['action'], 0, 6), 'https:', 'Password request form action is not secure');
    $form[0]['action'] = $this->httpsUrl('user');

    // Check that user login form action is secure.
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $this->assertEqual(substr($form[0]['action'], 0, 6), 'https:', 'Login form action is secure');
    $form[0]['action'] = $this->httpsUrl('user');

    $edit = array(
      'name' => $user->name,
      'pass' => $user->pass_raw,
    );
    $this->drupalPost(NULL, $edit, t('Log in'));
    // Check secure cookie on secure page.
    $this->assertTrue($this->cookies[$secure_session_name]['secure'], 'The secure cookie has the secure attribute');
    // Check insecure cookie on secure page.
    $this->assertFalse($this->cookies[$insecure_session_name]['secure'], 'The insecure cookie does not have the secure attribute');

    $sid = $this->cookies[$insecure_session_name]['value'];
    $ssid = $this->cookies[$secure_session_name]['value'];
    $this->assertSessionIds($sid, $ssid, 'Session has both secure and insecure SIDs');
    $cookies = array(
      $insecure_session_name . '=' . $sid,
      $secure_session_name . '=' . $ssid,
    );

    // Test that session data saved before login is still available on the
    // authenticated session.
    $this->drupalGet('session-test/get');
    $this->assertText($session_data, 'Session correctly returned the stored data set by the anonymous session.');

    foreach ($cookies as $cookie_key => $cookie) {
      foreach (array('admin/config', $this->httpsUrl('admin/config')) as $url_key => $url) {
        $this->curlClose();

        $this->drupalGet($url, array(), array('Cookie: ' . $cookie));
        if ($cookie_key == $url_key) {
          $this->assertText(t('Configuration'));
          $this->assertResponse(200);
        }
        else {
          $this->assertNoText(t('Configuration'));
          $this->assertResponse(403);
        }
      }
    }

    // Test that session data saved before login is not available using the
    // pre-login anonymous cookie.
    $this->cookies = array();
    $this->drupalGet('session-test/get', array('Cookie: ' . $anonymous_cookie));
    $this->assertNoText($session_data, 'Initial anonymous session is inactive after login.');

    // Clear browser cookie jar.
    $this->cookies = array();

    // Start an anonymous session on the secure site.
    $this->drupalGet($this->httpsUrl('session-test/set/1'));

    // Mock a login to the secure site using the secure session cookie.
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $form[0]['action'] = $this->httpsUrl('user');
    $this->drupalPost(NULL, $edit, t('Log in'));

    // Test that the user is also authenticated on the insecure site.
    $this->drupalGet("user/{$user->uid}/edit");
    $this->assertResponse(200);
  }

  /**
   * Tests that empty session IDs do not cause unrelated sessions to load.
   */
  public function testEmptySessionId() {
    global $is_https;

    if ($is_https) {
      $secure_session_name = session_name();
    }
    else {
      $secure_session_name = 'S' . session_name();
    }

    // Enable mixed mode for HTTP and HTTPS.
    variable_set('https', TRUE);

    $admin_user = $this->drupalCreateUser(array('access administration pages'));
    $standard_user = $this->drupalCreateUser(array('access content'));

    // First log in as the admin user on HTTP.
    // We cannot use $this->drupalLogin() here because we need to use the
    // special http.php URLs.
    $edit = array(
      'name' => $admin_user->name,
      'pass' => $admin_user->pass_raw
    );
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $form[0]['action'] = $this->httpUrl('user');
    $this->drupalPost(NULL, $edit, t('Log in'));

    $this->curlClose();

    // Now start a session for the standard user on HTTPS.
    $edit = array(
      'name' => $standard_user->name,
      'pass' => $standard_user->pass_raw
    );
    $this->drupalGet('user');
    $form = $this->xpath('//form[@id="user-login"]');
    $form[0]['action'] = $this->httpsUrl('user');
    $this->drupalPost(NULL, $edit, t('Log in'));

    // Make the secure session cookie blank.
    curl_setopt($this->curlHandle, CURLOPT_COOKIE, "$secure_session_name=");
    $this->drupalGet($this->httpsUrl('user'));
    $this->assertNoText($admin_user->name, 'User is not logged in as admin');
    $this->assertNoText($standard_user->name, "The user's own name is not displayed because the invalid session cookie has logged them out.");
  }

  /**
   * Test that there exists a session with two specific session IDs.
   *
   * @param $sid
   *   The insecure session ID to search for.
   * @param $ssid
   *   The secure session ID to search for.
   * @param $assertion_text
   *   The text to display when we perform the assertion.
   *
   * @return
   *   The result of assertTrue() that there's a session in the system that
   *   has the given insecure and secure session IDs.
   */
  protected function assertSessionIds($sid, $ssid, $assertion_text) {
    $args = array(
      ':sid' => drupal_session_id($sid),
      ':ssid' => !empty($ssid) ? drupal_session_id($ssid) : '',
    );
    return $this->assertTrue(db_query('SELECT timestamp FROM {sessions} WHERE sid = :sid AND ssid = :ssid', $args)->fetchField(), $assertion_text);
  }

  /**
   * Builds a URL for submitting a mock HTTPS request to HTTP test environments.
   *
   * @param $url
   *   A Drupal path such as 'user'.
   *
   * @return
   *   An absolute URL.
   */
  protected function httpsUrl($url) {
    global $base_url;
    return $base_url . '/modules/simpletest/tests/https.php?q=' . $url;
  }

  /**
   * Builds a URL for submitting a mock HTTP request to HTTPS test environments.
   *
   * @param $url
   *   A Drupal path such as 'user'.
   *
   * @return
   *   An absolute URL.
   */
  protected function httpUrl($url) {
    global $base_url;
    return $base_url . '/modules/simpletest/tests/http.php?q=' . $url;
  }
}

/**
 * Unit tests for session handling.
 */
class SessionUnitTestCase extends DrupalUnitTestCase {

  /**
   * {@inheritdoc}
   */
  public static function getInfo() {
    return array(
      'name' => 'Session unit tests',
      'description' => 'Test session handling functionality.',
      'group' => 'Session',
    );
  }

  function testCookieDomain() {
     $tests = array(
       array('example.com', '.example.com'),
       array('www.example.com', '.www.example.com'),
       array('subdomain.example.com', '.subdomain.example.com'),
     );

     foreach ($tests as $test) {
       $this->assertEqual(_drupal_get_cookie_domain($test[0]), $test[1], 'Correct $cookie_domain for host ' . $test[0]);
     }
  }

  /**
   * Unit test drupal_settings_initialize().
   */
  function testSessionInitialization() {
    global $base_url, $cookie_domain;

    $tests = array(
      array(
        'http_host' => 'example.com',
        'script_name' => '/index.php',
        'session_name' => 'SESSa379a6f6eeafb9a55e378c118034e275',
        'base_url' => 'http://example.com',
        'cookie_domain' => '.example.com',
      ),
      array(
        'http_host' => 'example.com',
        'script_name' => '/foo/index.php',
        'session_name' => 'SESS76f699faa11e8fbe9a70d933651df90f',
        'base_url' => 'http://example.com/foo',
        'cookie_domain' => '.example.com',
      ),
      array(
        'http_host' => 'one.two.example.com',
        'script_name' => '/index.php',
        'session_name' => 'SESSc0cc73483118b575693f5c255faeb739',
        'base_url' => 'http://one.two.example.com',
        'cookie_domain' => '.one.two.example.com',
      ),
      array(
        'http_host' => 'www.sub.example.com',
        'script_name' => '/index.php',
        'session_name' => 'SESSdc70980beaa5d571d3c9785cf9246c96',
        'base_url' => 'http://www.sub.example.com',
        'cookie_domain' => '.www.sub.example.com',
      ),
      array(
        'http_host' => 'example.com',
        'script_name' => '/foo/bar/index.php',
        'session_name' => 'SESSdbf7edcf4f2656b247021ceb4752f7f9',
        'base_url' => 'http://example.com/foo/bar',
        'cookie_domain' => '.example.com',
      ),
      array(
        'http_host' => 'www.sub.example.com',
        'script_name' => '/baz/index.php',
        'session_name' => 'SESS63543f965940db9e8a79801aa6d52423',
        'base_url' => 'http://www.sub.example.com/baz',
        'cookie_domain' => '.www.sub.example.com',
      ),
      array(
        'http_host' => 'sub.example.com',
        'script_name' => '/index.php',
        'session_name' => 'SESS005c4a974d8b94af421206f9ef34efeb',
        'base_url' => 'http://sub.example.com',
        'cookie_domain' => '.sub.example.com',
      ),
      array(
        'http_host' => 'www.example.com',
        'script_name' => '/index.php',
        'session_name' => 'SESS0e9f0e2600e4d8f26827597c5e324261',
        'base_url' => 'http://www.example.com',
        'cookie_domain' => '.www.example.com',
      ),
      array(
        'http_host' => 'www.example.com',
        'script_name' => '/foo/index.php',
        'session_name' => 'SESS0ddd0afc0bece848c840cdcd7b8ed9c9',
        'base_url' => 'http://www.example.com/foo',
        'cookie_domain' => '.www.example.com',
      ),
      array(
        'http_host' => 'www.example.com',
        'script_name' => '/bar/index.php',
        'session_name' => 'SESS22f2cd08599536cb4363fcd09e29d264',
        'base_url' => 'http://www.example.com/bar',
        'cookie_domain' => '.www.example.com',
      ),
    );

    foreach ($tests as $test) {
      $_SERVER['HTTP_HOST'] = $test['http_host'];
      $_SERVER['SCRIPT_NAME'] = $test['script_name'];
      $cookie_domain = NULL;
      $base_url = NULL;
      drupal_settings_initialize();
      $this->assertEqual(session_name(), $test['session_name'], 'Correct session_name for ' . $test['http_host'] . $test['script_name']);
      $this->assertEqual($base_url, $test['base_url'], 'Correct base_url for ' . $test['http_host'] . $test['script_name']);
      $this->assertEqual($cookie_domain, $test['cookie_domain'], 'Correct cookie_domain for ' . $test['http_host'] . $test['script_name']);
    }

  }
}
