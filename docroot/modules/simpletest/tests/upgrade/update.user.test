<?php

/**
 * @file
 * Provides update path tests for the User module.
 */

/**
 * Tests the User 7.0 -> 7.x update path.
 */
class UserUpdatePathTestCase extends UpdatePathTestCase {
  public static function getInfo() {
    return array(
      'name' => 'User update path',
      'description' => 'User update path tests.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Use the filled update path and our field data.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-7.filled.standard_all.database.php.gz',
    );
    parent::setUp();
  }

  /**
   * Tests that the update is successful.
   */
  public function testFilledUpgrade() {
    $this->assertTrue($this->performUpgrade(), 'The update was completed successfully.');
    $this->assertTrue(db_index_exists('users', 'picture'), 'The {users}.picture column has an index.');
  }
}
