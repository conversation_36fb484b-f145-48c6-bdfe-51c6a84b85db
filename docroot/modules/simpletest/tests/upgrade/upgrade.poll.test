<?php

/**
 * Upgrade test for poll.module.
 *
 * Load a bare installation of Drupal 6 and run the upgrade process on it.
 *
 * The install only contains dblog (although it's optional, it's only so that
 * another hook_watchdog module can take its place, the site is not functional
 * without watchdog) and update.
 */
class PollUpgradePathTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Poll upgrade path',
      'description'  => 'Poll upgrade path tests.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump files.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
    );
    parent::setUp();

    $this->uninstallModulesExcept(array('poll'));
  }

  /**
   * Test a successful upgrade.
   */
  public function testPollUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');

    // Check modules page for poll
    $this->drupalGet('admin/modules');

    // Verify that the poll data is still correctly available
    for ($i = 0; $i < 12; $i++) {
      $this->drupalGet("content/poll/$i");

      $nbchoices = ($i % 4) + 2;

      for ($c = 0; $c < $nbchoices; $c++) {
        $this->assertText("Choice $c for poll $i", 'Choice text is displayed correctly on poll view');
      }

      // Now check that the votes are correct
      $this->clickLink(t('Results'));

      for ($c = 0; $c < $nbchoices; $c++) {
        $this->assertText("Choice $c for poll $i", 'Choice text is displayed correctly on result view');
      }

      $nbvotes = floor (($i % 4) + 5);
      $elements = $this->xpath("//div[@class='percent']");
      for ($c = 0; $c < $nbchoices; $c++) {
        $votes = floor($nbvotes / $nbchoices);
        if (($nbvotes % $nbchoices) > $c) $votes++;
        $this->assertTrue(preg_match("/$votes vote/", $elements[$c]), 'The number of votes is displayed correctly: expected ' . $votes . ', got ' . $elements[$c]);
      }
    }
  }
}
