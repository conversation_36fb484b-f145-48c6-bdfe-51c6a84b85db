<?php

/**
 * Upgrade test for comment.module.
 */
class UploadUpgradePathTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Upload upgrade path',
      'description'  => 'Upload upgrade path tests.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump files.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.upload.database.php',
    );
    parent::setUp();
    // Set a small batch size to test multiple iterations of the batch.
    $this->variable_set('upload_update_batch_size', 2);

    $this->uninstallModulesExcept(array('upload'));
  }

  /**
   * Test a successful upgrade.
   */
  public function testUploadUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');
    $query = new EntityFieldQuery();
    $query->entityCondition('entity_type', 'node');
    $query->entityCondition('bundle', 'page');
    $query->age(FIELD_LOAD_REVISION);
    $query->fieldCondition('upload');
    $entities = $query->execute();
    $revisions = $entities['node'];
    // Node revision 50 should not have uploaded files, as the entry in {files}
    // is corrupted.
    $this->assertFalse((isset($revisions[50])), 'Nodes with missing files do not contain filefield data.');
    // Node revisions 51-53 should have uploaded files.
    $this->assertTrue((isset($revisions[51]) && isset($revisions[52]) && isset($revisions[53])), 'Nodes with uploaded files now contain filefield data.');
    // The test database lists uploaded filenames in the body of each node with
    // uploaded files attached. Make sure all files are there in the same order.
    foreach ($revisions as $vid => $revision) {
      $node = node_load($revision->nid, $vid);

      // Assemble a list of the filenames as recorded in the node body before
      // the upgrade.
      $recorded_filenames = preg_split('/\s+/', $node->body[LANGUAGE_NONE][0]['value']);
      // The first line of the node body should be "Attachments:"
      if (strstr($recorded_filenames[0], "Attachments:")) {
        unset($recorded_filenames[0]);
      }
      $recorded_filenames = array_values($recorded_filenames);

      $files = $node->upload[LANGUAGE_NONE];
      // Assemble a list of the filenames as they exist after the upgrade.
      $filenames = array();
      foreach ($files as $file) {
        $filenames[] = $file['filename'];
      }
      $this->assertIdentical($filenames, $recorded_filenames, 'The uploaded files are present in the same order after the upgrade.');
    }

    // Test for the file with repeating basename to only have the streaming
    // path replaced.
    $node = node_load(40, 53);
    $repeated_basename_file = $node->upload[LANGUAGE_NONE][4];
    $this->assertEqual($repeated_basename_file['uri'], 'private://drupal-6/file/directory/path/crazy-basename.png', "The file with the repeated basename path only had the stream portion replaced");

    // Ensure that filepaths are deduplicated.
    $node0 = node_load(41, 54);
    $node1 = node_load(41, 55);
    // Ensure that both revisions point to the same file ID.
    $items0 = field_get_items('node', $node0, 'upload');
    $this->assertEqual(count($items0), 1);
    $items1 = field_get_items('node', $node1, 'upload');
    $this->assertEqual(count($items1), 2);
    $this->assertEqual($items0[0]['fid'], $items1[0]['fid']);
    $this->assertEqual($items0[0]['fid'], $items1[1]['fid']);
    // The revision with more than one reference to the same file should retain
    // the original settings for each reference.
    $this->assertEqual($items1[0]['description'], 'first description');
    $this->assertEqual($items1[0]['display'], 0);
    $this->assertEqual($items1[1]['description'], 'second description');
    $this->assertEqual($items1[1]['display'], 1);
    // Ensure that the latest version of the files are used.
    $this->assertEqual($items1[0]['filesize'], 316);
    $this->assertEqual($items1[1]['filesize'], 316);
    // No duplicate files should remain on the Drupal 7 site.
    $this->assertEqual(0, db_query("SELECT COUNT(*) FROM {file_managed} GROUP BY uri HAVING COUNT(fid) > 1")->fetchField());

    // Make sure the file settings were properly migrated.
    $d6_file_directory_temp = '/drupal-6/file/directory/temp';
    $d6_file_directory_path = '/drupal-6/file/directory/path';
    $d6_file_downloads = 2; // FILE_DOWNLOADS_PRIVATE

    $this->assertNull(variable_get('file_directory_temp', NULL), "The 'file_directory_temp' variable was properly removed.");
    $this->assertEqual(variable_get('file_temporary_path', 'drupal-7-bogus'), $d6_file_directory_temp, "The 'file_temporary_path' setting was properly migrated.");
    $this->assertEqual(variable_get('file_default_scheme', 'drupal-7-bogus'), 'private', "The 'file_default_scheme' setting was properly migrated.");
    $this->assertEqual(variable_get('file_private_path', 'drupal-7-bogus'), $d6_file_directory_path, "The 'file_private_path' setting was properly migrated.");
  }
}
