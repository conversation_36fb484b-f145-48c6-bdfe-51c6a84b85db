<?php
/**
 * @file
 * Provides upgrade path tests for the Trigger module.
 */

/**
 * Tests the Trigger 6 -> 7 upgrade path.
 */
class UpgradePathTriggerTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Trigger upgrade path',
      'description'  => 'Trigger upgrade path tests for Drupal 6.x.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.trigger.database.php',
    );
    parent::setUp();
  }

  /**
   * Basic tests for the trigger upgrade.
   */
  public function testTaxonomyUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');
    $this->drupalGet('admin/structure/trigger/node');
    $this->assertRaw('<td>'. t('Make post sticky') .'</td>');
    $this->assertRaw('<td>'. t('Publish post') .'</td>');
    $this->drupalGet('admin/structure/trigger/comment');
    $this->assertRaw('<td>'. t('Publish comment') .'</td>');
  }
}
