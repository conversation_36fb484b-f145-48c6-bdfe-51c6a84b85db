<?php

/**
 * Upgrade test for forum.module.
 */
class ForumUpgradePathTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Forum upgrade path',
      'description'  => 'Upgrade path tests for the Forum module.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump files.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.forum.database.php',
    );
    parent::setUp();

    $this->uninstallModulesExcept(array('comment', 'forum', 'taxonomy'));
  }

  /**
   * Test a successful upgrade (no negotiation).
   */
  public function testForumUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');

    // Work around http://drupal.org/node/931512
    $this->drupalPost('admin/structure/types/manage/forum/fields', array(), t('Save'));

    // The D6 database forum vocabulary contains the term "Fruits" with id 81.
    $tid = 81;
    $this->drupalGet("forum/$tid");

    // There is one forum topic in Fruits, with the title "Apples".
    $this->clickLink('Apples');
    $this->clickLink('Edit');

    // Add a forum topic "Bananas" to the "Fruits" forum.
    $edit = array(
      'title' => $title = 'Bananas',
      'body[' . LANGUAGE_NONE . '][0][value]' => $body = 'It is another fruit.',
    );
    $this->drupalPost("node/add/forum/$tid", $edit, t('Save'));
    $type = t('Forum topic');
    $this->assertRaw(t('@type %title has been created.', array('@type' => $type, '%title' => $title)), 'Forum topic was created');

    // Retrieve node object, ensure that the topic was created and in the proper forum.
    $node = $this->drupalGetNodeByTitle($title);
    $this->assertTrue($node != NULL, format_string('Node @title was loaded', array('@title' => $title)));
    $this->assertEqual($node->taxonomy_forums[LANGUAGE_NONE][0]['tid'], $tid, 'Saved forum topic was in the expected forum');

    $this->drupalGet("forum/$tid");
    $this->assertText('Bananas');
    $this->drupalLogout();

    $this->drupalGet("node/add/forum/$tid");
    $this->assertResponse(200, 'User can access forum creation page.');
  }
}
