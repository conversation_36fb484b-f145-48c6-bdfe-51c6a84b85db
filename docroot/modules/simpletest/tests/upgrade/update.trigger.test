<?php
/**
 * @file
 * Provides upgrade path tests for the Trigger module.
 */

/**
 * Tests the Trigger 7.0 -> 7.x upgrade path.
 */
class TriggerUpdatePathTestCase extends UpdatePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Trigger update path',
      'description'  => 'Trigger update path tests.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Use the filled upgrade path and our trigger data.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-7.filled.standard_all.database.php.gz',
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-7.trigger.database.php',
    );
    parent::setUp();

    // Our test data includes node and comment trigger assignments.
    $this->uninstallModulesExcept(array('comment', 'trigger'));
  }

  /**
   * Tests that the upgrade is successful.
   */
  public function testFilledUpgrade() {
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');
  }
}
