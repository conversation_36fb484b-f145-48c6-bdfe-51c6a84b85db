<?php

/**
 * Upgrade test for node bodies.
 *
 * Load a filled installation of Drupal 6 and run the upgrade process on it.
 */
class NodeBodyUpgradePathTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Node body upgrade path',
      'description'  => 'Node body upgrade path tests.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
    );
    parent::setUp();
  }

  /**
   * Test a successful upgrade.
   */
  public function testNodeBodyUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');

    $instance = field_info_instance('node', 'body', 'story');
    $this->assertIdentical($instance['required'], 0, 'The required setting was preserved during the upgrade path.');
    $this->assertTrue($instance['description'], 'The description was preserved during the upgrade path');

    $this->drupalGet("content/1263769200");
    $this->assertText('node body (broken) - 37');

    // Find a published node revision and make sure it still has a body.
    $revision = db_query_range("SELECT r.nid, r.vid FROM {node_revision} r JOIN {node} n ON n.nid = r.nid WHERE n.status = 1 AND n.type <> 'poll' AND n.vid <> r.vid", 0, 1)->fetch();
    $revision = node_load($revision->nid, $revision->vid);
    $this->assertTrue(!empty($revision->body), 'Non-current node revisions still have a node body.');
    // Find an unpublished node revision and make sure it still has a body.
    $revision = db_query_range("SELECT r.nid, r.vid FROM {node_revision} r JOIN {node} n ON n.nid = r.nid WHERE n.status = 0 AND n.type <> 'poll' AND n.vid <> r.vid", 0, 1)->fetch();
    $revision = node_load($revision->nid, $revision->vid);
    $this->assertTrue(!empty($revision->body), 'Unpublished non-current node revisions still have a node body.');

    // Check that fields created during the upgrade can be edited and resaved
    // in the UI.
    $this->drupalPost('admin/structure/types/manage/story/fields/body', array(), t('Save settings'));
  }
}

/**
 * Tests the upgrade path for node disabled node types.
 *
 * Load a filled installation of Drupal 6 and run the upgrade process on it.
 */
class DisabledNodeTypeTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Disabled node type upgrade path',
      'description'  => 'Disabled node type upgrade path tests.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.node_type_broken.database.php',
    );
    parent::setUp();
  }

  /**
   * Tests a successful upgrade.
   */
  public function testDisabledNodeTypeUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');
    $this->assertTrue(field_info_instance('comment', 'comment_body', 'comment_node_broken'), 'Comment body field instance was created for comments attached to the disabled broken node type');
  }
}

/**
 * Upgrade test for node type poll.
 *
 * Load a bare installation of Drupal 6 and run the upgrade process on it.
 *
 * The install only contains dblog (although it's optional, it's only so that
 * another hook_watchdog module can take its place, the site is not functional
 * without watchdog) and update.
 */
class PollUpgradePathTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Poll upgrade path',
      'description'  => 'Poll upgrade path tests.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
    );
    parent::setUp();

    $this->uninstallModulesExcept(array('poll'));
  }

  /**
   * Test a successful upgrade.
   */
  public function testPollUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');

    // Check modules page for poll
    $this->drupalGet('admin/modules');

    // Verify that the poll data is still correctly available
    for ($i = 0; $i < 12; $i++) {
      $this->drupalGet("content/poll/$i");

      $nbchoices = ($i % 4) + 2;

      for ($c = 0; $c < $nbchoices; $c++) {
        $this->assertText("Choice $c for poll $i", 'Choice text is displayed correctly on poll view');
      }

      // Now check that the votes are correct
      $this->clickLink(t('Results'));

      for ($c = 0; $c < $nbchoices; $c++) {
        $this->assertText("Choice $c for poll $i", 'Choice text is displayed correctly on result view');
      }

      $nbvotes = floor (($i % 4) + 5);
      $elements = $this->xpath("//div[@class='percent']");
      for ($c = 0; $c < $nbchoices; $c++) {
        $votes = floor($nbvotes / $nbchoices);
        if (($nbvotes % $nbchoices) > $c) $votes++;
        $this->assertTrue(preg_match("/$votes vote/", $elements[$c]), 'The number of votes is displayed correctly: expected ' . $votes . ', got ' . $elements[$c]);
      }
    }
  }
}
