<?php

/**
 * Upgrade test for filter format identifiers.
 *
 * Filter format identifiers changed from sequential ids to machine names.
 * Verify that filter formats and references to filter formats in core are
 * converted properly.
 */
class FilterFormatUpgradePathTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Filter format upgrade path',
      'description' => 'Verifies that filter formats and references to filter formats are converted properly.',
      'group' => 'Upgrade path',
    );
  }

  function setUp() {
    // Path to the database dump.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
    );
    parent::setUp();
  }

  /**
   * Test a successful upgrade.
   */
  function testFilterFormatUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');

    $format = filter_format_load('1');
    $this->assertTrue($format->format == '1', 'Filter format found.');
    $format->format = 'test_filter';
    $format->name = 'Test filter';
    filter_format_save($format);
    $format = filter_format_load('test_filter');
    $this->assertTrue($format->format == 'test_filter', 'Saved a filter format with machine name.');

    $account = user_load(4);
    user_save($account, array('signature_format' => 'test_filter'));
    $account = user_load(4);
    $this->assertTrue($account->signature_format == 'test_filter', 'Signature format changed successfully to a filter format with machine name.');

    $delta = db_insert('block_custom')
      ->fields(array(
        'body' => 'Test block',
        'info' => 'Test block',
        'format' => 'test_filter',
      ))
      ->execute();
    $this->assertTrue($delta > 0, 'Created a custom block using a filter format with machine name.');
  }
}
