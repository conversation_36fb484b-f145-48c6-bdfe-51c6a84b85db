<?php

/**
 * Upgrade test for translatable content types of node.module.
 */
class TranslatableUpgradePathTestCase extends UpgradePathTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'Translatable content upgrade path',
      'description'  => 'Upgrade path tests for the translatable content types of Node module.',
      'group' => 'Upgrade path',
    );
  }

  public function setUp() {
    // Path to the database dump files.
    $this->databaseDumpFiles = array(
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.filled.database.php',
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.locale.database.php',
      drupal_get_path('module', 'simpletest') . '/tests/upgrade/drupal-6.translatable.database.php',
    );
    parent::setUp();

    $this->uninstallModulesExcept(array('locale'));
  }

  /**
   * Test a successful upgrade (no negotiation).
   */
  public function testTranslatableUpgrade() {
    if ($this->skipUpgradeTest) {
      return;
    }
    $this->assertTrue($this->performUpgrade(), 'The upgrade was completed successfully.');

    // The D6 database contains the english node "First translatable page" with
    // nid 53.
    $nid = 53;
    $title = 'First translatable page';
    $teaser = 'Teaser of the first translatable page.';
    $body = 'Body of the first translatable page.';

    // Check whether the node displays properly.
    $this->drupalGet("node/$nid");
    $this->assertText($body, 'Translatable node body displays properly');

    // Retrieve node object, ensure that both the body and the teaser has
    // survived upgrade properly.
    $node = $this->drupalGetNodeByTitle($title);
    $this->assertTrue($node != NULL, format_string('Node @title was loaded', array('@title' => $title)));
    $this->assertEqual($node->body[LANGUAGE_NONE][0]['value'], $body, 'Body of the node survived upgrade properly');
    $this->assertEqual($node->body[LANGUAGE_NONE][0]['summary'], $teaser, 'Teaser of the node survived upgrade properly');
  }
}
