<?php

db_insert('files')->fields(array(
  'fid',
  'uid',
  'filename',
  'filepath',
  'filemime',
  'filesize',
  'status',
  'timestamp',
))
/*
 * This entry is deliberately omitted to test the upgrade routine when facing
 * possible data corruption.
 *
->values(array(
  'fid' => '1',
  'uid' => '1',
  'filename' => 'powered-blue-80x15.png',
  'filepath' => 'sites/default/files/powered-blue-80x15.png',
  'filemime' => 'image/png',
  'filesize' => '1011',
  'status' => '1',
  'timestamp' => '1285700240',
)) */
->values(array(
  'fid' => '2',
  'uid' => '1',
  'filename' => 'powered-blue-80x15.png',
  'filepath' => 'sites/default/files/powered-blue-80x15_0.png',
  'filemime' => 'image/png',
  'filesize' => '1011',
  'status' => '1',
  'timestamp' => '1285700317',
))
->values(array(
  'fid' => '3',
  'uid' => '1',
  'filename' => 'powered-blue-88x31.png',
  'filepath' => 'sites/default/files/powered-blue-88x31.png',
  'filemime' => 'image/png',
  'filesize' => '2113',
  'status' => '1',
  'timestamp' => '1285700343',
))
->values(array(
  'fid' => '4',
  'uid' => '1',
  'filename' => 'powered-blue-135x42.png',
  'filepath' => 'sites/default/files/powered-blue-135x42.png',
  'filemime' => 'image/png',
  'filesize' => '3027',
  'status' => '1',
  'timestamp' => '1285700366',
))
->values(array(
  'fid' => '5',
  'uid' => '1',
  'filename' => 'powered-black-80x15.png',
  'filepath' => 'sites/default/files/powered-black-80x15.png',
  'filemime' => 'image/png',
  'filesize' => '1467',
  'status' => '1',
  'timestamp' => '1285700529',
))
->values(array(
  'fid' => '6',
  'uid' => '1',
  'filename' => 'powered-black-135x42.png',
  'filepath' => 'sites/default/files/powered-black-135x42.png',
  'filemime' => 'image/png',
  'filesize' => '2817',
  'status' => '1',
  'timestamp' => '1285700552',
))
->values(array(
  'fid' => '7',
  'uid' => '1',
  'filename' => 'forum-hot-new.png',
  'filepath' => 'sites/default/files/forum-hot-new.png',
  'filemime' => 'image/png',
  'filesize' => '237',
  'status' => '1',
  'timestamp' => '1285708937',
))
->values(array(
  'fid' => '8',
  'uid' => '1',
  'filename' => 'forum-hot.png',
  'filepath' => 'sites/default/files/forum-hot.png',
  'filemime' => 'image/png',
  'filesize' => '229',
  'status' => '1',
  'timestamp' => '1285708944',
))
->values(array(
  'fid' => '9',
  'uid' => '1',
  'filename' => 'forum-new.png',
  'filepath' => 'sites/default/files/forum-new.png',
  'filemime' => 'image/png',
  'filesize' => '175',
  'status' => '1',
  'timestamp' => '1285708950',
))
->values(array(
  'fid' => '10',
  'uid' => '1',
  'filename' => 'forum-sticky.png',
  'filepath' => 'sites/default/files/forum-sticky.png',
  'filemime' => 'image/png',
  'filesize' => '329',
  'status' => '1',
  'timestamp' => '1285708957',
))
/*
 * This is a case where the path is repeated twice.
 */
->values(array(
  'fid' => '11',
  'uid' => '1',
  'filename' => 'crazy-basename.png',
  'filepath' => '/drupal-6/file/directory/path/drupal-6/file/directory/path/crazy-basename.png',
  'filemime' => 'image/png',
  'filesize' => '329',
  'status' => '1',
  'timestamp' => '1285708958',
))
// On some Drupal 6 sites, more than one file can have the same filepath. See
// https://www.drupal.org/node/1260938.
->values(array(
  'fid' => '12',
  'uid' => '1',
  'filename' => 'duplicate-name.png',
  'filepath' => 'sites/default/files/duplicate-name.png',
  'filemime' => 'image/png',
  'filesize' => '314',
  'status' => '1',
  'timestamp' => '1285708958',
))
->values(array(
  'fid' => '13',
  'uid' => '1',
  'filename' => 'duplicate-name.png',
  'filepath' => 'sites/default/files/duplicate-name.png',
  'filemime' => 'image/png',
  'filesize' => '315',
  'status' => '1',
  'timestamp' => '1285708958',
))
->values(array(
  'fid' => '14',
  'uid' => '1',
  'filename' => 'duplicate-name.png',
  'filepath' => 'sites/default/files/duplicate-name.png',
  'filemime' => 'image/png',
  'filesize' => '316',
  'status' => '1',
  'timestamp' => '1285708958',
))
->execute();

db_insert('node')->fields(array(
  'nid',
  'vid',
  'type',
  'language',
  'title',
  'uid',
  'status',
  'created',
  'changed',
  'comment',
  'promote',
  'moderate',
  'sticky',
  'tnid',
  'translate',
))
->values(array(
  'nid' => '38',
  'vid' => '50',
  'type' => 'page',
  'language' => '',
  'title' => 'node title 38 revision 50',
  'uid' => '1',
  'status' => '1',
  'created' => '1285603317',
  'changed' => '1285603317',
  'comment' => '0',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '39',
  'vid' => '52',
  'type' => 'page',
  'language' => '',
  'title' => 'node title 39 revision 52',
  'uid' => '1',
  'status' => '1',
  'created' => '1285700317',
  'changed' => '1285700600',
  'comment' => '0',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '40',
  'vid' => '53',
  'type' => 'page',
  'language' => '',
  'title' => 'node title 40 revision 53',
  'uid' => '1',
  'status' => '1',
  'created' => '1285709012',
  'changed' => '1285709012',
  'comment' => '0',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '41',
  'vid' => '55',
  'type' => 'page',
  'language' => '',
  'title' => 'node title 41 revision 55',
  'uid' => '1',
  'status' => '1',
  'created' => '1285709012',
  'changed' => '1285709012',
  'comment' => '0',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
 ->execute();

db_insert('node_revisions')->fields(array(
  'nid',
  'vid',
  'uid',
  'title',
  'body',
  'teaser',
  'log',
  'timestamp',
  'format',
))
->values(array(
  'nid' => '38',
  'vid' => '50',
  'uid' => '1',
  'title' => 'node title 38 revision 50',
  'body' => "Attachments:\r\npowered-blue-80x15.png",
  'teaser' => "Attachments:\r\npowered-blue-80x15.png",
  'log' => '',
  'timestamp' => '1285603317',
  'format' => '1',
))
->values(array(
  'nid' => '39',
  'vid' => '51',
  'uid' => '1',
  'title' => 'node title 39 revision 51',
  'body' => "Attachments:\r\npowered-blue-80x15.png\r\npowered-blue-88x31.png\r\npowered-blue-135x42.png",
  'teaser' => "Attachments:\r\npowered-blue-80x15.png\r\npowered-blue-88x31.png\r\npowered-blue-135x42.png",
  'log' => '',
  'timestamp' => '1285700487',
  'format' => '1',
))
->values(array(
  'nid' => '39',
  'vid' => '52',
  'uid' => '1',
  'title' => 'node title 39 revision 52',
  'body' => "Attachments:\r\npowered-blue-88x31.png\r\npowered-black-80x15.png\r\npowered-black-135x42.png",
  'teaser' => "Attachments:\r\npowered-blue-88x31.png\r\npowered-black-80x15.png\r\npowered-black-135x42.png",
  'log' => '',
  'timestamp' => '1285700600',
  'format' => '1',
))
->values(array(
  'nid' => '40',
  'vid' => '53',
  'uid' => '1',
  'title' => 'node title 40 revision 53',
  'body' => "Attachments:\r\nforum-hot-new.png\r\nforum-hot.png\r\nforum-sticky.png\r\nforum-new.png\r\ncrazy-basename.png",
  'teaser' => "Attachments:\r\nforum-hot-new.png\r\nforum-hot.png\r\nforum-sticky.png\r\nforum-new.png\r\ncrazy-basename.png",
  'log' => '',
  'timestamp' => '1285709012',
  'format' => '1',
))
->values(array(
  'nid' => '41',
  'vid' => '54',
  'uid' => '1',
  'title' => 'node title 41 revision 54',
  'body' => "Attachments:\r\nduplicate-name.png",
  'teaser' => "Attachments:\r\nduplicate-name.png",
  'log' => '',
  'timestamp' => '1285709012',
  'format' => '1',
))
->values(array(
  'nid' => '41',
  'vid' => '55',
  'uid' => '1',
  'title' => 'node title 41 revision 55',
  'body' => "Attachments:\r\nduplicate-name.png\r\nduplicate-name.png",
  'teaser' => "Attachments:\r\nduplicate-name.png\r\nduplicate-name.png",
  'log' => '',
  'timestamp' => '1285709012',
  'format' => '1',
))
 ->execute();

db_create_table('upload', array(
  'fields' => array(
    'fid' => array(
      'type' => 'int',
      'unsigned' => TRUE,
      'not null' => TRUE,
      'default' => 0,
    ),
    'nid' => array(
      'type' => 'int',
      'unsigned' => TRUE,
      'not null' => TRUE,
      'default' => 0,
    ),
    'vid' => array(
      'type' => 'int',
      'unsigned' => TRUE,
      'not null' => TRUE,
      'default' => 0,
    ),
    'description' => array(
      'type' => 'varchar',
      'length' => 255,
      'not null' => TRUE,
      'default' => '',
    ),
    'list' => array(
      'type' => 'int',
      'unsigned' => TRUE,
      'not null' => TRUE,
      'default' => 0,
      'size' => 'tiny',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'default' => 0,
      'size' => 'tiny',
    ),
  ),
  'primary key' => array(
    'vid',
    'fid',
  ),
  'indexes' => array(
    'fid' => array(
      'fid',
    ),
    'nid' => array(
      'nid',
    ),
  ),
  'module' => 'upload',
  'name' => 'upload',
));
db_insert('upload')->fields(array(
  'fid',
  'nid',
  'vid',
  'description',
  'list',
  'weight',
))
->values(array(
  'fid' => '1',
  'nid' => '38',
  'vid' => '50',
  'description' => 'powered-blue-80x15.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '2',
  'nid' => '39',
  'vid' => '51',
  'description' => 'powered-blue-80x15.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '3',
  'nid' => '39',
  'vid' => '51',
  'description' => 'powered-blue-88x31.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '4',
  'nid' => '39',
  'vid' => '51',
  'description' => 'powered-blue-135x42.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '3',
  'nid' => '39',
  'vid' => '52',
  'description' => 'powered-blue-88x31.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '5',
  'nid' => '39',
  'vid' => '52',
  'description' => 'powered-black-80x15.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '6',
  'nid' => '39',
  'vid' => '52',
  'description' => 'powered-black-135x42.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '7',
  'nid' => '40',
  'vid' => '53',
  'description' => 'forum-hot-new.png',
  'list' => '1',
  'weight' => '-4',
))
->values(array(
  'fid' => '8',
  'nid' => '40',
  'vid' => '53',
  'description' => 'forum-hot.png',
  'list' => '1',
  'weight' => '-3',
))
->values(array(
  'fid' => '10',
  'nid' => '40',
  'vid' => '53',
  'description' => 'forum-sticky.png',
  'list' => '1',
  'weight' => '-2',
))
->values(array(
  'fid' => '9',
  'nid' => '40',
  'vid' => '53',
  'description' => 'forum-new.png',
  'list' => '1',
  'weight' => '-1',
))
->values(array(
  'fid' => '11',
  'nid' => '40',
  'vid' => '53',
  'description' => 'crazy-basename.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '12',
  'nid' => '41',
  'vid' => '54',
  'description' => 'duplicate-name.png',
  'list' => '1',
  'weight' => '0',
))
->values(array(
  'fid' => '13',
  'nid' => '41',
  'vid' => '55',
  'description' => 'first description',
  'list' => '0',
  'weight' => '0',
))
->values(array(
  'fid' => '14',
  'nid' => '41',
  'vid' => '55',
  'description' => 'second description',
  'list' => '1',
  'weight' => '0',
))
->execute();

// Add series of entries for invalid node vids to the {upload} table.
for ($i = 30; $i < 250; $i += 2) {
  db_insert('upload')->fields(array(
    'fid',
    'nid',
    'vid',
    'description',
    'list',
    'weight',
  ))
  // Invalid fid, invalid vid.
  ->values(array(
    'fid' => $i,
    'nid' => '40',
    'vid' => 26 + $i,
    'description' => 'crazy-basename.png',
    'list' => '1',
    'weight' => '0',
  ))
  // Valid fid, invalid vid.
  ->values(array(
    'fid' => 2,
    'nid' => '40',
    'vid' => 26 + $i + 1,
    'description' => 'crazy-basename.png',
    'list' => '1',
    'weight' => '0',
  ))
  ->execute();
}
