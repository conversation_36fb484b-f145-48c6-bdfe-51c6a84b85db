<?php

/**
 * Perform basic XML-RPC tests that do not require addition callbacks.
 */
class XMLRPCBasicTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name'  => 'XML-RPC basic',
      'description'  => 'Perform basic XML-RPC tests that do not require additional callbacks.',
      'group' => 'XML-RPC',
    );
  }

  /**
   * Ensure that a basic XML-RPC call with no parameters works.
   */
  protected function testListMethods() {
    // Minimum list of methods that should be included.
    $minimum = array(
      'system.multicall',
      'system.methodSignature',
      'system.getCapabilities',
      'system.listMethods',
      'system.methodHelp',
    );

    // Invoke XML-RPC call to get list of methods.
    $url = url(NULL, array('absolute' => TRUE)) . 'xmlrpc.php';
    $methods = xmlrpc($url, array('system.listMethods' => array()));

    // Ensure that the minimum methods were found.
    $count = 0;
    foreach ($methods as $method) {
      if (in_array($method, $minimum)) {
        $count++;
      }
    }

    $this->assertEqual($count, count($minimum), 'system.listMethods returned at least the minimum listing');
  }

  /**
   * Ensure that system.methodSignature returns an array of signatures.
   */
  protected function testMethodSignature() {
    $url = url(NULL, array('absolute' => TRUE)) . 'xmlrpc.php';
    $signature = xmlrpc($url, array('system.methodSignature' => array('system.listMethods')));
    $this->assert(is_array($signature) && !empty($signature) && is_array($signature[0]),
      'system.methodSignature returns an array of signature arrays.');
  }

  /**
   * Ensure that XML-RPC correctly handles invalid messages when parsing.
   */
  protected function testInvalidMessageParsing() {
    $invalid_messages = array(
      array(
        'message' => xmlrpc_message(''),
        'assertion' => 'Empty message correctly rejected during parsing.',
      ),
      array(
        'message' => xmlrpc_message('<?xml version="1.0" encoding="ISO-8859-1"?>'),
        'assertion' => 'Empty message with XML declaration correctly rejected during parsing.',
      ),
      array(
        'message' => xmlrpc_message('<?xml version="1.0"?><params><param><value><string>value</string></value></param></params>'),
        'assertion' => 'Non-empty message without a valid message type is rejected during parsing.',
      ),
      array(
        'message' => xmlrpc_message('<methodResponse><params><param><value><string>value</string></value></param></methodResponse>'),
        'assertion' => 'Non-empty malformed message is rejected during parsing.',
      ),
    );

    foreach ($invalid_messages as $assertion) {
      $this->assertFalse(xmlrpc_message_parse($assertion['message']), $assertion['assertion']);
    }
  }
}

class XMLRPCValidator1IncTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'XML-RPC validator',
      'description' => 'See <a href="http://www.xmlrpc.com/validator1Docs">the xmlrpc validator1 specification</a>.',
      'group' => 'XML-RPC',
    );
  }

  function setUp() {
    parent::setUp('xmlrpc_test');
  }

  /**
   * Run validator1 tests.
   */
  function testValidator1() {
    $xml_url = url(NULL, array('absolute' => TRUE)) . 'xmlrpc.php';
    srand();
    mt_srand();

    $array_1 = array(array('curly' => mt_rand(-100, 100)),
                  array('curly' => mt_rand(-100, 100)),
                  array('larry' => mt_rand(-100, 100)),
                  array('larry' => mt_rand(-100, 100)),
                  array('moe' => mt_rand(-100, 100)),
                  array('moe' => mt_rand(-100, 100)),
                  array('larry' => mt_rand(-100, 100)));
    shuffle($array_1);
    $l_res_1 = xmlrpc_test_arrayOfStructsTest($array_1);
    $r_res_1 = xmlrpc($xml_url, array('validator1.arrayOfStructsTest' => array($array_1)));
    $this->assertIdentical($l_res_1, $r_res_1);

    $string_2 = 't\'&>>zf"md>yr>xlcev<h<"k&j<og"w&&>">>uai"np&s>>q\'&b<>"&&&';
    $l_res_2 = xmlrpc_test_countTheEntities($string_2);
    $r_res_2 = xmlrpc($xml_url, array('validator1.countTheEntities' => array($string_2)));
    $this->assertIdentical($l_res_2, $r_res_2);

    $struct_3 = array('moe' => mt_rand(-100, 100), 'larry' => mt_rand(-100, 100), 'curly' => mt_rand(-100, 100), 'homer' => mt_rand(-100, 100));
    $l_res_3 = xmlrpc_test_easyStructTest($struct_3);
    $r_res_3 = xmlrpc($xml_url, array('validator1.easyStructTest' => array($struct_3)));
    $this->assertIdentical($l_res_3, $r_res_3);

    $struct_4 = array('sub1' => array('bar' => 13),
                    'sub2' => 14,
                    'sub3' => array('foo' => 1, 'baz' => 2),
                    'sub4' => array('ss' => array('sss' => array('ssss' => 'sssss'))));
    $l_res_4 = xmlrpc_test_echoStructTest($struct_4);
    $r_res_4 = xmlrpc($xml_url, array('validator1.echoStructTest' => array($struct_4)));
    $this->assertIdentical($l_res_4, $r_res_4);

    $int_5     = mt_rand(-100, 100);
    $bool_5    = (($int_5 % 2) == 0);
    $string_5  = $this->randomName();
    $double_5  = (double)(mt_rand(-1000, 1000) / 100);
    $time_5    = REQUEST_TIME;
    $base64_5  = $this->randomName(100);
    $l_res_5 = xmlrpc_test_manyTypesTest($int_5, $bool_5, $string_5, $double_5, xmlrpc_date($time_5), $base64_5);
    // See http://drupal.org/node/37766 why this currently fails
    $l_res_5[5] = $l_res_5[5]->data;
    $r_res_5 = xmlrpc($xml_url, array('validator1.manyTypesTest' => array($int_5, $bool_5, $string_5, $double_5, xmlrpc_date($time_5), xmlrpc_base64($base64_5))));
    // @todo Contains objects, objects are not equal.
    $this->assertEqual($l_res_5, $r_res_5);

    $size = mt_rand(100, 200);
    $array_6 = array();
    for ($i = 0; $i < $size; $i++) {
      $array_6[] = $this->randomName(mt_rand(8, 12));
    }

    $l_res_6 = xmlrpc_test_moderateSizeArrayCheck($array_6);
    $r_res_6 = xmlrpc($xml_url, array('validator1.moderateSizeArrayCheck' => array($array_6)));
    $this->assertIdentical($l_res_6, $r_res_6);

    $struct_7 = array();
    for ($y = 2000; $y < 2002; $y++) {
      for ($m = 3; $m < 5; $m++) {
        for ($d = 1; $d < 6; $d++) {
          $ys = (string) $y;
          $ms = sprintf('%02d', $m);
          $ds = sprintf('%02d', $d);
          $struct_7[$ys][$ms][$ds]['moe']   = mt_rand(-100, 100);
          $struct_7[$ys][$ms][$ds]['larry'] = mt_rand(-100, 100);
          $struct_7[$ys][$ms][$ds]['curly'] = mt_rand(-100, 100);
        }
      }
    }
    $l_res_7 = xmlrpc_test_nestedStructTest($struct_7);
    $r_res_7 = xmlrpc($xml_url, array('validator1.nestedStructTest' => array($struct_7)));
    $this->assertIdentical($l_res_7, $r_res_7);


    $int_8 = mt_rand(-100, 100);
    $l_res_8 = xmlrpc_test_simpleStructReturnTest($int_8);
    $r_res_8 = xmlrpc($xml_url, array('validator1.simpleStructReturnTest' => array($int_8)));
    $this->assertIdentical($l_res_8, $r_res_8);

    /* Now test multicall */
    $x = array();
    $x['validator1.arrayOfStructsTest'] = array($array_1);
    $x['validator1.countTheEntities'] = array($string_2);
    $x['validator1.easyStructTest'] = array($struct_3);
    $x['validator1.echoStructTest'] = array($struct_4);
    $x['validator1.manyTypesTest'] = array($int_5, $bool_5, $string_5, $double_5, xmlrpc_date($time_5), xmlrpc_base64($base64_5));
    $x['validator1.moderateSizeArrayCheck'] = array($array_6);
    $x['validator1.nestedStructTest'] = array($struct_7);
    $x['validator1.simpleStructReturnTest'] = array($int_8);

    $a_l_res = array($l_res_1, $l_res_2, $l_res_3, $l_res_4, $l_res_5, $l_res_6, $l_res_7, $l_res_8);
    $a_r_res = xmlrpc($xml_url, $x);
    $this->assertEqual($a_l_res, $a_r_res);
  }
}

class XMLRPCMessagesTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name'  => 'XML-RPC message and alteration',
      'description' => 'Test large messages and method alterations.',
      'group' => 'XML-RPC',
    );
  }

  function setUp() {
    parent::setUp('xmlrpc_test');
  }

  /**
   * Make sure that XML-RPC can transfer large messages.
   */
  function testSizedMessages() {
    // These tests can produce up to 128 x 160 words in the XML-RPC message
    // (see xmlrpc_test_message_sized_in_kb()) with 4 tags used to represent
    // each. Set a large enough tag limit to allow this to be tested.
    variable_set('xmlrpc_message_maximum_tag_count', 100000);

    $xml_url = url(NULL, array('absolute' => TRUE)) . 'xmlrpc.php';
    $sizes = array(8, 80, 160);
    foreach ($sizes as $size) {
      $xml_message_l = xmlrpc_test_message_sized_in_kb($size);
      $xml_message_r = xmlrpc($xml_url, array('messages.messageSizedInKB' => array($size)));

      $this->assertEqual($xml_message_l, $xml_message_r, format_string('XML-RPC messages.messageSizedInKB of %s Kb size received', array('%s' => $size)));
    }
  }

  /**
   * Ensure that hook_xmlrpc_alter() can hide even builtin methods.
   */
  protected function testAlterListMethods() {

    // Ensure xmlrpc_test_xmlrpc_alter() is disabled and retrieve regular list of methods.
    variable_set('xmlrpc_test_xmlrpc_alter', FALSE);
    $url = url(NULL, array('absolute' => TRUE)) . 'xmlrpc.php';
    $methods1 = xmlrpc($url, array('system.listMethods' => array()));

    // Enable the alter hook and retrieve the list of methods again.
    variable_set('xmlrpc_test_xmlrpc_alter', TRUE);
    $methods2 = xmlrpc($url, array('system.listMethods' => array()));

    $diff = array_diff($methods1, $methods2);
    $this->assertTrue(is_array($diff) && !empty($diff), 'Method list is altered by hook_xmlrpc_alter');
    $removed = reset($diff);
    $this->assertEqual($removed, 'system.methodSignature', 'Hiding builting system.methodSignature with hook_xmlrpc_alter works');
  }

  /**
   * Test limits on system.multicall that can prevent brute-force attacks.
   */
  function testMulticallLimit() {
    $url = url(NULL, array('absolute' => TRUE)) . 'xmlrpc.php';
    $multicall_args = array();
    $num_method_calls = 10;
    for ($i = 0; $i < $num_method_calls; $i++) {
      $struct = array('i' => $i);
      $multicall_args[] = array('methodName' => 'validator1.echoStructTest', 'params' => array($struct));
    }
    // Test limits of 1, 5, 9, 13.
    for ($limit = 1; $limit < $num_method_calls + 4; $limit += 4) {
      variable_set('xmlrpc_multicall_duplicate_method_limit', $limit);
      $results = xmlrpc($url, array('system.multicall' => array($multicall_args)));
      $this->assertEqual($num_method_calls, count($results));
      for ($i = 0; $i < min($limit, $num_method_calls); $i++) {
        $x = array_shift($results);
        $this->assertTrue(empty($x->is_error), "Result $i is not an error");
        $this->assertEqual($multicall_args[$i]['params'][0], $x);
      }
      for (; $i < $num_method_calls; $i++) {
        $x = array_shift($results);
        $this->assertFalse(empty($x->is_error), "Result $i is an error");
        $this->assertEqual(-156579, $x->code);
      }
    }
    variable_set('xmlrpc_multicall_duplicate_method_limit', -1);
    $results = xmlrpc($url, array('system.multicall' => array($multicall_args)));
    $this->assertEqual($num_method_calls, count($results));
    foreach ($results as $i => $x) {
      $this->assertTrue(empty($x->is_error), "Result $i is not an error");
    }
  }
}
