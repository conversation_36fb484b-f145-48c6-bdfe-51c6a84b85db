<?php

/**
 * @file
 * Install, update and uninstall functions for the update_test_2 module.
 */

/**
 * Implements hook_update_dependencies().
 *
 * @see update_test_1_update_dependencies()
 * @see update_test_3_update_dependencies()
 */
function update_test_2_update_dependencies() {
  // Combined with update_test_3_update_dependencies(), we are declaring here
  // that these two modules run updates in the following order:
  // 1. update_test_2_update_7000()
  // 2. update_test_3_update_7000()
  // 3. update_test_2_update_7001()
  // 4. update_test_2_update_7002()
  $dependencies['update_test_2'][7001] = array(
    'update_test_3' => 7000,
  );

  // These are coordinated with the corresponding dependencies declared in
  // update_test_1_update_dependencies().
  $dependencies['system'][7000] = array(
    'update_test_2' => 7001,
  );
  $dependencies['system'][7001] = array(
    'update_test_1' => 7001,
  );

  return $dependencies;
}

/**
 * Dummy update_test_2 update 7000.
 */
function update_test_2_update_7000() {
}

/**
 * Dummy update_test_2 update 7001.
 */
function update_test_2_update_7001() {
}

/**
 * Dummy update_test_2 update 7002.
 */
function update_test_2_update_7002() {
}
