<?php

/**
 * @file
 * Install, update and uninstall functions for the contact module.
 */

/**
 * Implements hook_schema().
 */
function contact_schema() {
  $schema['contact'] = array(
    'description' => 'Contact form category settings.',
    'fields' => array(
      'cid' => array(
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'Primary Key: Unique category ID.',
      ),
      'category' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Category name.',
        'translatable' => TRUE,
      ),
      'recipients' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'Comma-separated list of recipient e-mail addresses.',
      ),
      'reply' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'Text of the auto-reply message.',
      ),
      'weight' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => "The category's weight.",
      ),
      'selected' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'Flag to indicate whether or not category is selected by default. (1 = Yes, 0 = No)',
      ),
    ),
    'primary key' => array('cid'),
    'unique keys' => array(
      'category' => array('category'),
    ),
    'indexes' => array(
      'list' => array('weight', 'category'),
    ),
  );

  return $schema;
}

/**
 * Implements hook_install().
 */
function contact_install() {
  // Insert a default contact category.
  db_insert('contact')
    ->fields(array(
      'category' => 'Website feedback',
      'recipients' => variable_get('site_mail', ini_get('sendmail_from')),
      'selected' => 1,
      'reply' => '',
    ))
    ->execute();
}

/**
 * Implements hook_uninstall().
 */
function contact_uninstall() {
  variable_del('contact_default_status');
  variable_del('contact_threshold_limit');
  variable_del('contact_threshold_window');
}

/**
 * Implements hook_update_dependencies().
 */
function contact_update_dependencies() {
  // contact_update_7001() relies on the {role_permission} table being updated
  // to the new format and filled with data.
  $dependencies['contact'][7001] = array(
    'system' => 7007,
  );

  // contact_update_7002() relies on the {role_permission} table having the
  // module field, which is created in user_update_7006().
  $dependencies['contact'][7002] = array(
    'user' => 7006,
  );

  return $dependencies;
}

/**
 * @addtogroup updates-6.x-to-7.x
 * @{
 */

/**
 * Rename the threshold limit variable.
 */
function contact_update_7000() {
  variable_set('contact_threshold_limit', variable_get('contact_hourly_threshold', 5));
  variable_del('contact_hourly_threshold');
}

/**
 * Rename the administer contact forms permission.
 */
function contact_update_7001() {
  db_update('role_permission')
    ->fields(array('permission' => 'administer contact forms'))
    ->condition('permission', 'administer site-wide contact form')
    ->execute();
}

/**
 * Enable the 'access user contact forms' for registered users by default.
 */
function contact_update_7002() {
  // Do not use user_role_grant_permission() since it relies on
  // hook_permission(), which will not run for contact module if it is
  // disabled.
  db_merge('role_permission')
    ->key(array(
      'rid' => DRUPAL_AUTHENTICATED_RID,
      'permission' => 'access user contact forms',
      'module' => 'contact',
    ))
    ->execute();
}

/**
 * Change the weight column to normal int.
 */
function contact_update_7003() {
  db_drop_index('contact', 'list');
  db_change_field('contact', 'weight', 'weight', array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
    'description' => "The category's weight.",
  ), array(
    'indexes' => array(
      'list' => array('weight', 'category'),
    ),
  ));
}

/**
 * @} End of "addtogroup updates-6.x-to-7.x".
 */
