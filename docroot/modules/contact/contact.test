<?php
/**
 * @file
 * Tests for the Contact module.
 */

/**
 * Tests the site-wide contact form.
 */
class ContactSitewideTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Site-wide contact form',
      'description' => 'Tests site-wide contact form functionality.',
      'group' => 'Contact',
    );
  }

  function setUp() {
    parent::setUp('contact');
  }

  /**
   * Tests configuration options and the site-wide contact form.
   */
  function testSiteWideContact() {
    // Create and login administrative user.
    $admin_user = $this->drupalCreateUser(array('access site-wide contact form', 'administer contact forms', 'administer users'));
    $this->drupalLogin($admin_user);

    $flood_limit = 3;
    variable_set('contact_threshold_limit', $flood_limit);
    variable_set('contact_threshold_window', 600);

    // Set settings.
    $edit = array();
    $edit['contact_default_status'] = TRUE;
    $this->drupalPost('admin/config/people/accounts', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Setting successfully saved.');

    // Delete old categories to ensure that new categories are used.
    $this->deleteCategories();

    // Ensure that the contact form won't be shown without categories.
    user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array('access site-wide contact form'));
    $this->drupalLogout();
    $this->drupalGet('contact');
    $this->assertResponse(404);
    $this->drupalLogin($admin_user);
    $this->drupalGet('contact');
    $this->assertResponse(200);
    $this->assertText(t('The contact form has not been configured.'));

    // Add categories.
    // Test invalid recipients.
    $invalid_recipients = array('invalid', 'invalid@', 'invalid@site.', '@site.', '@site.com');
    foreach ($invalid_recipients as $invalid_recipient) {
      $this->addCategory($this->randomName(16), $invalid_recipient, '', FALSE);
      $this->assertRaw(t('%recipient is an invalid e-mail address.', array('%recipient' => $invalid_recipient)), format_string('Caught invalid recipient (@invalid_recipient).', array('@invalid_recipient' => $invalid_recipient)));
    }

    // Test validation of empty category and recipients fields.
    $this->addCategory($category = '', '', '', TRUE);
    $this->assertText(t('Category field is required.'), 'Caught empty category field');
    $this->assertText(t('Recipients field is required.'), 'Caught empty recipients field.');

    // Create first valid category.
    $recipients = array('<EMAIL>', '<EMAIL>', '<EMAIL>');
    $this->addCategory($category = $this->randomName(16), implode(',', array($recipients[0])), '', TRUE);
    $this->assertRaw(t('Category %category has been saved.', array('%category' => $category)), 'Category successfully saved.');

    // Make sure the newly created category is included in the list of categories.
    $this->assertNoUniqueText($category, 'New category included in categories list.');

    // Test update contact form category.
    $categories = $this->getCategories();
    $category_id = $this->updateCategory($categories, $category = $this->randomName(16), $recipients_str = implode(',', array($recipients[0], $recipients[1])), $reply = $this->randomName(30), FALSE);
    $category_array = db_query("SELECT category, recipients, reply, selected FROM {contact} WHERE cid = :cid", array(':cid' => $category_id))->fetchAssoc();
    $this->assertEqual($category_array['category'], $category);
    $this->assertEqual($category_array['recipients'], $recipients_str);
    $this->assertEqual($category_array['reply'], $reply);
    $this->assertFalse($category_array['selected']);
    $this->assertRaw(t('Category %category has been saved.', array('%category' => $category)), 'Category successfully saved.');

    // Ensure that the contact form is shown without a category selection input.
    user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array('access site-wide contact form'));
    $this->drupalLogout();
    $this->drupalGet('contact');
    $this->assertText(t('Your e-mail address'), 'Contact form is shown when there is one category.');
    $this->assertNoText(t('Category'), 'When there is only one category, the category selection element is hidden.');
    $this->drupalLogin($admin_user);

    // Add more categories.
    $this->addCategory($category = $this->randomName(16), implode(',', array($recipients[0], $recipients[1])), '', FALSE);
    $this->assertRaw(t('Category %category has been saved.', array('%category' => $category)), 'Category successfully saved.');

    $this->addCategory($category = $this->randomName(16), implode(',', array($recipients[0], $recipients[1], $recipients[2])), '', FALSE);
    $this->assertRaw(t('Category %category has been saved.', array('%category' => $category)), 'Category successfully saved.');

    // Try adding a category that already exists.
    $this->addCategory($category, '', '', FALSE);
    $this->assertNoRaw(t('Category %category has been saved.', array('%category' => $category)), 'Category not saved.');
    $this->assertRaw(t('A contact form with category %category already exists.', array('%category' => $category)), 'Duplicate category error found.');

    // Clear flood table in preparation for flood test and allow other checks to complete.
    db_delete('flood')->execute();
    $num_records_after = db_query("SELECT COUNT(*) FROM {flood}")->fetchField();
    $this->assertIdentical($num_records_after, '0', 'Flood table emptied.');
    $this->drupalLogout();

    // Check to see that anonymous user cannot see contact page without permission.
    user_role_revoke_permissions(DRUPAL_ANONYMOUS_RID, array('access site-wide contact form'));
    $this->drupalGet('contact');
    $this->assertResponse(403, 'Access denied to anonymous user without permission.');

    // Give anonymous user permission and see that page is viewable.
    user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array('access site-wide contact form'));
    $this->drupalGet('contact');
    $this->assertResponse(200, 'Access granted to anonymous user with permission.');

    // Submit contact form with invalid values.
    $this->submitContact('', $recipients[0], $this->randomName(16), $categories[0], $this->randomName(64));
    $this->assertText(t('Your name field is required.'), 'Name required.');

    $this->submitContact($this->randomName(16), '', $this->randomName(16), $categories[0], $this->randomName(64));
    $this->assertText(t('Your e-mail address field is required.'), 'E-mail required.');

    $this->submitContact($this->randomName(16), $invalid_recipients[0], $this->randomName(16), $categories[0], $this->randomName(64));
    $this->assertText(t('You must enter a valid e-mail address.'), 'Valid e-mail required.');

    $this->submitContact($this->randomName(16), $recipients[0], '', $categories[0], $this->randomName(64));
    $this->assertText(t('Subject field is required.'), 'Subject required.');

    $this->submitContact($this->randomName(16), $recipients[0], $this->randomName(16), $categories[0], '');
    $this->assertText(t('Message field is required.'), 'Message required.');

    // Test contact form with no default category selected.
    db_update('contact')
      ->fields(array('selected' => 0))
      ->execute();
    $this->drupalGet('contact');
    $this->assertRaw(t('- Please choose -'), 'Without selected categories the visitor is asked to chose a category.');

    // Submit contact form with invalid category id (cid 0).
    $this->submitContact($this->randomName(16), $recipients[0], $this->randomName(16), 0, '');
    $this->assertText(t('You must select a valid category.'), 'Valid category required.');

    // Submit contact form with correct values and check flood interval.
    for ($i = 0; $i < $flood_limit; $i++) {
      $this->submitContact($this->randomName(16), $recipients[0], $this->randomName(16), $categories[0], $this->randomName(64));
      $this->assertText(t('Your message has been sent.'), 'Message sent.');
    }
    // Submit contact form one over limit.
    $this->drupalGet('contact');
    $this->assertResponse(403, 'Access denied to anonymous user after reaching message treshold.');
    $this->assertRaw(t('You cannot send more than %number messages in @interval. Try again later.', array('%number' => variable_get('contact_threshold_limit', 3), '@interval' => format_interval(600))), 'Message threshold reached.');

    // Delete created categories.
    $this->drupalLogin($admin_user);
    $this->deleteCategories();
  }

  /**
  * Tests auto-reply on the site-wide contact form.
  */
  function testAutoReply() {
    // Create and login administrative user.
    $admin_user = $this->drupalCreateUser(array('access site-wide contact form', 'administer contact forms', 'administer permissions', 'administer users'));
    $this->drupalLogin($admin_user);

    // Set up three categories, 2 with an auto-reply and one without.
    $foo_autoreply = $this->randomName(40);
    $bar_autoreply = $this->randomName(40);
    $this->addCategory('foo', '<EMAIL>', $foo_autoreply, FALSE);
    $this->addCategory('bar', '<EMAIL>', $bar_autoreply, FALSE);
    $this->addCategory('no_autoreply', '<EMAIL>', '', FALSE);

    // Test the auto-reply for category 'foo'.
    $email = $this->randomName(32) . '@example.com';
    $subject = $this->randomName(64);
    $this->submitContact($this->randomName(16), $email, $subject, 2, $this->randomString(128));

    // We are testing the auto-reply, so there should be one e-mail going to the sender.
    $captured_emails = $this->drupalGetMails(array('id' => 'contact_page_autoreply', 'to' => $email, 'from' => '<EMAIL>'));
    $this->assertEqual(count($captured_emails), 1, 'Auto-reply e-mail was sent to the sender for category "foo".', 'Contact');
    $this->assertEqual($captured_emails[0]['body'], drupal_html_to_text($foo_autoreply), 'Auto-reply e-mail body is correct for category "foo".', 'Contact');

    // Test the auto-reply for category 'bar'.
    $email = $this->randomName(32) . '@example.com';
    $this->submitContact($this->randomName(16), $email, $this->randomString(64), 3, $this->randomString(128));

    // Auto-reply for category 'bar' should result in one auto-reply e-mail to the sender.
    $captured_emails = $this->drupalGetMails(array('id' => 'contact_page_autoreply', 'to' => $email, 'from' => '<EMAIL>'));
    $this->assertEqual(count($captured_emails), 1, 'Auto-reply e-mail was sent to the sender for category "bar".', 'Contact');
    $this->assertEqual($captured_emails[0]['body'], drupal_html_to_text($bar_autoreply), 'Auto-reply e-mail body is correct for category "bar".', 'Contact');

    // Verify that no auto-reply is sent when the auto-reply field is left blank.
    $email = $this->randomName(32) . '@example.com';
    $this->submitContact($this->randomName(16), $email, $this->randomString(64), 4, $this->randomString(128));
    $captured_emails = $this->drupalGetMails(array('id' => 'contact_page_autoreply', 'to' => $email, 'from' => '<EMAIL>'));
    $this->assertEqual(count($captured_emails), 0, 'No auto-reply e-mail was sent to the sender for category "no-autoreply".', 'Contact');
  }

  /**
   * Adds a category.
   *
   * @param string $category
   *   The category name.
   * @param string $recipients
   *   The list of recipient e-mail addresses.
   * @param string $reply
   *   The auto-reply text that is sent to a user upon completing the contact
   *   form.
   * @param boolean $selected
   *   Boolean indicating whether the category should be selected by default.
   */
  function addCategory($category, $recipients, $reply, $selected) {
    $edit = array();
    $edit['category'] = $category;
    $edit['recipients'] = $recipients;
    $edit['reply'] = $reply;
    $edit['selected'] = ($selected ? '1' : '0');
    $this->drupalPost('admin/structure/contact/add', $edit, t('Save'));
  }

  /**
   * Updates a category.
   *
   * @param string $category
   *   The category name.
   * @param string $recipients
   *   The list of recipient e-mail addresses.
   * @param string $reply
   *   The auto-reply text that is sent to a user upon completing the contact
   *   form.
   * @param boolean $selected
   *   Boolean indicating whether the category should be selected by default.
   */
  function updateCategory($categories, $category, $recipients, $reply, $selected) {
    $category_id = $categories[array_rand($categories)];
    $edit = array();
    $edit['category'] = $category;
    $edit['recipients'] = $recipients;
    $edit['reply'] = $reply;
    $edit['selected'] = ($selected ? '1' : '0');
    $this->drupalPost('admin/structure/contact/edit/' . $category_id, $edit, t('Save'));
    return ($category_id);
  }

  /**
   * Submits the contact form.
   *
   * @param string $name
   *   The name of the sender.
   * @param string $mail
   *   The e-mail address of the sender.
   * @param string $subject
   *   The subject of the message.
   * @param integer $cid
   *   The category ID of the message.
   * @param string $message
   *   The message body.
   */
  function submitContact($name, $mail, $subject, $cid, $message) {
    $edit = array();
    $edit['name'] = $name;
    $edit['mail'] = $mail;
    $edit['subject'] = $subject;
    $edit['cid'] = $cid;
    $edit['message'] = $message;
    $this->drupalPost('contact', $edit, t('Send message'));
  }

  /**
   * Deletes all categories.
   */
  function deleteCategories() {
    $categories = $this->getCategories();
    foreach ($categories as $category) {
      $category_name = db_query("SELECT category FROM {contact} WHERE cid = :cid", array(':cid' => $category))->fetchField();
      $this->drupalPost('admin/structure/contact/delete/' . $category, array(), t('Delete'));
      $this->assertRaw(t('Category %category has been deleted.', array('%category' => $category_name)), 'Category deleted successfully.');
    }
  }

  /**
   * Gets a list of all category IDs.
   *
   * @return array
   *   A list of the category IDs.
   */
  function getCategories() {
    $categories = db_query('SELECT cid FROM {contact}')->fetchCol();
    return $categories;
  }
}

/**
 * Tests the personal contact form.
 */
class ContactPersonalTestCase extends DrupalWebTestCase {
  private $admin_user;
  private $web_user;
  private $contact_user;

  public static function getInfo() {
    return array(
      'name' => 'Personal contact form',
      'description' => 'Tests personal contact form functionality.',
      'group' => 'Contact',
    );
  }

  function setUp() {
    parent::setUp('contact');

    // Create an admin user.
    $this->admin_user = $this->drupalCreateUser(array('administer contact forms', 'administer users'));

    // Create some normal users with their contact forms enabled by default.
    variable_set('contact_default_status', TRUE);
    $this->web_user = $this->drupalCreateUser(array('access user contact forms'));
    $this->contact_user = $this->drupalCreateUser();
  }

  /**
   * Tests access to the personal contact form.
   */
  function testPersonalContactAccess() {
    // Test allowed access to user with contact form enabled.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(200);

    // Test denied access to the user's own contact form.
    $this->drupalGet('user/' . $this->web_user->uid . '/contact');
    $this->assertResponse(403);

    // Test always denied access to the anonymous user contact form.
    $this->drupalGet('user/0/contact');
    $this->assertResponse(403);

    // Test that anonymous users can access the contact form.
    $this->drupalLogout();
    user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array('access user contact forms'));
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(200);

    // Test that users can disable their contact form.
    $this->drupalLogin($this->contact_user);
    $edit = array('contact' => FALSE);
    $this->drupalPost('user/' . $this->contact_user->uid . '/edit', $edit, 'Save');
    $this->drupalLogout();
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(403);

    // Test that user's contact status stays disabled when saving.
    $contact_user_temp = user_load($this->contact_user->uid, TRUE);
    user_save($contact_user_temp);
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(403);

    // Test that users can enable their contact form.
    $this->drupalLogin($this->contact_user);
    $edit = array('contact' => TRUE);
    $this->drupalPost('user/' . $this->contact_user->uid . '/edit', $edit, 'Save');
    $this->drupalLogout();
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(200);

    // Revoke the personal contact permission for the anonymous user.
    user_role_revoke_permissions(DRUPAL_ANONYMOUS_RID, array('access user contact forms'));
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(403);

    // Disable the personal contact form.
    $this->drupalLogin($this->admin_user);
    $edit = array('contact_default_status' => FALSE);
    $this->drupalPost('admin/config/people/accounts', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Setting successfully saved.');
    $this->drupalLogout();

    // Re-create our contacted user with personal contact forms disabled by
    // default.
    $this->contact_user = $this->drupalCreateUser();

    // Test denied access to a user with contact form disabled.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(403);

    // Test allowed access for admin user to a user with contact form disabled.
    $this->drupalLogin($this->admin_user);
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(200);

    // Re-create our contacted user as a blocked user.
    $this->contact_user = $this->drupalCreateUser();
    user_save($this->contact_user, array('status' => 0));

    // Test that blocked users can still be contacted by admin.
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(200);

    // Test that blocked users cannot be contacted by non-admins.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('user/' . $this->contact_user->uid . '/contact');
    $this->assertResponse(403);
  }

  /**
   * Tests the personal contact form flood protection.
   */
  function testPersonalContactFlood() {
    $flood_limit = 3;
    variable_set('contact_threshold_limit', $flood_limit);

    // Clear flood table in preparation for flood test and allow other checks to complete.
    db_delete('flood')->execute();
    $num_records_flood = db_query("SELECT COUNT(*) FROM {flood}")->fetchField();
    $this->assertIdentical($num_records_flood, '0', 'Flood table emptied.');

    $this->drupalLogin($this->web_user);

    // Submit contact form with correct values and check flood interval.
    for ($i = 0; $i < $flood_limit; $i++) {
      $this->submitPersonalContact($this->contact_user);
      $this->assertText(t('Your message has been sent.'), 'Message sent.');
    }

    // Submit contact form one over limit.
    $this->drupalGet('user/' . $this->contact_user->uid. '/contact');
    $this->assertRaw(t('You cannot send more than %number messages in @interval. Try again later.', array('%number' => $flood_limit, '@interval' => format_interval(variable_get('contact_threshold_window', 3600)))), 'Normal user denied access to flooded contact form.');

    // Test that the admin user can still access the contact form even though
    // the flood limit was reached.
    $this->drupalLogin($this->admin_user);
    $this->assertNoText('Try again later.', 'Admin user not denied access to flooded contact form.');
  }

  /**
   * Fills out a user's personal contact form and submits it.
   *
   * @param $account
   *   A user object of the user being contacted.
   * @param $message
   *   An optional array with the form fields being used.
   */
  protected function submitPersonalContact($account, array $message = array()) {
    $message += array(
      'subject' => $this->randomName(16),
      'message' => $this->randomName(64),
    );
    $this->drupalPost('user/' . $account->uid . '/contact', $message, t('Send message'));
  }
}
