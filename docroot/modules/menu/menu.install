<?php

/**
 * @file
 * Install, update and uninstall functions for the menu module.
 */

/**
 * Implements hook_schema().
 */
function menu_schema() {
  $schema['menu_custom'] = array(
    'description' => 'Holds definitions for top-level custom menus (for example, Main menu).',
    'fields' => array(
      'menu_name' => array(
        'type' => 'varchar',
        'length' => 32,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Primary Key: Unique key for menu. This is used as a block delta so length is 32.',
      ),
      'title' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Menu title; displayed at top of block.',
        'translatable' => TRUE,
      ),
      'description' => array(
        'type' => 'text',
        'not null' => FALSE,
        'description' => 'Menu description.',
        'translatable' => TRUE,
      ),
    ),
    'primary key' => array('menu_name'),
  );

  return $schema;
}

/**
 * Implements hook_install().
 */
function menu_install() {
  $system_menus = menu_list_system_menus();
  $t = get_t();
  $descriptions = array(
    'navigation' => $t('The <em>Navigation</em> menu contains links intended for site visitors. Links are added to the <em>Navigation</em> menu automatically by some modules.'),
    'user-menu' => $t("The <em>User</em> menu contains links related to the user's account, as well as the 'Log out' link."),
    'management' => $t('The <em>Management</em> menu contains links for administrative tasks.'),
    'main-menu' => $t('The <em>Main</em> menu is used on many sites to show the major sections of the site, often in a top navigation bar.'),
  );
  foreach ($system_menus as $menu_name => $title) {
    $menu = array(
      'menu_name' => $menu_name,
      'title' => $t($title),
      'description' => $descriptions[$menu_name],
    );
    menu_save($menu);
  }
}

/**
 * Implements hook_uninstall().
 */
function menu_uninstall() {
  menu_rebuild();
}

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Migrate the "Default menu for content" setting to individual node types.
 */
function menu_update_7000() {
  // Act only on sites originally on Drupal 6 that have a custom "Default menu
  // for content" setting.
  $default_node_menu = variable_get('menu_default_node_menu');
  if (isset($default_node_menu)) {
    // Remove variable no longer used in Drupal 7.
    variable_del('menu_default_node_menu');

    // Make sure the menu chosen as the default still exists.
    $defined_menus = db_query('SELECT * FROM {menu_custom}')->fetchAllAssoc('menu_name', PDO::FETCH_ASSOC);
    // If the menu does not exist, do nothing; nodes will use the default D7
    // node menu settings.
    if (!isset($defined_menus[$default_node_menu])) {
      return;
    }

    // Update the menu settings for each node type.
    foreach (_update_7000_node_get_types() as $type => $type_object) {
      $type_menus = variable_get('menu_options_' . $type);
      // If the site already has a custom menu setting for this node type (set
      // on the initial upgrade to Drupal 7.0), don't override it.
      if (!isset($type_menus)) {
        // Set up this node type so that the Drupal 6 "Default menu for content"
        // is still available in the "Menu settings" section.
        variable_set('menu_options_' . $type, array($default_node_menu));
        variable_set('menu_parent_' . $type, $default_node_menu . ':0');
      }
    }
  }
}

/**
 * Rename "Primary Links" and "Secondary Links" to their Drupal 7 equivalents.
 */
function menu_update_7001() {
  // Migrate D6 menu_primary_links_source to D7 menu_main_links_source (without
  // renaming).
  if (variable_get('menu_primary_links_source') !== NULL) {
    variable_set('menu_main_links_source', variable_get('menu_primary_links_source'));
    variable_del('menu_primary_links_source');
  }

  // Rename each menu, and any settings that refer to the old menu name.
  // - "Primary Links" has become system menu "Main menu".
  // - "Secondary Links" has become a new custom menu "Secondary menu".
  $rename = array(
    'primary-links' => array('main-menu', 'Main menu'),
    'secondary-links' => array('secondary-menu', 'Secondary menu'),
  );
  foreach ($rename as $from_menu => $to) {
    list($to_menu, $to_title) = $to;
    // Rename the menu, and links in the menu.
    db_update('menu_custom')
      ->fields(array('menu_name' => $to_menu, 'title' => $to_title))
      ->condition('menu_name', $from_menu)
      ->execute();
    db_update('menu_links')
      ->fields(array('menu_name' => $to_menu))
      ->condition('menu_name', $from_menu)
      ->execute();

    // Update any content type that used this menu as a default menu.
    // Note: these variables may be unset/default, in which case we leave them
    // alone. See menu_update_7000()
    foreach (_update_7000_node_get_types() as $type => $type_object) {
      $menu_options = variable_get('menu_options_' . $type);
      if ($menu_options !== NULL) {
        variable_set('menu_options_' . $type, str_replace($from_menu, $to_menu, $menu_options));
        if (variable_get('menu_parent_' . $type) == $from_menu . ':0') {
          variable_set('menu_parent_' . $type, $to_menu . ':0');
        }
      }
    }

    // Update the "source for primary links" and "source for secondary links" to
    // follow.
    if (variable_get('menu_main_links_source') == $from_menu) {
      variable_set('menu_main_links_source', $to_menu);
    }
    if (variable_get('menu_secondary_links_source') == $from_menu) {
      variable_set('menu_secondary_links_source', $to_menu);
    }
  }
}

/**
 * Rename the primary/secondary menu blocks to match previously renamed menus.
 */
function menu_update_7002(&$sandbox) {
  // Check for the presence of old or new table names.
  if (db_table_exists('blocks') || db_table_exists('block')) {
    $renamed_deltas = array(
      'menu' => array(
        'primary-links' => 'main-menu',
        'secondary-links' => 'secondary-menu',
      ),
    );

    $moved_deltas = array(
      'menu' => array('main-menu' => 'system'),
    );

    update_fix_d7_block_deltas($sandbox, $renamed_deltas, $moved_deltas);
  }
}
/**
 * Add missing custom menus to active menus list.
 */
function menu_update_7003(&$sandbox) {
  // Make sure all custom menus are present in the active menus variable so that
  // their items may appear in the active trail.
  // @see menu_set_active_menu_names()
  $active_menus = variable_get('menu_default_active_menus', array_keys(menu_list_system_menus()));
  $update_variable = FALSE;
  foreach (menu_get_names() as $menu_name) {
    if (!in_array($menu_name, $active_menus) && (strpos($menu_name, 'menu-') === 0)) {
      $active_menus[] = $menu_name;
      $update_variable = TRUE;
    }
  }
  if ($update_variable) {
    variable_set('menu_default_active_menus', $active_menus);
  }
  // Clear the menu cache.
  cache_clear_all(NULL, 'cache_menu');
}

/**
 * @} End of "addtogroup updates-7.x-extra".
 * The next series of updates should start at 8000.
 */
