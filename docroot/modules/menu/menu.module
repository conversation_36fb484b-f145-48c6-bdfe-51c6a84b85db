<?php

/**
 * @file
 * Allows administrators to customize the site's navigation menus.
 *
 * A menu (in this context) is a hierarchical collection of links, generally
 * used for navigation. This is not to be confused with the
 * @link menu Menu system @endlink of menu.inc and hook_menu(), which defines
 * page routing requests for <PERSON><PERSON><PERSON>, and also allows the defined page routing
 * URLs to be added to the main site navigation menu.
 */

/**
 * Maximum length of menu name as entered by the user. Database length is 32
 * and we add a menu- prefix.
 */
define('MENU_MAX_MENU_NAME_LENGTH_UI', 27);

/**
 * Implements hook_help().
 */
function menu_help($path, $arg) {
  switch ($path) {
    case 'admin/help#menu':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Menu module provides an interface for managing menus. A menu is a hierarchical collection of links, which can be within or external to the site, generally used for navigation. Each menu is rendered in a block that can be enabled and positioned through the <a href="@blocks">Blocks administration page</a>. You can view and manage menus on the <a href="@menus">Menus administration page</a>. For more information, see the online handbook entry for the <a href="@menu">Menu module</a>.', array('@blocks' => url('admin/structure/block'), '@menus' => url('admin/structure/menu'), '@menu' => 'http://drupal.org/documentation/modules/menu/')) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Managing menus') . '</dt>';
      $output .= '<dd>' . t('Users with the <em>Administer menus and menu items</em> permission can add, edit and delete custom menus on the <a href="@menu">Menus administration page</a>. Custom menus can be special site menus, menus of external links, or any combination of internal and external links. You may create an unlimited number of additional menus, each of which will automatically have an associated block. By selecting <em>list links</em>, you can add, edit, or delete links for a given menu. The links listing page provides a drag-and-drop interface for controlling the order of links, and creating a hierarchy within the menu.', array('@menu' => url('admin/structure/menu'), '@add-menu' => url('admin/structure/menu/add'))) . '</dd>';
      $output .= '<dt>' . t('Displaying menus') . '</dt>';
      $output .= '<dd>' . t('After you have created a menu, you must enable and position the associated block on the <a href="@blocks">Blocks administration page</a>.', array('@blocks' => url('admin/structure/block'))) . '</dd>';
      $output .= '</dl>';
      return $output;
    case 'admin/structure/menu/add':
      return '<p>' . t('You can enable the newly-created block for this menu on the <a href="@blocks">Blocks administration page</a>.', array('@blocks' => url('admin/structure/block'))) . '</p>';
  }
  if ($path == 'admin/structure/menu' && module_exists('block')) {
    return '<p>' . t('Each menu has a corresponding block that is managed on the <a href="@blocks">Blocks administration page</a>.', array('@blocks' => url('admin/structure/block'))) . '</p>';
  }
}

/**
 * Implements hook_permission().
 */
function menu_permission() {
  return array(
    'administer menu' => array(
      'title' => t('Administer menus and menu items'),
    ),
  );
}

/**
 * Implements hook_menu().
 */
function menu_menu() {
  $items['admin/structure/menu'] = array(
    'title' => 'Menus',
    'description' => 'Add new menus to your site, edit existing menus, and rename and reorganize menu links.',
    'page callback' => 'menu_overview_page',
    'access callback' => 'user_access',
    'access arguments' => array('administer menu'),
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/parents'] = array(
    'title' => 'Parent menu items',
    'page callback' => 'menu_parent_options_js',
    'type' => MENU_CALLBACK,
    'access arguments' => array('administer menu'),
  );
  $items['admin/structure/menu/list'] = array(
    'title' => 'List menus',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );
  $items['admin/structure/menu/add'] = array(
    'title' => 'Add menu',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('menu_edit_menu', 'add'),
    'access arguments' => array('administer menu'),
    'type' => MENU_LOCAL_ACTION,
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/settings'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('menu_configure'),
    'access arguments' => array('administer menu'),
    'type' => MENU_LOCAL_TASK,
    'weight' => 5,
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/manage/%menu'] = array(
    'title' => 'Customize menu',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('menu_overview_form', 4),
    'title callback' => 'menu_overview_title',
    'title arguments' => array(4),
    'access arguments' => array('administer menu'),
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/manage/%menu/list'] = array(
    'title' => 'List links',
    'weight' => -10,
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'context' => MENU_CONTEXT_PAGE | MENU_CONTEXT_INLINE,
  );
  $items['admin/structure/menu/manage/%menu/add'] = array(
    'title' => 'Add link',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('menu_edit_item', 'add', NULL, 4),
    'access arguments' => array('administer menu'),
    'type' => MENU_LOCAL_ACTION,
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/manage/%menu/edit'] = array(
    'title' => 'Edit menu',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('menu_edit_menu', 'edit', 4),
    'access arguments' => array('administer menu'),
    'type' => MENU_LOCAL_TASK,
    'context' => MENU_CONTEXT_PAGE | MENU_CONTEXT_INLINE,
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/manage/%menu/delete'] = array(
    'title' => 'Delete menu',
    'page callback' => 'menu_delete_menu_page',
    'page arguments' => array(4),
    'access arguments' => array('administer menu'),
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/item/%menu_link/edit'] = array(
    'title' => 'Edit menu link',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('menu_edit_item', 'edit', 4, NULL),
    'access arguments' => array('administer menu'),
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/item/%menu_link/reset'] = array(
    'title' => 'Reset menu link',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('menu_reset_item_confirm', 4),
    'access arguments' => array('administer menu'),
    'file' => 'menu.admin.inc',
  );
  $items['admin/structure/menu/item/%menu_link/delete'] = array(
    'title' => 'Delete menu link',
    'page callback' => 'menu_item_delete_page',
    'page arguments' => array(4),
    'access arguments' => array('administer menu'),
    'file' => 'menu.admin.inc',
  );
  return $items;
}

/**
 * Implements hook_theme().
 */
function menu_theme() {
  return array(
    'menu_overview_form' => array(
      'file' => 'menu.admin.inc',
      'render element' => 'form',
    ),
    'menu_admin_overview' => array(
      'file' => 'menu.admin.inc',
      'variables' => array('title' => NULL, 'name' => NULL, 'description' => NULL),
    ),
  );
}

/**
 * Implements hook_enable().
 *
 * Add a link for each custom menu.
 */
function menu_enable() {
  menu_rebuild();
  $base_link = db_query("SELECT mlid AS plid, menu_name FROM {menu_links} WHERE link_path = 'admin/structure/menu' AND module = 'system'")->fetchAssoc();
  $base_link['router_path'] = 'admin/structure/menu/manage/%';
  $base_link['module'] = 'menu';
  $result = db_query("SELECT * FROM {menu_custom}", array(), array('fetch' => PDO::FETCH_ASSOC));
  foreach ($result as $menu) {
    // $link is passed by reference to menu_link_save(), so we make a copy of $base_link.
    $link = $base_link;
    $link['mlid'] = 0;
    $link['link_title'] = $menu['title'];
    $link['link_path'] = 'admin/structure/menu/manage/' . $menu['menu_name'];
    $menu_link = db_query("SELECT mlid FROM {menu_links} WHERE link_path = :path AND plid = :plid", array(
      ':path' => $link['link_path'],
      ':plid' => $link['plid']
    ))
    ->fetchField();
    if (!$menu_link) {
      menu_link_save($link);
    }
  }
  menu_cache_clear_all();
}

/**
 * Title callback for the menu overview page and links.
 */
function menu_overview_title($menu) {
  return $menu['title'];
}

/**
 * Load the data for a single custom menu.
 *
 * @param $menu_name
 *   The unique name of a custom menu to load.
 * @return
 *   Array defining the custom menu, or FALSE if the menu doesn't exist.
 */
function menu_load($menu_name) {
  $all_menus = menu_load_all();
  return isset($all_menus[$menu_name]) ? $all_menus[$menu_name] : FALSE;
}

/**
 * Load all custom menu data.
 *
 * @return
 *   Array of custom menu data.
 */
function menu_load_all() {
  $custom_menus = &drupal_static(__FUNCTION__);
  if (!isset($custom_menus)) {
    if ($cached = cache_get('menu_custom', 'cache_menu')) {
      $custom_menus = $cached->data;
    }
    else {
      $custom_menus = db_query('SELECT * FROM {menu_custom}')->fetchAllAssoc('menu_name', PDO::FETCH_ASSOC);
      cache_set('menu_custom', $custom_menus, 'cache_menu');
    }
  }
  return $custom_menus;
}

/**
 * Save a custom menu.
 *
 * @param $menu
 *   An array representing a custom menu:
 *   - menu_name: The unique name of the custom menu (composed of lowercase
 *     letters, numbers, and hyphens).
 *   - title: The human readable menu title.
 *   - description: The custom menu description.
 *
 * Modules should always pass a fully populated $menu when saving a custom
 * menu, so other modules are able to output proper status or watchdog messages.
 *
 * @see menu_load()
 */
function menu_save($menu) {
  $status = db_merge('menu_custom')
    ->key(array('menu_name' => $menu['menu_name']))
    ->fields(array(
      'title' => $menu['title'],
      'description' => $menu['description'],
    ))
    ->execute();
  menu_cache_clear_all();

  switch ($status) {
    case SAVED_NEW:
      // Make sure the menu is present in the active menus variable so that its
      // items may appear in the menu active trail.
      // @see menu_set_active_menu_names()
      $active_menus = variable_get('menu_default_active_menus', array_keys(menu_get_menus()));
      if (!in_array($menu['menu_name'], $active_menus)) {
        $active_menus[] = $menu['menu_name'];
        variable_set('menu_default_active_menus', $active_menus);
      }

      module_invoke_all('menu_insert', $menu);
      break;

    case SAVED_UPDATED:
      module_invoke_all('menu_update', $menu);
      break;
  }
}

/**
 * Delete a custom menu and all contained links.
 *
 * Note that this function deletes all menu links in a custom menu. While menu
 * links derived from router paths may be restored by rebuilding the menu, all
 * customized and custom links will be irreversibly gone. Therefore, this
 * function should usually be called from a user interface (form submit) handler
 * only, which allows the user to confirm the action.
 *
 * @param $menu
 *   An array representing a custom menu:
 *   - menu_name: The unique name of the custom menu.
 *   - title: The human readable menu title.
 *   - description: The custom menu description.
 *
 * Modules should always pass a fully populated $menu when deleting a custom
 * menu, so other modules are able to output proper status or watchdog messages.
 *
 * @see menu_load()
 *
 * menu_delete_links() will take care of clearing the page cache. Other modules
 * should take care of their menu-related data by implementing
 * hook_menu_delete().
 */
function menu_delete($menu) {
  // Delete all links from the menu.
  menu_delete_links($menu['menu_name']);

  // Remove menu from active menus variable.
  $active_menus = variable_get('menu_default_active_menus', array_keys(menu_get_menus()));
  foreach ($active_menus as $i => $menu_name) {
    if ($menu['menu_name'] == $menu_name) {
      unset($active_menus[$i]);
      variable_set('menu_default_active_menus', $active_menus);
    }
  }

  // Delete the custom menu.
  db_delete('menu_custom')
    ->condition('menu_name', $menu['menu_name'])
    ->execute();

  menu_cache_clear_all();
  module_invoke_all('menu_delete', $menu);
}

/**
 * Return a list of menu items that are valid possible parents for the given menu item.
 *
 * @param $menus
 *   An array of menu names and titles, such as from menu_get_menus().
 * @param $item
 *   The menu item or the node type for which to generate a list of parents.
 *   If $item['mlid'] == 0 then the complete tree is returned.
 * @param $type
 *   The node type for which to generate a list of parents.
 *   If $item itself is a node type then $type is ignored.
 * @return
 *   An array of menu link titles keyed on the a string containing the menu name
 *   and mlid. The list excludes the given item and its children.
 *
 * @todo This has to be turned into a #process form element callback. The
 *   'menu_override_parent_selector' variable is entirely superfluous.
 */
function menu_parent_options($menus, $item, $type = '') {
  // The menu_links table can be practically any size and we need a way to
  // allow contrib modules to provide more scalable pattern choosers.
  // hook_form_alter is too late in itself because all the possible parents are
  // retrieved here, unless menu_override_parent_selector is set to TRUE.
  if (variable_get('menu_override_parent_selector', FALSE)) {
    return array();
  }

  $available_menus = array();
  if (!is_array($item)) {
    // If $item is not an array then it is a node type.
    // Use it as $type and prepare a dummy menu item for _menu_get_options().
    $type = $item;
    $item = array('mlid' => 0);
  }
  if (empty($type)) {
    // If no node type is set, use all menus given to this function.
    $available_menus = $menus;
  }
  else {
    // If a node type is set, use all available menus for this type.
    $type_menus = variable_get('menu_options_' . $type, array('main-menu' => 'main-menu'));
    foreach ($type_menus as $menu) {
      $available_menus[$menu] = $menu;
    }
  }

  return _menu_get_options($menus, $available_menus, $item);
}

/**
 * Page callback.
 * Get all the available menus and menu items as a JavaScript array.
 */
function menu_parent_options_js() {
  $available_menus = array();
  if (isset($_POST['menus']) && count($_POST['menus'])) {
    foreach ($_POST['menus'] as $menu) {
      $available_menus[$menu] = $menu;
    }
  }
  $options = _menu_get_options(menu_get_menus(), $available_menus, array('mlid' => 0));

  drupal_json_output($options);
}

/**
 * Helper function to get the items of the given menu.
 */
function _menu_get_options($menus, $available_menus, $item) {
  global $menu_admin;
  $menu_admin = TRUE;

  // If the item has children, there is an added limit to the depth of valid parents.
  if (isset($item['parent_depth_limit'])) {
    $limit = $item['parent_depth_limit'];
  }
  else {
    $limit = _menu_parent_depth_limit($item);
  }

  $options = array();
  foreach ($menus as $menu_name => $title) {
    if (isset($available_menus[$menu_name])) {
      $tree = menu_tree_all_data($menu_name, NULL);
      $options[$menu_name . ':0'] = '<' . $title . '>';
      _menu_parents_recurse($tree, $menu_name, '--', $options, $item['mlid'], $limit);
    }
  }

  $menu_admin = FALSE;
  return $options;
}

/**
 * Recursive helper function for menu_parent_options().
 */
function _menu_parents_recurse($tree, $menu_name, $indent, &$options, $exclude, $depth_limit) {
  foreach ($tree as $data) {
    if ($data['link']['depth'] > $depth_limit) {
      // Don't iterate through any links on this level.
      break;
    }
    if ($data['link']['mlid'] != $exclude && $data['link']['hidden'] >= 0) {
      $title = $indent . ' ' . truncate_utf8($data['link']['title'], 30, TRUE, FALSE);
      menu_add_link_labels($title, $data['link']);
      $options[$menu_name . ':' . $data['link']['mlid']] = $title;
      if ($data['below']) {
        _menu_parents_recurse($data['below'], $menu_name, $indent . '--', $options, $exclude, $depth_limit);
      }
    }
  }
}

/**
 * Adds labels to the title of a hidden, unpublished or logged-in menu link.
 *
 * @param string $title
 *   The title of the menu link. This will be modified as necessary to add the
 *   appropriate label in parentheses at the end.
 * @param array $item
 *   An array representing the menu link item.
 */
function menu_add_link_labels(&$title, $item) {
  if ($item['hidden']) {
    $title .= ' (' . t('disabled') . ')';
  }
  elseif (!empty($item['node_unpublished'])) {
    $title .= ' (' . t('unpublished') . ')';
  }
  elseif ($item['link_path'] == 'user' && $item['module'] == 'system') {
    $title .= ' (' . t('logged in users only') . ')';
  }
}

/**
 * Reset a system-defined menu link.
 */
function menu_reset_item($link) {
  // To reset the link to its original values, we need to retrieve its
  // definition from hook_menu(). Otherwise, for example, the link's menu would
  // not be reset, because properties like the original 'menu_name' are not
  // stored anywhere else. Since resetting a link happens rarely and this is a
  // one-time operation, retrieving the full menu router does no harm.
  $menu = menu_get_router();
  $router_item = $menu[$link['router_path']];
  $new_link = _menu_link_build($router_item);
  // Merge existing menu link's ID and 'has_children' property.
  foreach (array('mlid', 'has_children') as $key) {
    $new_link[$key] = $link[$key];
  }
  menu_link_save($new_link);
  return $new_link;
}

/**
 * Implements hook_block_info().
 */
function menu_block_info() {
  $menus = menu_get_menus(FALSE);

  $blocks = array();
  foreach ($menus as $name => $title) {
    $blocks[$name]['info'] = check_plain($title);
    // Menu blocks can't be cached because each menu item can have
    // a custom access callback. menu.inc manages its own caching.
    $blocks[$name]['cache'] = DRUPAL_NO_CACHE;
  }
  return $blocks;
}

/**
 * Implements hook_block_view().
 */
function menu_block_view($delta = '') {
  $menus = menu_get_menus(FALSE);
  $data['subject'] = check_plain($menus[$delta]);
  $data['content'] = menu_tree($delta);
  // Add contextual links for this block.
  if (!empty($data['content'])) {
    $data['content']['#contextual_links']['menu'] = array('admin/structure/menu/manage', array($delta));
  }
  return $data;
}

/**
 * Implements hook_block_view_alter().
 */
function menu_block_view_alter(&$data, $block) {
  // Add contextual links for system menu blocks.
  if ($block->module == 'system' && !empty($data['content'])) {
    $system_menus = menu_list_system_menus();
    if (isset($system_menus[$block->delta])) {
      $data['content']['#contextual_links']['menu'] = array('admin/structure/menu/manage', array($block->delta));
    }
  }
}

/**
 * Implements hook_node_insert().
 */
function menu_node_insert($node) {
  menu_node_save($node);
}

/**
 * Implements hook_node_update().
 */
function menu_node_update($node) {
  menu_node_save($node);
}

/**
 * Helper for hook_node_insert() and hook_node_update().
 */
function menu_node_save($node) {
  if (isset($node->menu)) {
    $link = &$node->menu;
    if (empty($link['enabled'])) {
      if (!empty($link['mlid'])) {
        menu_link_delete($link['mlid']);
      }
    }
    elseif (trim($link['link_title'])) {
      $link['link_title'] = trim($link['link_title']);
      $link['link_path'] = "node/$node->nid";
      if (trim($link['description'])) {
        $link['options']['attributes']['title'] = trim($link['description']);
      }
      else {
        // If the description field was left empty, remove the title attribute
        // from the menu link.
        unset($link['options']['attributes']['title']);
      }
      if (!menu_link_save($link)) {
        drupal_set_message(t('There was an error saving the menu link.'), 'error');
      }
    }
  }
}

/**
 * Implements hook_node_delete().
 */
function menu_node_delete($node) {
  // Delete all menu module links that point to this node.
  $result = db_query("SELECT mlid FROM {menu_links} WHERE link_path = :path AND module = 'menu'", array(':path' => 'node/' . $node->nid), array('fetch' => PDO::FETCH_ASSOC));
  foreach ($result as $m) {
    menu_link_delete($m['mlid']);
  }
}

/**
 * Implements hook_node_prepare().
 */
function menu_node_prepare($node) {
  if (empty($node->menu)) {
    // Prepare the node for the edit form so that $node->menu always exists.
    $menu_name = strtok(variable_get('menu_parent_' . $node->type, 'main-menu:0'), ':');
    $item = array();
    if (isset($node->nid)) {
      $mlid = FALSE;
      // Give priority to the default menu
      $type_menus = variable_get('menu_options_' . $node->type, array('main-menu' => 'main-menu'));
      if (in_array($menu_name, $type_menus)) {
        $mlid = db_query_range("SELECT mlid FROM {menu_links} WHERE link_path = :path AND menu_name = :menu_name AND module = 'menu' ORDER BY mlid ASC", 0, 1, array(
          ':path' => 'node/' . $node->nid,
          ':menu_name' => $menu_name,
        ))->fetchField();
      }
      // Check all allowed menus if a link does not exist in the default menu.
      if (!$mlid && !empty($type_menus)) {
        $mlid = db_query_range("SELECT mlid FROM {menu_links} WHERE link_path = :path AND module = 'menu' AND menu_name IN (:type_menus) ORDER BY mlid ASC", 0, 1, array(
          ':path' => 'node/' . $node->nid,
          ':type_menus' => array_values($type_menus),
        ))->fetchField();
      }
      if ($mlid) {
        $item = menu_link_load($mlid);
      }
    }
    // Set default values.
    $node->menu = $item + array(
      'link_title' => '',
      'mlid' => 0,
      'plid' => 0,
      'menu_name' => $menu_name,
      'weight' => 0,
      'options' => array(),
      'module' => 'menu',
      'expanded' => 0,
      'hidden' => 0,
      'has_children' => 0,
      'customized' => 0,
    );
  }
  // Find the depth limit for the parent select.
  if (!isset($node->menu['parent_depth_limit'])) {
    $node->menu['parent_depth_limit'] = _menu_parent_depth_limit($node->menu);
  }
}

/**
 * Find the depth limit for items in the parent select.
 */
function _menu_parent_depth_limit($item) {
  return MENU_MAX_DEPTH - 1 - (($item['mlid'] && $item['has_children']) ? menu_link_children_relative_depth($item) : 0);
}

/**
 * Calculate the delta for the weight element for a given set of menus.
 *
 * @param string|array $menu_names
 *   Menu name or an array of menu names.
 * @param int $max_delta
 *   Optional maximum value.
 *
 * @return int
 *   Delta value.
 */
function _menu_get_menu_weight_delta($menu_names, $max_delta = NULL) {

  if (is_string($menu_names)) {
    $menu_names = array($menu_names);
  }

  $weight_info = db_query("SELECT MAX(weight) AS max_weight, MIN(weight) as min_weight FROM {menu_links} WHERE menu_name IN (:menu_names)", array(':menu_names' => $menu_names))->fetchObject();

  $delta = max(abs((int) $weight_info->min_weight), abs((int) $weight_info->max_weight)) + 1;

  // Honor max param, if given.
  if (!is_null($max_delta) && $delta > $max_delta) {
    $delta = $max_delta;
  }

  // Provide a minimum.
  if ($delta < 50) {
    $delta = 50;
  }
  return $delta;
}

/**
 * Implements hook_form_BASE_FORM_ID_alter().
 *
 * Adds menu item fields to the node form.
 *
 * @see menu_node_submit()
 */
function menu_form_node_form_alter(&$form, $form_state) {
  // Generate a list of possible parents (not including this link or descendants).
  // @todo This must be handled in a #process handler.
  $link = $form['#node']->menu;
  $type = $form['#node']->type;
  // menu_parent_options() is goofy and can actually handle either a menu link
  // or a node type both as second argument. Pick based on whether there is
  // a link already (menu_node_prepare() sets mlid default to 0).
  $options = menu_parent_options(menu_get_menus(), $link['mlid'] ? $link : $type, $type);
  // If no possible parent menu items were found, there is nothing to display.
  if (empty($options)) {
    return;
  }

  $form['menu'] = array(
    '#type' => 'fieldset',
    '#title' => t('Menu settings'),
    '#access' => user_access('administer menu'),
    '#collapsible' => TRUE,
    '#collapsed' => !$link['link_title'],
    '#group' => 'additional_settings',
    '#attached' => array(
      'js' => array(drupal_get_path('module', 'menu') . '/menu.js'),
    ),
    '#tree' => TRUE,
    '#weight' => -2,
    '#attributes' => array('class' => array('menu-link-form')),
  );
  $form['menu']['enabled'] = array(
    '#type' => 'checkbox',
    '#title' => t('Provide a menu link'),
    '#default_value' => (int) (bool) $link['mlid'],
  );
  $form['menu']['link'] = array(
    '#type' => 'container',
    '#parents' => array('menu'),
    '#states' => array(
      'invisible' => array(
        'input[name="menu[enabled]"]' => array('checked' => FALSE),
      ),
    ),
  );

  // Populate the element with the link data.
  foreach (array('mlid', 'module', 'hidden', 'has_children', 'customized', 'options', 'expanded', 'hidden', 'parent_depth_limit') as $key) {
    $form['menu']['link'][$key] = array('#type' => 'value', '#value' => $link[$key]);
  }

  $form['menu']['link']['link_title'] = array(
    '#type' => 'textfield',
    '#title' => t('Menu link title'),
    '#maxlength' => 255,
    '#default_value' => $link['link_title'],
  );

  $form['menu']['link']['description'] = array(
    '#type' => 'textarea',
    '#title' => t('Description'),
    '#default_value' => isset($link['options']['attributes']['title']) ? $link['options']['attributes']['title'] : '',
    '#rows' => 1,
    '#description' => t('Shown when hovering over the menu link.'),
  );

  $default = ($link['mlid'] ? $link['menu_name'] . ':' . $link['plid'] : variable_get('menu_parent_' . $type, 'main-menu:0'));
  // If the current parent menu item is not present in options, use the first
  // available option as default value.
  // @todo User should not be allowed to access menu link settings in such a
  // case.
  if (!isset($options[$default])) {
    $array = array_keys($options);
    $default = reset($array);
  }
  $form['menu']['link']['parent'] = array(
    '#type' => 'select',
    '#title' => t('Parent item'),
    '#default_value' => $default,
    '#options' => $options,
    '#attributes' => array('class' => array('menu-parent-select')),
  );
  // Get number of items in all possible parent menus so the weight selector is
  // sized appropriately.
  $menu_names = array_keys(menu_get_menus());
  $menu_options = array();
  foreach ($menu_names as $menu_name) {
    if (isset($options[$menu_name . ':0'])) {
      $menu_options[] = $menu_name;
    }
  }
  // Make sure that we always have values in menu_options.
  $menu_options = !empty($menu_options) ? $menu_options : $menu_names;

  $form['menu']['link']['weight'] = array(
    '#type' => 'weight',
    '#title' => t('Weight'),
    '#delta' => _menu_get_menu_weight_delta($menu_options),
    '#default_value' => $link['weight'],
    '#description' => t('Menu links with smaller weights are displayed before links with larger weights.'),
  );
}

/**
 * Implements hook_node_submit().
 *
 * @see menu_form_node_form_alter()
 */
function menu_node_submit($node, $form, $form_state) {
  // Decompose the selected menu parent option into 'menu_name' and 'plid', if
  // the form used the default parent selection widget.
  if (!empty($form_state['values']['menu']['parent'])) {
    list($node->menu['menu_name'], $node->menu['plid']) = explode(':', $form_state['values']['menu']['parent']);
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 *
 * Adds menu options to the node type form.
 */
function menu_form_node_type_form_alter(&$form, $form_state) {
  $menu_options = menu_get_menus();
  $type = $form['#node_type'];
  $form['menu'] = array(
    '#type' => 'fieldset',
    '#title' => t('Menu settings'),
    '#collapsible' => TRUE,
    '#collapsed' => TRUE,
    '#attached' => array(
      'js' => array(drupal_get_path('module', 'menu') . '/menu.admin.js'),
    ),
    '#group' => 'additional_settings',
  );
  $form['menu']['menu_options'] = array(
    '#type' => 'checkboxes',
    '#title' => t('Available menus'),
    '#default_value' => variable_get('menu_options_' . $type->type, array('main-menu')),
    '#options' => $menu_options,
    '#description' => t('The menus available to place links in for this content type.'),
  );
  // To avoid an 'illegal option' error after saving the form we have to load
  // all available menu items.
  // Otherwise it is not possible to dynamically add options to the list.
  // @todo Convert menu_parent_options() into a #process callback.
  $options = menu_parent_options(menu_get_menus(), array('mlid' => 0));
  $form['menu']['menu_parent'] = array(
    '#type' => 'select',
    '#title' => t('Default parent item'),
    '#default_value' => variable_get('menu_parent_' . $type->type, 'main-menu:0'),
    '#options' => $options,
    '#description' => t('Choose the menu item to be the default parent for a new link in the content authoring form.'),
    '#attributes' => array('class' => array('menu-title-select')),
  );

  // Call Drupal.menu_update_parent_list() to filter the list of
  // available default parent menu items based on the selected menus.
  drupal_add_js(
    '(function ($) { Drupal.menu_update_parent_list(); })(jQuery);',
    array('scope' => 'footer', 'type' => 'inline')
  );
}

/**
 * Return an associative array of the custom menus names.
 *
 * @param $all
 *   If FALSE return only user-added menus, or if TRUE also include
 *   the menus defined by the system.
 * @return
 *   An array with the machine-readable names as the keys, and human-readable
 *   titles as the values.
 */
function menu_get_menus($all = TRUE) {
  if ($custom_menus = menu_load_all()) {
    if (!$all) {
      $custom_menus = array_diff_key($custom_menus, menu_list_system_menus());
    }
    foreach ($custom_menus as $menu_name => $menu) {
      $custom_menus[$menu_name] = t($menu['title']);
    }
    asort($custom_menus);
  }
  return $custom_menus;
}
