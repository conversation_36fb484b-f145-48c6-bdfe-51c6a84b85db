<?php

/**
 * @file
 * (X)HTML entities, as defined in HTML 4.01.
 *
 * @see http://www.w3.org/TR/html401/sgml/entities.html
 */

$html_entities = array(
  '&Aacute;' => 'Á',
  '&aacute;' => 'á',
  '&Acirc;' => 'Â',
  '&acirc;' => 'â',
  '&acute;' => '´',
  '&AElig;' => 'Æ',
  '&aelig;' => 'æ',
  '&Agrave;' => 'À',
  '&agrave;' => 'à',
  '&alefsym;' => 'ℵ',
  '&Alpha;' => 'Α',
  '&alpha;' => 'α',
  '&amp;' => '&',
  '&and;' => '∧',
  '&ang;' => '∠',
  '&Aring;' => 'Å',
  '&aring;' => 'å',
  '&asymp;' => '≈',
  '&Atilde;' => 'Ã',
  '&atilde;' => 'ã',
  '&Auml;' => 'Ä',
  '&auml;' => 'ä',
  '&bdquo;' => '„',
  '&Beta;' => 'Β',
  '&beta;' => 'β',
  '&brvbar;' => '¦',
  '&bull;' => '•',
  '&cap;' => '∩',
  '&Ccedil;' => 'Ç',
  '&ccedil;' => 'ç',
  '&cedil;' => '¸',
  '&cent;' => '¢',
  '&Chi;' => 'Χ',
  '&chi;' => 'χ',
  '&circ;' => 'ˆ',
  '&clubs;' => '♣',
  '&cong;' => '≅',
  '&copy;' => '©',
  '&crarr;' => '↵',
  '&cup;' => '∪',
  '&curren;' => '¤',
  '&dagger;' => '†',
  '&Dagger;' => '‡',
  '&darr;' => '↓',
  '&dArr;' => '⇓',
  '&deg;' => '°',
  '&Delta;' => 'Δ',
  '&delta;' => 'δ',
  '&diams;' => '♦',
  '&divide;' => '÷',
  '&Eacute;' => 'É',
  '&eacute;' => 'é',
  '&Ecirc;' => 'Ê',
  '&ecirc;' => 'ê',
  '&Egrave;' => 'È',
  '&egrave;' => 'è',
  '&empty;' => '∅',
  '&emsp;' => ' ',
  '&ensp;' => ' ',
  '&Epsilon;' => 'Ε',
  '&epsilon;' => 'ε',
  '&equiv;' => '≡',
  '&Eta;' => 'Η',
  '&eta;' => 'η',
  '&ETH;' => 'Ð',
  '&eth;' => 'ð',
  '&Euml;' => 'Ë',
  '&euml;' => 'ë',
  '&euro;' => '€',
  '&exist;' => '∃',
  '&fnof;' => 'ƒ',
  '&forall;' => '∀',
  '&frac12;' => '½',
  '&frac14;' => '¼',
  '&frac34;' => '¾',
  '&frasl;' => '⁄',
  '&Gamma;' => 'Γ',
  '&gamma;' => 'γ',
  '&ge;' => '≥',
  '&harr;' => '↔',
  '&hArr;' => '⇔',
  '&hearts;' => '♥',
  '&hellip;' => '…',
  '&Iacute;' => 'Í',
  '&iacute;' => 'í',
  '&Icirc;' => 'Î',
  '&icirc;' => 'î',
  '&iexcl;' => '¡',
  '&Igrave;' => 'Ì',
  '&igrave;' => 'ì',
  '&image;' => 'ℑ',
  '&infin;' => '∞',
  '&int;' => '∫',
  '&Iota;' => 'Ι',
  '&iota;' => 'ι',
  '&iquest;' => '¿',
  '&isin;' => '∈',
  '&Iuml;' => 'Ï',
  '&iuml;' => 'ï',
  '&Kappa;' => 'Κ',
  '&kappa;' => 'κ',
  '&Lambda;' => 'Λ',
  '&lambda;' => 'λ',
  '&lang;' => '〈',
  '&laquo;' => '«',
  '&larr;' => '←',
  '&lArr;' => '⇐',
  '&lceil;' => '⌈',
  '&ldquo;' => '“',
  '&le;' => '≤',
  '&lfloor;' => '⌊',
  '&lowast;' => '∗',
  '&loz;' => '◊',
  '&lrm;' => '‎',
  '&lsaquo;' => '‹',
  '&lsquo;' => '‘',
  '&macr;' => '¯',
  '&mdash;' => '—',
  '&micro;' => 'µ',
  '&middot;' => '·',
  '&minus;' => '−',
  '&Mu;' => 'Μ',
  '&mu;' => 'μ',
  '&nabla;' => '∇',
  '&nbsp;' => ' ',
  '&ndash;' => '–',
  '&ne;' => '≠',
  '&ni;' => '∋',
  '&not;' => '¬',
  '&notin;' => '∉',
  '&nsub;' => '⊄',
  '&Ntilde;' => 'Ñ',
  '&ntilde;' => 'ñ',
  '&Nu;' => 'Ν',
  '&nu;' => 'ν',
  '&Oacute;' => 'Ó',
  '&oacute;' => 'ó',
  '&Ocirc;' => 'Ô',
  '&ocirc;' => 'ô',
  '&OElig;' => 'Œ',
  '&oelig;' => 'œ',
  '&Ograve;' => 'Ò',
  '&ograve;' => 'ò',
  '&oline;' => '‾',
  '&Omega;' => 'Ω',
  '&omega;' => 'ω',
  '&Omicron;' => 'Ο',
  '&omicron;' => 'ο',
  '&oplus;' => '⊕',
  '&or;' => '∨',
  '&ordf;' => 'ª',
  '&ordm;' => 'º',
  '&Oslash;' => 'Ø',
  '&oslash;' => 'ø',
  '&Otilde;' => 'Õ',
  '&otilde;' => 'õ',
  '&otimes;' => '⊗',
  '&Ouml;' => 'Ö',
  '&ouml;' => 'ö',
  '&para;' => '¶',
  '&part;' => '∂',
  '&permil;' => '‰',
  '&perp;' => '⊥',
  '&Phi;' => 'Φ',
  '&phi;' => 'φ',
  '&Pi;' => 'Π',
  '&pi;' => 'π',
  '&piv;' => 'ϖ',
  '&plusmn;' => '±',
  '&pound;' => '£',
  '&prime;' => '′',
  '&Prime;' => '″',
  '&prod;' => '∏',
  '&prop;' => '∝',
  '&Psi;' => 'Ψ',
  '&psi;' => 'ψ',
  '&radic;' => '√',
  '&rang;' => '〉',
  '&raquo;' => '»',
  '&rarr;' => '→',
  '&rArr;' => '⇒',
  '&rceil;' => '⌉',
  '&rdquo;' => '”',
  '&real;' => 'ℜ',
  '&reg;' => '®',
  '&rfloor;' => '⌋',
  '&Rho;' => 'Ρ',
  '&rho;' => 'ρ',
  '&rlm;' => '‏',
  '&rsaquo;' => '›',
  '&rsquo;' => '’',
  '&sbquo;' => '‚',
  '&Scaron;' => 'Š',
  '&scaron;' => 'š',
  '&sdot;' => '⋅',
  '&sect;' => '§',
  '&shy;' => '­',
  '&Sigma;' => 'Σ',
  '&sigma;' => 'σ',
  '&sigmaf;' => 'ς',
  '&sim;' => '∼',
  '&spades;' => '♠',
  '&sub;' => '⊂',
  '&sube;' => '⊆',
  '&sum;' => '∑',
  '&sup1;' => '¹',
  '&sup2;' => '²',
  '&sup3;' => '³',
  '&sup;' => '⊃',
  '&supe;' => '⊇',
  '&szlig;' => 'ß',
  '&Tau;' => 'Τ',
  '&tau;' => 'τ',
  '&there4;' => '∴',
  '&Theta;' => 'Θ',
  '&theta;' => 'θ',
  '&thetasym;' => 'ϑ',
  '&thinsp;' => ' ',
  '&THORN;' => 'Þ',
  '&thorn;' => 'þ',
  '&tilde;' => '˜',
  '&times;' => '×',
  '&trade;' => '™',
  '&Uacute;' => 'Ú',
  '&uacute;' => 'ú',
  '&uarr;' => '↑',
  '&uArr;' => '⇑',
  '&Ucirc;' => 'Û',
  '&ucirc;' => 'û',
  '&Ugrave;' => 'Ù',
  '&ugrave;' => 'ù',
  '&uml;' => '¨',
  '&upsih;' => 'ϒ',
  '&Upsilon;' => 'Υ',
  '&upsilon;' => 'υ',
  '&Uuml;' => 'Ü',
  '&uuml;' => 'ü',
  '&weierp;' => '℘',
  '&Xi;' => 'Ξ',
  '&xi;' => 'ξ',
  '&Yacute;' => 'Ý',
  '&yacute;' => 'ý',
  '&yen;' => '¥',
  '&yuml;' => 'ÿ',
  '&Yuml;' => 'Ÿ',
  '&Zeta;' => 'Ζ',
  '&zeta;' => 'ζ',
  '&zwj;' => '‍',
  '&zwnj;' => '‌',
  '&gt;' => '>',
  '&lt;' => '<',
  '&quot;' => '"',
  // Add apostrophe (XML).
  '&apos;' => "'",
);
