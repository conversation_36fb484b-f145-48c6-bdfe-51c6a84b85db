# Sardegna Turismo - Setup Docker

Questo progetto utilizza Docker Compose per creare un ambiente di sviluppo completo con MySQL, Adminer e Drupal.

## Prerequisiti

- Docker
- Docker Compose
- Make (opzionale, per usare i comandi del Makefile)

## Avvio rapido

1. **Clona il repository e naviga nella directory del progetto**

2. **Configura le variabili d'ambiente**
   Il file `.env` è già configurato con valori di default per lo sviluppo locale.
   Puoi modificare le credenziali del database e altre configurazioni se necessario.

3. **Avvia i container**
   ```bash
   docker-compose up -d
   ```

   Oppure usando Make:
   ```bash
   make up
   ```

4. **Importa il database (se necessario)**
   Se hai un dump del database da importare:
   ```bash
   ./import-database.sh /path/to/your/dump.sql
   ```

5. **Verifica che i servizi siano attivi**
   ```bash
   docker-compose ps
   ```

## Servizi disponibili

- **Drupal**: http://localhost (porta 80)
- **Adminer**: http://localhost:8080 (gestione database)
- **MySQL**: localhost:3306
- **Memcache**: localhost:11211

## Credenziali database (default)

- **Host**: mysql (interno ai container) / localhost (esterno)
- **Database**: sardegnaturismosite
- **Username**: user
- **Password**: password
- **Root Password**: password

## Import del database

Il progetto include uno script automatizzato per l'import di database di grandi dimensioni:

```bash
./import-database.sh /path/to/your/dump.sql
```

Lo script:
- Verifica la connessione al database
- Ricrea il database pulito
- Importa il dump con configurazioni ottimizzate per file grandi
- Verifica l'import mostrando il numero di tabelle importate
- Supporta indicatori di progresso se `pv` è installato

## Comandi utili

### Usando Make
```bash
make up      # Avvia i container
make down    # Ferma i container
make stop    # Ferma i container (alias di down)
make prune   # Rimuove i container e i volumi
make ps      # Lista i container attivi
make logs    # Visualizza i log
make shell   # Accedi al container Drupal
```

### Usando Docker Compose direttamente
```bash
docker-compose up -d                    # Avvia in background
docker-compose down                     # Ferma e rimuove i container
docker-compose logs -f                  # Visualizza i log in tempo reale
docker-compose exec drupal bash         # Accedi al container Drupal
docker-compose exec mysql mysql -u root -p  # Accedi a MySQL
```

## Struttura dei container

- **mysql**: Database MySQL 5.7 con inizializzazione automatica
- **adminer**: Interfaccia web per gestire il database
- **drupal**: Container PHP 8.1 con Apache e Drupal
- **memcache**: Cache Memcached per migliorare le performance

## Configurazione

### File .env
Contiene tutte le variabili d'ambiente utilizzate dai container. Le principali sono:

- `PROJECT_NAME`: Nome del progetto (usato per i nomi dei container)
- `MYSQL_*`: Configurazioni del database
- `DRUPAL_*`: Configurazioni specifiche di Drupal
- `*_EXTERNAL_PORT`: Porte esposte sui servizi

### Volumi persistenti

- `./mysql-data`: Dati del database MySQL
- `./docroot/sites/default/files`: File caricati in Drupal

## Troubleshooting

### I container non si avviano
1. Verifica che le porte non siano già in uso:
   ```bash
   netstat -tulpn | grep :80
   netstat -tulpn | grep :3306
   netstat -tulpn | grep :8081
   ```

2. Controlla i log per errori:
   ```bash
   docker-compose logs
   ```

### Problemi di permessi
Se hai problemi con i permessi dei file:
```bash
sudo chown -R $USER:$USER ./docroot/sites/default/files
chmod -R 755 ./docroot/sites/default/files
```

### Reset completo
Per ripartire da zero:
```bash
make prune  # o docker-compose down -v
docker system prune -a
make up     # o docker-compose up -d
```

### Accesso al database
Puoi accedere al database in diversi modi:

1. **Tramite Adminer**: http://localhost:8080
2. **Tramite command line**:
   ```bash
   docker-compose exec mysql mysql -u user -p sardegnaturismosite
   ```
3. **Da un client esterno**: localhost:3306

## Sviluppo

Il codice Drupal è montato come volume, quindi le modifiche ai file PHP sono immediatamente visibili.

Per eseguire comandi Drush:
```bash
docker-compose exec drupal drush status
docker-compose exec drupal drush cc all
```

## Note di sicurezza

- Le credenziali di default sono per lo sviluppo locale
- Cambia sempre le password in produzione
- Il file `.env` contiene informazioni sensibili - non committarlo in produzione
